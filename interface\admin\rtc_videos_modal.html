<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>音视频播放</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
    <style>
        body {
            background-color: #f5f7fa;
            padding: 15px;
        }
        
        .video-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-top: 20px;
        }
        
        .video-item {
            position: relative;
            width: 100%;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            transition: all 0.3s;
            overflow: hidden;
            margin-bottom: 20px;
        }
        
        .video-item:hover {
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }
        
        .video-item video {
            width: 100%;
            max-height: 400px;
            border-radius: 8px 8px 0 0;
            background-color: #000;
        }
        
        .video-info {
            padding: 15px;
            border-top: 1px solid rgba(0, 0, 0, 0.05);
        }
        
        .video-time {
            color: #999;
            font-size: 13px;
        }
        
        .empty-message {
            text-align: center;
            padding: 50px 0;
            color: #999;
            font-style: italic;
        }
        
        .layui-btn-container {
            margin-top: 20px;
            text-align: center;
        }
        
        /* 加载动画 */
        .loading-container {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
        }
        
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(0, 0, 0, 0.1);
            border-radius: 50%;
            border-top-color: #1E9FFF;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }
    </style>
</head>

<body>
    <div class="layui-container">
        <div id="video-container" class="video-container">
            <div class="loading-container">
                <div class="loading-spinner"></div>
            </div>
        </div>
        
        <div class="layui-btn-container">
            <button type="button" class="layui-btn layui-btn-danger" id="delete-all-btn">
                <i class="layui-icon layui-icon-delete"></i> 删除所有视频
            </button>
        </div>
    </div>

    <script>
        layui.use(['layer'], function() {
            var $ = layui.$;
            var layer = layui.layer;
            
            // 获取URL参数
            var pid = getUrlParam('pid');
            if (!pid) {
                showError('参数错误：缺少房间ID');
                return;
            }
            
            // 加载视频列表
            loadVideos(pid);
            
            // 删除所有视频按钮点击事件
            $('#delete-all-btn').on('click', function() {
                layer.confirm('确定要删除所有视频记录吗？<br>删除后将无法恢复，且视频文件将从云存储中删除。', {
                    icon: 3,
                    title: '删除确认',
                    btn: ['确定删除', '取消']
                }, function(index) {
                    deleteAllVideos(pid);
                    layer.close(index);
                });
            });
            
            // 获取URL参数函数
            function getUrlParam(name) {
                var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
                var r = window.location.search.substr(1).match(reg);
                if (r != null) return unescape(r[2]); return null;
            }
            
            // 加载视频列表
            function loadVideos(pid) {
                $.ajax({
                    url: serverUrl + '/admin/rtc_videos/list',
                    type: 'POST',
                    data: {
                        pid: pid
                    },
                    success: function(res) {
                        if (res.code === 200) {
                            renderVideos(res.data);
                        } else {
                            showError(res.msg || '获取视频列表失败');
                        }
                    },
                    error: function(err) {
                        showError(err.responseJSON ? err.responseJSON.msg : '请求失败');
                    }
                });
            }
            
            // 渲染视频列表
            function renderVideos(videos) {
                var container = $('#video-container');
                container.empty();
                
                if (videos && videos.length > 0) {
                    videos.forEach(function(video, index) {
                        var videoHtml = `
                            <div class="video-item">
                                <video controls>
                                    <source src="${video.Path}" type="video/webm">
                                    您的浏览器不支持视频播放，请更换浏览器。
                                </video>
                                <div class="video-info">
                                    <div class="video-time">录制时间：${video.Create_time.replace('T', ' ').replace('Z', '')}</div>
                                </div>
                            </div>
                        `;
                        container.append(videoHtml);
                    });
                } else {
                    container.html('<div class="empty-message">暂无视频记录</div>');
                }
            }
            
            // 删除所有视频
            function deleteAllVideos(pid) {
                layer.load(2);
                $.ajax({
                    url: serverUrl + '/admin/rtc_videos/del',
                    type: 'POST',
                    data: {
                        pid: pid
                    },
                    success: function(res) {
                        layer.closeAll('loading');
                        if (res.code === 200) {
                            layer.msg('删除成功', {icon: 1, time: 1000});
                            // 刷新视频列表
                            loadVideos(pid);
                        } else {
                            layer.msg(res.msg || '删除失败', {icon: 2});
                        }
                    },
                    error: function(err) {
                        layer.closeAll('loading');
                        layer.msg(err.responseJSON ? err.responseJSON.msg : '请求失败', {icon: 2});
                    }
                });
            }
            
            // 显示错误信息
            function showError(message) {
                $('#video-container').html(`
                    <div class="empty-message">
                        <i class="layui-icon layui-icon-face-cry" style="font-size: 24px; margin-right: 10px;"></i>
                        ${message}
                    </div>
                `);
                $('#delete-all-btn').hide();
            }
        });
    </script>
</body>
</html>
