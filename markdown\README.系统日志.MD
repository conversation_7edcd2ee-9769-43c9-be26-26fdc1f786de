# 请帮我制作2个前端页面
## 数据结构
    id	int	ID
    type	smallint	日志类型：0系统日志1小程序日志
    user_id	int	操作人ID
    contents	varchar	日志内容
    ip	varchar	IP
    is_display	tinyint	标记删除则为0
    create_time	timestamp	发生时间

# 页面需求
    1、帮我做显示日志的前端列表页面：‘/interface/admin/system_logs_list.html’
    目前没有这个文件，你需要新建，页面排版参考：‘/interface/admin/drug_list.html’
    不要"添加"、"编辑"按钮
    API地址：/admin/system_logs/list
    参数：page、limit、key（不搜索则为空）
    显示字段：ID,TYPE(值为0则显示系统日志，否则小程序日志),USER_ID（通过/admin/user/list_low帮我渲染成具体操作者的姓名）,CONTENTS,create_time


    2、列表页的“详情”按钮，点过去需要1个详情页，这个详情页也需要你帮我新建并制作，文件路径：‘/interface/admin/system_logs_detail.html’
    详情页制作时参考‘/interface/admin/drug_detail.html’
    API地址：/admin/system_logs/detail
    接收参数：ID
    显示字段：ID,TYPE,USER_ID,CONTENTS,ip,create_time

    3、列表页中有个“删除”按钮，点击后向后端发送删除请求：
    API地址：/admin/system_logs/del
    参数：id
