<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 绑定售后员工</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
    <script src="/dist/js/xm-select.js"></script>
    <style>
        .info-panel {
            background-color: #f9f9f9;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
        }

        .info-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .info-item {
            margin-bottom: 10px;
        }

        .info-label {
            display: inline-block;
            width: 100px;
            text-align: right;
            padding-right: 10px;
            color: #666;
        }

        .btn-container {
            text-align: center;
            margin-top: 20px;
        }

        .layui-btn {
            min-width: 100px;
        }

        /* 错误提示样式 */
        .error-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(5px);
            z-index: 9999;
            display: flex;
            justify-content: center;
            align-items: center;
            opacity: 1;
            transition: opacity 0.3s ease;
        }

        .error-card {
            width: 400px;
            text-align: center;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.95);
            transform: translateY(0);
            transition: all 0.3s ease;
        }

        /* 页面模糊效果 */
        .page-blur {
            filter: blur(5px);
            pointer-events: none;
            user-select: none;
        }

        /* 美化xm-select树形结构 */
        .xm-tree-icon.xm-tree-icon-parent {
            border-width: 5px;
        }

        .xm-tree-icon.xm-tree-icon-parent.expand {
            border-color: transparent transparent transparent #4096ff;
        }

        .xm-tree-icon.xm-tree-icon-leaf {
            border: 0;
            margin-right: 3px;
        }

        /* 选中态样式增强 */
        .xm-tree-show .xm-option.selected {
            background-color: #e6f4ff !important;
        }

        /* 调整tree节点间距 */
        .xm-tree-show {
            padding: 5px 0 !important;
        }

        .xm-tree-show .xm-option {
            padding-left: 30px !important;
        }

        .xm-tree-show .xm-tree-item .xm-option {
            padding-left: 35px !important;
        }

        .xm-tree-show .xm-tree-item .xm-tree-item .xm-option {
            padding-left: 60px !important;
        }
    </style>
</head>

<body>
    <div class="layui-padding-3">
        <!-- 目标转移信息 -->
        <div class="info-panel">
            <!-- <div class="info-title">绑定售后员工</div> -->
            <div class="warning_tips">
                售前转订单到售后，只是转的订单；您当前的操作，是将该订单对应的客户由售后部门分配到了具体的组、人，这将意味着该用户的所有数据，如订单、病历、处方等将一并被转移。
            </div>
            <div class="layui-form">
                <div class="info-item">
                    <label class="info-label">售后部门：</label>
                    <div class="layui-inline" style="width: 260px;">
                        <div id="after_dep_tree" style="min-width: 100px;"></div>
                    </div>
                </div>
                <div class="info-item">
                    <label class="info-label">售后客服：</label>
                    <div class="layui-inline" style="width: 260px;">
                        <select name="after_staff_id" id="after_staff_id" lay-filter="after_staff_id">
                            <option value="">请先选择部门</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="btn-container">
            <button type="button" class="layui-btn" id="confirm_transfer">确认绑定</button>
            <button type="button" class="layui-btn layui-btn-primary" id="cancel_btn">取消</button>
        </div>
    </div>

    <!-- 错误提示覆盖层 -->
    <div class="error-overlay" style="display: none;">
        <div class="error-card layui-card">
            <div class="layui-card-body">
                <p style="font-size: 16px; padding: 20px 10px; color: #333;" id="error-message"></p>
                <div style="padding: 10px 0 20px 0;">
                    <button class="layui-btn layui-btn-normal return-btn" style="padding: 0 30px;">返回</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        layui.use(['form', 'layer', 'jquery'], function() {
            var form = layui.form;
            var layer = layui.layer;
            var $ = layui.jquery;

            // 全局变量
            var departmentData = JSON.parse(localStorage.getItem('local_departments') || '[]');
            var order_id = request.get('order_id');
            var pat_id = request.get('pat_id');
            var dep_id = request.get('dep_id');
            var selectedDepId = null;
            var selectedStaffId = null;

            // 初始化检查
            if (!order_id || !pat_id || !dep_id) {
                layer.msg('参数错误，缺少必要参数', {icon: 2, time: 2000});
                return false;
            }

            // 初始化部门树
            initDepartmentTree();

            // 初始化部门树函数
            function initDepartmentTree() {
                // 过滤出以dep_id为顶级节点的部门数据
                var filteredData = filterDepartmentData(departmentData, parseInt(dep_id));

                if (filteredData.length === 0) {
                    layer.msg('未找到相关部门数据', {icon: 2, time: 2000});
                    return;
                }

                // 初始化xm-select组件
                var departmentSelect = xmSelect.render({
                    el: '#after_dep_tree',
                    model: { label: { type: 'text' } },
                    radio: true,
                    clickClose: true,
                    empty: '没有可选择的部门',
                    searchTips: '输入关键词搜索',
                    toolbar: {
                        show: true,
                        list: ['CLEAR']
                    },
                    filterable: true,
                    height: 'auto',
                    data: filteredData,
                    tree: {
                        show: true,
                        strict: true,
                        expandedKeys: true,
                        autoExpandParent: true
                    },
                    theme: {
                        color: '#4096ff',
                    },
                    on: function(data) {
                        if(data.isAdd && data.change.length > 0) {
                            // 设置选中的部门ID
                            selectedDepId = data.change[0].value;
                            console.log("选中的部门ID：", selectedDepId);

                            // 加载该部门下的售后客服
                            loadAfterStaff(selectedDepId);

                            return data.change.slice(0, 1); // 确保只能选一个
                        }
                    }
                });
            }

            // 过滤部门数据，只保留指定顶级节点及其子节点
            function filterDepartmentData(data, topId) {
                // 找到顶级节点
                var topNode = data.find(item => item.Id === topId);
                if (!topNode) return [];

                // 构建树形结构
                var treeData = [];
                var idMapping = {};

                // 创建ID到对象的映射
                data.forEach(item => {
                    if (isNodeOrDescendant(item, topId, data)) {
                        idMapping[item.Id] = {
                            name: item.Name,
                            value: item.Id,
                            pid: item.Pid,
                            children: []
                        };
                    }
                });

                // 构建树形结构
                data.forEach(item => {
                    if (isNodeOrDescendant(item, topId, data)) {
                        // 当前节点
                        const node = idMapping[item.Id];

                        // 如果是顶级节点，直接添加到树中
                        if (item.Id === topId) {
                            treeData.push(node);
                        }
                        // 否则添加到父节点的children数组
                        else if (idMapping[item.Pid]) {
                            idMapping[item.Pid].children.push(node);
                        }
                    }
                });

                // 对各级节点按Sort字段排序
                function sortTree(nodes) {
                    if (!nodes || !nodes.length) return nodes;

                    // 查找对应的原始数据以获取Sort值
                    nodes.sort((a, b) => {
                        const aSort = data.find(item => item.Id === a.value)?.Sort || 0;
                        const bSort = data.find(item => item.Id === b.value)?.Sort || 0;
                        return bSort - aSort; // 降序排列
                    });

                    // 递归排序子节点
                    nodes.forEach(node => {
                        if (node.children && node.children.length) {
                            sortTree(node.children);
                        }
                    });

                    return nodes;
                }

                // 对树进行排序
                sortTree(treeData);

                return treeData;
            }

            // 判断节点是否是指定顶级节点或其后代
            function isNodeOrDescendant(node, topId, data) {
                if (node.Id === topId) return true;

                // 向上查找父节点
                let currentId = node.Pid;
                let loopProtection = 0;

                while (currentId && loopProtection < 100) {
                    if (currentId === topId) return true;

                    const parentNode = data.find(item => item.Id === currentId);
                    if (!parentNode) break;

                    currentId = parentNode.Pid;
                    loopProtection++;
                }

                return false;
            }

            // 加载售后客服
            function loadAfterStaff(depId) {
                if (!depId) {
                    $('#after_staff_id').html('<option value="">请先选择部门</option>');
                    form.render('select');
                    return;
                }

                layer.load(2);
                $.ajax({
                    url: '/admin/user/list_low',
                    type: 'post',
                    data: {
                        department_id: depId
                    },
                    success: function(res) {
                        layer.closeAll('loading');
                        if (res.code === 200 && res.data && res.data.length > 0) {
                            var html = '<option value="">请选择售后客服</option>';
                            for (var i = 0; i < res.data.length; i++) {
                                html += '<option value="' + res.data[i].ID + '">' + res.data[i].Name + '</option>';
                            }
                            $('#after_staff_id').html(html);
                            form.render('select');
                        } else {
                            $('#after_staff_id').html('<option value="">该部门下暂无客服</option>');
                            form.render('select');
                            layer.msg('该部门下暂无客服', {icon: 0, time: 1500});
                        }
                    },
                    error: function(xhr) {
                        layer.closeAll('loading');
                        $('#after_staff_id').html('<option value="">请求失败</option>');
                        form.render('select');
                        layer.msg('请求失败：' + xhr.responseText, {icon: 2, time: 2000});
                    }
                });
            }

            // 监听售后客服选择
            form.on('select(after_staff_id)', function(data) {
                selectedStaffId = data.value;
                console.log("选中的售后客服ID：", selectedStaffId);
            });

            // 确认绑定按钮点击事件
            $('#confirm_transfer').click(function() {
                if (!selectedDepId) {
                    layer.msg('请选择售后部门', {icon: 2, time: 1500});
                    return false;
                }

                if (!selectedStaffId) {
                    layer.msg('请选择售后客服', {icon: 2, time: 1500});
                    return false;
                }

                // 执行绑定操作
                transferPatient();
            });

            // 取消按钮点击事件
            $('#cancel_btn').click(function() {
                var index = parent.layer.getFrameIndex(window.name);
                parent.layer.close(index);
            });

            // 错误提示中的返回按钮点击事件
            $('.return-btn').click(function() {
                var index = parent.layer.getFrameIndex(window.name);
                parent.layer.close(index);
            });

            // 执行绑定操作
            function transferPatient() {
                layer.load(2);
                $.ajax({
                    url: '/admin/patient_account/asst_transfer',
                    type: 'post',
                    data: {
                        pat_id: pat_id,
                        asst_dep_id: selectedDepId,
                        asst_id: selectedStaffId,
                        order_id: order_id
                    },
                    success: function(res) {
                        layer.closeAll('loading');
                        if (res.code === 200) {
                            layer.msg(res.msg || '绑定成功', {icon: 1, time: 1000});
                            // 关闭当前弹窗并刷新父页面
                            setTimeout(function() {
                                var index = parent.layer.getFrameIndex(window.name);
                                parent.location.reload();
                                parent.layer.close(index);
                            }, 1000);
                        } else {
                            layer.msg(res.msg || '绑定失败', {icon: 2, time: 2000});
                        }
                    },
                    error: function(xhr) {
                        layer.closeAll('loading');
                        layer.msg('操作失败：' + xhr.responseText, {icon: 2, time: 2000});
                    }
                });
            }
        });
    </script>
</body>

</html>