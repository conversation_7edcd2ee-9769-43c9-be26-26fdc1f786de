<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 转给售后</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
    <style>
        .info-panel {
            background-color: #f9f9f9;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
        }

        .info-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .info-item {
            margin-bottom: 10px;
        }

        .info-label {
            display: inline-block;
            width: 100px;
            text-align: right;
            padding-right: 10px;
            color: #666;
        }

        .btn-container {
            text-align: center;
            margin-top: 20px;
        }

        .layui-btn {
            min-width: 100px;
        }

        /* 错误提示样式 */
        .error-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(5px);
            z-index: 9999;
            display: flex;
            justify-content: center;
            align-items: center;
            opacity: 1;
            transition: opacity 0.3s ease;
        }

        .error-card {
            width: 400px;
            text-align: center;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.95);
            transform: translateY(0);
            transition: all 0.3s ease;
        }

        /* 页面模糊效果 */
        .page-blur {
            filter: blur(5px);
            pointer-events: none;
            user-select: none;
        }
    </style>
</head>

<body>
    <div class="layui-padding-3">
        <!-- 患者信息 -->
        <div class="info-panel">
            <div class="info-title">患者信息</div>
            <div class="info-item">
                <span class="info-label">帐号ID：</span>
                <span id="cust_id">-</span>
            </div>
            <div class="info-item">
                <span class="info-label">客户姓名：</span>
                <span id="cust_name">-</span>
            </div>
            <div class="info-item">
                <span class="info-label">创建时间：</span>
                <span id="create_time">-</span>
            </div>
        </div>

        <!-- 当前所属信息 -->
        <div class="info-panel">
            <div class="info-title">当前所属信息</div>
            <div class="info-item">
                <span class="info-label">售前部门：</span>
                <span id="current_dep">-</span>
            </div>
            <div class="info-item">
                <span class="info-label">当前医助：</span>
                <span id="current_asst">-</span>
            </div>
        </div>

        <!-- 目标转移信息 -->
        <div class="info-panel">
            <div class="info-title">目标转移信息</div>
            <div class="layui-form">
                <div class="info-item">
                    <label class="info-label">售后部门：</label>
                    <div class="layui-inline" style="width: 260px;">
                        <select name="after_dep_id" id="after_dep_id" lay-filter="after_dep_id">
                            <option value="">请先选择部门</option>
                        </select>
                    </div>
                </div>

            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="btn-container">
            <button type="button" class="layui-btn" id="confirm_transfer">确认转移</button>
            <button type="button" class="layui-btn layui-btn-primary" id="cancel_btn">取消</button>
        </div>
    </div>

    <!-- 错误提示覆盖层 -->
    <div class="error-overlay" style="display: none;">
        <div class="error-card layui-card">
            <div class="layui-card-body">
                <p style="font-size: 16px; padding: 20px 10px; color: #333;" id="error-message"></p>
                <div style="padding: 10px 0 20px 0;">
                    <button class="layui-btn layui-btn-normal return-btn" style="padding: 0 30px;">返回</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        layui.use(['form', 'layer', 'jquery'], function() {
            var form = layui.form;
            var layer = layui.layer;
            var $ = layui.jquery;
            // 全局变量存储部门数据
            var departmentData = JSON.parse(localStorage.getItem('local_departments') || '[]');
            // 存储售前部门数字
            var preSaleDeptNumber = null;
            // 存储账号ID
            var accountPid = null;

            // 获取URL中的患者ID参数
            var order_id = request.get('id');
            var patId = request.get('patid');
            if (!patId) {
                layer.msg('参数错误', {icon: 2, time: 1000});
                return false;
            }

            // 检查订单状态
            checkOrderStatus(order_id);

            // 从部门名称中提取数字
            function extractNumberFromDeptName(deptName) {
                if (!deptName || typeof deptName !== 'string') return null;

                // 匹配"售前X部"或"售前X组"格式，提取数字X
                var match = deptName.match(/售前(\d+)[部组]/);
                if (match && match[1]) {
                    return match[1];
                }
                return null;
            }

            // 检查订单状态函数
            function checkOrderStatus(orderId) {
                if (!orderId) {
                    return;
                }

                $.ajax({
                    url: '/normal/get_order_status',
                    type: 'post',
                    data: {
                        order_id: orderId
                    },
                    success: function(res) {
                        if (res.code === 200) {
                            var status = res.data;
                            // 如果状态不是6（待转售后），则显示错误提示
                            if (status !== 6) {
                                // 添加模糊效果
                                $('.layui-padding-3').addClass('page-blur');

                                // 显示错误提示
                                var statusText = Order_Status[status] || '未知状态';
                                $('#error-message').text('该订单不应在本流程操作，当前状态：' + statusText);
                                $('.error-overlay').show();
                            } else {
                                // 正常加载页面内容
                                loadPatientInfo(patId, function() {
                                    // 患者信息加载完成后，再加载售后部门列表
                                    loadAfterDepartments();
                                });
                            }
                        } else {
                            layer.msg(res.msg || '获取订单状态失败', {icon: 2, time: 2000});
                        }
                    },
                    error: function(xhr) {
                        layer.msg('请求失败：' + xhr.responseText, {icon: 2, time: 2000});
                    }
                });
            }

            // 不再需要初始加载医助列表

            // 确认转移按钮点击事件
            $('#confirm_transfer').click(function() {
                var afterDepId = $('#after_dep_id').val();
                if (!afterDepId) {
                    layer.msg('请选择售后部门', {icon: 2, time: 1000});
                    return false;
                }

                // 执行转移操作
                transferPatient(order_id, afterDepId);
            });

            // 取消按钮点击事件
            $('#cancel_btn').click(function() {
                var index = parent.layer.getFrameIndex(window.name);
                parent.layer.close(index);
            });

            // 错误提示中的返回按钮点击事件
            $('.return-btn').click(function() {
                var index = parent.layer.getFrameIndex(window.name);
                parent.layer.close(index);
            });

            // 加载患者信息函数
            function loadPatientInfo(patId, callback) {
                layer.load(2);
                $.ajax({
                    url: '/admin/patient_profile/detail',
                    type: 'post',
                    data: {id: patId,order_id:order_id},
                    success: function(res) {
                        layer.closeAll('loading');
                        if (res.code === 200) {
                            var data = res.data;
                            // 保存账号ID到全局变量
                            accountPid = data.Pid;
                            $('#cust_id').text(data.Pid);
                            $('#cust_name').text(data.Name);
                            $('#create_time').text(Utc2time(data.Create_time));

                            // 加载当前医助信息
                            if (data.Asst_id) {
                                // 加载医助信息，同时获取部门信息
                                loadAssistantInfo(data.Asst_id, callback);
                            } else {
                                $('#current_dep').text('-');
                                $('#current_asst').text('-');
                                // 如果没有医助信息，直接执行回调
                                if (typeof callback === 'function') {
                                    callback();
                                }
                            }
                        } else {
                            layer.msg(res.msg, {icon: 2, time: 1000});
                            // 出错时也执行回调，确保流程继续
                            if (typeof callback === 'function') {
                                callback();
                            }
                        }
                    },
                    error: function(xhr) {
                        layer.closeAll('loading');
                        layer.msg('加载失败：' + xhr.responseText, {icon: 2, time: 2000});
                        // 出错时也执行回调，确保流程继续
                        if (typeof callback === 'function') {
                            callback();
                        }
                    }
                });
            }

            // 加载售后部门列表 - 简化版，仅用于显示
            function loadAfterDepartments() {
                layer.load(2);
                $.ajax({
                    url: '/admin/department/list',
                    type: 'post',
                    data: {
                        name: '售后部门',
                        store_id: global_default_store_id
                    },
                    success: function(res) {
                        layer.closeAll('loading');
                        if (res.code === 200) {
                            var data = res.data;
                            var html = '<option value="">请选择售后部门</option>';
                            var matchedDeptId = null;

                            // 获取当前用户信息
                            let local_userinfo = localStorage.getItem('local_userinfo');
                            let isAdmin = false;

                            if (local_userinfo) {
                                local_userinfo = JSON.parse(local_userinfo);
                                // 检查是否为超级管理员
                                isAdmin = local_userinfo.Id === 1;
                            }

                            for (var i = 0; i < data.length; i++) {
                                var isSelected = '';
                                var isDisabled = '';
                                var matchFound = false;

                                // 如果有售前部门数字，尝试匹配售后部门
                                if (preSaleDeptNumber) {
                                    // 匹配"售后X部"或"售后X组"格式
                                    var match = data[i].name.match(/售后(\d+)[部组]/);
                                    if (match && match[1]) {
                                        // 确保类型一致，都转为字符串进行比较
                                        if (String(match[1]) === String(preSaleDeptNumber)) {
                                            isSelected = ' selected';
                                            matchedDeptId = data[i].id;
                                            matchFound = true;
                                        }
                                    }
                                }

                                // 如果不是超级管理员且有匹配的售前部门数字，则禁用不匹配的选项
                                if (!isAdmin && preSaleDeptNumber && !matchFound) {
                                    isDisabled = ' disabled';
                                }

                                html += '<option value="' + data[i].id + '"' + isSelected + isDisabled + '>' + data[i].name + '</option>';
                            }

                            $('#after_dep_id').html(html);
                            form.render('select');
                        } else {
                            layer.msg(res.msg, {icon: 2, time: 1000});
                        }
                    },
                    error: function(xhr) {
                        layer.closeAll('loading');
                        layer.msg('加载失败：' + xhr.responseText, {icon: 2, time: 2000});
                    }
                });
            }

            // 加载医助信息
            function loadAssistantInfo(asstId, callback) {
                if (!asstId) {
                    $('#current_asst').text('-');
                    $('#current_dep').text('-');
                    // 如果没有医助ID，直接执行回调
                    if (typeof callback === 'function') {
                        callback();
                    }
                    return;
                }

                layer.load(2);
                $.ajax({
                    url: '/admin/user/list_low',
                    type: 'post',
                    data: {
                        user_id_list: asstId
                    },
                    success: function(res) {
                        layer.closeAll('loading');
                        if (res.code === 200 && res.data && res.data.length > 0) {
                            var assistant = res.data[0];
                            $('#current_asst').text(assistant.Name);

                            // 获取医助的部门信息
                            if (assistant.Department_id) {
                                // 如果医助有部门ID，使用findDepartmentName函数查找部门名称
                                findDepartmentName(assistant.Department_id, callback);
                            } else {
                                // 如果医助没有部门ID，尝试使用id2department函数
                                if (departmentData && departmentData.length > 0) {
                                    // 使用type=1参数获取完整的部门层级路径
                                    var depName = id2department(parseInt(asstId), departmentData, 1);
                                    if (depName && depName !== '-') {
                                        $('#current_dep').text(depName);
                                        // 提取售前部门数字 - 仍然使用最后一级部门名称
                                        var lastLevelName = depName.split(' - ').slice(-2, -1)[0];
                                        preSaleDeptNumber = extractNumberFromDeptName(lastLevelName);
                                    } else {
                                        $('#current_dep').text('未知部门');
                                    }
                                } else {
                                    $('#current_dep').text('未知部门');
                                }

                                // 执行回调
                                if (typeof callback === 'function') {
                                    callback();
                                }
                            }
                        } else {
                            $('#current_asst').text('-');
                            $('#current_dep').text('-');

                            // 执行回调
                            if (typeof callback === 'function') {
                                callback();
                            }
                        }
                    },
                    error: function(xhr) {
                        layer.closeAll('loading');
                        $('#current_asst').text('-');
                        $('#current_dep').text('-');


                        // 执行回调
                        if (typeof callback === 'function') {
                            callback();
                        }
                    }
                });
            }

            // 直接查询部门名称
            function findDepartmentName(deptId, callback) {
                if (!deptId) {
                    $('#current_dep').text('-');
                    // 如果没有部门ID，直接执行回调
                    if (typeof callback === 'function') {
                        callback();
                    }
                    return;
                }

                // 先尝试从缓存中查找
                if (departmentData && departmentData.length > 0) {
                    // 确保部门ID是数字类型
                    var deptIdNum = parseInt(deptId);
                    // 使用id2department函数获取完整的部门层级路径
                    var deptName = id2department(deptIdNum, departmentData, 1);
                    if (deptName && deptName !== '-') {
                        $('#current_dep').text(deptName);

                        // 提取售前部门数字 - 使用最后一级部门名称
                        var lastLevelName = deptName.split(' - ').slice(-2, -1)[0];
                        preSaleDeptNumber = extractNumberFromDeptName(lastLevelName);

                        // 执行回调
                        if (typeof callback === 'function') {
                            callback();
                        }
                        return;
                    }
                }

                // 如果缓存中没有找到，直接请求部门信息
                layer.load(2);
                $.ajax({
                    url: '/admin/department/detail',
                    type: 'post',
                    data: {id: deptId},
                    success: function(res) {
                        layer.closeAll('loading');
                        if (res.code === 200 && res.data) {
                            var deptName = res.data.name || '未知部门';

                            // 尝试从部门数据中查找完整路径
                            if (departmentData && departmentData.length > 0) {
                                // 将获取到的部门添加到departmentData中，以便id2department函数能找到它
                                var found = false;
                                for (var i = 0; i < departmentData.length; i++) {
                                    if (departmentData[i].Id === parseInt(deptId)) {
                                        found = true;
                                        break;
                                    }
                                }
                                if (!found) {
                                    // 临时添加到departmentData
                                    departmentData.push({
                                        Id: parseInt(deptId),
                                        Pid: res.data.pid || 0,
                                        Name: deptName
                                    });
                                }
                                // 再次尝试获取完整路径
                                var fullPath = id2department(parseInt(deptId), departmentData, 1);
                                if (fullPath && fullPath !== '-') {
                                    deptName = fullPath;
                                }
                            }
                            $('#current_dep').text(deptName);
                            var lastLevelName = deptName.split(' - ').slice(-2, -1)[0];
                            preSaleDeptNumber = extractNumberFromDeptName(lastLevelName);
                        } else {
                            $('#current_dep').text('部门ID: ' + deptId);
                        }

                        // 执行回调
                        if (typeof callback === 'function') {
                            callback();
                        }
                    },
                    error: function(xhr) {
                        layer.closeAll('loading');
                        // 出错时直接显示部门ID
                        $('#current_dep').text('部门ID: ' + deptId);

                        // 执行回调
                        if (typeof callback === 'function') {
                            callback();
                        }
                    }
                });
            }

            // 执行转移操作
            function transferPatient(order_id, asst_dep_id) {
                // 确保使用账号ID而不是URL中的ID
                if (!accountPid) {
                    layer.msg('账号ID获取失败，请刷新页面重试', {icon: 2, time: 2000});
                    return;
                }

                layer.load(2);
                $.ajax({
                    url: '/admin/patient_account/transfer',
                    type: 'post',
                    data: {
                        id: accountPid, // 使用账号ID (Pid)而不是URL中的ID
                        asst_dep_id: asst_dep_id,
                        order_id:order_id
                    },
                    success: function(res) {
                        layer.closeAll('loading');
                        if (res.code === 200) {
                            layer.msg(res.msg || '转移成功', {icon: 1, time: 1000});
                            // 关闭当前弹窗并刷新父页面
                            setTimeout(function() {
                                var index = parent.layer.getFrameIndex(window.name);
                                parent.location.reload();
                                parent.layer.close(index);
                            }, 1000);
                        } else {
                            layer.msg(res.msg || '转移失败', {icon: 2, time: 2000});
                        }
                    },
                    error: function(xhr) {
                        layer.closeAll('loading');
                        layer.msg('操作失败：' + xhr.responseText, {icon: 2, time: 2000});
                    }
                });
            }
        });
    </script>
</body>

</html>