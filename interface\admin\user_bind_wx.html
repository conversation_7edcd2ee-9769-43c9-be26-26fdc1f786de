<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 绑定微信</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
    <style>
        body {
            width: 480px;
            padding: 30px 50px 0 50px;
        }

        #sign_img {
            border-radius: 5px;
            width: 150px;
            min-height: 50px;
        }

        #avatar_img {
            border-radius: 50%;
            border: 1px solid #ccc;
            width: 80px;
            height: 80px;
        }

        #Department i {
            margin-right: 5px;
        }

        .preview-text {
            color: #1E9FFF;
            font-size: 12px;
            margin-left: 10px;
            cursor: pointer;
        }

        .preview-img {
            display: inline-block;
            margin-left: 20px;
        }

        .bind_status {
            margin: 70px auto 0 auto;
            font-size: 17px;
            font-weight: 700;
        }
    </style>
</head>

<body>

    <div style="text-align: center;margin-top: 20px;">
        <img id="show">
        <div class="bind_status">绑定检测...</div>
    </div>


    <script>
        layui.use(['form', 'upload', 'jquery', 'layer'], function () {
            var form = layui.form;
            var upload = layui.upload;
            var $ = layui.jquery;
            var layer = layui.layer;
            var dropdown = layui.dropdown;
            var index = parent.layer.getFrameIndex(window.name);
            // 获取ID参数
            let department_id = request.get('department_id')
            let user_id = request.get('user_id')
            if (!department_id || !user_id) {
                layer.msg('参数错误，请联系管理员！', { icon: 2, time: 2000 });
                return false;
            }
            let wx_qr_code = `/static/uploads/wx_qr_code/${department_id}_${user_id}.jpg?rand=${Math.random()}`;
            var image = new Image();
            image.src = wx_qr_code;
            image.onload = function () {
                $('#show').attr('src', wx_qr_code)
            };
            image.onerror = function () {
                layer.load(2);
                $.ajax({
                    url: '/admin/get_wx_acode',
                    type: 'post',
                    data: { department_id: department_id, user_id: user_id },
                    dataType: 'json',
                    success: function (res) {
                        layer.closeAll('loading');
                        if (res.code == 200) {
                            $('#show').attr('src', wx_qr_code)
                            layer.msg('获取微信二维码成功！');
                        } else {
                            layer.msg(res.msg, { icon: 2, time: 2000 });
                        }
                    },
                    error: function (res) {
                        layer.closeAll('loading');
                        layer.msg(res.responseJSON.msg, { icon: 2, time: 5000 });
                    }
                });
            };
            var check_bind = function () {
                $.ajax({
                    url: '/admin/user/check_bind_wx',
                    type: 'post',
                    data: { department_id: department_id, user_id: user_id },
                    dataType: 'json',
                    success: function (res) {
                        console.log(res.data.openid)
                        if (res.data.openid !== "") {
                            $('.bind_status').html('<font color="green">已绑定微信</font> <button class="layui-btn layui-btn-xs unbind_wx">解除绑定</button>');
                            // 解除绑定
                            $('.unbind_wx').on('click', function () {
                                layer.confirm('确定解除绑定微信吗？', { icon: 3, title: '提示' }, function (index) {
                                    $.ajax({
                                        url: '/admin/user/unbind_wx',
                                        type: 'post',
                                        data: { department_id: department_id, user_id: user_id },
                                        dataType: 'json',
                                        success: function (res) {
                                            if (res.code == 200) {
                                                layer.msg('解除绑定成功！', { icon: 1, time: 2000 });
                                                $('.bind_status').html('<font color="red">未绑定微信</font> <button class="layui-btn layui-btn-xs refresh_wx">刷新</button>');
                                                // 刷新
                                                $('.refresh_wx').on('click', function () {
                                                    window.location.reload();
                                                });
                                            } else {
                                                layer.msg(res.msg, { icon: 2, time: 2000 });
                                            }
                                        },
                                        error: function (err) {
                                            layer.msg('解除绑定失败，请稍后重试！', { icon: 2, time: 2000 });
                                        }
                                    });
                                    layer.close(index);
                                });
                            });
                        } else {
                            $('.bind_status').html('<font color="red">未绑定微信</font> <button class="layui-btn layui-btn-xs refresh_wx">刷新</button>');
                            // 刷新
                            $('.refresh_wx').on('click', function () {
                                window.location.reload();
                            });
                        }
                    },
                    error: function (err) {
                        layer.msg('获取绑定状态失败，请稍后重试！', { icon: 2, time: 2000 });
                    }
                });
            };
            setInterval(check_bind, 2000);
        });
    </script>
</body>

</html>