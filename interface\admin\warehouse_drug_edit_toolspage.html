<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>药材选择-修改</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
    <style>
        .layui-table-cell {
            height: auto;
            line-height: 28px;
        }

        /* 添加的新样式 */
        .layui-btn-group {
            margin-bottom: 10px;
        }

        .layui-btn-group .layui-btn {
            margin-right: 5px;
        }

        .layui-input-group {
            display: flex;
            margin-bottom: 10px;
        }

        .layui-input-group .layui-input {
            margin-right: 5px;
        }

        /* 页面布局样式 */
        .page-container {
            display: flex;
            height: calc(100vh - 110px);
        }

        /* 左侧已选药品样式 */
        .selected-drugs-panel {
            width: 280px;
            overflow-y: auto;
            background-color: #f8f8f8;
            padding: 10px;
            border-right: 1px solid #e6e6e6;
            margin-right: 10px;
        }

        .selected-drugs-panel .panel-title {
            font-weight: bold;
            font-size: 16px;
            margin-bottom: 10px;
            text-align: center;
            color: #1E9FFF;
        }

        .no-drugs-selected {
            text-align: center;
            color: #999;
            padding: 20px 0;
        }

        /* 左侧药品列表悬停效果 */
        #selectedDrugsList tr:hover {
            background-color: #f0f9ff;
        }

        #selectedDrugsList .delete-drug-btn {
            opacity: 0.7;
            transition: opacity 0.3s;
        }

        #selectedDrugsList .delete-drug-btn:hover {
            opacity: 1;
        }

        /* 表格容器样式 - 添加滚动支持 */
        .table-container {
            position: relative;
            /* overflow-y: auto; */
            flex: 1;
        }

        /* 键盘显示时的表格容器样式 */
        .keyboard-visible .table-container {
            height: calc(100vh - 280px) !important;
            margin-bottom: 220px;
        }

        /* 虚拟键盘样式 */
        #virtualKeyboard {
            position: fixed;
            bottom: 60px;
            left: 0;
            right: 0;
            background-color: #f1f1f1;
            padding: 10px;
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.2);
            text-align: center;
            display: none;
            z-index: 1000;
        }

        #keyboardInput {
            font-size: 18px;
            margin-bottom: 5px;
            background-color: white;
            padding: 5px 10px;
            border-radius: 3px;
            min-height: 24px;
        }

        .keyboard-row {
            display: flex;
            justify-content: center;
            margin-bottom: 5px;
        }

        .key {
            width: 40px;
            height: 40px;
            margin: 2px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: white;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            font-weight: bold;
        }

        /* 行选中样式 - 增强显示效果 */
        .selected-row {
            background-color: #e6f7ff !important;
            border: 2px solid #1E9FFF !important;
        }

        /* 当前输入焦点样式 - 增强显示效果 */
        .input-focus {
            border: 2px solid #FF5722 !important;
            box-shadow: 0 0 8px rgba(255, 87, 34, 0.6) !important;
            background-color: #fff8e1 !important;
        }

        /* 数量输入弹窗样式 */
        .quantity-input-modal {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 300px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.3);
            z-index: 2000;
            padding: 20px;
            text-align: center;
            display: none;
        }

        .quantity-input-modal .drug-name {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .quantity-input-modal .inventory {
            margin-bottom: 20px;
            color: #666;
        }

        .quantity-input-modal .quantity-field {
            font-size: 18px;
            padding: 10px;
            border: 2px solid #1E9FFF;
            border-radius: 4px;
            background-color: #f8f8f8;
            text-align: center;
            margin-bottom: 5px;
        }

        .quantity-input-modal .note {
            font-size: 12px;
            color: #999;
            margin-top: 10px;
        }

        /* 操作提示样式 */
        #tipBox {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 400px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
            z-index: 3000;
            padding: 20px;
            text-align: left;
            display: none;
        }

        #tipBox h3 {
            text-align: center;
            margin-bottom: 15px;
            color: #1E9FFF;
        }

        #tipBox ul {
            padding-left: 20px;
            margin-bottom: 20px;
        }

        #tipBox ul li {
            margin-bottom: 10px;
            line-height: 1.6;
        }

        #tipBox .note {
            font-size: 13px;
            color: #666;
            font-style: italic;
            margin-bottom: 15px;
            padding: 10px;
            background-color: #f9f9f9;
            border-left: 3px solid #1E9FFF;
        }

        #tipBox .tip-btn {
            text-align: center;
        }
    </style>
</head>

<body style="padding: 10px;">
    <!-- 操作小技巧提示框 -->
    <div id="tipBox">
        <h3>操作小技巧</h3>
        <ul>
            <li>翻页：左右键</li>
            <li>选择药品：上下键</li>
            <li>提交数据：Ctrl+Enter</li>
            <li>清除数据：移到当前数据所在行，按回退键、Del均可</li>
            <li>左侧面板：显示已选择的所有药品，点击删除图标可移除药品</li>
        </ul>
        <div class="note">
            注：搜索后，想输入数量，要先按下回车。这是为了避免数字也是药材名的一部分的可能性。但当您输入药材数量时想切换药材搜索，直接按字母就可以搜索，因为药材数量不可能有字母。<br>
            已选择的药品会显示在左侧面板中，即使翻页或搜索其他药品，也能清楚看到已选择的药品。点击红色删除按钮可快速移除不需要的药品。
        </div>
        <div class="tip-btn">
            <button type="button" class="layui-btn" id="tipConfirmBtn">我知道了</button>
        </div>
    </div>

    <!-- 页面主容器 -->
    <div class="page-container">
        <!-- 左侧已选药品面板 -->
        <div class="selected-drugs-panel">
            <!-- <div class="panel-title">已选择药品</div> -->
            <table class="layui-table" lay-size="sm">
                <thead>
                    <tr>
                        <th style="width: 57%;">药品名称</th>
                        <th style="width: 25%;">数量</th>
                        <th style="width: 18%; text-align: center;">操作</th>
                    </tr>
                </thead>
                <tbody id="selectedDrugsList">
                    <!-- 动态插入已选择的药品 -->
                </tbody>
            </table>
            <div id="noDrugsSelected" class="no-drugs-selected">
                暂无选择药品
            </div>
        </div>

        <!-- 添加表格容器div -->
        <div class="table-container layui-table-view layui-table-view-1 layui-form layui-border-box">
            <table id="medsTable" lay-filter="medsTable"></table>
        </div>
    </div>

    <!-- 虚拟键盘 -->
    <div id="virtualKeyboard">
        <div id="keyboardInput"></div>
        <div class="keyboard-row">
            <div class="key">Q</div>
            <div class="key">W</div>
            <div class="key">E</div>
            <div class="key">R</div>
            <div class="key">T</div>
            <div class="key">Y</div>
            <div class="key">U</div>
            <div class="key">I</div>
            <div class="key">O</div>
            <div class="key">P</div>
        </div>
        <div class="keyboard-row">
            <div class="key">A</div>
            <div class="key">S</div>
            <div class="key">D</div>
            <div class="key">F</div>
            <div class="key">G</div>
            <div class="key">H</div>
            <div class="key">J</div>
            <div class="key">K</div>
            <div class="key">L</div>
        </div>
        <div class="keyboard-row">
            <div class="key">Z</div>
            <div class="key">X</div>
            <div class="key">C</div>
            <div class="key">V</div>
            <div class="key">B</div>
            <div class="key">N</div>
            <div class="key">M</div>
        </div>
    </div>

    <!-- 数量输入弹窗 -->
    <div class="quantity-input-modal" id="quantityInputModal">
        <div class="drug-name" id="modalDrugName">木鳖子</div>
        <div class="inventory" id="modalInventory">库存量：9999997</div>
        <div class="quantity-field" id="modalQuantityField">请在此处输入数量</div>
        <div class="note">注：回车后确认并显示第1行，按上下键，切换上下行</div>
    </div>

    <div
        style="position: fixed; bottom: 0; left: 0; right: 0; background-color: #fff; padding: 10px; text-align: center; box-shadow: 0 -2px 10px rgba(0,0,0,0.1);">
        <button type="button" class="layui-btn" id="submitBtn">确认保存该处方药材</button>
        <button type="button" class="layui-btn layui-btn-primary" id="cancelBtn">清空重新</button>
    </div>

    <script>
        layui.use(['table', 'layer', 'form'], function () {
            var table = layui.table;
            var layer = layui.layer;
            var form = layui.form;
            var $ = layui.$;
            window.focus();

            // 从父窗口获取数据
            var parentData = window.parent.drugModalData || {};
            var prescriptionId = parentData.prescriptionId;
            var existingDrugs = parentData.existingDrugs || [];

            // 存储所有选中药品的数据对象
            var selectedDrugsData = {};
            // 添加一个变量来记录选择顺序
            var selectionOrder = [];

            // 初始化已选药品数据
            if (existingDrugs && existingDrugs.length > 0) {
                existingDrugs.forEach(function (drug) {
                    var id = drug.wh_drug_id;
                    selectedDrugsData[id] = {
                        wh_meds_id: id,
                        meds_id: drug.drug_id || 0, // 确保drug_id不为undefined或null
                        name: drug.name,
                        price: drug.price,
                        stock: 0, // 库存量会在表格渲染时更新
                        single_dose: drug.quantity
                    };
                    // 添加到选择顺序数组
                    if (selectionOrder.indexOf(id) === -1) {
                        selectionOrder.push(id);
                    }
                });
                // 初始化左侧面板
                updateSelectedDrugsPanel();
            }

            // 定义查询参数对象
            var queryParams = {
                limit: 10,
                key: '',
                category_id: ''
            };

            // 键盘操作状态管理
            var keyboardState = {
                mode: 'search', // 'search', 'selectRow', 'inputQuantity'
                selectedRowIndex: -1,
                inputBuffer: '',
                $currentInput: null,
                searchPending: false, // 标记是否有待处理的搜索请求
                recentlySwitchedRow: false // 标记是否刚刚通过上下键切换了行
            };

            // 更新左侧已选择药品面板
            function updateSelectedDrugsPanel() {
                var $selectedDrugsList = $('#selectedDrugsList');
                var $noDrugsSelected = $('#noDrugsSelected');

                // 清空当前列表
                $selectedDrugsList.empty();

                // 如果没有选中药品，显示提示信息
                if (selectionOrder.length === 0) {
                    $noDrugsSelected.show();
                    return;
                }

                // 隐藏"暂无选择药品"提示
                $noDrugsSelected.hide();

                // 使用selectionOrder数组确保按照用户选择顺序显示
                // 从头到尾遍历，这样最先添加的会显示在最上面
                selectionOrder.forEach(function (id) {
                    var drug = selectedDrugsData[id];
                    if (drug && drug.single_dose && parseFloat(drug.single_dose) > 0) {
                        var newRow = $('<tr>').attr('data-id', drug.wh_meds_id);
                        newRow.append($('<td>').text(drug.name));
                        newRow.append($('<td>').text(drug.single_dose));
                        // 使用layui图标创建更美观的删除按钮
                        var deleteBtn = $('<button>')
                            .addClass('layui-btn layui-btn-danger layui-btn-xs delete-drug-btn')
                            .html('<i class="layui-icon layui-icon-delete"></i>');
                        newRow.append($('<td>').css('text-align', 'center').append(deleteBtn));
                        $selectedDrugsList.append(newRow);
                    }
                });

                // 绑定删除按钮事件
                $('.delete-drug-btn').on('click', function() {
                    var $row = $(this).closest('tr');
                    var id = $row.data('id');

                    // 从数据和选择顺序中移除
                    delete selectedDrugsData[id];
                    var index = selectionOrder.indexOf(id);
                    if (index > -1) {
                        selectionOrder.splice(index, 1);
                    }

                    // 更新表格中对应行的输入框值
                    var $input = $('.meds-dose[data-id="' + id + '"]');
                    if ($input.length) {
                        $input.val('');
                    }

                    // 更新左侧面板
                    updateSelectedDrugsPanel();
                });
            }

            // 初始化页面加载时设置为选择行模式
            $(document).ready(function () {
                // 检查是否显示操作提示
                checkAndShowTips();

                // 确保初始状态为选择行模式
                keyboardState.mode = 'selectRow'; // 改为selectRow而不是search
                keyboardState.inputBuffer = '';
                keyboardState.selectedRowIndex = 0; // 设置为第一行
                keyboardState.$currentInput = null;

                // 设置搜索框为只读，使用虚拟键盘输入
                $('#searchKey').attr('readonly', true);

                // 确保焦点在文档上，以便能捕获键盘事件
                $(document).focus();

                console.log('页面初始化完成，当前模式:', keyboardState.mode);

                // 初始化左侧面板
                updateSelectedDrugsPanel();

                // 在表格渲染完成后，选中第一行
                setTimeout(function () {
                    var $rows = $('.layui-table-box .layui-table-body tbody tr');
                    if ($rows.length > 0) {
                        // 清除所有行的选中状态和输入框焦点
                        $rows.removeClass('selected-row');
                        $('.meds-dose').removeClass('input-focus');

                        // 选中第一行并设置状态
                        var $firstRow = $rows.eq(0);
                        $firstRow.addClass('selected-row');
                        keyboardState.selectedRowIndex = 0;

                        // 滚动到第一行
                        try {
                            $firstRow[0].scrollIntoView({ block: 'center' });
                        } catch (e) {
                            console.error('滚动到第一行出错:', e);
                        }

                        console.log('表格渲染完成后自动选中第一行');

                        // 如果当前是数量输入模式，则设置相应的焦点
                        if (keyboardState.mode === 'inputQuantity') {
                            var $input = $firstRow.find('.meds-dose');
                            $input.addClass('input-focus');
                            keyboardState.$currentInput = $input;

                            // 显示数量输入模态框
                            showQuantityInputModal($firstRow);
                        }
                    }
                }, 200); // 给表格一点渲染时间
            });

            // 检查并显示操作提示
            function checkAndShowTips() {
                // 检查本地存储中是否已经显示过提示
                var tipsShown = localStorage.getItem('drugEditTipsShown');
                if (!tipsShown) {
                    // 显示提示框
                    $('#tipBox').show();

                    // 绑定确认按钮事件
                    $('#tipConfirmBtn').on('click', function() {
                        $('#tipBox').hide();
                        localStorage.setItem('drugEditTipsShown', 'true');
                    });
                }
            }

            // 渲染表格
            var tableIns = table.render({
                elem: '#medsTable',
                url: '/admin/warehouse_drug/list',
                method: 'post',
                where: queryParams,
                page: true,
                cols: [[
                    { field: 'ID', title: '库存ID', sort: true, align: 'center' },
                    { field: 'Drug_id', title: '药品ID', sort: true, align: 'center' },
                    { field: 'Drug_name', title: '药品名称', sort: true },
                    { field: 'Price', title: '单价', sort: true, align: 'center' },
                    { field: 'Quantity', title: '库存量', sort: true, align: 'center' },
                    {
                        title: '数量', templet: function (d) {
                            var value = selectedDrugsData[d.ID]?.single_dose || '';
                            return '<input type="number" class="layui-input meds-dose" data-id="' + d.ID + '" min="1" max="999999" step="1" value="' + value + '" onkeyup="this.value = this.value > 999999 ? 999999 : this.value;" readonly>';
                        }
                    }
                ]],
                response: {
                    statusName: 'code',
                    statusCode: 200,
                    msgName: 'msg',
                    countName: 'count',
                    dataName: 'data'
                },
                height: 'full-100',
                done: function (res) {
                    // 标记搜索请求已完成
                    keyboardState.searchPending = false;
                    console.log('表格渲染完成，当前模式:', keyboardState.mode, '数据行数:', res.data?.length || 0);

                    // 限制数量的输入范围
                    $('.meds-dose').on('input', function () {
                        updateQuantityInput($(this));
                    });

                    // 添加数量输入框的点击事件处理
                    $('.meds-dose').on('click', function (e) {
                        // 阻止事件冒泡，防止触发其他事件
                        e.stopPropagation();

                        // 获取当前点击的输入框
                        var $input = $(this);
                        var $row = $input.closest('tr');

                        // 清除之前的状态
                        $('.layui-table-box .layui-table-body tbody tr').removeClass('selected-row');
                        $('.meds-dose').removeClass('input-focus');

                        // 设置当前行为选中状态
                        $row.addClass('selected-row');
                        $input.addClass('input-focus');

                        // 更新状态
                        keyboardState.mode = 'inputQuantity';
                        keyboardState.$currentInput = $input;
                        keyboardState.inputBuffer = $input.val() || '';

                        // 获取行索引
                        var $rows = $('.layui-table-box .layui-table-body tbody tr');
                        var rowIndex = $rows.index($row);
                        keyboardState.selectedRowIndex = rowIndex;

                        console.log('点击输入框，行索引:', rowIndex);

                        // 隐藏虚拟键盘，只显示数量输入模态框
                        hideVirtualKeyboard();

                        // 显示数量输入模态框
                        showQuantityInputModal($row);

                        // 将焦点放在文档上，以便捕获键盘事件
                        $(document).focus();
                    });

                    // 回填已选中的数据
                    Object.keys(selectedDrugsData).forEach(function (id) {
                        var $input = $('.meds-dose[data-id="' + id + '"]');
                        if ($input.length) {
                            $input.val(selectedDrugsData[id].single_dose);
                        }
                    });

                    // 获取表格行
                    var $rows = $('.layui-table-box .layui-table-body tbody tr');
                    console.log('表格渲染完成后的行数:', $rows.length);

                    // 如果表格有行，则始终默认选中第一行（无论当前是什么模式）
                    if ($rows.length > 0) {
                        // 清除所有行的选中状态和输入框焦点
                        $rows.removeClass('selected-row');
                        $('.meds-dose').removeClass('input-focus');

                        // 选中第一行并设置状态
                        var $firstRow = $rows.eq(0);
                        $firstRow.addClass('selected-row');
                        keyboardState.selectedRowIndex = 0;

                        // 滚动到第一行
                        try {
                            $firstRow[0].scrollIntoView({ block: 'center' });
                        } catch (e) {
                            console.error('滚动到第一行出错:', e);
                        }

                        console.log('表格渲染完成后自动选中第一行');

                        // 如果当前是数量输入模式，则设置相应的焦点
                        if (keyboardState.mode === 'inputQuantity') {
                            var $input = $firstRow.find('.meds-dose');
                            $input.addClass('input-focus');
                            keyboardState.$currentInput = $input;

                            // 显示数量输入模态框
                            showQuantityInputModal($firstRow);
                        }
                    }

                    // 添加表格行点击事件
                    $('.layui-table-box .layui-table-body tbody').on('click', 'tr', function (e) {
                        // 如果点击的是输入框，不处理（让输入框的点击事件处理）
                        if ($(e.target).hasClass('meds-dose') || $(e.target).closest('.meds-dose').length > 0) {
                            return;
                        }

                        // 清除之前的选中状态
                        $('.layui-table-box .layui-table-body tbody tr').removeClass('selected-row');
                        $('.meds-dose').removeClass('input-focus');

                        // 设置当前行为选中状态
                        var $row = $(this);
                        $row.addClass('selected-row');

                        // 获取行索引
                        var $rows = $('.layui-table-box .layui-table-body tbody tr');
                        var rowIndex = $rows.index($row);

                        // 更新状态
                        keyboardState.mode = 'selectRow';
                        keyboardState.selectedRowIndex = rowIndex;
                        keyboardState.$currentInput = null;
                        keyboardState.inputBuffer = '';

                        console.log('点击表格行，行索引:', rowIndex);

                        // 隐藏键盘和模态框
                        hideVirtualKeyboard();
                        hideQuantityInputModal();
                    });

                    // 更新已选药品的库存信息
                    res.data.forEach(function(row) {
                        if (selectedDrugsData[row.ID]) {
                            selectedDrugsData[row.ID].stock = row.Quantity;
                        }
                    });
                }
            });

            // 更新数量输入框的值并保存数据
            function updateQuantityInput($input) {
                var val = $input.val();
                var id = $input.data('id');

                // 如果输入为空或0，从选中数据中删除该条记录
                if (!val || parseFloat(val) === 0) {
                    delete selectedDrugsData[id];
                    // 从选择顺序数组中也移除
                    var index = selectionOrder.indexOf(id);
                    if (index > -1) {
                        selectionOrder.splice(index, 1);
                    }
                    updateSelectedDrugsPanel(); // 更新左侧面板
                    return;
                }

                // 允许输入0
                if (val < 0) {
                    $input.val(0);
                    delete selectedDrugsData[id];
                    // 从选择顺序数组中也移除
                    var index = selectionOrder.indexOf(id);
                    if (index > -1) {
                        selectionOrder.splice(index, 1);
                    }
                    updateSelectedDrugsPanel(); // 更新左侧面板
                    return;
                }
                // 如果大于0，则最小值为1
                if (val < 1) {
                    layer.msg('数量不能小于1', { icon: 2 });
                    $input.val(1);
                    val = 1;
                }
                // 限制最大值为999999
                if (val > 999999) {
                    $input.val(999999);
                    val = 999999;
                }
                // 限制小数点后一位
                if (val.toString().split('.')[1]?.length > 1) {
                    val = parseFloat(val).toFixed(1);
                    $input.val(val);
                }

                // 保存选中的药品数据
                var $row = $input.closest('tr');

                // 检查是否是新选择的药品
                var isNewSelection = !selectedDrugsData[id];

                selectedDrugsData[id] = {
                    wh_meds_id: $row.find('td[data-field="ID"]').text(),
                    meds_id: $row.find('td[data-field="Drug_id"]').text(),
                    name: $row.find('td[data-field="Drug_name"]').text(),
                    price: $row.find('td[data-field="Price"]').text(),
                    stock: $row.find('td[data-field="Quantity"]').text(),
                    single_dose: parseFloat(val)
                };

                // 如果是新选择的药品，添加到选择顺序数组中
                if (isNewSelection) {
                    selectionOrder.push(id);
                }

                // 更新左侧面板
                updateSelectedDrugsPanel();
            }

            // 搜索按钮点击事件
            $('#searchBtn').on('click', function () {
                executeSearch();
            });

            // 执行搜索函数 - 将搜索逻辑提取为单独的函数
            function executeSearch() {
                if (keyboardState.searchPending) return; // 如果有待处理的搜索，则不执行

                keyboardState.searchPending = true; // 标记搜索开始
                queryParams.key = keyboardState.inputBuffer; // 使用inputBuffer而不是searchKey的值
                console.log('执行搜索:', queryParams.key);

                table.reload('medsTable', {
                    where: queryParams,
                    page: {
                        curr: 1 // 重置到第一页
                    }
                });
            }

            // 处理分页
            function handlePagination(direction) {
                var $pager = $('.layui-laypage-btn').parent();
                if (!$pager.length) return;

                if (direction === 'prev') {
                    var $prevBtn = $pager.find('.layui-laypage-prev:not(.layui-disabled)');
                    if ($prevBtn.length) {
                        $prevBtn.trigger('click');
                    }
                } else if (direction === 'next') {
                    var $nextBtn = $pager.find('.layui-laypage-next:not(.layui-disabled)');
                    if ($nextBtn.length) {
                        $nextBtn.trigger('click');
                    }
                }
            }

            // 显示虚拟键盘
            function showVirtualKeyboard() {
                $('#virtualKeyboard').show();
            }

            // 隐藏虚拟键盘
            function hideVirtualKeyboard() {
                $('#virtualKeyboard').hide();
            }

            // 显示数量输入模态框
            function showQuantityInputModal($row) {
                if (!$row || $row.length === 0) {
                    console.error('showQuantityInputModal: 无效的行元素', $row);
                    return;
                }

                try {
                    console.log('显示数量输入模态框，行元素:', $row);

                    // 获取药品信息
                    var drugName = $row.find('td[data-field="Drug_name"]').text();
                    var inventory = $row.find('td[data-field="Quantity"]').text();

                    console.log('获取到的药品信息:', { drugName, inventory });
                    console.log('表格行内容:', $row.html());

                    if (!drugName) {
                        console.warn('未能获取到药品名称，尝试直接获取DOM内容');
                        // 尝试不同的方式获取内容
                        drugName = $row.find('td:eq(2)').text();
                        console.log('尝试用td:eq(2)获取药品名称:', drugName);

                        if (!drugName) {
                            // 遍历所有单元格查看内容
                            var cellContents = [];
                            $row.find('td').each(function (i) {
                                cellContents.push(i + ': ' + $(this).text());
                            });
                            console.log('所有单元格内容:', cellContents);

                            drugName = $row.find('td').eq(2).text() || '未知药品';
                        }
                    }

                    if (!inventory) {
                        console.warn('未能获取到库存量，尝试直接获取DOM内容');
                        inventory = $row.find('td:eq(4)').text();
                        console.log('尝试用td:eq(4)获取库存量:', inventory);

                        if (!inventory) {
                            inventory = $row.find('td').eq(4).text() || '0';
                        }
                    }

                    // 获取当前行的数量输入框值
                    var $input = $row.find('.meds-dose');
                    var currentValue = $input.val() || '';

                    // 如果没有从keyboardState获取到输入，则尝试从输入框获取
                    if (!keyboardState.inputBuffer && currentValue) {
                        keyboardState.inputBuffer = currentValue;
                    }

                    // 更新模态框内容
                    $('#modalDrugName').text(drugName);
                    $('#modalInventory').text('库存量：' + inventory);

                    // 如果inputBuffer为空但当前输入框有值，使用当前输入框的值
                    if (!keyboardState.inputBuffer && currentValue) {
                        $('#modalQuantityField').text(currentValue);
                    } else if (keyboardState.inputBuffer) {
                        $('#modalQuantityField').text(keyboardState.inputBuffer);
                    } else {
                        $('#modalQuantityField').text('请在此处输入数量');
                    }

                    // 显示模态框
                    $('#quantityInputModal').show();

                } catch (e) {
                    console.error('显示数量输入模态框出错:', e);
                }
            }

            // 隐藏数量输入模态框
            function hideQuantityInputModal() {
                $('#quantityInputModal').hide();
            }

            // 选中特定行
            function selectRow(index) {
                // 清除之前的选中状态
                console.log('执行selectRow, 索引:', index, '当前模式:', keyboardState.mode);

                // 尝试多种选择器找到表格行
                var $rows = $('#medsTable tbody tr');
                if ($rows.length === 0) {
                    $rows = $('.layui-table-box .layui-table-body tbody tr');
                    console.log('使用layui特定选择器找到行数:', $rows.length);
                }

                if ($rows.length === 0) {
                    console.warn('selectRow: 无法找到表格行');
                    return;
                }

                // 清除样式时根据当前模式决定
                if (keyboardState.mode === 'inputQuantity') {
                    // 在输入数量模式下清除所有选中状态
                    $rows.removeClass('selected-row');
                    $('.meds-dose').removeClass('input-focus');
                } else if (keyboardState.mode === 'selectRow') {
                    // 在选择行模式下只清除行的选中状态
                    $rows.removeClass('selected-row');
                } else {
                    // 搜索模式下全部清除
                    $rows.removeClass('selected-row');
                    $('.meds-dose').removeClass('input-focus');
                }

                // 确保索引在有效范围内
                if (index < 0) index = 0;
                if (index >= $rows.length) index = $rows.length - 1;

                // 选中当前行并更新状态
                var $currentRow = $rows.eq(index);
                $currentRow.addClass('selected-row');
                keyboardState.selectedRowIndex = index;
                console.log('已选中行:', index, $currentRow);

                // 如果是输入数量模式，设置输入焦点
                if (keyboardState.mode === 'inputQuantity') {
                    keyboardState.$currentInput = $currentRow.find('.meds-dose');
                    keyboardState.$currentInput.addClass('input-focus');
                }

                // 滚动到可见区域
                try {
                    $currentRow[0].scrollIntoView({ block: 'center' });
                } catch (e) {
                    console.error('滚动到行出错:', e);
                }
            }

            // 全局键盘事件监听
            $(document).on('keydown', function (e) {
                // 已经捕获的键不触发默认行为
                if ([8, 13, 37, 38, 39, 40].includes(e.keyCode)) {
                    e.preventDefault();
                }

                // 处理Ctrl+Enter快捷键，触发提交按钮点击
                if (e.keyCode === 13 && e.ctrlKey) {
                    layer.load(2);
                    console.log('检测到Ctrl+Enter快捷键，触发提交按钮');
                    $('#submitBtn').trigger('click');
                    return;
                }

                // 处理Backspace键 (8) 和 DEL键 (46) - 在任何非空输入状态下可用
                if (e.keyCode === 8 || e.keyCode === 46) {
                    // 在搜索模式下，删除一个字符并触发搜索
                    if (keyboardState.mode === 'search' && keyboardState.inputBuffer.length > 0) {
                        keyboardState.inputBuffer = keyboardState.inputBuffer.slice(0, -1);
                        updateVirtualKeyboardInput(keyboardState.inputBuffer);
                        executeSearch();
                    }
                    // 在数量输入模式下，删除一个字符并更新模态框和输入框
                    else if (keyboardState.mode === 'inputQuantity') {
                        // 完全清空输入
                        keyboardState.inputBuffer = '';

                        // 更新模态框显示
                        $('#modalQuantityField').text('请在此处输入数量');

                        // 更新输入框的值
                        if (keyboardState.$currentInput) {
                            keyboardState.$currentInput.val('');
                            updateQuantityInput(keyboardState.$currentInput);
                        }

                        // 更新左侧面板
                        updateSelectedDrugsPanel();
                    }
                    return; // 不再执行后续的模式处理
                }

                // 处理字母键输入 (65-90: A-Z) - 在任何模式下，字母键都触发搜索模式
                if (e.keyCode >= 65 && e.keyCode <= 90) {
                    // 如果不是搜索模式，切换到搜索模式
                    if (keyboardState.mode !== 'search') {
                        // 清除所有选中状态
                        $('.layui-table-box .layui-table-body tbody tr').removeClass('selected-row');
                        $('.meds-dose').removeClass('input-focus');
                        hideQuantityInputModal();

                        // 切换到搜索模式
                        keyboardState.mode = 'search';
                        keyboardState.selectedRowIndex = -1;
                        keyboardState.$currentInput = null;
                        keyboardState.inputBuffer = String.fromCharCode(e.keyCode);

                        // 显示虚拟键盘并更新输入
                        showVirtualKeyboard();
                        updateVirtualKeyboardInput(keyboardState.inputBuffer);

                        // 执行搜索
                        executeSearch();
                        return; // 不再执行后续的模式处理
                    } else {
                        // 已经是搜索模式，累加输入
                        keyboardState.inputBuffer += String.fromCharCode(e.keyCode);
                        updateVirtualKeyboardInput(keyboardState.inputBuffer);
                        showVirtualKeyboard();
                        executeSearch();
                        return; // 不再执行后续的模式处理
                    }
                }

                // 处理数字键 (48-57: 0-9, 96-105: 小键盘0-9) - 在selectRow模式下，直接进入inputQuantity模式
                if ((e.keyCode >= 48 && e.keyCode <= 57) || (e.keyCode >= 96 && e.keyCode <= 105)) {
                    var char;
                    if (e.keyCode >= 96 && e.keyCode <= 105) {
                        // 小键盘数字转换为普通数字
                        char = String.fromCharCode(e.keyCode - 48);
                    } else {
                        char = String.fromCharCode(e.keyCode);
                    }

                    // 根据当前模式决定行为
                    if (keyboardState.mode === 'selectRow') {
                        console.log('selectRow模式下输入数字，直接切换到inputQuantity模式');

                        // 获取当前选中行
                        var $rows = $('.layui-table-box .layui-table-body tbody tr');
                        if ($rows.length === 0 || keyboardState.selectedRowIndex < 0) {
                            return;
                        }

                        var rowIndex = keyboardState.selectedRowIndex;
                        if (rowIndex >= $rows.length) rowIndex = $rows.length - 1;

                        var $currentRow = $rows.eq(rowIndex);

                        // 先切换到输入数量模式，再设置初始数字
                        keyboardState.mode = 'inputQuantity';
                        keyboardState.inputBuffer = char; // 设置为第一个数字

                        // 设置当前行的输入框为焦点
                        var $input = $currentRow.find('.meds-dose');
                        $input.addClass('input-focus');
                        $input.val(char);
                        keyboardState.$currentInput = $input;

                        // 确保虚拟键盘被隐藏
                        hideVirtualKeyboard();

                        // 显示数量输入模态框
                        showQuantityInputModal($currentRow);

                        // 更新输入框的值
                        updateQuantityInput($input);

                        return; // 阻止继续处理
                    }
                    else if (keyboardState.mode === 'search') {
                        // 在搜索模式下继续累加数字
                        keyboardState.inputBuffer += char;
                        updateVirtualKeyboardInput(keyboardState.inputBuffer);
                        executeSearch();
                        return; // 阻止继续处理
                    }
                    else if (keyboardState.mode === 'inputQuantity') {
                        // 在数量输入模式下，累加数字
                        keyboardState.inputBuffer += char;

                        // 更新模态框显示
                        $('#modalQuantityField').text(keyboardState.inputBuffer);

                        // 更新输入框的值
                        if (keyboardState.$currentInput) {
                            keyboardState.$currentInput.val(keyboardState.inputBuffer);
                            updateQuantityInput(keyboardState.$currentInput);
                        }

                        return; // 阻止继续处理
                    }
                }

                // 处理分页的左右箭头键 (所有模式下均可使用)
                if (e.keyCode === 37 || e.keyCode === 39) {  // 左右箭头键
                    // 左箭头键 - 上一页
                    if (e.keyCode === 37) {
                        handlePagination('prev');
                    }
                    // 右箭头键 - 下一页
                    else if (e.keyCode === 39) {
                        handlePagination('next');
                    }
                    return; // 箭头键处理完毕，不再执行下面的模式处理
                }

                // 处理上下箭头键 - 在selectRow和inputQuantity模式下可用
                if (e.keyCode === 38 || e.keyCode === 40) {  // 上下箭头键
                    if (keyboardState.mode === 'selectRow' || keyboardState.mode === 'inputQuantity') {
                        var newIndex = keyboardState.selectedRowIndex;

                        // 上箭头键 - 选择上一行
                        if (e.keyCode === 38) {
                            newIndex--;
                        }
                        // 下箭头键 - 选择下一行
                        else if (e.keyCode === 40) {
                            newIndex++;
                        }

                        // 选择新行
                        selectRow(newIndex);

                        // 如果是数量输入模式，显示数量输入模态框
                        if (keyboardState.mode === 'inputQuantity') {
                            var $rows = $('.layui-table-box .layui-table-body tbody tr');
                            if ($rows.length > 0 && newIndex >= 0 && newIndex < $rows.length) {
                                var $currentRow = $rows.eq(newIndex);
                                showQuantityInputModal($currentRow);
                            }
                        }
                    }
                    return; // 上下箭头键处理完毕，不再执行下面的模式处理
                }

                // 处理回车键 - 根据当前模式决定行为
                if (e.keyCode === 13 && !e.ctrlKey) {
                    if (keyboardState.mode === 'search') {
                        // 搜索模式下，回车切换到选择行模式
                        keyboardState.mode = 'selectRow';
                        keyboardState.selectedRowIndex = 0;
                        hideVirtualKeyboard();

                        // 选中第一行
                        selectRow(0);
                    }
                    else if (keyboardState.mode === 'selectRow') {
                        // 选择行模式下，回车切换到数量输入模式
                        keyboardState.mode = 'inputQuantity';
                        keyboardState.inputBuffer = '';

                        // 获取当前选中行
                        var $rows = $('.layui-table-box .layui-table-body tbody tr');
                        if ($rows.length > 0 && keyboardState.selectedRowIndex >= 0 && keyboardState.selectedRowIndex < $rows.length) {
                            var $currentRow = $rows.eq(keyboardState.selectedRowIndex);
                            var $input = $currentRow.find('.meds-dose');
                            $input.addClass('input-focus');
                            keyboardState.$currentInput = $input;

                            // 显示数量输入模态框
                            showQuantityInputModal($currentRow);
                        }
                    }
                    else if (keyboardState.mode === 'inputQuantity') {
                        // 数量输入模式下，回车确认输入并切换回选择行模式
                        if (keyboardState.$currentInput) {
                            // 更新输入框的值
                            keyboardState.$currentInput.val(keyboardState.inputBuffer);
                            updateQuantityInput(keyboardState.$currentInput);
                        }

                        // 切换回选择行模式
                        keyboardState.mode = 'selectRow';
                        keyboardState.inputBuffer = '';
                        hideQuantityInputModal();

                        // 保持当前行选中状态
                        selectRow(keyboardState.selectedRowIndex);
                    }
                    return; // 回车键处理完毕，不再执行下面的模式处理
                }
            });

            // 更新虚拟键盘显示的输入内容
            function updateVirtualKeyboardInput(text) {
                $('#keyboardInput').text(text);
            }

            // 清空按钮点击事件
            $('#cancelBtn').on('click', function () {
                layer.confirm('确定要清空所有已选择的药品吗？', {
                    btn: ['确定', '取消'],
                    title: '操作确认',
                    icon: 3
                }, function (index) {
                    // 清空所有选中的药品数据
                    selectedDrugsData = {};
                    selectionOrder = [];

                    // 清空所有输入框的值
                    $('.meds-dose').val('');

                    // 更新左侧面板
                    updateSelectedDrugsPanel();

                    layer.close(index);
                    layer.msg('已清空所有选择', { icon: 1 });
                });
            });

            // 提交按钮点击事件
            $('#submitBtn').on('click', function () {
                submitSelection();
            });

            // 提交选择的药品
            function submitSelection() {
                var selectedData = [];

                // 使用selectionOrder数组确保按照用户选择顺序处理药品
                selectionOrder.forEach(function (id) {
                    var item = selectedDrugsData[id];
                    if (item && item.single_dose && parseFloat(item.single_dose) > 0) {
                        // 确保drug_id不为null，如果解析失败则使用0
                        var drugId = parseInt(item.meds_id);
                        if (isNaN(drugId)) {
                            // 尝试从表格中获取Drug_id
                            var $row = $('.layui-table-box .layui-table-body tbody tr').filter(function() {
                                return $(this).find('td[data-field="ID"]').text() == item.wh_meds_id;
                            });
                            if ($row.length > 0) {
                                drugId = parseInt($row.find('td[data-field="Drug_id"]').text()) || 0;
                            } else {
                                // 如果在当前页面找不到，尝试从API获取
                                console.log('无法在当前页面找到药品ID，使用默认值0');
                                drugId = 0; // 如果无法获取，则设置为0
                            }
                        }

                        selectedData.push({
                            wh_drug_id: parseInt(item.wh_meds_id),
                            drug_id: drugId,
                            single_dose: parseFloat(item.single_dose)
                        });
                    }
                });

                if (selectedData.length === 0) {
                    layer.msg('请至少选择一个药品并输入数量', { icon: 2 });
                    return;
                }

                // 保存到数据库
                layer.load(2);
                $.ajax({
                    url: '/admin/prescription/edit_drug_save',
                    type: 'POST',
                    data: {
                        pre_id: prescriptionId,
                        drugs: JSON.stringify(selectedData)
                    },
                    success: function (res) {
                        layer.closeAll('loading');
                        if (res.code === 200) {
                            layer.msg('保存成功', { icon: 1 });
                            // 确保父窗口有这个函数再调用
                            if (typeof window.parent.loadPrescriptionDrugs === 'function') {
                                window.parent.loadPrescriptionDrugs(prescriptionId);
                            }
                            // 关闭弹窗
                            var index = parent.layer.getFrameIndex(window.name);
                            parent.layer.close(index);
                            parent.layer.msg('药材数据保存成功', { icon: 1 });
                            // 刷新父页面
                            parent.location.reload();
                        } else {
                            layer.msg(res.msg || '保存失败', { icon: 2 });
                        }
                    },
                    error: function (xhr) {
                        layer.closeAll('loading');
                        try {
                            var response = JSON.parse(xhr.responseText);
                            layer.msg('保存失败：' + response.msg, { icon: 2 });
                        } catch (e) {
                            layer.msg('保存失败：' + xhr.responseText, { icon: 2 });
                        }
                    }
                });
            }
        });
    </script>