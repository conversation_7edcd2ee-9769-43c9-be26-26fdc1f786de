<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 药材库库存管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
    <style>
        #floating-submit {
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            display: block !important;
            bottom: -100px;
        }
    </style>
</head>

<body>
    <div class="layui-layout layui-layout-admin">
        <!-- 头部 -->
        <div class="layui-header">
            <div class="layui-logo">幸年堂</div>
            <ul class="layui-nav layui-layout-right">
                <li class="layui-nav-item" style="background-color:transparent!important;padding-right:10px">
                    <a href="javascript:;" style="display:flex;align-items:center;color:#fff"><img
                            src="/dist/images/user.png" class="layui-nav-img"><span class="user_name"></span></a>
                    <dl class="layui-nav-child pop_menu">
                        <dd><i class="iconfont">&#xe62f;</i><a href="change_password.html#7">用户资料</a></dd>
                        <dd><i class="iconfont">&#xe601;</i><a href="javascript:void(0)" class="refresh_cache">刷新缓存</a>
                        </dd>
                        <dd><i class="iconfont">&#xe634;</i><a href="login_out.html">退出登录</a></dd>
                    </dl>
                </li>
            </ul>
        </div>
        <!-- 导航 -->
        <ul class="layui-nav layui-nav-tree layui-nav-side" id="menuContainer" lay-bar="disabled" lay-accordion>
            <div style="margin: 50px 0;color: #bbb;text-align: center;">菜单加载中...</div>
        </ul>
        <!-- 主体内容 -->
        <div class="layui-body">
            <div class="body_child">
                <div class="layui-panel">
                    <div class="layui-card-header">
                        <div class="layui-row" style="padding-top:10px;">
                            <div class="layui-col-md11 layui-col-sm10" style="display: flex;">
                                <div>药材库库存管理</div>
                            </div>
                            <div class="layui-col-md1 layui-col-sm2" style="text-align: right;">
                                <button class="layui-btn layui-btn-primary layui-border-red perm_check_btn" lay-event="add" res_id="64" onclick="add()">
                                    <i class="layui-icon">&#xe654;</i> 采购入库
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="layui-padding-3">
                        <div id="data_search">
                            <div class="layui-form">
                                <div class="layui-row">
                                    <div class="layui-col-md3" style="min-width:380px;">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label">搜索</label>
                                            <div class="layui-input-block">
                                                <input type="text" name="key" placeholder="请输入药材、供应商、批号"
                                                    autocomplete="off" class="layui-input" lay-affix="clear">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-md3">
                                        <div class="layui-form-item">
                                            <div class="layui-input-inline" style="margin-left: 10px;">
                                                <button class="layui-btn" lay-submit lay-filter="search"
                                                    style="width: 120px;">筛选</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <table id="mytable" lay-filter="mytable" class="layui-table" lay-skin="row"></table>
                    </div>
                </div>
                <br>
                <br>
            </div>
        </div>
    </div>

    <!-- 添加浮动提交按钮的容器 -->
    <div id="floating-submit" class="perm_check_btn" res_id="66" style="display: none; position: fixed; left: 50%; bottom: -100px; transform: translateX(-50%); transition: all 0.3s ease;">
        <button class="layui-btn layui-btn-lg" style="padding: 0 60px;">
            <i class="layui-icon layui-icon-right"></i> 报损
        </button>
    </div>

    <script type="text/html" id="TPL-bar">
        <button class="layui-btn layui-btn-xs perm_check_btn" lay-event="detail" res_id="63">详情</button>
    </script>

    <script>
        layui.use(['element', 'layer', 'util'], function () {
            var element = layui.element;
            var form = layui.form;
            var layer = layui.layer;
            var util = layui.util;
            var table = layui.table;
            var $ = layui.$;

            // 页面顶部插入缓存的用户数据
            let local_userinfo = localStorage.getItem('local_userinfo');
            let user_name = "";
            if (local_userinfo) {
                local_userinfo = JSON.parse(local_userinfo);
                user_name = local_userinfo.Name + " ( " + local_userinfo.Roles + " )";
            } else {
                user_name = "未登录";
            }
            $('.user_name').text(user_name);
            render_menu($);
            $('.refresh_cache').click(function () {
                refresh_cache($);
            });
            render_button($);
            //引入TABLE模块
            layer.load(2);
            table.render({
                elem: '#mytable'
                , even: true
                , url: serverUrl + "/admin/warehouse_drug/list"
                , method: 'post'
                , response: {
                    statusName: 'code'
                    , statusCode: 200
                    , msgName: 'msg'
                    , countName: 'count'
                    , dataName: 'data'
                }
                , cols: [[
                    { type: 'checkbox', fixed: 'left' },
                    { field: 'ID', title: '库存ID', align: 'center', width: 80 }
                    // , { field: 'Drug_id', title: '药品ID', width: 100, align: 'center' }
                    , { 
                        field: 'Drug_name', 
                        title: '药材名称',
                        templet: function(d) {
                            return '<a href="javascript:;" class="btn_arg_pm" lay-event="drug_detail">' + d.Drug_name + '</a>';
                        }
                    }
                    , { field: 'Code', title: '批号' }
                    , { field: 'Last_supplier', title: '最新供应商<i class="layui-icon layui-icon-tips layui-font-14" lay-event="sort_tips" style="margin-left: 5px;"></i>', width: 150 }
                    , { field: 'Spec', title: '规则', width: 60, align: 'center' }
                    , {
                        field: 'Quantity', title: '库存数量', width: 100, align: 'center', templet: function (d) {
                            return d.Quantity <= d.Min_stock ? '<span class="layui-badge layui-bg-red">' + d.Quantity + '</span>' : d.Quantity;
                        }
                    }
                    , { field: 'Min_stock', title: '下限预警值', width: 100, align: 'center' }
                    , { field: 'Cost_price', title: '进货价', width: 100, align: 'center' }
                    , { field: 'Price', title: '出货价', width: 100, align: 'center' }
                    , {
                        field: 'Is_for_check', title: '盘点状态', width: 100, align: 'center',
                        templet: function (d) {
                            return d.Is_for_check === 1 ? '是' : '否';
                        }
                    }
                    , {
                        field: 'Exp_date', title: '有效期', width: 120, align: 'center',
                        templet: function (d) {
                            return d.Exp_date ? d.Exp_date.split('T')[0] : '';
                        }
                    }
                    , {
                        field: 'Create_time', title: '建档时间', width: 180, align: 'center',
                        templet: function (d) {
                            return d.Create_time ? d.Create_time.replace('T', ' ').replace('Z', '') : '';
                        }
                    }
                    , { title: '操作', align: 'center', toolbar: '#TPL-bar', width: 200, fixed: 'right' }
                ]]
                , page: true
                , limit: 12
                , done: function () {
                    layer.closeAll('loading');
                }
            });

            // 监听表格复选框选择
            table.on('checkbox(mytable)', function(obj){
                var checkStatus = table.checkStatus('mytable');
                var selectedData = checkStatus.data;
                var $floatingSubmit = $('#floating-submit');
                
                if(selectedData.length > 0) {
                    $floatingSubmit.css('bottom', '20px').show();
                } else {
                    $floatingSubmit.css('bottom', '-100px');
                }
            });

            // 点击下一步按钮
            $('#floating-submit .layui-btn').on('click', function() {
                var checkStatus = table.checkStatus('mytable');
                var selectedData = checkStatus.data;
                if(selectedData.length === 0) {
                    layer.msg('请至少选择一条数据');
                    return;
                }
                
                var selectedIds = selectedData.map(function(item) {
                    return item.ID;
                });
                
                // 使用模态框打开报损页面
                layer.open({
                    type: 2,
                    title: '药品报损',
                    area: ['900px', '830px'],
                    shadeClose: true,
                    content: 'warehouse_drug_loss.html',
                    success: function(layero, index) {
                        // 获取iframe页的窗口对象
                        var iframeWin = window[layero.find('iframe')[0]['name']];
                        // 向iframe页传递数据
                        iframeWin.setDrugData(selectedData);
                    }
                });
            });

            // 表头自定义元素工具事件 --- 2.8.8+
            table.on('colTool(mytable)', function (obj) {
                var event = obj.event;
                if (event === 'sort_tips') {
                    layer.alert('因为库存不会一直同一个厂商供货，所以厂商VS库存是1:N的关系，所以这个设计多少有点问题，暂命名为"最新供应商"，后续会沟通改进。', {
                        title: '解释为什么是最新供应商？',
                        shadeClose: true,
                        btn: ['知道了']
                    });
                }
            });
            //监听工具条
            table.on('tool(mytable)', function (obj) {
                var data = obj.data;
                if (obj.event === 'detail') {
                    layer.open({
                        type: 2,
                        title: '库存药材详情',
                        area: ['1000px', '600px'],
                        shadeClose: true,
                        content: 'warehouse_drug_detail.html?id=' + data.ID
                    });
                } else if (obj.event === 'drug_detail') {
                    layer.open({
                        type: 2,
                        title: '药材详情',
                        area: ['1000px', '800px'],
                        shadeClose: true,
                        content: '/admin/drug_detail.html?id=' + data.Drug_id
                    });
                }
            });

            //监听搜索
            form.on('submit(search)', function (data) {
                layer.load(2);
                table.reload('mytable', {
                    where: data.field
                    , page: {
                        curr: 1
                    }
                });
                return false;
            });

            // 添加库存药材函数定义
            window.add = function () {
                layer.open({
                    type: 2,
                    title: '采购入库',
                    shadeClose: true,
                    area: ['1000px', '830px'],
                    content: 'warehouse_drug_add.html'
                });
            };
        });
    </script>
</body>

</html>