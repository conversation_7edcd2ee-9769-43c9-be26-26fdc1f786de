package patient

import (
	"fmt"
	"mstproject/pkg/common"
	"mstproject/pkg/database"
	"net/http"
)

type department struct {
	ID      int    `json:"id"`
	Name    string `json:"name"`
	Details string `json:"details"`
}

// 医生科室信息，公共数据，不鉴权
func Department_list(w http.ResponseWriter, r *http.Request) {
	limit := r.FormValue("limit")
	if limit == "" {
		limit = "10"
	}
	store_id := r.FormValue("store_id")
	if store_id == "" {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "门店ID不得为空",
		})
		return
	}
	name := r.<PERSON>ue("name")
	if name == "" {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "科室名称不得为空",
		})
		return
	}
	var departments []department
	sql := "select id,name,details from department where pid in(select id from department where pid = ? and name = ?) limit ?"
	err := database.GetAll(sql, &departments, store_id, name, limit)
	if err != nil {
		common.JSONResponse(w, http.StatusNotFound, map[string]interface{}{
			"code":  500,
			"msg":   "获取科室信息失败",
			"error": err.Error(),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "ok",
		"data": departments,
		// "sql":  common.DebugSql(sql, limit),
	})
}

// 科室信息详情
func Department_detail(w http.ResponseWriter, r *http.Request) {
	id, err := common.CheckInt(r.FormValue("id"))
	if id < 0 || err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "参数错误",
		})
		return
	}
	var department department
	sql := "select id,name,details from department where id = ?"
	err = database.GetRow(sql, &department, id)
	if err != nil {
		common.JSONResponse(w, http.StatusNotFound, map[string]interface{}{
			"code":  500,
			"msg":   "获取科室信息失败",
			"error": err.Error(),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "ok",
		"data": department,
		// "sql":  common.DebugSql(sql, id),
	})
}

// OPENID绑定手机号
func Bind_user(w http.ResponseWriter, r *http.Request) {
	phone, err := common.CheckPhone(r.FormValue("phone"))
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "对不起，电话号码不得为空！",
			"err":  err.Error(),
		})
		return
	}
	password, err := common.CheckPassword(r.FormValue("password"))
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "对不起，密码不得为空！",
			"err":  err.Error(),
		})
		return
	}
	openid, err := common.CheckStr(r.FormValue("openid"))
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "对不起，OPENID不得为空！",
			"err":  err.Error(),
		})
		return
	}
	useragent := r.FormValue("useragent")

	// 先判断绑定的是不是职能角色 - 本来这个函数之前是只做患者绑定的，但小程序审核太慢，客户急需使用，已无法等待小程序审核，故将职能部门放在了患者URL中，虽然不易理解，但能跑通
	sql := "select id from rbac_user where phone = ? and pwd = ? and status = 1"
	var userid int
	_ = database.GetOne(sql, &userid, phone, common.Md5Hash(password))
	if userid > 0 {
		sql = "update rbac_user set openid = ? where id = ?"
		_, err = database.Query(sql, openid, userid)
		if err != nil {
			common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
				"code": 500,
				"msg":  "绑定失败",
				"err":  err.Error(),
			})
		}
		common.JSONResponse(w, http.StatusOK, map[string]any{
			"code":      200,
			"msg":       "绑定成功",
			"is_master": true,
			"sql":       common.DebugSql(sql, openid, userid),
		})
		common.Add_log_wxapp(
			fmt.Sprintf("职能角色绑定微信成功，手机号：%s，OPENID：%s", phone, openid),
			userid,
			r,
			useragent,
		)
		return
	} else {
		//应该是患者了
		// 验证手机号是否已被绑定
		var patientid int
		sql = "select id from patient_account where openid = ? and phone <> ?"
		err = database.GetOne(sql, &patientid, openid, phone)
		if err == nil && patientid > 0 {
			common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
				"code": 500,
				"msg":  "您的微信已经绑到了其他账号，请解绑后再试",
			})
			common.Add_log_wxapp(
				fmt.Sprintf("绑定失败，原因：手机号（%s） VS OPENID(%s)已绑定到其它帐号", openid, phone),
				patientid,
				r,
				useragent,
			)
			return
		}
		// 验证手机号和密码是否匹配
		sql = "select id from patient_account where phone = ? and pwd = ? and openid = '0'"
		err = database.GetOne(sql, &patientid, phone, common.Md5Hash(password))
		if err != nil {
			common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
				"code": 500,
				"msg":  "帐号已被别的用户绑定或密码错误",
				"err":  err.Error(),
			})
			common.Add_log_wxapp(
				fmt.Sprintf("绑定失败，原因：帐号已被别的用户绑定或帐（%s）密（%s）错误", phone, password),
				patientid,
				r,
				useragent,
			)
			return
		}
		// fmt.Println(patientid)
		// 绑定手机号
		sql = "update patient_account set openid = ? where id = ?"
		result, err := database.Query(sql, openid, patientid)
		if err != nil {
			common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
				"code": 500,
				"msg":  "绑定失败",
				"err":  err.Error(),
			})
			common.Add_log_wxapp(
				"绑定失败，原因：数据库更新失败",
				patientid,
				r,
				useragent,
			)
			return
		}
		RowsAffected, _ := result.RowsAffected()
		common.JSONResponse(w, http.StatusOK, map[string]interface{}{
			"code":         200,
			"msg":          "绑定成功",
			"is_master":    false,
			"RowsAffected": RowsAffected,
		})
		if RowsAffected > 0 {
			common.Add_log_wxapp(
				fmt.Sprintf("绑定手机号：%s", phone),
				patientid,
				r,
				useragent,
			)
		}
	}

}

// 获取医生详情信息
func Doctor_detail(w http.ResponseWriter, r *http.Request) {
	id, err := common.CheckInt(r.FormValue("id"))
	if id < 0 || err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "参数错误",
		})
		return
	}
	var doctor struct {
		ID                int     `db:"id"`
		Name              string  `db:"name"`
		ConsultationFee   float64 `db:"consultationFee"`
		ProfessionalTitle string  `db:"professionalTitle"`
		Introduction      string  `db:"introduction"`
		Proficient        string  `db:"proficient"`
		DepartmentID      int     `db:"department_id"`
		Department        string  `db:"department"`
	}
	sql := `
	SELECT a.id, a.name, a.consultationFee,a.professionalTitle, a.introduction, a.proficient, a.department_id, ifnull(c.name, '未知科室') AS department 
	  FROM rbac_user AS a 
	  LEFT JOIN rbac_user_roles AS b ON b.user_id = a.id 
	  LEFT JOIN department AS c ON c.id = a.department_id 
	  WHERE a.id = ? AND b.role_id = 4
	`
	err = database.GetRow(sql, &doctor, id)
	if err != nil {
		common.JSONResponse(w, http.StatusNotFound, map[string]interface{}{
			"code":  500,
			"msg":   "获取医生信息失败",
			"error": err.Error(),
			"sql":   common.DebugSql(sql, id),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "ok",
		"data": doctor,
		"sql":  common.DebugSql(sql, id),
	})
}

// 获取医生列表信息
func Doctor_list(w http.ResponseWriter, r *http.Request) {
	limit, _ := common.CheckInt(r.FormValue("limit"))
	page, _ := common.CheckInt(r.FormValue("page"))
	if limit < 0 {
		limit = 10
	}
	if page < 1 {
		page = 1
	}
	offset := (page - 1) * limit
	att_sql := ""
	is_recommend := r.FormValue("is_recommend")
	if is_recommend != "" {
		att_sql = " and is_recommend = " + is_recommend
	}
	sql := `
		SELECT a.id, a.name, a.consultationFee,a.professionalTitle, a.introduction, a.proficient, a.department_id, ifnull(c.name, '未知科室') AS department 
		FROM rbac_user AS a 
		LEFT JOIN rbac_user_roles AS b ON b.user_id = a.id 
		LEFT JOIN department AS c ON c.id = a.department_id 
		WHERE b.role_id = 4 and status = 1
	` + att_sql
	var args []any
	departmentID, err := common.CheckInt(r.FormValue("department_id"))
	if err == nil && departmentID > 0 {
		sql += " AND a.department_id = ? "
		args = append(args, departmentID)
	}
	args = append(args, limit, offset) // 将偏移量和限制添加到参数中
	sql += " ORDER BY a.sort DESC"
	sql += " LIMIT ? OFFSET ?"

	type doctor struct {
		ID                int     `db:"id"`
		Name              string  `db:"name"`
		ConsultationFee   float64 `db:"consultationFee"`
		ProfessionalTitle string  `db:"professionalTitle"`
		Introduction      string  `db:"introduction"`
		Proficient        string  `db:"proficient"`
		DepartmentID      int     `db:"department_id"`
		Department        string  `db:"department"`
	}
	var doctors []doctor
	err = database.GetAll(sql, &doctors, args...)
	if err != nil {
		common.JSONResponse(w, http.StatusNotFound, map[string]interface{}{
			"code":  500,
			"msg":   "获取医生列表失败",
			"error": err.Error(),
			"sql":   common.DebugSql(sql, args...),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "ok",
		"data": doctors,
		"sql":  common.DebugSql(sql, args...),
	})
}

// 幻灯片列表
func Patient_slides_list(w http.ResponseWriter, r *http.Request) {
	var pics string
	sql := "SELECT value_data FROM configs WHERE attribute = 'weichat_patient_slides'"
	err := database.GetOne(sql, &pics)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败",
			"err":  err.Error(),
			// "sql":  common.DebugSql(sql),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "ok",
		"data": pics,
	})
}
