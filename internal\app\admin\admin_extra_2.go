package admin

import (
	"database/sql"
	"fmt"
	"io"
	"mstproject/internal/app/normal"
	"mstproject/pkg/common"
	"mstproject/pkg/config"
	"mstproject/pkg/database"
	"net/http"
	"os"
	"strconv"
	"strings"
)

// Article结构体定义
type Article struct {
	ID          int    `db:"id"`          // ID
	Pid         int    `db:"pid"`         // 文章分类
	User_id     int    `db:"user_id"`     // 发布人
	Title       string `db:"title"`       // 文章标题
	CoverPic    string `db:"coverPic"`    // 封面图片
	Contents    string `db:"contents"`    // 文章内容
	Visits      int    `db:"visits"`      // 展示次数
	Sort        int    `db:"sort"`        // 排序
	Create_time string `db:"create_time"` // 文章建立时间
}

// 盘点分类列表 29
func Stock_take_list(w http.ResponseWriter, r *http.Request) {
	api_id := 29
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	// 分页参数
	limit, err := strconv.Atoi(r.FormValue("limit"))
	if err != nil || limit < 1 {
		limit = 10
	}
	page, err := strconv.Atoi(r.FormValue("page"))
	if err != nil || page < 1 {
		page = 1
	}
	offset := (page - 1) * limit
	// 搜索参数
	var attSql string
	var params []interface{}

	// 药名搜索
	key := r.FormValue("key")
	if key != "" {
		key = strings.TrimSpace(key)
		attSql += " AND name like ?"
		params = append(params, "%"+key+"%")
	}
	stock_type := r.FormValue("stock_type")
	if stock_type == "0" || stock_type == "1" || stock_type == "2" {
		attSql += " and stock_type =?"
		params = append(params, stock_type)
	}
	// 总数
	var count int
	sql := "SELECT COUNT(id) FROM stock_take WHERE 1 " + attSql
	err = database.GetOne(sql, &count, params...)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "读取数据总数失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, params...),
		})
		return
	}

	// 字段：id,name,stock_type,user_id,check_mode,check_status,check_time,create_time
	type StockTake struct {
		ID           int    `db:"id"`
		Name         string `db:"name"`
		Stock_type   int    `db:"stock_type"`
		User_id      int    `db:"user_id"`
		Check_mode   int    `db:"check_mode"`
		Check_status int    `db:"check_status"`
		Check_time   string `db:"check_time"`
		Create_time  string `db:"create_time"`
	}
	var stock_take []StockTake
	sql = "SELECT id, name, stock_type, user_id, check_mode, check_status, check_time, create_time FROM stock_take WHERE 1 " + attSql + " ORDER BY id DESC LIMIT ?,?"
	params = append(params, offset, limit)
	err = database.GetAll(sql, &stock_take, params...)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, params...),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":  200,
		"msg":   "ok",
		"data":  stock_take,
		"count": count,
	})
}

// 盘点分类添加 30
func Stock_take_add(w http.ResponseWriter, r *http.Request) {
	api_id := 30
	session, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	name := r.FormValue("name")
	if name == "" {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "盘点单名称不得为空",
		})
		return
	}

	stock_type, err := common.CheckInt(r.FormValue("stock_type"))
	if err != nil || stock_type < 0 || stock_type > 2 {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "商品类型无效",
		})
		return
	}

	check_mode, err := common.CheckInt(r.FormValue("check_mode"))
	if err != nil || (check_mode != 0 && check_mode != 1) {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "盘点方式无效",
		})
		return
	}

	sql := "insert into stock_take (name, stock_type, user_id, check_mode, check_status, create_time) values (?, ?, ?, ?, 0, NOW())"
	result, err := database.Query(sql, name, stock_type, session.Values["id"], check_mode)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据添加失败",
			"sql":  common.DebugSql(sql, name, stock_type, session.Values["id"], check_mode),
		})
		return
	}
	new_id, _ := result.LastInsertId()
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":         200,
		"msg":          "数据添加成功",
		"RowsAffected": new_id,
	})
	common.Add_log(fmt.Sprintf("添加盘点单，ID：%d", new_id), r)
}

// 盘点分类修改 31
func Stock_take_edit(w http.ResponseWriter, r *http.Request) {
	api_id := 31
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	id, err := common.CheckInt(r.FormValue("id"))
	if id < 1 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "无效的ID",
		})
		return
	}

	check_result := r.FormValue("check_result")
	if check_result == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "盘点结果不得为空",
		})
		return
	}

	check_status, err := common.CheckInt(r.FormValue("check_status"))
	if err != nil || (check_status != 0 && check_status != 1) {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "盘点状态无效",
		})
		return
	}

	sql := "update stock_take set check_status=?,check_result=? where id=?"
	result, err := database.Query(sql, check_status, check_result, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据修改失败",
		})
		return
	}
	RowsAffected, _ := result.RowsAffected()
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":         200,
		"msg":          "数据修改成功",
		"RowsAffected": RowsAffected,
	})
	if RowsAffected > 0 {
		common.Add_log(fmt.Sprintf("盘点单设置已盘点，ID：%d，影响行数：%d", id, RowsAffected), r)
	}
}

// 盘点分类删除 32
func Stock_take_del(w http.ResponseWriter, r *http.Request) {
	api_id := 32
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	id, err := common.CheckInt(r.FormValue("id"))
	if id < 1 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}
	sql := "delete from stock_take where id=?"
	result, err := database.Query(sql, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据删除失败",
		})
		return
	}
	RowsAffected, _ := result.RowsAffected()
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":         200,
		"msg":          "数据删除成功",
		"RowsAffected": RowsAffected,
		// "sql":  common.DebugSql(sql, Perm),
	})
	if RowsAffected > 0 {
		common.Add_log(fmt.Sprintf("删除盘点单，ID：%d", id), r)
	}
}

// 盘点详情
func Stock_take_detail(w http.ResponseWriter, r *http.Request) {
	api_id := 33
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	id, err := common.CheckInt(r.FormValue("id"))
	if id < 1 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "无效的ID",
		})
		return
	}
	// 盘点单详情
	type StockTake struct {
		ID           int    `db:"id"`
		Check_status int    `db:"check_status"`
		Check_result string `db:"check_result"`
	}
	sql := "SELECT id, check_status, check_result FROM stock_take WHERE id=?"
	var stock_take StockTake
	err = database.GetRow(sql, &stock_take, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, id),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "ok",
		"data": stock_take,
	})
}

// 赠品列表 118
func Gift_list(w http.ResponseWriter, r *http.Request) {
	api_id := 118
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	// 分页参数
	limit, err := strconv.Atoi(r.FormValue("limit"))
	if err != nil || limit < 1 {
		limit = 10
	}
	page, err := strconv.Atoi(r.FormValue("page"))
	if err != nil || page < 1 {
		page = 1
	}
	offset := (page - 1) * limit
	// 搜索参数
	var attSql string
	var params []interface{}

	// 赠品名称搜索
	key := r.FormValue("key")
	if key != "" {
		key = strings.TrimSpace(key)
		attSql += " AND name like ?"
		params = append(params, "%"+key+"%")
	}

	// 总数
	var count int
	sql := "SELECT COUNT(id) FROM gifts WHERE 1 " + attSql
	err = database.GetOne(sql, &count, params...)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "读取数据总数失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, params...),
		})
		return
	}

	type Gift struct {
		ID           int     `db:"id"`
		Name         string  `db:"name"`
		Factory_date string  `db:"factory_date"`
		Price        float64 `db:"price"`
		Create_time  string  `db:"create_time"`
	}
	var gifts []Gift
	sql = "SELECT id, name, factory_date, price, create_time FROM gifts WHERE 1 " + attSql + " ORDER BY id DESC LIMIT ?,?"
	params = append(params, offset, limit)
	err = database.GetAll(sql, &gifts, params...)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, params...),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":  200,
		"msg":   "ok",
		"data":  gifts,
		"count": count,
	})
}

// 赠品添加 71
func Gift_add(w http.ResponseWriter, r *http.Request) {
	api_id := 71
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	name := r.FormValue("name")
	if name == "" {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "赠品名称不得为空",
		})
		return
	}

	factory_date := r.FormValue("factory_date")
	if factory_date == "" {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "生产日期不得为空",
		})
		return
	}

	price := r.FormValue("price")
	if price == "" {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "参考价格不得为空",
		})
		return
	}

	sql := "insert into gifts (name, factory_date, price, create_time) values (?, ?, ?, NOW())"
	result, err := database.Query(sql, name, factory_date, price)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据添加失败",
			"sql":  common.DebugSql(sql, name, factory_date, price),
		})
		return
	}
	new_id, _ := result.LastInsertId()
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":         200,
		"msg":          "数据添加成功",
		"RowsAffected": new_id,
	})
	common.Add_log(fmt.Sprintf("添加赠品，ID：%d", new_id), r)
}

// 赠品修改 174
func Gift_edit(w http.ResponseWriter, r *http.Request) {
	api_id := 174
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	id, err := common.CheckInt(r.FormValue("id"))
	if id < 1 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "无效的ID",
		})
		return
	}

	name := r.FormValue("name")
	if name == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "赠品名称不得为空",
		})
		return
	}

	factory_date := r.FormValue("factory_date")
	if factory_date == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "生产日期不得为空",
		})
		return
	}

	price := r.FormValue("price")
	if price == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "参考价格不得为空",
		})
		return
	}

	sql := "update gifts set name=?, factory_date=?, price=? where id=?"
	result, err := database.Query(sql, name, factory_date, price, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据修改失败",
		})
		return
	}
	RowsAffected, _ := result.RowsAffected()
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":         200,
		"msg":          "数据修改成功",
		"RowsAffected": RowsAffected,
	})
	if RowsAffected > 0 {
		common.Add_log(fmt.Sprintf("修改赠品信息，ID：%d，影响行数：%d", id, RowsAffected), r)
	}
}

// 赠品详情 175
func Gift_detail(w http.ResponseWriter, r *http.Request) {
	api_id := 175
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	id, err := common.CheckInt(r.FormValue("id"))
	if id < 1 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "无效的ID",
		})
		return
	}

	type Gift struct {
		ID           int     `db:"id"`
		Name         string  `db:"name"`
		Factory_date string  `db:"factory_date"`
		Price        float64 `db:"price"`
		Create_time  string  `db:"create_time"`
	}
	var gift Gift
	sql := "SELECT id, name, factory_date, price, create_time FROM gifts WHERE id=?"
	err = database.GetOne(sql, &gift, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败",
			"err":  err.Error(),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "ok",
		"data": gift,
	})
}

// 赠品删除 176
func Gift_del(w http.ResponseWriter, r *http.Request) {
	api_id := 176
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	id, err := common.CheckInt(r.FormValue("id"))
	if id < 1 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}
	sql := "delete from gifts where id=?"
	result, err := database.Query(sql, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据删除失败",
		})
		return
	}
	RowsAffected, _ := result.RowsAffected()
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":         200,
		"msg":          "数据删除成功",
		"RowsAffected": RowsAffected,
	})
	if RowsAffected > 0 {
		common.Add_log(fmt.Sprintf("删除赠品，ID：%d", id), r)
	}
}

// 赠品库存补货 72
func Warehouse_gifts_in(w http.ResponseWriter, r *http.Request) {
	api_id := 72
	session, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}

	// 赠品ID
	gift_id, err := common.CheckInt(r.FormValue("gift_id"))
	if gift_id < 1 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "赠品ID不能为空",
		})
		return
	}

	// 赠品数量
	quantity, err := common.CheckFloat(r.FormValue("quantity"))
	if quantity < 0 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "补货数量不能为空或小于0",
		})
		return
	}

	// 赠品价格
	price, err := common.CheckFloat(r.FormValue("price"))
	if price < 0 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "赠品价格不能为空或小于0",
		})
		return
	}

	// 查询当前赠品库存
	var warehouse_gift struct {
		ID       int     `db:"id"`
		Quantity float64 `db:"quantity"`
	}
	sql := "SELECT id, quantity FROM warehouse_gifts WHERE gift_id = ? LIMIT 1"
	err = database.GetRow(sql, &warehouse_gift, gift_id)
	if err != nil {
		// 如果查询出错，说明需要新增库存记录
		sqls := []database.SQLExec{
			{Query: "INSERT INTO warehouse_gifts (gift_id, quantity, price) VALUES (?, ?, ?)",
				Args: []interface{}{
					gift_id, quantity, price,
				}},
			{Query: "INSERT INTO warehouse_gifts_log (pid, user_id, kind, change_data, old_data, new_data) VALUES (?, ?, ?, ?, ?, ?)",
				Args: []interface{}{
					gift_id, session.Values["id"], 1, quantity, 0, quantity,
				}},
		}

		// 执行事务
		RowsAffected, err := database.ExecuteTransaction_with_affectedRows(sqls)
		if err != nil {
			common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
				"code": 500,
				"msg":  "赠品新增库存失败",
				"err":  err.Error(),
			})
			return
		}

		common.JSONResponse(w, http.StatusOK, map[string]interface{}{
			"code": 200,
			"msg":  "赠品新增库存成功",
			"id":   gift_id,
		})

		if RowsAffected > 0 {
			common.Add_log(fmt.Sprintf("赠品%d新增库存%f，影响行数：%d", gift_id, quantity, RowsAffected), r)
		}
	} else {
		// 补货场景
		new_data := warehouse_gift.Quantity + quantity
		sqls := []database.SQLExec{
			{Query: "UPDATE warehouse_gifts SET quantity = quantity + ?, price = ? WHERE id = ?",
				Args: []interface{}{
					quantity, price, warehouse_gift.ID,
				}},
			{Query: "INSERT INTO warehouse_gifts_log (pid, user_id, kind, change_data, old_data, new_data) VALUES (?, ?, ?, ?, ?, ?)",
				Args: []interface{}{
					gift_id, session.Values["id"], 1, quantity, warehouse_gift.Quantity, new_data,
				}},
		}

		// 执行事务
		RowsAffected, err := database.ExecuteTransaction_with_affectedRows(sqls)
		if err != nil {
			common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
				"code": 500,
				"msg":  "赠品补货失败",
				"err":  err.Error(),
			})
			return
		}

		common.JSONResponse(w, http.StatusOK, map[string]interface{}{
			"code": 200,
			"msg":  "赠品补货成功",
			"id":   gift_id,
		})

		if RowsAffected > 0 {
			common.Add_log(fmt.Sprintf("赠品%d补货%f，影响行数：%d", gift_id, quantity, RowsAffected), r)
		}
	}
}

// 成品库存ID2商品名称 - 之所以与赠品、药材分开，是因为成品没有品种表，成品是自己制作的成品药
// 20250312 新增pre_id字段，给使用成品药的处方检索旧处方ID之使用
func Warehouse2productname_finisheddrug(w http.ResponseWriter, r *http.Request) {
	api_id := config.NormalPerm
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	id, err := common.CheckInt(r.FormValue("wid"))
	if err != nil || id < 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "无效的ID",
		})
		return
	}
	var product struct {
		ID     int    `db:"id"`
		Pre_id int    `db:"pre_id"`
		Name   string `db:"name"`
	}
	sql := "SELECT id, name,pre_id FROM warehouse_finisheddrug WHERE id = ?"
	err = database.GetRow(sql, &product, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败",
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "获取库存ID对应品名成功",
		"data": product,
	})
}

// 库存ID2商品名称
func Warehouse2productname(w http.ResponseWriter, r *http.Request) {
	api_id := config.NormalPerm
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	Ware_type, err := common.CheckInt(r.FormValue("type"))
	if err != nil || Ware_type < 0 || Ware_type > 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "库存类型不能为空",
		})
		return
	}
	warehouse_id := r.FormValue("wid")
	if warehouse_id == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "库存ID不能为空",
		})
		return
	}

	Ware_array := [][]string{{"drug", "warehouse_drug", "drug_id"}, {"gifts", "warehouse_gifts", "gift_id"}}
	type Product struct {
		ID   int    `db:"id"`
		Name string `db:"name"`
	}
	var product Product
	sql := "SELECT a.id, a.name FROM " + Ware_array[Ware_type][0] + " AS a LEFT JOIN " + Ware_array[Ware_type][1] + " AS b ON a.id = b." + Ware_array[Ware_type][2] + " WHERE b.id = ?"
	err = database.GetRow(sql, &product, warehouse_id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败",
		})
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "获取库存ID对应品名成功",
		"data": product,
	})
}

// func Article_category_id2name(w http.ResponseWriter, r *http.Request) {
// 	api_id := config.NormalPerm
// 	_, isLogin := common.Check_Perm(w, r, api_id)
// 	if !isLogin {
// 		return
// 	}
// 	id, err := common.CheckInt(r.FormValue("id"))
// 	if err != nil || id < 0 {
// 		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
// 			"code": 500,
// 			"msg":  "文章分类ID不能为空",
// 		})
// 		return
// 	}
// 	var name string
// 	sql := "SELECT title as name from article_category where id = ?"
// 	err = database.GetRow(sql, &name, id)
// 	if err != nil {
// 		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
// 			"code": 500,
// 			"msg":  "数据查询失败",
// 		})
// 	}
// 	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
// 		"code": 200,
// 		"msg":  "获取文章分类ID对应名称成功",
// 		"data": name,
// 	})
// }

// 下面开始文章的增删改查
// 文章列表
func Article_list(w http.ResponseWriter, r *http.Request) {
	api_id := config.NormalPerm
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}

	// 分页参数
	limit, err := strconv.Atoi(r.FormValue("limit"))
	if err != nil || limit < 1 {
		limit = 10
	}
	page, err := strconv.Atoi(r.FormValue("page"))
	if err != nil || page < 1 {
		page = 1
	}
	offset := (page - 1) * limit

	// 搜索参数
	var attSql string
	var params []interface{}
	key := r.FormValue("key")
	if key != "" {
		attSql = " WHERE (title LIKE ? OR contents LIKE ?)"
		params = append(params, "%"+key+"%", "%"+key+"%")
	}

	// 父ID筛选
	pid := r.FormValue("pid")
	if pid != "" {
		if attSql == "" {
			attSql = " WHERE pid = ? "
		} else {
			attSql += " AND pid = ? "
		}
		params = append(params, pid)
	}

	// 查询总数
	var count int
	countSql := "SELECT COUNT(id) FROM article" + attSql
	err = database.GetOne(countSql, &count, params...)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "读取数据总数失败",
			"err":  err.Error(),
		})
		return
	}

	// 查询文章列表
	sql := "SELECT id, pid, user_id, title, coverPic, visits, sort, create_time FROM article" + attSql + " ORDER BY sort DESC LIMIT ?, ?"
	params = append(params, offset, limit)
	var articles []Article
	err = database.GetAll(sql, &articles, params...)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败",
			"err":  err.Error(),
		})
		return
	}

	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":  200,
		"msg":   "ok",
		"data":  articles,
		"count": count,
	})
}

// 添加文章
func Article_add(w http.ResponseWriter, r *http.Request) {
	api_id := config.NormalPerm
	session, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	user_id := session.Values["id"]

	pid, err := common.CheckInt(r.FormValue("pid"))
	if err != nil || pid < 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "文章分类不能为空",
		})
		return
	}

	title := r.FormValue("title")
	if title == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "文章标题不能为空",
		})
		return
	}

	contents := r.FormValue("contents")
	if contents == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "文章内容不能为空",
		})
		return
	}

	coverPic := r.FormValue("coverPic")

	// 获取最大排序值
	var maxSort int
	sql := "SELECT COALESCE(MAX(sort), 0) FROM article"
	err = database.GetOne(sql, &maxSort)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "获取排序值失败",
			"err":  err.Error(),
		})
		return
	}

	// 添加文章
	sql = "INSERT INTO article (pid, user_id, title, coverPic, contents, visits, sort) VALUES (?, ?, ?, ?, ?, ?, ?)"
	result, err := database.Query(sql, pid, user_id, title, coverPic, contents, 0, maxSort+10)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "添加文章失败",
			"err":  err.Error(),
		})
		return
	}
	new_id, _ := result.LastInsertId()
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "添加文章成功",
		"id":   new_id,
	})
	common.Add_log(fmt.Sprintf("添加文章，ID：%d，标题：%s", new_id, title), r)
}

// 修改文章
func Article_edit(w http.ResponseWriter, r *http.Request) {
	api_id := config.NormalPerm
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	id, err := common.CheckInt(r.FormValue("id"))
	if err != nil || id < 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "文章ID不能为空",
		})
		return
	}

	pid, err := common.CheckInt(r.FormValue("Pid"))
	if err != nil || pid < 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "文章分类不能为空",
		})
		return
	}

	title := r.FormValue("Title")
	if title == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "文章标题不能为空",
		})
		return
	}

	contents := r.FormValue("Contents")
	if contents == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "文章内容不能为空",
		})
		return
	}

	coverPic := r.FormValue("CoverPic")
	sort, err := common.CheckInt(r.FormValue("Sort"))
	if err != nil || sort < 0 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "排序不能为空",
		})
		return
	}

	// 修改文章
	sql := "UPDATE article SET pid = ?, title = ?, coverPic = ?, contents = ?, sort = ? WHERE id = ?"
	result, err := database.Query(sql, pid, title, coverPic, contents, sort, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "修改文章失败",
			"err":  err.Error(),
		})
		return
	}
	RowsAffected, _ := result.RowsAffected()
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":         200,
		"msg":          "修改文章成功",
		"RowsAffected": RowsAffected,
	})
	if RowsAffected > 0 {
		common.Add_log(fmt.Sprintf("修改文章，ID：%d，标题：%s", id, title), r)
	}
}

// 删除文章
func Article_del(w http.ResponseWriter, r *http.Request) {
	api_id := config.NormalPerm
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	id, err := common.CheckInt(r.FormValue("id"))
	if err != nil || id < 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "文章ID不能为空",
		})
		return
	}
	// 删除文章
	sql := "DELETE FROM article WHERE id = ?"
	result, err := database.Query(sql, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "删除文章失败",
			"err":  err.Error(),
		})
		return
	}
	RowsAffected, _ := result.RowsAffected()
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":         200,
		"msg":          "删除文章成功",
		"RowsAffected": RowsAffected,
	})
	if RowsAffected > 0 {
		common.Add_log(fmt.Sprintf("删除文章，ID：%d", id), r)
	}
}

// 文章详情
func Article_detail(w http.ResponseWriter, r *http.Request) {
	api_id := config.NormalPerm
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	id, err := common.CheckInt(r.FormValue("id"))
	if err != nil || id < 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "文章ID不能为空",
		})
		return
	}

	// 查询文章详情
	sql := "SELECT id, pid, user_id, title, coverPic, contents, visits, sort, create_time FROM article WHERE id = ?"
	var article Article
	err = database.GetOne(sql, &article, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "文章不存在或查询失败",
			"err":  err.Error(),
		})
		return
	}

	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "ok",
		"data": article,
	})
}

// 通用 - 检查处方对应的药品，是否入库
func Find_prescription_to_warehouse_by_id(w http.ResponseWriter, r *http.Request) {
	api_id := config.NormalPerm
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	id, err := common.CheckInt(r.FormValue("id"))
	if err != nil || id < 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]any{
			"code": 500,
			"msg":  "处方ID不能为空",
		})
		return
	}
	sql := "select id from warehouse_finisheddrug where pre_id = ?"
	var has_id int
	err = database.GetOne(sql, &has_id, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]any{
			"code": 500,
			"msg":  "查询失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, id),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]any{
		"code": 200,
		"msg":  "ok",
		"data": has_id,
	})
}

// 查看指定订单状态，是否允许其旗下病历允许开处方
func Check_order_status_to_prescription(w http.ResponseWriter, r *http.Request) {
	api_id := config.NormalPerm
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	id, err := common.CheckInt(r.FormValue("id"))
	if err != nil || id < 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "病历ID不能为空",
		})
		return
	}
	type Order struct {
		ID              int `db:"id"`
		Status          int `db:"status"`
		PayReviewStatus int `db:"pay_review_status"`
	}
	sql := "select status,pay_review_status from orders where id = (select ord_id from patient_records where id = ?)"
	var order Order
	err = database.GetRow(sql, &order, id)
	var code int
	if err != nil {
		code = 500
	} else {
		code = 200
	}
	common.JSONResponse(w, http.StatusOK, map[string]any{
		"code": code,
		"msg":  "ok",
		"data": order,
		"sql":  common.DebugSql(sql, id),
	})
}

// 售前转到售后部门后，售后部门待分组的订单列表 - 同时也会分人哦
func Order_clerk_aftersales_wait_list(w http.ResponseWriter, r *http.Request) {
	api_id := 185
	session, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	var attSql string = ""
	if session.Values["role_ids"] == "1" {
		childDepartmentIDs, err := common.Get_department_children_ids(config.Global_sale_department_after_sale)
		if err == nil && len(childDepartmentIDs) > 0 {
			strChildDepartmentIDs := make([]string, len(childDepartmentIDs))
			for i, id := range childDepartmentIDs {
				strChildDepartmentIDs[i] = strconv.Itoa(id)
			}
			attSql += fmt.Sprintf(" AND asst_dep_id IN (%s)", strings.Join(strChildDepartmentIDs, ","))
		}
	} else {
		attSql += fmt.Sprintf(" and asst_dep_id = %v", session.Values["department_id"])
	}
	// 处理医生ID过滤
	doc_id := r.FormValue("doc_id")
	if doc_id != "" {
		attSql += " AND doc_id = " + doc_id
	} else {
		doc_dep_id := r.FormValue("doc_dep_id")
		if doc_dep_id != "" {
			// 使用dp_ids_cache_get获取部门下的员工ID列表
			doc_dep_id_int, _ := strconv.Atoi(doc_dep_id)
			userIDs, err := normal.Dp_ids_cache_get(doc_dep_id_int)
			if err == nil && len(userIDs) > 0 {
				// 将员工ID数组转为逗号分隔的字符串
				userIDsStr := make([]string, len(userIDs))
				for i, id := range userIDs {
					userIDsStr[i] = strconv.Itoa(id)
				}
				attSql += " AND doc_id IN (" + strings.Join(userIDsStr, ",") + ")"
			} else {
				common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
					"code": 500,
					"msg":  "该医生部门下无医生人员",
				})
				return
			}
		}
	}

	pat_pro_id := r.FormValue("pat_pro_id")
	if pat_pro_id != "" {
		attSql += " AND pat_pro_id = " + pat_pro_id
	}
	// 分页参数
	limit, err := strconv.Atoi(r.FormValue("limit"))
	if err != nil || limit < 10 {
		limit = 10
	}
	page, err := strconv.Atoi(r.FormValue("page"))
	if err != nil || page < 1 {
		page = 1
	}
	offset := (page - 1) * limit
	// 总数
	var count int
	sql := "SELECT COUNT(id) FROM orders WHERE 1 " + attSql
	err = database.GetOne(sql, &count)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "读取数据总数失败",
			"sql":  common.DebugSql(sql),
		})
		return
	}
	// 数据查询
	type Order struct {
		ID                int     `db:"id"`                // 主键ID
		Doc_id            int     `db:"doc_id"`            // 医生ID
		Asst_id           int     `db:"asst_id"`           // 医助ID（售前）
		Pat_id            int     `db:"pat_id"`            // 患者帐号ID
		Pat_pro_id        int     `db:"pat_pro_id"`        // 患者信息ID
		Express           string  `db:"express"`           // 快递公司
		Tracking_num      string  `db:"tracking_num"`      // 快递单号
		Total_money       float64 `db:"total_money"`       // 总款
		Pre_pay           float64 `db:"pre_pay"`           // 预付
		Final_pay         float64 `db:"final_pay"`         // 尾款
		Record_ids        string  `db:"record_ids"`        // 病历ID集合
		Status            int     `db:"status"`            // 订单状态
		Doc_dep_id        int     `db:"doc_dep_id"`        // 医生部门ID
		Asst_dep_id       int     `db:"asst_dep_id"`       // 医助部门ID
		Pay_review_status int     `db:"pay_review_status"` // 支付审核状态
		Address           string  `db:"address"`           // 收货地址
		Final_is_full     int     `db:"final_is_full"`     //是否全额尾款
		Create_time       string  `db:"create_time"`       // 创建时间
	}

	sql = "SELECT id,doc_id,pat_id,pat_pro_id,record_ids,asst_id,express,tracking_num,address,total_money,pre_pay,final_pay,pay_review_status,status,doc_dep_id,asst_dep_id,final_is_full,create_time FROM orders WHERE 1 " + attSql + " ORDER BY id DESC limit ? OFFSET ?"
	var order []Order
	err = database.GetAll(sql, &order, limit, offset)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, limit, offset),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":  200,
		"msg":   "ok",
		"data":  order,
		"count": count,
		"sql":   common.DebugSql(sql, limit, offset),
	})
}

// 订单列表 - 售后文员
func Order_clerk_aftersales_list(w http.ResponseWriter, r *http.Request) {
	api_id := 183
	session, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	var departmentIDsStr []string
	if session.Values["role_ids"] != "1" {
		department_id := session.Values["department_id"]
		// fmt.Println("department_id:", department_id)
		departmentIDs, err := common.Get_department_children_ids(department_id.(int))
		// fmt.Println("departmentIDs:", departmentIDs)
		if err != nil {
			common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
				"code": 500,
				"msg":  "获取部门下科室ID失败: " + err.Error(),
			})
			return
		}
		// 将部门ID数组转为字符串，用于SQL查询
		departmentIDsStr = make([]string, len(departmentIDs))
		for i, id := range departmentIDs {
			departmentIDsStr[i] = strconv.Itoa(id)
		}
		// 检查部门权限
		if len(departmentIDsStr) == 0 {
			common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
				"code": 500,
				"msg":  "对不起，您尚未授权管理售后部门。",
			})
			return
		}
	}

	var attSql string = ""
	status := r.FormValue("Order_Status")
	if status != "" {
		attSql += " AND status = " + status
	}

	// 20250522 去掉单按部门筛选，直接选人员ID
	// asst_dep_id := r.FormValue("asst_dep_id")
	// fmt.Println(asst_dep_id)
	// if asst_dep_id != "" {
	// 	// 检查用户传递的部门ID是否在允许的部门列表中
	// 	if session.Values["role_ids"] != "1" {
	// 		isValidDepartment := false
	// 		for _, deptID := range departmentIDsStr {
	// 			if deptID == asst_dep_id {
	// 				isValidDepartment = true
	// 				break
	// 			}
	// 		}

	// 		if !isValidDepartment {
	// 			common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
	// 				"code": 500,
	// 				"msg":  "无权访问该部门数据",
	// 			})
	// 			return
	// 		}
	// 	}

	// 	// 使用用户传递的部门ID查询该部门下的员工
	// 	asst_dep_id_int, _ := strconv.Atoi(asst_dep_id)
	// 	userIDs, err := normal.Dp_ids_cache_get(asst_dep_id_int)
	// 	if err == nil && len(userIDs) > 0 {
	// 		// 将员工ID数组转为逗号分隔的字符串
	// 		userIDsStr := make([]string, len(userIDs))
	// 		for i, id := range userIDs {
	// 			userIDsStr[i] = strconv.Itoa(id)
	// 		}
	// 		attSql += " AND asst_id IN (" + strings.Join(userIDsStr, ",") + ")"
	// 	} else {
	// 		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
	// 			"code": 500,
	// 			"msg":  "该医助部门下无医助人员",
	// 		})
	// 		return
	// 	}
	// } else {
	// 	if session.Values["role_ids"] != "1" {
	// 		attSql += fmt.Sprintf(" AND asst_dep_id IN (%s)", strings.Join(departmentIDsStr, ","))
	// 	}
	// }

	// 医助数据隔离
	if session.Values["role_ids"] != "1" {
		attSql += fmt.Sprintf(" AND asst_dep_id IN (%s)", strings.Join(departmentIDsStr, ","))
	} else {
		childDepartmentIDs, err := common.Get_department_children_ids(config.Global_sale_department_after_sale)
		if err == nil && len(childDepartmentIDs) > 0 {
			var groupIDsStr []string
			for _, childDeptID := range childDepartmentIDs {
				groupIDs, err := common.Get_department_children_ids(childDeptID)
				if err == nil && len(groupIDs) > 0 {
					for _, groupID := range groupIDs {
						groupIDsStr = append(groupIDsStr, strconv.Itoa(groupID))
					}
				}
			}
			if len(groupIDsStr) > 0 {
				attSql += fmt.Sprintf(" AND asst_dep_id IN (%s)", strings.Join(groupIDsStr, ","))
			}
		}
	}
	// 处理医生ID过滤
	doc_id := r.FormValue("doc_id")
	if doc_id != "" {
		attSql += " AND doc_id = " + doc_id
	} else {
		doc_dep_id := r.FormValue("doc_dep_id")
		if doc_dep_id != "" {
			// 使用dp_ids_cache_get获取部门下的员工ID列表
			doc_dep_id_int, _ := strconv.Atoi(doc_dep_id)
			userIDs, err := normal.Dp_ids_cache_get(doc_dep_id_int)
			if err == nil && len(userIDs) > 0 {
				// 将员工ID数组转为逗号分隔的字符串
				userIDsStr := make([]string, len(userIDs))
				for i, id := range userIDs {
					userIDsStr[i] = strconv.Itoa(id)
				}
				attSql += " AND doc_id IN (" + strings.Join(userIDsStr, ",") + ")"
			} else {
				common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
					"code": 500,
					"msg":  "该医生部门下无医生人员",
				})
				return
			}
		}
	}

	// 处理医助ID过滤
	asst_id := r.FormValue("asst_id")
	if asst_id != "" {
		attSql += " AND asst_id = " + asst_id
	}
	pat_pro_id := r.FormValue("pat_pro_id")
	if pat_pro_id != "" {
		attSql += " AND pat_pro_id = " + pat_pro_id
	}
	// 分页参数
	limit, err := strconv.Atoi(r.FormValue("limit"))
	if err != nil || limit < 10 {
		limit = 10
	}
	page, err := strconv.Atoi(r.FormValue("page"))
	if err != nil || page < 1 {
		page = 1
	}
	offset := (page - 1) * limit
	// 总数
	var count int
	sql := "SELECT COUNT(id) FROM orders WHERE 1 " + attSql
	err = database.GetOne(sql, &count)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "读取数据总数失败",
			"sql":  common.DebugSql(sql),
		})
		return
	}
	// 数据查询
	type Order struct {
		ID                int     `db:"id"`                // 主键ID
		Doc_id            int     `db:"doc_id"`            // 医生ID
		Asst_id           int     `db:"asst_id"`           // 医助ID（售前）
		Pat_id            int     `db:"pat_id"`            // 患者帐号ID
		Pat_pro_id        int     `db:"pat_pro_id"`        // 患者信息ID
		Express           string  `db:"express"`           // 快递公司
		Tracking_num      string  `db:"tracking_num"`      // 快递单号
		Total_money       float64 `db:"total_money"`       // 总款
		Pre_pay           float64 `db:"pre_pay"`           // 预付
		Final_pay         float64 `db:"final_pay"`         // 尾款
		Record_ids        string  `db:"record_ids"`        // 病历ID集合
		Status            int     `db:"status"`            // 订单状态
		Doc_dep_id        int     `db:"doc_dep_id"`        // 医生部门ID
		Asst_dep_id       int     `db:"asst_dep_id"`       // 医助部门ID
		Pay_review_status int     `db:"pay_review_status"` // 支付审核状态
		Address           string  `db:"address"`           // 收货地址
		Final_is_full     int     `db:"final_is_full"`     //是否全额尾款
		Create_time       string  `db:"create_time"`       // 创建时间
	}

	sql = "SELECT id,doc_id,pat_id,pat_pro_id,record_ids,asst_id,express,tracking_num,address,total_money,pre_pay,final_pay,pay_review_status,status,doc_dep_id,asst_dep_id,final_is_full,create_time FROM orders WHERE 1 " + attSql + " ORDER BY id DESC limit ? OFFSET ?"
	var order []Order
	err = database.GetAll(sql, &order, limit, offset)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败",
			"err":  err.Error(),
			// "sql":  common.DebugSql(sql, limit, offset),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":  200,
		"msg":   "ok",
		"data":  order,
		"count": count,
		"sql":   common.DebugSql(sql, limit, offset),
	})

}

// 订单列表 - 倾向于物流数据
func Order_list_express(w http.ResponseWriter, r *http.Request) {
	api_id := 133
	// 订单表：orders
	session, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	var departmentIDsStr []string
	if session.Values["role_ids"] != "1" {
		department_id := session.Values["department_id"]
		// fmt.Println("department_id:", department_id)
		departmentIDs, err := common.Get_department_children_ids(department_id.(int))
		// fmt.Println("departmentIDs:", departmentIDs)
		if err != nil {
			common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
				"code": 500,
				"msg":  "获取部门下科室ID失败: " + err.Error(),
			})
			return
		}
		// 将部门ID数组转为字符串，用于SQL查询
		departmentIDsStr = make([]string, len(departmentIDs))
		for i, id := range departmentIDs {
			departmentIDsStr[i] = strconv.Itoa(id)
		}
		// 检查部门权限
		if len(departmentIDsStr) == 0 {
			common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
				"code": 500,
				"msg":  "对不起，您尚未授权管理售前部门。",
			})
			return
		}
	}
	// 带分页的查询
	var attSql string = ""
	status := r.FormValue("Order_Status")
	if status != "" {
		attSql += " AND status = " + status
	}
	// 医助数据隔离
	if session.Values["role_ids"] != "1" {
		attSql += fmt.Sprintf(" AND asst_dep_id IN (%s)", strings.Join(departmentIDsStr, ","))
	} else {
		childDepartmentIDs, err := common.Get_department_children_ids(config.Global_sale_department_pre_sale)
		if err == nil && len(childDepartmentIDs) > 0 {
			var groupIDsStr []string
			for _, childDeptID := range childDepartmentIDs {
				groupIDs, err := common.Get_department_children_ids(childDeptID)
				if err == nil && len(groupIDs) > 0 {
					for _, groupID := range groupIDs {
						groupIDsStr = append(groupIDsStr, strconv.Itoa(groupID))
					}
				}
			}
			if len(groupIDsStr) > 0 {
				attSql += fmt.Sprintf(" AND asst_dep_id IN (%s)", strings.Join(groupIDsStr, ","))
			}
		}
	}
	doc_id := r.FormValue("doc_id")
	if doc_id != "" {
		attSql += " AND doc_id = " + doc_id
	} else {
		doc_dep_id := r.FormValue("doc_dep_id")
		if doc_dep_id != "" {
			// 使用dp_ids_cache_get获取部门下的员工ID列表
			doc_dep_id_int, _ := strconv.Atoi(doc_dep_id)
			userIDs, err := normal.Dp_ids_cache_get(doc_dep_id_int)
			if err == nil && len(userIDs) > 0 {
				// 将员工ID数组转为逗号分隔的字符串
				userIDsStr := make([]string, len(userIDs))
				for i, id := range userIDs {
					userIDsStr[i] = strconv.Itoa(id)
				}
				attSql += " AND doc_id IN (" + strings.Join(userIDsStr, ",") + ")"
			} else {
				common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
					"code": 500,
					"msg":  "该医生部门下无医生人员",
				})
				return
			}
		}
	}
	// 处理医助ID过滤
	asst_id := r.FormValue("asst_id")
	if asst_id != "" {
		attSql += " AND asst_id = " + asst_id
	}
	pat_pro_id := r.FormValue("pat_pro_id")
	if pat_pro_id != "" {
		attSql += " AND pat_pro_id = " + pat_pro_id
	}
	// 分页参数
	limit, err := strconv.Atoi(r.FormValue("limit"))
	if err != nil || limit < 10 {
		limit = 10
	}
	page, err := strconv.Atoi(r.FormValue("page"))
	if err != nil || page < 1 {
		page = 1
	}
	offset := (page - 1) * limit
	// 总数
	var count int
	sql := "SELECT COUNT(id) FROM orders WHERE 1 " + attSql
	err = database.GetOne(sql, &count)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "读取数据总数失败",
			// "sql":  common.DebugSql(sql),
		})
		return
	}
	// 数据查询
	type Order struct {
		ID                int     `db:"id"`                // 主键ID
		Doc_id            int     `db:"doc_id"`            // 医生ID
		Asst_id           int     `db:"asst_id"`           // 医助ID（售前）
		Pat_id            int     `db:"pat_id"`            // 患者帐号ID
		Pat_pro_id        int     `db:"pat_pro_id"`        // 患者信息ID
		Express           string  `db:"express"`           // 快递公司
		Tracking_num      string  `db:"tracking_num"`      // 快递单号
		Total_money       float64 `db:"total_money"`       // 总款
		Pre_pay           float64 `db:"pre_pay"`           // 预付
		Final_pay         float64 `db:"final_pay"`         // 尾款
		Record_ids        string  `db:"record_ids"`        // 病历ID集合
		Status            int     `db:"status"`            // 订单状态
		Doc_dep_id        int     `db:"doc_dep_id"`        // 医生部门ID
		Asst_dep_id       int     `db:"asst_dep_id"`       // 医助部门ID
		Pay_review_status int     `db:"pay_review_status"` // 支付审核状态
		Address           string  `db:"address"`           // 收货地址
		Final_is_full     int     `db:"final_is_full"`     //是否全额尾款
		Create_time       string  `db:"create_time"`       // 创建时间
	}

	sql = "SELECT id,doc_id,pat_id,pat_pro_id,record_ids,asst_id,express,tracking_num,address,total_money,pre_pay,final_pay,pay_review_status,status,doc_dep_id,asst_dep_id,final_is_full,create_time FROM orders WHERE 1 " + attSql + " ORDER BY id DESC limit ? OFFSET ?"
	var order []Order
	err = database.GetAll(sql, &order, limit, offset)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败",
			"err":  err.Error(),
			// "sql":  common.DebugSql(sql, limit, offset),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":  200,
		"msg":   "ok",
		"data":  order,
		"count": count,
		"sql":   common.DebugSql(sql, limit, offset),
	})
}

// 解绑用户微信小程序的绑定
func Un_bind_user(w http.ResponseWriter, r *http.Request) {
	api_id := config.NormalPerm
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	id, err := common.CheckInt(r.FormValue("id"))
	if err != nil || id < 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "无效的ID",
		})
		return
	}
	sql := "update patient_account set openid = '0' where id = ?"
	result, err := database.Query(sql, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "解绑失败",
			"err":  err.Error(),
		})
		return
	}
	RowsAffected, _ := result.RowsAffected()
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":         200,
		"msg":          "解绑成功",
		"RowsAffected": RowsAffected,
	})
	if RowsAffected > 0 {
		common.Add_log(fmt.Sprintf("解绑用户微信小程序的绑定，ID：%d", id), r)
	}
}

// 根据订单号查询物流表中的数据：
func Express_info_by_ord_id(w http.ResponseWriter, r *http.Request) {
	api_id := config.NormalPerm
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	id, err := common.CheckInt(r.FormValue("id"))
	if err != nil || id < 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "无效的ID",
		})
		return
	}
	type Express_info struct {
		Status            int    `db:"status"`
		Last_contents     string `db:"last_contents"`
		Express_last_time string `db:"express_last_time"`
		Last_update_time  string `db:"last_update_time"`
		Create_time       string `db:"create_time"`
	}
	sql := "select status,last_contents,express_last_time,last_update_time,create_time from express_information where ord_id = ?"
	var express_info Express_info
	err = database.GetRow(sql, &express_info, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]any{
			"code": 500,
			"msg":  "数据查询失败",
			"err":  err.Error(),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]any{
		"code": 200,
		"msg":  "ok",
		"data": express_info,
	})
}

func Update_stamp(w http.ResponseWriter, r *http.Request) {
	api_id := config.SystemPerm
	if _, isLogin := common.Check_Perm(w, r, api_id); !isLogin {
		return
	}

	// 设置最大内存限制，防止大文件攻击
	err := r.ParseMultipartForm(2 << 20) // 限制2MB
	if err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "无法解析上传文件: " + err.Error(),
		})
		return
	}

	// 获取上传的文件
	file, handler, err := r.FormFile("file")
	if err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "获取上传文件失败: " + err.Error(),
		})
		return
	}
	defer file.Close()

	// 检查文件类型
	if handler.Header.Get("Content-Type") != "image/png" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "只允许上传PNG格式的图片",
		})
		return
	}

	// 检查文件大小
	if handler.Size > 1024*1024 { // 1MB
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "上传图片不能超过1MB",
		})
		return
	}

	// 确保目录存在
	uploadDir := "static/uploads/sys"
	if _, err := os.Stat(uploadDir); os.IsNotExist(err) {
		if err := os.MkdirAll(uploadDir, 0755); err != nil {
			common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
				"code": 500,
				"msg":  "无法创建目录: " + err.Error(),
			})
			return
		}
	}

	// 保存文件路径
	filepath := config.Dist_catagory + "/uploads/sys/stamp.png"
	// 创建目标文件
	dst, err := os.Create(filepath)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "无法创建文件: " + err.Error(),
		})
		return
	}
	defer dst.Close()

	// 复制文件内容
	if _, err = io.Copy(dst, file); err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "无法保存文件: " + err.Error(),
		})
		return
	}

	// 记录操作日志
	common.Add_log("更新系统印章图片", r)

	// 返回成功响应
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "印章上传成功",
	})
}

// 超级管理员可随意转移用户
func Transfer_user(w http.ResponseWriter, r *http.Request) {
	api_id := 184
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	// 患者帐号ID
	pat_id, err := common.CheckInt(r.FormValue("pat_id"))
	if err != nil || pat_id < 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "无效的用户ID",
		})
		return
	}
	// 部门ID
	asst_dep_id, err := common.CheckInt(r.FormValue("asst_dep_id"))
	if err != nil || asst_dep_id < 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "请选择正确的部门",
		})
		return
	}
	// 销售人员ID
	asst_id, err := common.CheckInt(r.FormValue("asst_id"))
	if err != nil || asst_id < 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "无效的销售人员ID",
		})
		return
	}

	// 判断是否为售前部门
	is_pre_sale, err := common.CheckInt(r.FormValue("is_pre_sale"))
	if err != nil {
		is_pre_sale = 0 // 默认非售前
	}

	// 根据部门类型设置 is_transfer 值
	// 如果是售前部门，is_transfer = 0；如果是售后部门，is_transfer = 1
	is_transfer := 0
	if is_pre_sale == 0 {
		is_transfer = 1 // 售后
	}

	// 使用事务执行多个SQL语句
	sqls := []database.SQLExec{
		// 患者帐号 - 更新 asst_id 和 is_transfer
		{Query: "update patient_account set asst_id = ?, is_transfer = ? where id = ?",
			Args: []any{
				asst_id, is_transfer, pat_id,
			}},
		// 订单
		{Query: "update orders set asst_id=?, asst_dep_id=? where pat_id = ?",
			Args: []any{
				asst_id, asst_dep_id, pat_id,
			}},
		// 患者档案
		{Query: "update patient_profile set asst_id = ? where pid = ?",
			Args: []any{
				asst_id, pat_id,
			}},
		// 病历
		{Query: "update patient_records set asst_id = ?, asst_dep_id=? where pat_id = ?",
			Args: []any{
				asst_id, asst_dep_id, pat_id,
			}},
		// 处方
		{Query: "update prescription set asst_id = ? where pat_id = ?",
			Args: []any{
				asst_id, pat_id,
			}},
	}

	// 执行事务
	RowsAffected, err := database.ExecuteTransaction_with_affectedRows(sqls)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "转移失败",
			"err":  err.Error(),
		})
		return
	}

	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":         200,
		"msg":          "转移成功",
		"RowsAffected": RowsAffected,
	})

	if RowsAffected > 0 {
		// 记录日志，包含部门类型和转移状态
		deptTypeStr := "售前"
		if is_pre_sale == 0 {
			deptTypeStr = "售后"
		}
		common.Add_log(fmt.Sprintf("将用户转移至%s部门；部门ID：%d；用户帐号ID：%d；销售人员ID：%d；is_transfer值：%d",
			deptTypeStr, asst_dep_id, pat_id, asst_id, is_transfer), r)
	}
}

// 分配售后客服
func Patient_account_asst_transfer(w http.ResponseWriter, r *http.Request) {
	api_id := 184
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	// 患者帐号ID
	pat_id, err := common.CheckInt(r.FormValue("pat_id"))
	if err != nil || pat_id < 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "无效的用户ID",
		})
		return
	}
	// 订单ID
	order_id, err := common.CheckInt(r.FormValue("order_id"))
	if err != nil || order_id < 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "无效的订单ID",
		})
		return
	}
	// 售后部门ID
	asst_dep_id, err := common.CheckInt(r.FormValue("asst_dep_id"))
	if err != nil || asst_dep_id < 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "请选择正确的售后部门",
		})
		return
	}
	// 售后人员ID
	asst_id, err := common.CheckInt(r.FormValue("asst_id"))
	if err != nil || asst_id < 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "无效的售后人员ID",
		})
		return
	}

	sql := "select id from orders where id = ? and status = 7"
	var order_id_int int
	err = database.GetOne(sql, &order_id_int, order_id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "订单状态错误，无法转移",
			"err":  err.Error(),
		})
		return
	}
	// 查看当前用户转移状态
	sql = "select is_transfer from patient_account where id = ?"
	var is_transfer int
	err = database.GetOne(sql, &is_transfer, pat_id)
	// fmt.Println(common.DebugSql(sql, pat_id))
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "查询失败",
			"err":  err.Error(),
		})
		return
	}
	if is_transfer == 1 {
		common.JSONResponse(w, http.StatusOK, map[string]interface{}{
			"code": 200,
			"msg":  "该用户已转至售后，请勿重复转移；新客户多次下单场景，已为当前订单作核销处理",
		})
		_, _ = database.Query("update orders set status = 8 where id = ?", order_id)
		return
	}
	// 使用事务执行多个SQL语句
	sqls := []database.SQLExec{
		// 患者帐号
		{Query: "update patient_account set is_transfer = 1,asst_id = ? where id = ?",
			Args: []any{
				asst_id, pat_id,
			}},
		// 当前订单变成核销
		{Query: "update orders set status = 8,asst_id=?,asst_dep_id=? where id = ?",
			Args: []any{
				asst_id, asst_dep_id, order_id,
			}},
		// 其它订单不改变状态
		{Query: "update orders set asst_id=?,asst_dep_id=? where pat_id = ? and id <> ?",
			Args: []any{
				asst_id, asst_dep_id, pat_id, order_id,
			}},
		// 患者档案
		{Query: "update patient_profile set asst_id = ? where pid = ?",
			Args: []any{
				asst_id, pat_id,
			}},
		// 病历
		{Query: "update patient_records set asst_id = ?,asst_dep_id=? where pat_id = ?",
			Args: []any{
				asst_id, asst_dep_id, pat_id,
			}},
		// 处方
		{Query: "update prescription set asst_id = ? where pat_id = ?",
			Args: []any{
				asst_id, pat_id,
			}},
	}
	fmt.Println("update orders set asst_id=?,asst_dep_id=? where pat_id = ? and id <> ?", asst_id, asst_dep_id, pat_id, order_id)
	// 执行事务
	RowsAffected, err := database.ExecuteTransaction_with_affectedRows(sqls)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "转移失败",
			"err":  err.Error(),
		})
		return
	}

	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":         200,
		"msg":          "转移成功",
		"RowsAffected": RowsAffected,
	})

	if RowsAffected > 0 {
		common.Add_log(fmt.Sprintf("将订单对应患者分配至售后；状态改为已核销；售后ID：%d；订单ID：%d；用户帐号ID：%d；分配给的售后人员ID：%d", asst_dep_id, order_id, pat_id, asst_id), r)
	}
}

// 售前转售后
func Patient_account_transfer(w http.ResponseWriter, r *http.Request) {
	api_id := 179
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	// 用户ID
	id, err := common.CheckInt(r.FormValue("id"))
	if err != nil || id < 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "无效的ID",
		})
		return
	}
	// 订单状态检查
	order_id, err := common.CheckInt(r.FormValue("order_id"))
	if err != nil || order_id < 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "无效的ID",
		})
		return
	}
	sql := "select id from orders where id = ? and status = 6"
	var order_id_int int
	err = database.GetOne(sql, &order_id_int, order_id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "订单状态错误，无法转移",
			"err":  err.Error(),
		})
		return
	}
	// 查看当前用户转移状态
	sql = "select is_transfer from patient_account where id = ?"
	var is_transfer int
	err = database.GetOne(sql, &is_transfer, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "查询失败",
			"err":  err.Error(),
		})
		return
	}
	//&& session.Values["role_ids"] != "1"
	if is_transfer == 1 {
		common.JSONResponse(w, http.StatusOK, map[string]any{
			"code": 200,
			"msg":  "该用户已转至售后，请勿重复转移；新客户多次下单场景，已为当前订单作核销处理",
		})
		_, _ = database.Query("update orders set status = 8 where id = ?", order_id)
		return
	}
	//转移用户至售后 - 部门ID
	asst_dep_id, err := common.CheckInt(r.FormValue("asst_dep_id"))
	if err != nil || asst_dep_id < 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "请选择售后人员",
		})
		return
	}

	sql = "update orders set status = 7,asst_dep_id=? where id = ?"
	result, err := database.Query(sql, asst_dep_id, order_id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "转售后部门失败",
			"err":  err.Error(),
		})
		return
	}
	RowsAffected, _ := result.RowsAffected()

	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":         200,
		"msg":          "已转至售后部门",
		"sql":          common.DebugSql(sql, asst_dep_id, order_id),
		"RowsAffected": RowsAffected,
	})

	if RowsAffected > 0 {
		common.Add_log(fmt.Sprintf("转移用户【%d】至售后部门ID【%d】，订单【%d】状态改为‘待分配’", id, asst_dep_id, order_id), r)
	}
}

// 销售管理员-部门列表
func Salemaster_bind_department_list(w http.ResponseWriter, r *http.Request) {
	api_id := 181
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	user_id := r.FormValue("user_id")
	if user_id == "" {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]any{
			"code": 500,
			"msg":  "用户ID不得为空",
		})
		return
	}
	type Salemaster_department struct {
		User_id int    `db:"user_id"` // 用户ID
		Dep_ids string `db:"dep_ids"` // 部门IDs
	}
	sql := "select user_id,dep_ids from salemaster_department where user_id = ?"
	var salemaster_department Salemaster_department
	_ = database.GetRow(sql, &salemaster_department, user_id)
	common.JSONResponse(w, http.StatusOK, map[string]any{
		"code": 200,
		"msg":  "ok",
		"data": salemaster_department,
	})
}

// 更新绑定
func Salemaster_bind_department_bind(w http.ResponseWriter, r *http.Request) {
	api_id := 182
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	user_id := r.FormValue("user_id")
	if user_id == "" {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]any{
			"code": 500,
			"msg":  "用户ID不得为空",
		})
		return
	}
	dep_ids := r.FormValue("dep_ids")
	// 先检查该用户是否已存在记录
	var count int
	checkSql := "SELECT COUNT(*) FROM salemaster_department WHERE user_id = ?"
	err := database.GetOne(checkSql, &count, user_id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]any{
			"code": 500,
			"msg":  "数据查询失败",
			"err":  err.Error(),
		})
		return
	}

	var result sql.Result
	var operation string

	if count > 0 {
		// 记录存在，执行更新操作
		sql := "UPDATE salemaster_department SET dep_ids = ? WHERE user_id = ?"
		result, err = database.Query(sql, dep_ids, user_id)
		operation = "更新"
	} else {
		if dep_ids == "" {
			common.JSONResponse(w, http.StatusBadRequest, map[string]any{
				"code": 500,
				"msg":  "您选择的是解绑事件，但当前用户并无部门权限记录",
			})
			return
		}
		// 记录不存在，执行插入操作
		sql := "INSERT INTO salemaster_department (user_id, dep_ids) VALUES (?, ?)"
		result, err = database.Query(sql, user_id, dep_ids)
		operation = "新增"
	}

	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]any{
			"code": 500,
			"msg":  "数据" + operation + "失败",
			"err":  err.Error(),
		})
		return
	}

	RowsAffected, _ := result.RowsAffected()
	common.JSONResponse(w, http.StatusOK, map[string]any{
		"code":         200,
		"msg":          "ok",
		"RowsAffected": RowsAffected,
	})

	if RowsAffected > 0 { // 记录操作日志
		common.Add_log(fmt.Sprintf("%s销售数据管理员【%s】的部门绑定，新部门ID：%s", operation, user_id, dep_ids), r)
	}
}
