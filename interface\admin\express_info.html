<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>物流信息查询</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
    <style>
        body {
            padding: 15px;
            background-color: #f2f2f2;
        }

        .express-container {
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            padding: 15px;
        }

        .express-header {
            display: flex;
            align-items: center;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }

        .express-logo {
            width: 40px;
            height: 40px;
            margin-right: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #1E9FFF;
            color: white;
            border-radius: 50%;
        }

        .express-info {
            flex: 1;
        }

        .express-name {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .express-number {
            font-size: 14px;
            color: #666;
        }

        .express-status {
            margin-top: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
            font-size: 16px;
            font-weight: bold;
            color: #1E9FFF;
        }

        .express-timeline {
            margin-top: 15px;
        }

        .timeline-item {
            position: relative;
            padding-left: 20px;
            padding-bottom: 20px;
        }

        .timeline-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: 6px;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background-color: #e2e2e2;
        }

        .timeline-item:after {
            content: "";
            position: absolute;
            left: 4px;
            top: 16px;
            width: 2px;
            height: calc(100% - 16px);
            background-color: #e2e2e2;
        }

        .timeline-item:last-child:after {
            display: none;
        }

        .timeline-item.active:before {
            background-color: #1E9FFF;
        }

        .timeline-time {
            font-size: 12px;
            color: #999;
            margin-bottom: 5px;
        }

        .timeline-location {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }

        .timeline-content {
            font-size: 14px;
            color: #333;
        }

        .timeline-item.active .timeline-time,
        .timeline-item.active .timeline-location,
        .timeline-item.active .timeline-content {
            color: #1E9FFF;
            font-weight: bold;
        }

        .no-data {
            text-align: center;
            padding: 30px 0;
            color: #999;
        }
    </style>
</head>

<body>
    <div class="express-container">
        <div class="express-header">
            <div class="express-logo">
                <i class="layui-icon layui-icon-release"></i>
            </div>
            <div class="express-info">
                <div class="express-name" id="express-name">加载中...</div>
                <div class="express-number" id="express-number">运单号：加载中...</div>
            </div>
        </div>
        <div class="express-status" id="express-status">加载中...</div>
        <div class="express-timeline" id="express-timeline">
            <div class="no-data">加载中...</div>
        </div>
    </div>

    <script>
        layui.use(['layer'], function () {
            var layer = layui.layer;
            var $ = layui.jquery;

            // 获取URL参数
            function getUrlParam(name) {
                var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
                var r = window.location.search.substr(1).match(reg);
                if (r != null) return decodeURIComponent(r[2]); return null;
            }

            // 状态码映射
            var stateMap = {
                "0": "无轨迹",
                "1": "揽收",
                "2": "在途中",
                "3": "签收",
                "4": "问题件",
                "5": "退件",
                "6": "派送中",
                "7": "转单",
                "10": "待清关",
                "11": "清关中",
                "12": "已清关",
                "13": "清关异常",
                "14": "收件人拒签",
                "301": "正在收件",
                "302": "派送中",
                "303": "已签收",
                "304": "派送失败",
                "306": "待派送",
                "307": "快递拦截",
                "F00": "已签收"
            };

            // 快递公司映射
            var companyMap = {
                "shunfeng": "顺丰速运",
                "youzhengguonei": "邮政快递包裹",
                "yuantong": "圆通速递",
                "shentong": "申通快递",
                "zhongtong": "中通快递",
                "yunda": "韵达快递",
                "jd": "京东物流",
                "ems": "EMS",
                "tiantian": "天天快递",
                "huitongkuaidi": "百世快递"
            };

            var express_type = getUrlParam('express_type');
            var tracking_num = getUrlParam('tracking_num');
            var ord_id = getUrlParam('ord_id');
            var pat_pro_id = getUrlParam('pat_pro_id');
            var phone = getUrlParam('phone');

            if (!express_type || !tracking_num) {
                layer.msg('参数错误，无法查询物流信息');
                return;
            }

            // 显示基本信息
            $('#express-number').text('运单号：' + tracking_num);

            // 加载物流信息
            layer.load(2);
            $.ajax({
                url: '/patient/express_info',
                type: 'get',
                data: {
                    express_type: express_type,
                    tracking_num: tracking_num,
                    phone: phone,
                    ord_id: ord_id,
                    pat_pro_id: pat_pro_id
                },
                success: function (res) {
                    layer.closeAll('loading');

                    if (res && res.code === 200 && res.data) {
                        var data = res.data;

                        // 更新快递公司名称
                        var companyName = companyMap[data.com] || data.com || '未知快递';
                        $('#express-name').text(companyName);

                        // 更新物流状态
                        var stateText = stateMap[data.condition] || stateMap[data.state] || '运输中';
                        $('#express-status').text(stateText);

                        // 渲染物流轨迹
                        if (data.data && data.data.length > 0) {
                            var timelineHtml = '';

                            data.data.forEach(function (item, index) {
                                var isActive = index === 0 ? 'active' : '';
                                timelineHtml += `
                                    <div class="timeline-item ${isActive}">
                                        <div class="timeline-time">${item.time || ''}</div>
                                        <div class="timeline-location">${item.location || ''}</div>
                                        <div class="timeline-content">${item.context || ''}</div>
                                    </div>
                                `;
                            });

                            $('#express-timeline').html(timelineHtml);
                        } else {
                            $('#express-timeline').html('<div class="no-data">暂无物流轨迹信息</div>');
                        }
                    } else {
                        $('#express-status').text('查询失败');
                        $('#express-timeline').html('<div class="no-data">' + (res.msg || '暂无物流信息') + '</div>');
                    }
                },
                error: function (xhr) {
                    layer.closeAll('loading');
                    $('#express-status').text('查询失败');
                    $('#express-timeline').html('<div class="no-data">网络错误，请稍后重试</div>');
                    console.error('物流信息查询失败:', xhr);
                }
            });


        });
    </script>
</body>

</html>
