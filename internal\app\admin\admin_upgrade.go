package admin

import (
	"encoding/json"
	"fmt"
	"mstproject/pkg/common"
	"mstproject/pkg/config"
	"mstproject/pkg/database"
	"net/http"
	"strconv"
	"strings"
	"time"
)

// 订单创建 42
func Order_add(w http.ResponseWriter, r *http.Request) {
	api_id := 42
	session, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	record_ids := r.FormValue("record_ids")
	if record_ids == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "病历ID不能为空",
		})
		return
	}
	var err error
	// 判断诸多病历中是否隶属于一个患者
	sql := "SELECT count(id) FROM patient_records WHERE id IN (" + record_ids + ") GROUP BY pat_pro_id"
	var count []int
	err = database.GetAll(sql, &count)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "查询数据失败",
			"err":  err.Error(),
		})
		return
	}
	if len(count) > 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "请选择同一用户帐号下，同一患者的病历",
		})
		return
	}
	// 找出一个合适的病历，从中提取医生、医助数据放在订单中
	var record_id string
	record_ids_array := strings.Split(record_ids, ",")
	if len(record_ids_array) > 1 {
		record_ids_default := r.FormValue("record_ids_default")
		if record_ids_default == "" {
			sql := "SELECT count(id)c FROM patient_records WHERE id IN (" + record_ids + ") GROUP BY doc_id,asst_id"
			var count []int
			if err := database.GetAll(sql, &count); err != nil {
				common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
					"code": 500,
					"msg":  "查询数据失败",
					"err":  err.Error(),
				})
				return
			}
			if len(count) > 1 {
				common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
					"code": 500,
					"msg":  "这些病历并非隶属于同一位医生或医助，请选择一个病历当作订单的主病历作为列表展示用。",
				})
				return
			}
			record_id = record_ids_array[0]
		} else {
			if !common.InArray(record_ids_array, record_ids_default) {
				common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
					"code": 500,
					"msg":  "您传的主病历ID不在病历列表中，请重新选择",
				})
				return
			}
			record_id = record_ids_default
		}
	} else {
		record_id = record_ids
	}
	// 提取医生、医助数据
	var record struct {
		Doc_id        int `db:"doc_id"`
		Asst_id       int `db:"asst_id"`
		Pat_id        int `db:"pat_id"`
		Pat_pro_id    int `db:"pat_pro_id"`
		Department_id int `db:"department_id"`
		Status        int `db:"status"`
	}
	sql = "SELECT doc_id,asst_id,pat_id,pat_pro_id,department_id,status FROM patient_records WHERE id = ?"

	if err := database.GetRow(sql, &record, record_id); err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "查询病历数据失败",
			"err":  err.Error(),
		})
		return
	}
	if record.Status != 2 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "该病历当前状态为：" + config.Record_Status[record.Status] + "，非待下单状态。",
		})
		return
	}
	doc_id := record.Doc_id
	doc_dep_id := record.Department_id
	pat_id := record.Pat_id
	pat_pro_id := record.Pat_pro_id
	asst_id := record.Asst_id
	// 根据医助查询医助所在的售前部门ID
	sql = "select department_id from rbac_user where id = ?"
	var asst_dep_id int
	if err := database.GetOne(sql, &asst_dep_id, asst_id); err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "病历ID：" + record_id + " -> 对应医助ID:" + strconv.Itoa(asst_id) + " -> 售前部门ID查询失败<br>即：添加该病历的用户，未划分售前部门",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, asst_id),
		})
		return
	}

	// 钱类别
	total_money, err := common.CheckFloat(r.FormValue("total_money"))
	if total_money < 0 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "总金额不能为空",
		})
		return
	}
	pre_pay, err := common.CheckFloat(r.FormValue("pre_pay"))
	if pre_pay < 0 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "预付金额不能为空",
		})
		return
	}
	// 发货时间
	delivery_time := r.FormValue("delivery_time")
	if delivery_time == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "发货时间不能为空",
		})
		return
	}
	delivery_time_time, err := time.Parse("2006-01-02", delivery_time)
	if err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "发货时间格式错误",
		})
		return
	}
	if delivery_time_time.Before(time.Now().AddDate(0, 0, 3)) {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "发货时间至少是当前时间的3天后",
		})
		return
	}
	// 收货地址
	address := r.FormValue("address")
	if address == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "收货地址不得为空",
		})
		return
	}
	// 截图相关
	cr_pic := r.FormValue("cr_pic")
	// if cr_pic == "" {
	// 	common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
	// 		"code": 500,
	// 		"msg":  "上传的代收款凭证不能为空",
	// 	})
	// 	return
	// }
	pay_pic := r.FormValue("pay_pic")
	// if pay_pic == "" {
	// 	common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
	// 		"code": 500,
	// 		"msg":  "上传的支付截图不能为空",
	// 	})
	// 	return
	// }

	// 检查赠品数据并收集所有需要的信息
	type GiftData struct {
		WhGiftID     int     // 赠品ID
		Nums         int     // 赠品数量(对应orders_gifts表的nums字段)
		CurrentStock float64 // 当前库存量(对应warehouse_gifts表的quantity字段)
		UserNotes    string  // 用户备注
	}

	giftCount := 0
	for {
		wh_gift_id := r.FormValue(fmt.Sprintf("gifts[%d][wh_gift_id]", giftCount))
		if wh_gift_id == "" {
			break
		}
		giftCount++
	}

	// if giftCount == 0 {
	// 	common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
	// 		"code": 500,
	// 		"msg":  "请至少选择一个赠品",
	// 	})
	// 	return
	// }

	// 检查赠品数据并收集所有需要的信息
	giftDataList := make([]GiftData, 0)
	for i := 0; i < giftCount; i++ {
		wh_gift_id, err := common.CheckInt(r.FormValue(fmt.Sprintf("gifts[%d][wh_gift_id]", i)))
		if err != nil {
			common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
				"code": 500,
				"msg":  "赠品ID格式错误",
				"err":  err.Error(),
			})
			return
		}

		// 将变量名从quantity改为nums，以匹配数据库字段名
		nums, err := common.CheckInt(r.FormValue(fmt.Sprintf("gifts[%d][nums]", i)))
		if err != nil || nums < 1 {
			common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
				"code": 500,
				"msg":  "赠品数量格式错误",
				"err":  err.Error(),
			})
			return
		}

		// 获取备注，如果为空则设为"无"
		userNotes := r.FormValue(fmt.Sprintf("gifts[%d][user_notes]", i))
		if userNotes == "" {
			userNotes = "无"
		}

		// 检查库存
		var currentStock float64
		sql = "SELECT quantity FROM warehouse_gifts WHERE id = ? FOR UPDATE"
		err = database.GetOne(sql, &currentStock, wh_gift_id)
		if err != nil {
			common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
				"code": 500,
				"msg":  "查询赠品库存失败",
				"err":  err.Error(),
			})
			return
		}

		if float64(nums) > currentStock {
			common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
				"code": 500,
				"msg":  fmt.Sprintf("赠品ID %d 库存不足，当前库存: %.2f, 需要数量: %d", wh_gift_id, currentStock, nums),
			})
			return
		}

		giftDataList = append(giftDataList, GiftData{
			WhGiftID:     wh_gift_id,
			Nums:         nums,
			CurrentStock: currentStock,
			UserNotes:    userNotes,
		})
	}

	// 构建事务
	sqls := make([]database.SQLExec, 0)

	// 1. 添加订单主表记录
	sqls = append(sqls, database.SQLExec{
		Query: "INSERT INTO orders(doc_id,asst_id,cr_pic,pay_pic,total_money,pre_pay,pat_id,pat_pro_id,doc_dep_id,asst_dep_id,record_ids,delivery_time,address) VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?)",
		Args: []any{
			doc_id, asst_id, cr_pic, pay_pic, total_money, pre_pay,
			pat_id, pat_pro_id, doc_dep_id, asst_dep_id, record_ids, delivery_time,
			address,
		},
	})

	// 立即保存订单ID到变量中，只需要设置一次
	sqls = append(sqls, database.SQLExec{
		Query: "SET @order_id = LAST_INSERT_ID()",
		Args:  []interface{}{},
	})

	// 2. 为每个赠品添加相关SQL
	for _, giftData := range giftDataList {
		newStock := giftData.CurrentStock - float64(giftData.Nums)

		// 添加订单赠品记录 - 直接使用@order_id，不需要重复设置
		sqls = append(sqls, database.SQLExec{
			Query: "INSERT INTO orders_gifts (ord_id, wh_gift_id, nums) VALUES (@order_id, ?, ?)",
			Args: []interface{}{
				giftData.WhGiftID, giftData.Nums,
			},
		})

		// 更新库存
		sqls = append(sqls, database.SQLExec{
			Query: "UPDATE warehouse_gifts SET quantity = quantity - ? WHERE id = ?",
			Args: []interface{}{
				giftData.Nums, giftData.WhGiftID,
			},
		})

		// 修改库存日志SQL
		sqls = append(sqls, database.SQLExec{
			Query: "INSERT INTO warehouse_gifts_log (pid,user_id,kind,change_data,old_data,new_data,notes,user_notes) VALUES (?,?,?,?,?,?,?,?)",
			Args: []interface{}{
				giftData.WhGiftID, session.Values["id"], 0, giftData.Nums,
				giftData.CurrentStock, newStock, "新建订单出库赠品", giftData.UserNotes,
			},
		})
	}

	// 3. 更新病历表的订单ID
	sqls = append(sqls, database.SQLExec{
		Query: "UPDATE patient_records SET ord_id = @order_id, status=4 WHERE id IN (" + record_ids + ")",
		Args:  []interface{}{},
	})

	// 执行事务
	RowsAffected, err := database.ExecuteTransaction_with_affectedRows(sqls)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "创建订单失败",
			"err":  err.Error(),
		})
		return
	}

	// 获取新插入的订单ID
	var newID int64
	sql = "SELECT LAST_INSERT_ID()"
	err = database.GetOne(sql, &newID)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "获取新订单ID失败",
			"err":  err.Error(),
		})
		return
	}

	// 返回成功响应
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":   200,
		"msg":    "创建订单成功",
		"new_id": newID,
	})
	common.Add_log(fmt.Sprintf("创建订单成功，订单ID：%d，影响行数：%d", newID, RowsAffected), r)
}

// DrugData 结构体用于存储药品数据
type DrugData struct {
	Drug_id     int
	Wh_drug_id  int
	Single_dose float64
}

func Prescription_add_with_finished_drug(w http.ResponseWriter, r *http.Request) {
	api_id := 38
	session, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	// 验证病历ID
	record_id := r.FormValue("record_id")
	if record_id == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "病历ID不能为空",
		})
		return
	}
	// 查看该病历对应的订单是否已审核订金
	sql := "select id from orders where id = (select ord_id from patient_records where id = ?) and status = 0 and pay_review_status > 0"
	var order_id int
	err := database.GetOne(sql, &order_id, record_id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]any{
			"code": 500,
			"msg":  "对不起，当前病历对应的订单订金尚未审核。",
			"data": order_id,
			"sql":  common.DebugSql(sql, record_id),
		})
		return
	}
	// 获取病历信息
	var record struct {
		Doc_id     int `db:"doc_id"`
		Asst_id    int `db:"asst_id"`
		Pat_id     int `db:"pat_id"`
		Pat_pro_id int `db:"pat_pro_id"`
		Pre_id     int `db:"pre_id"`
		Ord_id     int `db:"ord_id"`
	}
	if err := database.GetRow("SELECT doc_id,asst_id,pat_id,pat_pro_id,pre_id,ord_id FROM patient_records WHERE id = ?", &record, record_id); err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "查询病历数据失败",
			"err":  err.Error(),
		})
		return
	}

	if record.Pre_id > 0 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "该病历已开具处方（处方ID：" + strconv.Itoa(record.Pre_id) + "），不能重复开具",
		})
		return
	}

	// 构建处方数据
	prescriptionData := struct {
		diagnosis  string
		tx_plan    string
		askfor     string
		tx_day     string
		tx_type    string
		dosage     string
		totalDoses string
	}{
		diagnosis:  r.FormValue("diagnosis"),
		tx_plan:    r.FormValue("tx_plan"),
		askfor:     r.FormValue("askfor"),
		tx_day:     r.FormValue("tx_day"),
		tx_type:    r.FormValue("tx_type"),
		dosage:     r.FormValue("dosage"),
		totalDoses: r.FormValue("totalDoses"),
	}
	finishedDrugId, err := common.CheckInt(r.FormValue("finishedDrugId"))
	if err != nil || finishedDrugId < 1 {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]any{
			"code": 500,
			"msg":  "对不起，选择成品药ID错误",
		})
		return
	}
	//查找该成品药ID是否可以绑定
	sql = "select id from warehouse_finisheddrug where id = ? and status = 1 and kind in(1,2)"
	var has_id int
	err = database.GetOne(sql, &has_id, finishedDrugId)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]any{
			"code": 500,
			"msg":  "对不起，无效成品药ID",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, finishedDrugId),
		})
		return
	}
	// 各种校验完毕，开始执行SQL

	queries := []database.TxQuery{
		// 1. 添加处方主表记录
		{
			Query: `INSERT INTO prescription(record_id,doc_id,asst_id,pat_id,pat_pro_id,diagnosis,tx_plan,askfor,tx_day,totalDoses,tx_type,dosage,ord_id,finished_drug_id,verify_1,verify_1_user_id,verify_1_desc,verify_2,verify_2_user_id,verify_2_desc,verify_3,verify_3_user_id,verify_3_desc,status) 
					VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)`,
			Args: []any{
				record_id, record.Doc_id, record.Asst_id, record.Pat_id, record.Pat_pro_id,
				prescriptionData.diagnosis, prescriptionData.tx_plan, prescriptionData.askfor,
				prescriptionData.tx_day, prescriptionData.totalDoses, prescriptionData.tx_type, prescriptionData.dosage,
				record.Ord_id, finishedDrugId,
				1, session.Values["id"], "由于选择的成品，调配审核直接过 - " + time.Now().Format("2006-01-02 15:04:05"), //调配审核直接过，因为选择的是成品
				1, session.Values["id"], "由于选择的成品，调配审核直接过 - " + time.Now().Format("2006-01-02 15:04:05"), //调配审核直接过，因为选择的是成品
				1, session.Values["id"], "由于选择的成品，调配审核直接过 - " + time.Now().Format("2006-01-02 15:04:05"), //调配审核直接过，因为选择的是成品
				5, //由于是直接选择的成品，所以新处方状态直接默认为已入成品库
			},
		},
		// 2. 设置变量存储处方ID
		{
			Query: "SET @prescription_id = LAST_INSERT_ID()",
			Args:  []any{},
		},
	}

	// 3. 更新病历表的处方ID
	queries = append(queries, database.TxQuery{
		Query: "UPDATE patient_records SET pre_id = @prescription_id, status = 6 WHERE id = ?", //选择成品药，不用制药流程，所以病历流程结束
		Args:  []any{record_id},
	})

	// 4. 给warehouse_finisheddrug加上新处方ID标记
	queries = append(queries, database.TxQuery{
		Query: "UPDATE warehouse_finisheddrug SET new_pre_id = @prescription_id,new_ord_id = ?,update_time=NOW() WHERE id = ?",
		Args:  []any{order_id, finishedDrugId},
	})

	// 执行事务
	newID, affectedRows, err := database.ExecuteTxWithResult(queries)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "创建处方失败",
			"err":  err.Error(),
		})
		return
	}

	// 返回成功响应
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":   200,
		"msg":    "创建处方成功",
		"new_id": newID,
	})
	common.Add_log(fmt.Sprintf("创建处方成功，绑定成品药，处方ID：%d，影响行数：%d，涉及到的表有：处方表prescription,成品药库warehouse_finisheddrug,病历表patient_records", newID, affectedRows), r)
}

// Prescription_add 处方添加
func Prescription_add(w http.ResponseWriter, r *http.Request) {
	api_id := 38
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}

	// 验证病历ID
	record_id := r.FormValue("record_id")
	if record_id == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "病历ID不能为空",
		})
		return
	}
	// 查看该病历对应的订单是否已审核订金
	sql := "select id from orders where id = (select ord_id from patient_records where id = ?) and status = 0 and pay_review_status > 0"
	var order_id int
	err := database.GetOne(sql, &order_id, record_id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]any{
			"code": 500,
			"msg":  "对不起，当前病历对应的订单订金尚未审核。",
			"data": order_id,
			"sql":  common.DebugSql(sql, record_id),
		})
		return
	}
	// 获取病历信息
	var record struct {
		Doc_id     int `db:"doc_id"`
		Asst_id    int `db:"asst_id"`
		Pat_id     int `db:"pat_id"`
		Pat_pro_id int `db:"pat_pro_id"`
		Pre_id     int `db:"pre_id"`
		Ord_id     int `db:"ord_id"`
	}
	if err := database.GetRow("SELECT doc_id,asst_id,pat_id,pat_pro_id,pre_id,ord_id FROM patient_records WHERE id = ?", &record, record_id); err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "查询病历数据失败",
			"err":  err.Error(),
		})
		return
	}

	if record.Pre_id > 0 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "该病历已开具处方（处方ID：" + strconv.Itoa(record.Pre_id) + "），不能重复开具",
		})
		return
	}

	// 构建处方数据
	prescriptionData := struct {
		diagnosis  string
		tx_plan    string
		askfor     string
		tx_day     string
		tx_type    string
		dosage     string
		totalDoses string
	}{
		diagnosis:  r.FormValue("diagnosis"),
		tx_plan:    r.FormValue("tx_plan"),
		askfor:     r.FormValue("askfor"),
		tx_day:     r.FormValue("tx_day"),
		tx_type:    r.FormValue("tx_type"),
		dosage:     r.FormValue("dosage"),
		totalDoses: r.FormValue("totalDoses"),
	}

	// 验证药品数据
	drugDataList, err := validateAndCollectDrugData(r)
	if err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}

	// 检查库存是否充足
	for _, drug := range drugDataList {
		var currentStock float64
		if err := database.GetOne("SELECT quantity FROM warehouse_drug WHERE id = ?", &currentStock, drug.Wh_drug_id); err != nil {
			common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
				"code": 500,
				"msg":  "查询药品库存失败",
				"err":  err.Error(),
			})
			return
		}

		if currentStock < 1 {
			common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
				"code": 500,
				"msg":  fmt.Sprintf("药品ID %d 库存不足，当前库存: %.2f", drug.Wh_drug_id, currentStock),
			})
			return
		}
	}

	// 构建事务查询
	queries := []database.TxQuery{
		// 1. 添加处方主表记录
		{
			Query: `INSERT INTO prescription(record_id,doc_id,asst_id,pat_id,pat_pro_id,diagnosis,tx_plan,askfor,tx_day,totalDoses,tx_type,dosage,ord_id) 
					VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?)`,
			Args: []any{
				record_id, record.Doc_id, record.Asst_id, record.Pat_id, record.Pat_pro_id,
				prescriptionData.diagnosis, prescriptionData.tx_plan, prescriptionData.askfor,
				prescriptionData.tx_day, prescriptionData.totalDoses, prescriptionData.tx_type, prescriptionData.dosage,
				record.Ord_id,
			},
		},
		// 2. 设置变量存储处方ID
		{
			Query: "SET @prescription_id = LAST_INSERT_ID()",
			Args:  []any{},
		},
	}

	// 3. 为每个药品添加处方药品记录
	for _, drug := range drugDataList {
		queries = append(queries, database.TxQuery{
			Query: "INSERT INTO prescription_drug(pre_id,drug_id,wh_drug_id,quantity) VALUES(@prescription_id,?,?,?)",
			Args:  []any{drug.Drug_id, drug.Wh_drug_id, drug.Single_dose},
		})
	}

	// 4. 更新病历表的处方ID
	queries = append(queries, database.TxQuery{
		Query: "UPDATE patient_records SET pre_id = @prescription_id, status = 5 WHERE id = ?",
		Args:  []any{record_id},
	})

	// 执行事务
	newID, affectedRows, err := database.ExecuteTxWithResult(queries)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "创建处方失败",
			"err":  err.Error(),
		})
		return
	}

	// 返回成功响应
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":   200,
		"msg":    "创建处方成功",
		"new_id": newID,
	})
	common.Add_log(fmt.Sprintf("创建处方成功，绑定中药材，处方ID：%d，影响行数：%d，涉及到的表有：处方表prescription,处方药材表prescription_drug,病历表patient_records", newID, affectedRows), r)
}

// 验证并收集药品数据
func validateAndCollectDrugData(r *http.Request) ([]DrugData, error) {
	drugCount := 0
	for {
		if r.FormValue(fmt.Sprintf("drugs[%d][wh_drug_id]", drugCount)) == "" {
			break
		}
		drugCount++
	}

	if drugCount == 0 {
		return nil, fmt.Errorf("请至少选择一个药品")
	}

	drugDataList := make([]DrugData, 0, drugCount)
	for i := 0; i < drugCount; i++ {
		drug, err := parseDrugData(r, i)
		if err != nil {
			return nil, err
		}
		drugDataList = append(drugDataList, drug)
	}

	return drugDataList, nil
}

// 解析单个药品数据
func parseDrugData(r *http.Request, index int) (DrugData, error) {
	wh_drug_id, err := common.CheckInt(r.FormValue(fmt.Sprintf("drugs[%d][wh_drug_id]", index)))
	if err != nil {
		return DrugData{}, fmt.Errorf("药品库存ID格式错误: %v", err)
	}

	drug_id, err := common.CheckInt(r.FormValue(fmt.Sprintf("drugs[%d][drug_id]", index)))
	if err != nil {
		return DrugData{}, fmt.Errorf("药品ID格式错误: %v", err)
	}

	single_dose, err := common.CheckFloat(r.FormValue(fmt.Sprintf("drugs[%d][single_dose]", index)))
	if err != nil || single_dose < 0.1 {
		return DrugData{}, fmt.Errorf("数量格式错误")
	}

	return DrugData{
		Wh_drug_id:  wh_drug_id,
		Drug_id:     drug_id,
		Single_dose: single_dose,
	}, nil
}

// 处方编辑
func Prescription_edit(w http.ResponseWriter, r *http.Request) {
	api_id := 39
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}

	// 验证处方ID
	id, err := common.CheckInt(r.FormValue("id"))
	if id < 1 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "处方ID不能为空",
		})
		return
	}

	// 构建处方数据
	prescriptionData := struct {
		diagnosis  string
		tx_plan    string
		askfor     string
		tx_day     string
		tx_type    string
		dosage     string
		totalDoses string
	}{
		diagnosis:  r.FormValue("diagnosis"),
		tx_plan:    r.FormValue("tx_plan"),
		askfor:     r.FormValue("askfor"),
		tx_day:     r.FormValue("tx_day"),
		tx_type:    r.FormValue("tx_type"),
		dosage:     r.FormValue("dosage"),
		totalDoses: r.FormValue("totalDoses"),
	}

	// 更新处方主表
	sql := `UPDATE prescription SET diagnosis=?, tx_plan=?, askfor=?, tx_day=?, tx_type=?, dosage=?, totalDoses=? WHERE id=?`
	result, err := database.Query(sql, prescriptionData.diagnosis, prescriptionData.tx_plan, prescriptionData.askfor,
		prescriptionData.tx_day, prescriptionData.tx_type, prescriptionData.dosage, prescriptionData.totalDoses, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "修改处方失败",
			"err":  err.Error(),
		})
		return
	}

	affectedRows, _ := result.RowsAffected()

	// 返回成功响应
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "修改处方成功",
	})
	common.Add_log(fmt.Sprintf("修改处方成功，处方ID：%d，影响行数：%d", id, affectedRows), r)
}

// 处方药品修改
func Prescription_edit_drug_save(w http.ResponseWriter, r *http.Request) {
	api_id := 39
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}

	// 1. 获取参数
	pre_id, err := common.CheckInt(r.FormValue("pre_id"))
	if err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "处方ID不能为空",
		})
		return
	}

	drugs := r.FormValue("drugs")
	if drugs == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "药品数据不能为空",
		})
		return
	}

	// 2. 解析药品数据
	type DrugData struct {
		Wh_drug_id  int     `json:"wh_drug_id"`
		Drug_id     int     `json:"drug_id"`
		Single_dose float64 `json:"single_dose"`
	}
	var drugList []DrugData
	if err := json.Unmarshal([]byte(drugs), &drugList); err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  fmt.Sprintf("药品数据格式错误: %v", err),
		})
		return
	}

	// 打印接收到的数据，用于调试
	// fmt.Printf("Received drug data: %+v\n", drugList)

	// 3. 开启事务
	tx, err := database.DB.Begin()
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "开启事务失败",
		})
		return
	}
	defer tx.Rollback()

	// 4. 删除原有药品数据
	_, err = tx.Exec("DELETE FROM prescription_drug WHERE pre_id = ?", pre_id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "删除原有药品数据失败",
		})
		return
	}

	// 5. 插入新的药品数据
	stmt, err := tx.Prepare("INSERT INTO prescription_drug (pre_id, drug_id, wh_drug_id, quantity) VALUES (?, ?, ?, ?)")
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  fmt.Sprintf("准备插入语句失败: %v", err),
		})
		return
	}
	defer stmt.Close()

	for _, drug := range drugList {
		// 检查库存是否充足
		var currentStock float64
		if err := database.GetOne("SELECT quantity FROM warehouse_drug WHERE id = ?", &currentStock, drug.Wh_drug_id); err != nil {
			common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
				"code": 500,
				"msg":  "查询药品库存失败",
				"err":  err.Error(),
			})
			return
		}

		if currentStock < drug.Single_dose {
			common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
				"code": 500,
				"msg":  fmt.Sprintf("药品ID %d 库存不足，当前库存: %.2f, 需要数量: %.2f", drug.Wh_drug_id, currentStock, drug.Single_dose),
			})
			return
		}

		// 打印每条要插入的数据，用于调试
		// fmt.Printf("Inserting drug: pre_id=%d, drug_id=%d, wh_drug_id=%d, quantity=%f\n",
		// 	pre_id, drug.Drug_id, drug.Wh_drug_id, drug.Single_dose)

		_, err = stmt.Exec(pre_id, drug.Drug_id, drug.Wh_drug_id, drug.Single_dose)
		if err != nil {
			common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
				"code": 500,
				"msg":  fmt.Sprintf("插入药品数据失败: %v", err),
			})
			return
		}
	}

	// 6. 提交事务
	if err = tx.Commit(); err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "提交事务失败",
		})
		return
	}

	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "保存成功",
	})

	common.Add_log(fmt.Sprintf("修改处方药品成功,处方ID：%d", pre_id), r)
}

// 订单修改 43
func Order_edit(w http.ResponseWriter, r *http.Request) {
	api_id := 43
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}

	id, err := common.CheckInt(r.FormValue("id"))
	if id < 1 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "订单ID不能为空",
		})
		return
	}

	// 1. 检查发货时间是否符合要求（必须在订单创建时间3天后），顺便求出当前订单的状态status
	delivery_time := r.FormValue("delivery_time")
	if delivery_time == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "发货时间不能为空",
		})
		return
	}
	type Order struct {
		Status      int     `json:"status"`
		Final_pay   float64 `json:"final_pay"`
		Create_time string  `json:"create_time"`
	}
	sql := "SELECT status,final_pay,create_time FROM orders WHERE id = ?"
	var order Order
	err = database.GetRow(sql, &order, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "查询订单信息失败",
			"err":  err.Error(),
		})
		return
	}
	Create_time, err := time.Parse(time.RFC3339, order.Create_time)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "解析订单创建时间失败",
			"err":  err.Error(),
		})
		return
	}
	createDateOnly := time.Date(Create_time.Year(), Create_time.Month(), Create_time.Day(), 0, 0, 0, 0, Create_time.Location())

	deliveryTime, err := time.Parse("2006-01-02", delivery_time)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "解析发货时间失败",
			"err":  err.Error(),
		})
		return
	}
	dayDiff := int(deliveryTime.Sub(createDateOnly).Hours() / 24)
	if dayDiff < 3 {
		// 构造包含发货时间和订单创建时间的错误消息
		errMsg := fmt.Sprintf("发货时间必须在订单创建时间3天后。当前发货时间: %s，订单创建时间: %s",
			deliveryTime.Format("2006-01-02"),
			createDateOnly.Format("2006-01-02"))
		common.JSONResponse(w, http.StatusBadRequest, map[string]any{
			"code": 400, // 更改为400，因为这是客户端错误
			"msg":  errMsg,
		})
		return
	}
	if deliveryTime.Before(time.Now()) {
		common.JSONResponse(w, http.StatusBadRequest, map[string]any{
			"code": 500,
			"msg":  "发货时间必须大于现在，否则您在做无意义修改。",
		})
		return
	}

	// 2. 获取表单数据
	total_money, err := common.CheckInt(r.FormValue("total_money"))
	if err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "订单总金额格式错误",
		})
		return
	}
	pre_pay, err := common.CheckInt(r.FormValue("pre_pay"))
	if err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "预付金额格式错误",
		})
		return
	}

	final_pay := r.FormValue("final_pay")
	if final_pay == "" {
		final_pay = "0"
	}

	finalPayInt, err := strconv.Atoi(final_pay)
	if err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "尾款支付金额格式错误",
		})
		return
	}

	if (total_money - pre_pay - finalPayInt) < 0 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "总额、预付、尾款，三数据不合理",
		})
		return
	}

	if finalPayInt > 0 {
		// 查看当前订单状态，必须是“已收货 3”，才可以继续，否则阻止
		if order.Status != 3 {
			common.JSONResponse(w, http.StatusBadRequest, map[string]any{
				"code": 500,
				"msg":  "当前订单状态码【" + strconv.Itoa(order.Status) + "】不允许操作已付尾款，期待订单状态为：已收货【3】",
			})
			return
		}
		if order.Final_pay > 0 {
			common.JSONResponse(w, http.StatusBadRequest, map[string]any{
				"code": 500,
				"msg":  "当前订单状态下，尾款数据异常",
			})
			return
		}
	}

	// 获取图片数据
	cr_pic := r.FormValue("cr_pic")
	if cr_pic == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "代收款凭证不能为空",
		})
		return
	}
	pay_pic := r.FormValue("pay_pic")

	// 地址信息
	address := r.FormValue("address")
	if address == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "地址不能为空",
		})
		return
	}

	// 3. 更新订单数据
	sql = `UPDATE orders SET total_money=?, pre_pay=?, final_pay=?, cr_pic=?, pay_pic=?, delivery_time=?,address=? WHERE id=?`
	result, err := database.Query(sql, total_money, pre_pay, final_pay, cr_pic, pay_pic, delivery_time, address, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "修改订单失败",
			"err":  err.Error(),
		})
		return
	}

	affectedRows, _ := result.RowsAffected()

	// 4. 返回成功响应
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "修改订单成功",
	})
	common.Add_log(fmt.Sprintf("修改订单成功，订单ID：%d，影响行数：%d", id, affectedRows), r)
}

// 保存订单赠品数据
func Order_edit_gift_save(w http.ResponseWriter, r *http.Request) {
	api_id := 43
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}

	// 1. 获取参数
	ord_id, err := common.CheckInt(r.FormValue("ord_id"))
	if err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "订单ID不能为空",
		})
		return
	}

	gifts := r.FormValue("gifts")
	if gifts == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "赠品数据不能为空",
		})
		return
	}

	// 2. 解析赠品数据
	type GiftData struct {
		Wh_gift_id int `json:"wh_gift_id"`
		Quantity   int `json:"quantity"`
	}
	var giftList []GiftData
	if err := json.Unmarshal([]byte(gifts), &giftList); err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  fmt.Sprintf("赠品数据格式错误: %v", err),
		})
		return
	}

	// 3. 开启事务
	tx, err := database.DB.Begin()
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "开启事务失败",
		})
		return
	}
	defer tx.Rollback()

	// 4. 删除原有赠品数据
	_, err = tx.Exec("DELETE FROM orders_gifts WHERE ord_id = ?", ord_id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "删除原有赠品数据失败",
		})
		return
	}

	// 5. 插入新的赠品数据
	stmt, err := tx.Prepare("INSERT INTO orders_gifts (ord_id, wh_gift_id, nums, status, time) VALUES (?, ?, ?, 0, NOW())")
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  fmt.Sprintf("准备插入语句失败: %v", err),
		})
		return
	}
	defer stmt.Close()

	for _, gift := range giftList {
		_, err = stmt.Exec(ord_id, gift.Wh_gift_id, gift.Quantity)
		if err != nil {
			common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
				"code": 500,
				"msg":  fmt.Sprintf("插入赠品数据失败: %v", err),
			})
			return
		}
	}

	// 6. 提交事务
	if err = tx.Commit(); err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "提交事务失败",
		})
		return
	}

	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "保存成功",
	})

	common.Add_log(fmt.Sprintf("修改订单赠品成功,订单ID：%d", ord_id), r)
}
