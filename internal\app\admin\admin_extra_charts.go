package admin

import (
	"fmt"
	"mstproject/pkg/common"
	"mstproject/pkg/config"
	"mstproject/pkg/database"
	"net/http"
	"time"
)

func Charts_Records(w http.ResponseWriter, r *http.Request) {
	api_id := config.NormalPerm
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	deptIds := r.FormValue("deptids")
	if deptIds == "" {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "参数错误",
		})
		return
	}

	date_type := r.FormValue("date_type")

	// 构建时间条件
	var timeCondition string
	now := time.Now()

	switch date_type {
	case "0": // 最近3天
		startDate := now.AddDate(0, 0, -3).Format("2006-01-02")
		timeCondition = fmt.Sprintf(" AND DATE(create_time) >= '%s'", startDate)
	case "1": // 最近7天
		startDate := now.AddDate(0, 0, -7).Format("2006-01-02")
		timeCondition = fmt.Sprintf(" AND DATE(create_time) >= '%s'", startDate)
	case "2": // 最近1月
		startDate := now.AddDate(0, -1, 0).Format("2006-01-02")
		timeCondition = fmt.Sprintf(" AND DATE(create_time) >= '%s'", startDate)
	case "3": // 最近3月
		startDate := now.AddDate(0, -3, 0).Format("2006-01-02")
		timeCondition = fmt.Sprintf(" AND DATE(create_time) >= '%s'", startDate)
	default: // 所有时间，不添加时间条件
		timeCondition = ""
	}

	// 构建SQL查询
	sql := fmt.Sprintf("SELECT count(id) as count, asst_dep_id as department, asst_id, DATE(create_time) as date FROM patient_records WHERE asst_dep_id in(%s)%s GROUP BY asst_dep_id, asst_id, DATE(create_time) ORDER BY date", deptIds, timeCondition)

	// 定义数据结构
	type RecordData struct {
		Count      float64   `db:"count"`
		Department int       `db:"department"`
		AsstId     int       `db:"asst_id"`
		Date       time.Time `db:"date"`
	}

	var records []RecordData
	err := database.GetAll(sql, &records)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, []interface{}{}),
		})
		return
	}

	// 返回数据
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "查询成功",
		"data": records,
		"sql":  common.DebugSql(sql, []interface{}{}),
	})
}

func Charts_Orders(w http.ResponseWriter, r *http.Request) {
	api_id := config.NormalPerm
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	deptIds := r.FormValue("deptids")
	if deptIds == "" {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "参数错误",
		})
		return
	}
	date_type := r.FormValue("date_type")
	// 构建时间条件
	var timeCondition string
	now := time.Now()

	switch date_type {
	case "0": // 最近3天
		startDate := now.AddDate(0, 0, -3).Format("2006-01-02")
		timeCondition = fmt.Sprintf(" AND DATE(create_time) >= '%s'", startDate)
	case "1": // 最近7天
		startDate := now.AddDate(0, 0, -7).Format("2006-01-02")
		timeCondition = fmt.Sprintf(" AND DATE(create_time) >= '%s'", startDate)
	case "2": // 最近1月
		startDate := now.AddDate(0, -1, 0).Format("2006-01-02")
		timeCondition = fmt.Sprintf(" AND DATE(create_time) >= '%s'", startDate)
	case "3": // 最近3月
		startDate := now.AddDate(0, -3, 0).Format("2006-01-02")
		timeCondition = fmt.Sprintf(" AND DATE(create_time) >= '%s'", startDate)
	default: // 所有时间，不添加时间条件
		timeCondition = ""
	}

	//是否按金额
	var sql string
	by_money := r.FormValue("by_money")
	if by_money == "1" {
		sql = fmt.Sprintf("SELECT sum(total_money) as count, asst_dep_id as department, asst_id, DATE(create_time) as date FROM orders WHERE asst_dep_id in(%s)%s GROUP BY asst_dep_id, asst_id, DATE(create_time) ORDER BY date", deptIds, timeCondition)
	} else {
		sql = fmt.Sprintf("SELECT count(id) as count, asst_dep_id as department, asst_id, DATE(create_time) as date FROM orders WHERE asst_dep_id in(%s)%s GROUP BY asst_dep_id, asst_id, DATE(create_time) ORDER BY date", deptIds, timeCondition)
	}

	// 定义数据结构
	type RecordData struct {
		Count      float64   `db:"count"`
		Department int       `db:"department"`
		AsstId     int       `db:"asst_id"`
		Date       time.Time `db:"date"`
	}

	var records []RecordData
	err := database.GetAll(sql, &records)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, []interface{}{}),
		})
		return
	}

	// 返回数据
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "查询成功",
		"data": records,
		"sql":  common.DebugSql(sql, []interface{}{}),
	})
}
