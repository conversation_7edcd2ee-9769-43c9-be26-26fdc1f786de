<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 处方详情</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
    <style>
        h3 {
            margin: 30px 0 20px 0;
            border-left: 3px solid #666;
            padding-left: 15px;
        }

        /* 重置打印区域的背景色 */
        .print-area {
            background-color: #fff !important;
        }

        .layui-card {
            background-color: #fff !important;
        }

        /* 修改打印预览的样式 */
        @media print {
            body {
                background-color: #fff !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            .print-area {
                background-color: #fff !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            .layui-card {
                background-color: #fff !important;
                box-shadow: none !important;
            }
        }

        .image-grid {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .image-grid img {
            width: 200px;
            height: 200px;
            object-fit: cover;
            cursor: pointer;
            border-radius: 4px;
            border: 1px solid #ddd;
        }

        .info-item {
            margin-bottom: 10px;
        }

        .info-label {
            font-weight: bold;
            display: inline-block;
            min-width: 100px;
        }

        #yimatong {
            width: 600px;
            border: 1px solid #ddd;
            padding: 50px 20px;
            margin: 0 auto;
        }

        #chufangdan {
            width: 600px;
            border: 1px solid #ddd;
            padding: 50px 20px 10px 20px;
            margin: 0 auto;
            position: relative;
        }

        .yibao_checkbox {
            display: flex;
        }

        .yibao_checkbox input {
            margin-right: 4px;
        }
    </style>
</head>

<body>
    <div class="layui-layout layui-layout-admin">
        <!-- 头部 -->
        <div class="layui-header">
            <div class="layui-logo">幸年堂</div>
            <ul class="layui-nav layui-layout-right">
                <li class="layui-nav-item" style="background-color:transparent!important;padding-right:10px">
                    <a href="javascript:;" style="display:flex;align-items:center;color:#fff"><img
                            src="/dist/images/user.png" class="layui-nav-img"><span class="user_name"></span></a>
                    <dl class="layui-nav-child pop_menu">
                        <dd><i class="iconfont">&#xe62f;</i><a href="change_password.html#7">用户资料</a></dd>
                        <dd><i class="iconfont">&#xe601;</i><a href="javascript:void(0)" class="refresh_cache">刷新缓存</a>
                        </dd>
                        <dd><i class="iconfont">&#xe634;</i><a href="login_out.html">退出登录</a></dd>
                    </dl>
                </li>
            </ul>
        </div>
        <!-- 导航 -->
        <ul class="layui-nav layui-nav-tree layui-nav-side" id="menuContainer" lay-bar="disabled" lay-accordion>
            <div style="margin: 50px 0;color: #bbb;text-align: center;">菜单加载中...</div>
        </ul>

        <!-- 主体内容 -->
        <div class="layui-body">
            <div class="body_child">
                <div class="layui-panel">
                    <div class="layui-card-header">
                        <div class="layui-row" style="padding-top:10px;">
                            <div class="layui-col-md11 layui-col-sm10">处方详情</div>
                        </div>
                    </div>

                    <div class="layui-padding-3" style="min-height: 600px;">

                        <h3>患者基础信息</h3>
                        <!-- 填充患者基础信息，接口/admin/patient_profile/detail -->
                        <div class="layui-form">
                            <div id="patient_data" class="layui-row">
                                <i
                                    class='layui-icon layui-icon-loading-1 layui-anim layui-anim-rotate layui-anim-loop'></i>
                            </div>
                        </div>

                        <h3>病历信息</h3>
                        <!-- 填充病历信息，接口/admin/patient_records/detail -->
                        <div class="layui-form">
                            <div id="record_data" class="layui-row">
                                <i
                                    class='layui-icon layui-icon-loading-1 layui-anim layui-anim-rotate layui-anim-loop'></i>
                            </div>
                        </div>

                        <!-- 舌苔照显示部分 -->
                        <div class="layui-form-item">
                            <label class="layui-form-label">舌苔照</label>
                            <div class="layui-input-block">
                                <div class="image-grid" id="photo_tongue_container"></div>
                            </div>
                        </div>

                        <!-- 检查单显示部分 -->
                        <div class="layui-form-item">
                            <label class="layui-form-label">检查单</label>
                            <div class="layui-input-block">
                                <div class="image-grid" id="photo_sheet_container"></div>
                            </div>
                        </div>

                        <h3>诊断信息及医嘱</h3>
                        <div class="layui-row">
                            <div class="layui-col-md6">
                                <div class="info-item">
                                    <div class="info-label">诊断信息：</div>
                                    <div class="info-content" id="diagnosis"></div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">治疗计划：</div>
                                    <div class="info-content" id="tx_plan"></div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">医嘱：</div>
                                    <div class="info-content" id="askfor"></div>
                                </div>
                            </div>
                        </div>

                        <h3>用药信息</h3>
                        <div class="layui-row">
                            <div class="layui-col-md12">
                                <div class="info-item">
                                    <div class="info-label">疗程：</div>
                                    <div class="info-content">
                                        <span id="tx_day"></span> 天，
                                        药品类型：<span id="tx_type"></span>
                                    </div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">使用方法：</div>
                                    <div class="info-content" id="dosage_detail">
                                        每 <span id="dosage_1"></span> 天
                                        <span id="dosage_2"></span> 次，
                                        每次 <span id="dosage_3"></span>
                                        * <span id="dosage_4"></span>
                                    </div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">禁忌：</div>
                                    <div class="info-content" id="dosage_5"></div>
                                </div>
                            </div>
                        </div>

                        <h3 id="prescription_drug_title">处方药品信息</h3>
                        <table class="layui-table">
                            <thead>
                                <tr>
                                    <th>库存ID</th>
                                    <th>药品ID</th>
                                    <th>药品名称</th>
                                    <th>数量</th>
                                </tr>
                            </thead>
                            <tbody id="prescription_drug_table_body">
                                <!-- 药品数据将在这里动态插入 -->
                            </tbody>
                        </table>

                        <div class="layui-row" style="margin-top: 30px;">
                            <div class="layui-col-md12">
                                <div class="info-item">
                                    <div class="info-label">处方状态：</div>
                                    <div class="info-content" id="status"></div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">创建时间：</div>
                                    <div class="info-content" id="create_time"></div>
                                </div>
                            </div>
                        </div>


                        <h3>一码通</h3>

                        <div class="layui-row">
                            <div class="layui-card-body" id="yimatong">
                                <div id="ymt_child">
                                    <div style="text-align: center; margin-bottom: 20px;">
                                        <h2>一码通</h2>
                                    </div>
                                    <div class="layui-row">
                                        <div class="layui-col-md10 layui-col-md-offset1">
                                            <table class="layui-table" lay-skin="line">
                                                <tbody>
                                                    <tr>
                                                        <td width="15%">编号</td>
                                                        <td width="35%" id="patient_phone"></td>
                                                        <td width="15%">内标码</td>
                                                        <td width="35%" id="internal_code"></td>
                                                    </tr>
                                                    <tr>
                                                        <td>姓名</td>
                                                        <td id="patient_name"></td>
                                                        <td>年龄</td>
                                                        <td id="patient_age"></td>
                                                    </tr>
                                                    <tr>
                                                        <td>性别</td>
                                                        <td id="patient_gender"></td>
                                                        <td>共：</td>
                                                        <td id="totalDoses"></td>
                                                    </tr>
                                                    <tr>
                                                        <td>类型</td>
                                                        <td id="medicine_type"></td>
                                                        <td>天数</td>
                                                        <td id="days_count"></td>
                                                    </tr>
                                                    <tr>
                                                        <td>地址</td>
                                                        <td colspan="3" id="patient_address"></td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                            <div class="layui-row" style="margin-top: 20px;">
                                                <div class="layui-col-md4">
                                                    <div id="qrcode" style="width: 150px; height: 150px;"></div>
                                                    <div style="margin-top: 10px;">使用手机微信扫一扫</div>
                                                </div>
                                                <div class="layui-col-md8"
                                                    style="text-align: right; padding-top: 120px;">
                                                    <div>打印时间: <span id="print_time"></span></div>
                                                    <div>打印人: <span id="print_person"></span></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="layui-row" style="margin-top: 20px; text-align: center;">
                                <button type="button" class="layui-btn" onclick="printQRCode()">
                                    <i class="layui-icon">&#xe615;</i> 打印一码通
                                </button>
                                <div id="one_barcode">...</div>
                            </div>
                        </div>

                        <h3>处方单</h3>

                        <div class="print-area" id="prescriptionArea">
                            <div class="layui-card">
                                <div class="layui-card-body">
                                    <div class="layui-card" id="chufangdan">
                                        <div id="cfd_child">
                                            <div
                                                style="position: absolute; top: 50%; right: 20px; z-index:99; transform: translateY(-50%); font-size: 20px; font-weight: bold;">
                                                <div style="text-align: center;">当</div>
                                                <div style="text-align: center;margin:10px 0">日</div>
                                                <div style="text-align: center;">有</div>
                                                <div style="text-align: center;margin:10px 0">效</div>
                                            </div>
                                            <div class="layui-card-body">
                                                <!-- 处方标题 -->
                                                <div style="text-align: center; margin-bottom: 20px;">
                                                    <h1 style="font-size: 24px; font-weight: bold;">哈尔滨幸年堂智慧中医门诊部</h1>
                                                    <div style="font-size: 16px; margin: 10px 0 30px 0;">互联网医院处方单</div>
                                                </div>

                                                <!-- 处方基本信息 -->
                                                <div class="layui-row"
                                                    style="margin-bottom: 15px; border-bottom: 1px solid #e6e6e6; padding-bottom: 10px;">
                                                    <div class="layui-col-xs6">
                                                        <div><span style="font-weight: bold;">处方ID：</span><span
                                                                id="pre_id"></span></div>
                                                    </div>
                                                    <div class="layui-col-xs6"
                                                        style="text-align: right; position: relative;">
                                                        <div><span style="font-weight: bold;">开方日期：</span><span
                                                                id="create_date_pre"></span></div>
                                                        <img src="/static/uploads/sys/stamp.png"
                                                            style="width: 160px; height: 160px; position: absolute; top: 70px; right: 50px;">
                                                    </div>
                                                    <div class="layui-col-xs12">
                                                        <div style="display: flex;"><span
                                                                style="font-weight: bold;">费别：</span>
                                                            <div class="layui-form-item"
                                                                style="display: inline-block; margin-bottom: 0;">
                                                                <div
                                                                    style="display: flex; align-items: center;width:160px;justify-content: space-between;">
                                                                    <div class="yibao_checkbox"><input type="checkbox"
                                                                            name="fee_type_1" lay-skin="primary"
                                                                            title="自费">自费</div>
                                                                    <div class="yibao_checkbox"><input type="checkbox"
                                                                            name="fee_type_2" lay-skin="primary"
                                                                            title="医保">医保</div>
                                                                    <div class="yibao_checkbox"><input type="checkbox"
                                                                            name="fee_type_3" lay-skin="primary"
                                                                            title="其它">其它</div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="layui-row" style="margin-bottom: 15px;">
                                                    <div class="layui-col-xs3">
                                                        <div><span style="font-weight: bold;">姓名：</span><span
                                                                id="patient_name_pre"></span></div>
                                                    </div>
                                                    <div class="layui-col-xs3">
                                                        <div><span style="font-weight: bold;">性别：</span><span
                                                                id="patient_gender_pre"></span></div>
                                                    </div>
                                                    <div class="layui-col-xs3">
                                                        <div><span style="font-weight: bold;">年龄：</span><span
                                                                id="patient_age_pre"></span></div>
                                                    </div>
                                                    <div class="layui-col-xs3">
                                                        <div><span style="font-weight: bold;">手机：</span><span
                                                                id="patient_phone_pre"></span></div>
                                                    </div>
                                                </div>

                                                <div class="layui-row" style="margin-bottom: 15px;">
                                                    <div class="layui-col-xs12">
                                                        <div><span style="font-weight: bold;">家庭地址：</span><span
                                                                id="patient_address_pre"></span></div>
                                                    </div>
                                                </div>

                                                <div class="layui-row"
                                                    style="margin-bottom: 15px; border-bottom: 1px solid #e6e6e6; padding-bottom: 10px;">
                                                    <div class="layui-col-xs12">
                                                        <div><span style="font-weight: bold;">诊断：</span>
                                                            <span id="diagnosis_pre"></span>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- 处方内容 -->
                                                <div style="margin: 20px 0;">
                                                    <h2
                                                        style="font-size: 26px; font-weight: bold; margin-bottom: 15px;">
                                                        R：</h2>

                                                    <!-- 药品表格 -->
                                                    <table class="layui-table" lay-skin="line">
                                                        <tbody id="RP_table_body">
                                                        </tbody>
                                                    </table>
                                                </div>

                                                <!-- 用药信息汇总 -->
                                                <div style="margin: 20px 0; padding-top: 10px;">
                                                    <div style="font-size: 14px; line-height: 1.8;">
                                                        <div><span id="medicine_summary"></span></div>
                                                        <div id="dosage_instruction"></div>
                                                        <div style="margin-top: 8px;">
                                                            <span>科室：<span id="department_info"></span></span>
                                                            <span style="margin-left: 50px;">医师：<span
                                                                    id="doctor_name"></span></span>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- 签名区域 - 2行2列 -->
                                                <div class="layui-form sign_box" style="margin-top: 30px;">
                                                    <table class="layui-table" lay-skin="line">
                                                        <tr>
                                                            <td width="25%">医生</td>
                                                            <td width="25%">审方</td>
                                                            <td width="25%">调配</td>
                                                            <td width="25%">复核发药</td>
                                                        </tr>
                                                        <tr style="height: 70px;">
                                                            <td id="verify_1"></td>
                                                            <td id="verify_2"></td>
                                                            <td id="verify_3"></td>
                                                            <td id="verify_4"></td>
                                                        </tr>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 返回按钮 -->
                        <div class="layui-row" style="display: flex;justify-content: center;margin-top: 50px;">
                            <button type="button" class="layui-btn" onclick="printPreview()">
                                <i class="layui-icon">&#xe615;</i> 打印处方单
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 整合所有layui.use调用为一个
        layui.use(['element', 'layer', 'util', 'form'], function () {
            var element = layui.element;
            var layer = layui.layer;
            var util = layui.util;
            var form = layui.form;
            var $ = layui.$;  // 统一使用layui的jQuery引用
            var dosage_1, dosage_2, dosage_3, dosage_4, dosage_5;

            // 渲染表单元素
            form.render();

            // 页面顶部插入缓存的用户数据
            let local_userinfo = localStorage.getItem('local_userinfo');
            let user_name = "";
            if (local_userinfo) {
                local_userinfo = JSON.parse(local_userinfo);
                user_name = local_userinfo.Name + " ( " + local_userinfo.Roles + " )";
            } else {
                user_name = "未登录";
            }
            $('.user_name').text(user_name);

            render_menu($);
            $('.refresh_cache').click(function () {
                refresh_cache($);
            });

            // 获取处方ID
            var prescription_id = request.get('id');
            if (!prescription_id) {
                layer.msg('处方ID不能为空', { icon: 2 });
                return;
            }

            // 存储处方数据的全局变量，避免重复请求
            var prescriptionData = null;
            var patientData = null;

            //根据处方ID求出来的病历ID来填充患者基础信息
            var render_patient_info = function (record_ids) {
                layer.load(2);
                $.ajax({
                    url: serverUrl + "/admin/patient_profile/get_patient_ids_by_record_ids",
                    type: "post",
                    data: { record_ids: record_ids },
                    success: function (res) {
                        layer.closeAll('loading');
                        let data = res.data;
                        patientData = data;
                        let html = '';
                        for (let i = 0; i < data.length; i++) {
                            html += `
                                        <!-- 第1大行 -->
                                        <div class="layui-col-xs3">
                                            <div class="info-item">
                                                <span class="info-label">手机号码：</span>
                                                <span class="info-content">${data[i].Phone || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">患者姓名：</span>
                                                <span class="info-content">${data[i].Name || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">患者性别：</span>
                                                <span class="info-content">${data[i].Sex == 0 ? '女' : '男' || '-'}</span>
                                            </div>
                                        </div>

                                        <div class="layui-col-xs3">
                                            <div class="info-item">
                                                <span class="info-label">关系：</span>
                                                <span class="info-content">${FamilyRelation[data[i].Relation] || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">身高：</span>
                                                <span class="info-content">${data[i].Height || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">体重：</span>
                                                <span class="info-content">${data[i].Weight || '-'}</span>
                                            </div>
                                        </div>

                                        <div class="layui-col-xs3">
                                            <div class="info-item">
                                                <span class="info-label">身份证号：</span>
                                                <span class="info-content">${data[i].Idcard || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">年龄：</span>
                                                <span class="info-content">${date2age(data[i].Born_date)}岁</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">医保卡：</span>
                                                <span class="info-content">${data[i].Ins_card_num || '-'}</span>
                                            </div>
                                        </div>

                                        <div class="layui-col-xs3">
                                            <div class="info-item">
                                                <span class="info-label">患者来源：</span>
                                                <span class="info-content">${Patient_From[data[i].Patient_from] || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">患者等级：</span>
                                                <span class="info-content">${data[i].Level + '级' || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">微信号：</span>
                                                <span class="info-content">${data[i].Weixin || '-'}</span>
                                            </div>
                                        </div>


                                        <div class="layui-col-xs6">
                                            <div class="info-item">
                                                <span class="info-label">患者备注：</span>
                                                <span class="info-content">${data[i].Customer_notes || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">既往病史：</span>
                                                <span class="info-content">${data[i].Medical_history || '-'}</span>
                                            </div>
                                        </div>
                                        <div class="layui-col-xs6">
                                            <div class="info-item">
                                                <span class="info-label">主诉：</span>
                                                <span class="info-content">${data[i].Chief_complaint || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">过敏史：</span>
                                                <span class="info-content">${data[i].Allergies || '-'}</span>
                                            </div>
                                        </div>
                                        <div class="layui-col-xs12">
                                            <div class="info-item">
                                                <span class="info-label">详细地址：</span>
                                                <span class="info-content">${removeAllPipe(data[i].Address) || '-'}</span>
                                            </div>
                                        </div>
                            `;
                        }
                        $('#patient_data').html(html);

                        // 填充处方单上的患者信息
                        if (data.length > 0) {
                            $('#patient_name_pre').text(data[0].Name || '-');
                            $('#patient_gender_pre').text(data[0].Sex == 0 ? '女' : '男');
                            $('#patient_age_pre').text(date2age(data[0].Born_date) + '岁');
                            $('#patient_phone_pre').text(data[0].Phone || '-');
                            $('#patient_address_pre').text(data[0].Address ? removeAllPipe(data[0].Address) : '-');

                            // 同时填充一码通上的患者信息
                            $('#patient_phone').text(data[0].Phone || '-');
                            $('#patient_name').text(data[0].Name || '-');
                            $('#patient_gender').text(data[0].Sex == 0 ? '女' : '男');
                            $('#patient_age').text(date2age(data[0].Born_date) + '岁');
                            $('#patient_address').text(data[0].Address ? removeAllPipe(data[0].Address) : '-');
                        }
                    },
                    error: function () {
                        layer.closeAll('loading');
                        layer.msg('获取患者信息失败', { icon: 2 });
                    }
                });
            }

            // 获取病历信息
            var render_record_info = function (record_id) {
                layer.load(2);
                $.ajax({
                    url: serverUrl + "/admin/patient_records/detail",
                    type: "post",
                    data: { id: record_id },
                    success: function (res) {
                        layer.closeAll('loading');
                        if (res.code == 200) {
                            let data = res.data;
                            let html = '';

                            // 构建病历信息HTML
                            html += `
                                <!-- 第1行：患者现状 -->
                                <div class="layui-col-md4">
                                    <div class="info-item">
                                        <span class="info-label">患者主诉：</span>
                                        <span class="info-content">${data.Chief_complaint || '-'}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">复诊主诉：</span>
                                        <span class="info-content">${data.Re_chief_complaint || '-'}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">现病史：</span>
                                        <span class="info-content">${data.History_of_present_illness || '-'}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">现需治疗：</span>
                                        <span class="info-content">${data.Now_needs || '-'}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">大小便情况：</span>
                                        <span class="info-content">${data.Urination || '-'}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">有无三高：</span>
                                        <span class="info-content">${data.Triad || '-'}</span>
                                    </div>
                                </div>

                                <!-- 第2行：病史记录 -->
                                <div class="layui-col-md4">
                                    <div class="info-item">
                                        <span class="info-label">既往病史：</span>
                                        <span class="info-content">${data.Past_medical_history || '-'}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">个人史：</span>
                                        <span class="info-content">${data.Personal_history || '-'}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">家族史：</span>
                                        <span class="info-content">${data.Family_history || '-'}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">过敏史：</span>
                                        <span class="info-content">${data.Allergy_history || '-'}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">用药史：</span>
                                        <span class="info-content">${data.Past_medication_history || '-'}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">上次用药时间：</span>
                                        <span class="info-content">${data.Last_medication_time ? data.Last_medication_time.replace('T', ' ').replace('Z', '') : '-'}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">上次用药情况：</span>
                                        <span class="info-content">${data.Last_medical || '-'}</span>
                                    </div>
                                </div>

                                <!-- 第3行：诊疗信息 -->
                                <div class="layui-col-md4">
                                    <div class="info-item">
                                        <span class="info-label">诊断信息：</span>
                                        <span class="info-content">${data.Diagnosis_information || '-'}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">治疗方案：</span>
                                        <span class="info-content">${data.Treatment_plan || '-'}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">患者状态：</span>
                                        <span class="info-content">${Record_Status[data.Status] || '-'}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">核销时间：</span>
                                        <span class="info-content">${data.Discharge_time == '0001-01-01T00:00:00Z' ? "未核销" : "已核销"}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">创建时间：</span>
                                        <span class="info-content">${data.Create_time ? data.Create_time.replace('T', ' ').replace('Z', '') : '-'}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">对应处方：</span>
                                        <span class="info-content">${data.Pre_id == 0 ? "未开处方" : data.Pre_id}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">意向剂型：</span>
                                        <span class="info-content">${data.Tx_day || '-'}天，${data.Tx_type || '-'}</span>
                                    </div>
                                </div>
                            `;
                            $('#record_data').html(html);

                            // 从本地存储获取科室信息并显示
                            if (data.Department_id) {
                                let local_departments = localStorage.getItem('local_departments');
                                if (local_departments) {
                                    try {
                                        local_departments = JSON.parse(local_departments);
                                        let department = local_departments.find(dept => dept.Id == data.Department_id);
                                        if (department) {
                                            $('#department_info').text(department.Name || '中医内科');
                                        } else {
                                            $('#department_info').text('中医内科');
                                        }
                                    } catch (e) {
                                        console.error('解析科室信息出错', e);
                                        $('#department_info').text('中医内科');
                                    }
                                } else {
                                    $('#department_info').text('中医内科');
                                }
                            } else {
                                $('#department_info').text('中医内科');
                            }

                            // 显示舌苔照图片
                            if (data.Photo_tongue) {
                                let tongue_photos = data.Photo_tongue.split('\n');
                                let container = $('#photo_tongue_container');
                                container.empty();
                                tongue_photos.forEach(photo => {
                                    if (photo.trim()) {
                                        let pic = '/static/uploads/normal_pics/photo_tongue/' + photo.trim();
                                        container.append(`<img src="${pic}" alt="舌苔照" onclick="viewImage('${pic}')">`);
                                    }
                                });
                            }

                            // 显示检查单图片
                            if (data.Photo_sheet) {
                                let sheet_photos = data.Photo_sheet.split('\n');
                                let container = $('#photo_sheet_container');
                                container.empty();
                                sheet_photos.forEach(photo => {
                                    if (photo.trim()) {
                                        let pic = '/static/uploads/normal_pics/photo_sheet/' + photo.trim();
                                        container.append(`<img src="${pic}" alt="检查单" onclick="viewImage('${pic}')">`);
                                    }
                                });
                            }
                        } else {
                            console.log(res)
                            layer.msg(res.data.msg || '获取病历信息失败', { icon: 2 });
                        }
                    },
                    error: function (res) {
                        layer.closeAll('loading');

                        // 添加模糊效果
                        $('body').append('<div id="blur-overlay" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(255, 255, 255, 0.7); backdrop-filter: blur(5px); z-index: 19891014;"></div>');

                        // 显示确认弹窗
                        layer.confirm(res.responseJSON.msg || '获取病历信息失败', {
                            icon: 2,
                            title: '错误提示',
                            btn: ['返回上一页'],
                            closeBtn: 0, // 不显示关闭按钮
                            shadeClose: false // 点击遮罩不关闭
                        }, function (index) {
                            // 移除模糊效果
                            $('#blur-overlay').remove();
                            // 关闭弹窗
                            layer.close(index);
                            // 返回上一页
                            history.go(-1);
                        });
                    }
                });
            };

            // 加载处方详情
            $.ajax({
                url: '/admin/prescription/detail',
                type: 'POST',
                data: { id: prescription_id },
                success: function (res) {
                    if (res.code === 200) {
                        var data = res.data;
                        prescriptionData = data;  // 存储处方数据，避免重复请求

                        // 填充基本信息
                        $('#diagnosis').text(data.Diagnosis || '-');
                        $('#tx_plan').text(data.Tx_plan || '-');
                        $('#askfor').text(data.Askfor || '-');
                        $('#tx_day').text(data.Tx_day || '-');
                        $('#tx_type').text(data.Tx_type || '-');

                        // 处理用法用量
                        if (data.Dosage) {
                            var dosage = data.Dosage.split('|');
                            dosage_1 = dosage[0] || '-';
                            dosage_2 = dosage[1] || '-';
                            dosage_3 = dosage[2] || '-';
                            dosage_4 = dosage[3] || '-';
                            dosage_5 = dosage[4] || '-';
                            $('#dosage_1').text(dosage[0] || '-');
                            $('#dosage_2').text(dosage[1] || '-');
                            $('#dosage_3').text(dosage[2] || '-');
                            $('#dosage_4').text(dosage[3] || '-');
                            $('#dosage_5').text(dosage[4] || '-');

                            // 填充处方单的用法用量说明
                            $('#dosage_instruction').html(`
                                每 ${dosage_1} 天 ${dosage_2} 剂，${prescriptionData.Tx_day || '-'} 天，每 ${dosage_1} 天 ${dosage_2} 次，每次 ${dosage_3} 克<br>
                                禁忌：${dosage_5 || '无'}
                            `);
                        }

                        // 填充处方单上的字段
                        $('#pre_id').text('C' + data.ID || '-');
                        $('#pre_type').text(data.Tx_type || '-');
                        $('#diagnosis_pre').text(data.Diagnosis || '-');

                        // 格式化处方创建日期
                        var createDate = data.Create_time ? new Date(data.Create_time) : new Date();
                        var formattedDate = createDate.getFullYear() + '-' +
                            ('0' + (createDate.getMonth() + 1)).slice(-2) + '-' +
                            ('0' + createDate.getDate()).slice(-2);
                        $('#create_date_pre').text(formattedDate);

                        // 根据Doc_id获取医生姓名
                        if (data.Doc_id) {
                            getDoctorName(data.Doc_id);
                        } else {
                            $('#doctor_name').text('-');
                        }

                        // 填充患者基础信息
                        render_patient_info(data.Record_id);

                        // 填充病历信息
                        render_record_info(data.Record_id);

                        $('#status').text(Pre_Status[data.Status] || '未知状态');

                        // 格式化时间
                        $('#create_time').text(data.Create_time ? data.Create_time.replace('T', ' ').replace('Z', '') : '-');

                        // 医生签名
                        let Verify_1 = data.Verify_1;
                        let Verify_1_str = '';
                        if (Verify_1 == 2) {
                            Verify_1_str = "<div class='layui-form-mid'><font color='red'><i class=\"iconfont\">&#xe637;</i> " + data.Verify_1_desc + "</font></div>";
                        } else if (Verify_1 == 0) {
                            Verify_1_str = "<div class='layui-form-mid'><font color='blue'>待审核</font></div>";
                        } else {
                            Verify_1_str = "<img src='/static/uploads/icons/sign_" + data.Verify_1_user_id + ".png?" + new Date().getTime() + "'/>";
                        }
                        $('#verify_1').html(Verify_1_str);

                        // 审方签名
                        let Verify_2 = data.Verify_2;
                        let Verify_2_str = '';
                        if (Verify_2 == 2) {
                            Verify_2_str = "<div class='layui-form-mid'><font color='red'><i class=\"iconfont\">&#xe637;</i> " + data.Verify_2_desc + "</font></div>";
                        } else if (Verify_2 == 0) {
                            Verify_2_str = "<div class='layui-form-mid'><font color='blue'>待审核</font></div>";
                        } else {
                            Verify_2_str = "<img src='/static/uploads/icons/sign_" + data.Verify_2_user_id + ".png?" + new Date().getTime() + "'/>";
                        }
                        $('#verify_2').html(Verify_2_str);

                        // 调配签名
                        let Verify_3 = data.Verify_3;
                        let Verify_3_str = '';
                        if (Verify_3 == 2) {
                            Verify_3_str = "<div class='layui-form-mid'><font color='red'><i class=\"iconfont\">&#xe637;</i> " + data.Verify_3_desc + "</font></div>";
                        } else if (Verify_3 == 0) {
                            Verify_3_str = "<div class='layui-form-mid'><font color='blue'>待审核</font></div>";
                        } else {
                            Verify_3_str = "<img src='/static/uploads/icons/sign_" + data.Verify_3_user_id + ".png?" + new Date().getTime() + "'/>";
                        }
                        $('#verify_3').html(Verify_3_str);

                        // 复核发药签名
                        let Verify_4 = data.Verify_4;
                        let Verify_4_str = '';
                        if (Verify_4 == 2) {
                            Verify_4_str = "<div class='layui-form-mid'><font color='red'><i class=\"iconfont\">&#xe637;</i> " + data.Verify_4_desc + "</font></div>";
                        } else if (Verify_4 == 0) {
                            Verify_4_str = "<div class='layui-form-mid'><font color='blue'>待审核</font></div>";
                        } else {
                            Verify_4_str = "<img src='/static/uploads/icons/sign_" + data.Verify_4_user_id + ".png?" + new Date().getTime() + "'/>";
                        }
                        $('#verify_4').html(Verify_4_str);

                        // 加载药品信息
                        if (data.Finished_drug_id == 0) {
                            loadPrescriptionDrugs(prescription_id);
                        } else {
                            // 根据成品药ID，求出对应处方ID
                            layer.load(2);
                            $.ajax({
                                url: '/admin/tools/warehouse2productname_finisheddrug',
                                type: 'POST',
                                data: { wid: data.Finished_drug_id },
                                success: function (res) {
                                    layer.closeAll('loading');
                                    if (res.code === 200 && res.data && res.data.Pre_id) {
                                        loadPrescriptionDrugs(res.data.Pre_id);
                                        $('#prescription_drug_title').html('处方药品信息（选择的成品药，<a href="/admin/prescription_show.html?id=' + res.data.Pre_id + '#/admin/prescription_list.html" target="_blank" class="red_font">原成品药对应处方ID</a>）');
                                    }
                                }, error: function (res) {
                                    layer.closeAll('loading');
                                    layer.msg(res.msg || '请求成品药信息出错', { icon: 2 });
                                }
                            });
                        }
                        let one_barcode = `<div style="font-size:12px;color:#333;margin:30px auto;"><i class="layui-icon" style="font-size:16px;color:#333;margin-right:5px;margin-top:-2px;">&#xe702;</i>该一码通（可以理解为处方单一码通，入库专用），其无法作订单发货登记物流使用，因为1个订单可能包含N个处方单。物流需要【 <a href="patient_profile_show.html?id=` + data.Pat_pro_id + `#/admin/patient_profile_list.html" style="color:#1677ff" target=_blank>用户一码通</a> 】</div>`;
                        $('#one_barcode').html(one_barcode);

                        // 初始化一码通数据
                        initYiMaTong();
                    } else {
                        layer.msg(res.msg || '加载失败', { icon: 2 });
                    }
                },
                error: function () {
                    layer.msg('服务器错误', { icon: 2 });
                }
            });

            // 加载处方药品信息
            function loadPrescriptionDrugs(prescription_id) {
                $.ajax({
                    url: '/admin/prescription/drug',
                    type: 'POST',
                    data: { pre_id: prescription_id },
                    success: function (res) {
                        if (res.code === 200) {
                            var tableBody = $('#prescription_drug_table_body');
                            var RP_table_body = $('#RP_table_body');
                            tableBody.empty();
                            RP_table_body.empty();

                            // 计算单剂味数和单剂总克数
                            var totalDrugs = res.data.length; // 单剂味数
                            var totalGrams = 0; // 单剂总克数

                            res.data.forEach(function (item) {
                                // 累加总克数
                                totalGrams += parseFloat(item.Quantity || 0);

                                var row = `
                                    <tr>
                                        <td>${item.Wh_drug_id || '-'}</td>
                                        <td>${item.Drug_id || '-'}</td>
                                        <td>${item.Name || '-'}</td>
                                        <td>${item.Quantity || '-'}</td>
                                    </tr>
                                `;
                                tableBody.append(row);
                            });

                            // 动态生成3列N行的RP_table_body
                            var rowsPerColumn = Math.ceil(res.data.length / 3); // 每列的行数
                            for (let i = 0; i < rowsPerColumn; i++) {
                                var RP_row = `
                                    <tr>
                                        <td>${res.data[i] ? res.data[i].Name + ' ' + res.data[i].Quantity + 'g' : ''}</td>
                                        <td>${res.data[i + rowsPerColumn] ? res.data[i + rowsPerColumn].Name + ' ' + res.data[i + rowsPerColumn].Quantity + 'g' : ''}</td>
                                        <td>${res.data[i + 2 * rowsPerColumn] ? res.data[i + 2 * rowsPerColumn].Name + ' ' + res.data[i + 2 * rowsPerColumn].Quantity + 'g' : ''}</td>
                                    </tr>
                                `;
                                RP_table_body.append(RP_row);
                            }

                            // 计算总剂量和总克数
                            var totalDoses = prescriptionData.TotalDoses || prescriptionData.Tx_day || 0;
                            var totalWeight = totalGrams * totalDoses;

                            // 更新药品汇总信息
                            $('#medicine_summary').html(`
                                单剂 ${totalDrugs} 味，单剂 ${totalGrams.toFixed(0)}g，共 ${totalDoses} 剂，共 ${totalWeight.toFixed(0)}g
                            `);

                        } else {
                            layer.msg(res.msg || '加载药品信息失败', { icon: 2 });
                        }
                    },
                    error: function () {
                        layer.msg('服务器错误', { icon: 2 });
                    }
                });
            }

            // 图片查看函数 - 整合到主layui.use中
            window.viewImage = function (src) {
                layer.photos({
                    photos: {
                        title: "查看图片",
                        start: 0,
                        data: [{ src: src }]
                    },
                    footer: false
                });
            };

            // 显示二维码图片 - 整合到主layui.use中
            window.displayQRCode = function (base64Data) {
                // 清空之前的二维码
                document.getElementById('qrcode').innerHTML = '';
                // 显示后端生成的二维码
                $('#qrcode').append('<img src="data:image/png;base64,' + base64Data + '" style="width:150px;height:150px;border:1px solid #eee;">');
            };

            // 打印预览函数
            window.printPreview = function () {
                // 加载html2canvas
                var script = document.createElement('script');
                script.src = '/dist/js/html2canvas.min.js';
                script.onload = function () {
                    // 将处方区域转换为图片
                    html2canvas(document.getElementById('cfd_child'), {
                        backgroundColor: '#ffffff', // 设置背景为白色
                        useCORS: true,             // 允许跨域
                        scale: 2,                  // 提高清晰度
                        logging: false,            // 关闭日志
                        removeContainer: true      // 移除临时容器
                    }).then(function (canvas) {
                        // 创建图片预览
                        var img = canvas.toDataURL('image/png');
                        layer.open({
                            type: 1,
                            title: '打印预览',
                            area: ['800px', '900px'],
                            content: '<div style="background-color: #fff;"><img src="' + img + '" style="width: 100%; height: auto;"/></div>',
                            btn: ['打印', '取消'],
                            yes: function (index) {
                                // 创建打印窗口
                                var printWindow = window.open('', '_blank');
                                printWindow.document.write(`
                                    <html>
                                    <head>
                                        <title>处方单打印预览</title>
                                        <style>
                                            body {
                                                margin: 0;
                                                padding: 0;
                                                background-color: #fff;
                                            }
                                            img {
                                                width: 100%;
                                                height: auto;
                                            }
                                        </style>
                                    </head>
                                    <body>
                                        <img src="${img}">
                                    </body>
                                    </html>
                                `);
                                printWindow.document.close();

                                // 等待图片加载完成后打印
                                printWindow.onload = function () {
                                    printWindow.print();
                                    printWindow.close();
                                };
                                layer.close(index);
                            }
                        });
                    });
                };
                document.body.appendChild(script);
            };

            // 打印一码通
            window.printQRCode = function () {
                // 加载html2canvas
                var script = document.createElement('script');
                script.src = '/dist/js/html2canvas.min.js';
                script.onload = function () {
                    // 获取一码通区域
                    var qrcodeArea = document.getElementById('ymt_child');

                    // 将一码通区域转换为图片
                    html2canvas(qrcodeArea, {
                        backgroundColor: '#ffffff',
                        useCORS: true,
                        scale: 2,
                        logging: false,
                        removeContainer: true
                    }).then(function (canvas) {
                        // 创建图片预览
                        var img = canvas.toDataURL('image/png');
                        layer.open({
                            type: 1,
                            title: '一码通打印预览',
                            area: ['700px', '800px'],
                            content: '<div style="background-color: #fff;"><img src="' + img + '" style="width: 100%; height: auto;"/></div>',
                            btn: ['打印', '取消'],
                            yes: function (index) {
                                // 创建打印窗口
                                var printWindow = window.open('', '_blank');
                                printWindow.document.write(`
                                    <html>
                                    <head>
                                        <title>一码通打印预览</title>
                                        <style>
                                            body {
                                                margin: 0;
                                                padding: 0;
                                                background-color: #fff;
                                            }
                                            img {
                                                width: 100%;
                                                height: auto;
                                            }
                                        </style>
                                    </head>
                                    <body>
                                        <img src="${img}">
                                    </body>
                                    </html>
                                `);
                                printWindow.document.close();

                                // 等待图片加载完成后打印
                                printWindow.onload = function () {
                                    printWindow.print();
                                    printWindow.close();
                                };
                                layer.close(index);
                            }
                        });
                    });
                };
                document.body.appendChild(script);
            };

            // 初始化一码通数据
            function initYiMaTong() {
                if (!prescriptionData) return;

                // 设置当前时间和打印人
                var now = new Date();
                var dateStr = now.getFullYear() + '/' +
                    (now.getMonth() + 1).toString().padStart(2, '0') + '/' +
                    now.getDate().toString().padStart(2, '0');
                $('#print_time').text(dateStr);

                // 获取当前登录用户
                var userInfo = JSON.parse(localStorage.getItem('local_userinfo') || '{}');
                $('#print_person').text(userInfo.Name || 'XXXXXX');

                // 填充处方相关信息
                $('#internal_code').text('幸年堂');
                $('#totalDoses').text(prescriptionData.TotalDoses + '剂');
                $('#medicine_type').text(prescriptionData.Tx_type);
                $('#days_count').text(prescriptionData.Tx_day + '天，每 ' + dosage_1 + ' 天 ' + dosage_2 + ' 次');

                // 计算克数信息
                var gramPerDay = 24; // 默认值
                if (prescriptionData.Dosage) {
                    var dosage = prescriptionData.Dosage.split('|');
                    if (dosage.length >= 4) {
                        var times = parseInt(dosage[1]) || 2;
                        var amount = parseInt(dosage[2]) || 12;
                        gramPerDay = times * amount;
                    }
                }
                $('#gram_per_day').text(gramPerDay + 'g/天');

                var totalDays = parseInt(prescriptionData.Tx_day);
                var totalGram = gramPerDay * totalDays;
                $('#total_gram').text(totalGram + 'g');

                // 获取处方二维码
                $.ajax({
                    url: serverUrl + "/admin/codebar/prescription_qrcode",
                    type: "post",
                    data: {
                        "id": prescription_id,
                    },
                    success: function (res) {
                        if (res.code === 200) {
                            // 显示二维码
                            displayQRCode(res.url);
                        } else {
                            layer.msg(res.msg || '获取二维码失败', { icon: 2 });
                        }
                    },
                    error: function (err) {
                        console.error(err);
                        layer.msg(err.responseJSON ? err.responseJSON.msg : '获取二维码失败', { icon: 2 });
                        // 显示错误提示，不再使用前端生成二维码
                        document.getElementById('qrcode').innerHTML = '<div style="width:150px;height:150px;display:flex;align-items:center;justify-content:center;background:#f8f8f8;border:1px dashed #ddd;color:#999;text-align:center;font-size:12px;">二维码获取失败</div>';
                    }
                });
            }

            // 根据医生ID获取医生姓名
            function getDoctorName(docId) {
                $.ajax({
                    url: serverUrl + '/admin/user/list_low',
                    type: 'POST',
                    data: { id: docId }, // 直接通过id参数查询特定医生
                    success: function (res) {
                        if (res.code === 200 && res.data && res.data.length > 0) {
                            // 获取返回的第一个用户作为医生信息
                            const doctor = res.data[0];
                            if (doctor) {
                                $('#doctor_name').text(doctor.Name || '-');
                            } else {
                                // 未找到匹配的医生
                                $('#doctor_name').text('-');
                            }
                        } else {
                            $('#doctor_name').text('-');
                        }
                    },
                    error: function () {
                        $('#doctor_name').text('-');
                        console.error('获取医生信息失败');
                    }
                });
            }
        });
    </script>
</body>

</html>