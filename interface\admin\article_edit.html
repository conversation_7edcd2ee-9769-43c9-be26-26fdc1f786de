<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 编辑文章</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
</head>

<body>

    <div class="layui-padding-3">
        <form class="layui-form" lay-filter="myform">
            <input type="hidden" name="id" value="">

            <!-- 使用卡片式背景包裹整个表单 -->
            <div class="layui-card">
                <div class="layui-card-body">
                    <div class="layui-row layui-form-item">
                        <!-- 第1行：文章分类 占8；排序 占4 -->
                        <div class="layui-col-xs8 layui-col-sm8 layui-col-md8">
                            <div class="layui-form-item margin-bottom-none">
                                <label class="layui-form-label">文章分类</label>
                                <div class="layui-input-block">
                                    <select name="Pid" lay-verify="required" lay-reqtext="请选择文章分类" class="layui-select">
                                        <option value="">请选择文章分类</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-xs4 layui-col-sm4 layui-col-md4">
                            <div class="layui-form-item margin-bottom-none">
                                <label class="layui-form-label">排序</label>
                                <div class="layui-input-block">
                                    <input type="number" name="Sort" lay-verify="required|number" lay-reqtext="请输入排序值"
                                        placeholder="请输入排序值" autocomplete="off" class="layui-input">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="layui-row layui-form-item">
                        <!-- 第2行：文章标题 占12 -->
                        <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                            <div class="layui-form-item">
                                <label class="layui-form-label">文章标题</label>
                                <div class="layui-input-block">
                                    <input type="text" name="Title" lay-verify="required" lay-reqtext="请输入文章标题"
                                        placeholder="请输入文章标题" autocomplete="off" class="layui-input">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="layui-row layui-form-item">
                        <!-- 第3行：封面图片上传 占6；图片预览 占6 -->
                        <div class="layui-col-xs6 layui-col-sm6 layui-col-md6">
                            <div class="layui-form-item">
                                <label class="layui-form-label">封面图片</label>
                                <div class="layui-input-block">
                                    <input type="hidden" name="CoverPic" value="">
                                    <div class="layui-upload upload-container" id="upload-container">
                                        <button type="button" class="layui-btn upload_big_btn" id="upload-image-btn">
                                            <div class="btn_big_font">
                                                <i class="layui-icon layui-icon-upload btn_big_font"></i> 上传图片
                                            </div>
                                            <div>jpg、png、jpeg、bmp、gif格式，1M以内</div>
                                            <div>支持点击、拖拽和 Ctrl+V 上传</div>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-xs6 layui-col-sm6 layui-col-md6">
                            <div class="layui-form-item">
                                <div class="layui-input-block">
                                    <div class="image-preview-container-normal" id="image-container"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="layui-row">
                        <!-- 第4行：文章内容 占12 -->
                        <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                            <div class="layui-form-item layui-form-text">
                                <label class="layui-form-label">文章内容</label>
                                <div class="layui-input-block">
                                    <textarea name="Contents" placeholder="请输入文章内容"
                                        class="layui-textarea content-textarea" lay-verify="required"
                                        lay-reqtext="请输入文章内容"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="layui-row">
                        <!-- 第5行：提交和重置按钮 占12 -->
                        <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                            <div class="layui-form-item text-center">
                                <button class="layui-btn layui-btn-normal" lay-submit
                                    lay-filter="formDemo">立即提交</button>
                                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <script>
        layui.use(['form', 'layer', 'upload'], function () {
            var form = layui.form;
            var layer = layui.layer;
            var upload = layui.upload;
            var $ = layui.$;
            // 获取URL参数
            var id = request.get('id');
            if (!id) {
                layer.msg('缺少ID参数', { icon: 2, time: 2000 }, function () {
                    var index = parent.layer.getFrameIndex(window.name);
                    parent.layer.close(index);
                });
                return;
            }

            // 加载文章分类
            $.ajax({
                url: serverUrl + '/admin/article_category/list',
                type: 'POST',
                success: function (res) {
                    if (res.code === 200 && res.data) {
                        var options = '';
                        res.data.forEach(function (item) {
                            options += '<option value="' + item.ID + '">' + item.Title + '</option>';
                        });
                        $('select[name="Pid"]').append(options);

                        // 加载文章详情
                        loadArticleDetail();
                    }
                }
            });

            // 加载文章详情
            function loadArticleDetail() {
                layer.load(2);
                $.ajax({
                    url: serverUrl + '/admin/article/detail',
                    type: 'POST',
                    data: { id: id },
                    success: function (res) {
                        layer.closeAll('loading');
                        if (res.code === 200 && res.data) {
                            // 填充表单
                            form.val('myform', res.data);

                            // 如果有封面图片，显示预览
                            if (res.data.CoverPic && res.data.CoverPic !== '') {
                                // 从路径中提取文件名
                                res.data.CoverPic = global_article_pic_path + res.data.CoverPic;
                                const filename = res.data.CoverPic.split('/').pop();
                                appendImagePreview('image-container', res.data.CoverPic, filename);
                            }
                        } else {
                            layer.msg(res.msg || '获取文章详情失败', { icon: 2, time: 2000 }, function () {
                                var index = parent.layer.getFrameIndex(window.name);
                                parent.layer.close(index);
                            });
                        }
                    },
                    error: function (err) {
                        layer.closeAll('loading');
                        layer.msg(err.responseJSON ? err.responseJSON.msg : '请求失败', { icon: 2, time: 2000 }, function () {
                            var index = parent.layer.getFrameIndex(window.name);
                            parent.layer.close(index);
                        });
                    }
                });
            }

            // 图片预览函数
            function appendImagePreview(containerId, filepath, filename) {
                const container = $(`#${containerId}`);
                // 清空现有内容，确保只有一张图片
                container.empty();

                const imageItem = $(`
                    <div class="image-preview-item" data-filename="${filename}" data-filepath="${filepath}">
                        <img src="${filepath}">
                        <div class="delete-btn">删除</div>
                    </div>
                `);

                // 点击图片查看大图
                imageItem.find('img').on('click', function () {
                    layer.photos({
                        photos: {
                            title: '查看图片',
                            data: [{ src: filepath }]
                        },
                        footer: false
                    });
                });

                // 删除图片
                imageItem.find('.delete-btn').on('click', function () {
                    layer.confirm('确定删除该图片吗？', { icon: 3, title: '提示' }, function (index) {
                        $.ajax({
                            url: '/admin/normal_pic_del',
                            type: 'POST',
                            data: {
                                filename: filename,
                                category: 'article'
                            },
                            success: function (res) {
                                if (res.code === 200) {
                                    // 清空图片路径
                                    $('input[name="CoverPic"]').val('');
                                    container.empty();
                                    layer.msg('删除成功', { icon: 1, time: 1000 });
                                }
                            },
                            error: function (data) {
                                layer.confirm('删除失败: ' + data.responseJSON.msg + '，是否移除该图片？',
                                    { icon: 3, title: '提示' },
                                    function (index) {
                                        // 清空图片路径
                                        $('input[name="CoverPic"]').val('');
                                        container.empty();
                                        layer.msg('移除成功', { icon: 1, time: 1000 });
                                    }
                                );
                            }
                        });
                        layer.close(index);
                    });
                });

                container.append(imageItem);
            }

            // 添加粘贴上传功能
            document.addEventListener('paste', function(event) {
                const items = event.clipboardData && event.clipboardData.items;
                let file = null;
                
                if (items && items.length) {
                    // 遍历剪切板内容
                    for (let i = 0; i < items.length; i++) {
                        if (items[i].type.indexOf('image') !== -1) {
                            file = items[i].getAsFile();
                            break;
                        }
                    }
                }
                
                if (!file) {
                    return;
                }
                
                // 阻止默认粘贴行为
                event.preventDefault();

                // 使用通用的压缩上传函数
                autoCompressAndUpload(file, {
                    data: { category: 'article' },
                    success: function(res) {
                        if (res.code === 200) {
                            const fileInfo = res.data[0];
                            // 显示图片预览
                            appendImagePreview('image-container', fileInfo.filepath, fileInfo.filename);
                            // 将图片路径保存到隐藏字段中
                            $('input[name="CoverPic"]').val(fileInfo.filepath);
                            layer.msg('上传成功', {icon: 1, time: 1000});
                        } else {
                            layer.msg(res.msg || '上传失败', {icon: 2});
                        }
                    },
                    error: function(error) {
                        layer.msg(error.message || '上传出错', {icon: 2});
                    }
                });
            });

            // 初始化文件上传
            upload.render({
                elem: '#upload-image-btn',
                url: '/admin/upload_normal_pic',
                multiple: true,
                data: {
                    category: 'article',
                },
                drag: true,
                before: function(obj) {
                    obj.preview(function(index, file, result) {
                        // 在预览回调中进行文件上传
                        autoCompressAndUpload(file, {
                            data: { category: 'article' },
                            success: function(res) {
                                if (res.code === 200) {
                                    const fileInfo = res.data[0];
                                    // 显示图片预览
                                    appendImagePreview('image-container', fileInfo.filepath, fileInfo.filename);
                                    // 将图片路径保存到隐藏字段中
                                    $('input[name="CoverPic"]').val(fileInfo.filepath);
                                    layer.msg('上传成功', {icon: 1, time: 1000});
                                } else {
                                    layer.msg(res.msg || '上传失败', {icon: 2});
                                }
                            },
                            error: function(error) {
                                layer.msg(error.message || '上传出错', {icon: 2});
                            }
                        });
                    });
                    return false;
                }
            });

            //监听提交
            form.on('submit(formDemo)', function (data) {
                // 确保ID被包含在提交数据中
                data.field.id = id;
                data.field.CoverPic = data.field.CoverPic.replace(global_article_pic_path, '');
                layer.load(2);
                $.ajax({
                    url: serverUrl + '/admin/article/edit',
                    type: 'POST',
                    data: data.field,
                    success: function (res) {
                        layer.closeAll('loading');
                        if (res.code === 200) {
                            layer.msg('修改成功', { icon: 1, time: 1000 }, function () {
                                // 关闭当前iframe层
                                var index = parent.layer.getFrameIndex(window.name);
                                parent.layer.close(index);
                                // 刷新父页面表格
                                parent.layui.table.reload('mytable');
                            });
                        } else {
                            layer.msg(res.msg || '修改失败', { icon: 2, time: 2000 });
                        }
                    },
                    error: function (err) {
                        layer.closeAll('loading');
                        layer.msg(err.responseJSON ? err.responseJSON.msg : '请求失败', { icon: 2, time: 2000 });
                    }
                });
                return false;
            });
        });
    </script>
</body>

</html>