# 幸年堂医疗系统

## 项目介绍
本系统是一个医疗服务平台，提供线上问诊、音视频诊疗、医患沟通等功能。系统采用Go语言开发后端，前端使用Layui框架构建界面。

## 主要功能
- 音视频诊疗：支持医生与患者进行实时在线诊疗
- 音视频自动录制：可配置患者进入诊室后自动开始录制
- NAS存储：支持将诊疗视频自动上传至NAS服务器保存
- 用户管理：包括医生、患者账户管理及权限控制
- 系统设置：提供各项功能的配置选项

## 技术栈
- 后端：Go
- 前端：HTML, CSS, JavaScript, Layui
- 存储：支持NAS网络存储

## 系统配置

### Redis数据库隔离

系统通过Redis数据库编号实现不同环境数据隔离：

- 在`pkg/config/config.go`中定义了全局变量`REDIS_DBNUM`（默认值为5）
- 所有Redis操作**统一**使用此配置的数据库编号，代码中已移除自定义编号参数
- Redis客户端函数`Redis_client`已简化，不再接受数据库编号参数
- 部署多个环境（生产和测试）时，只需修改此配置值即可实现缓存隔离
- Redis默认支持16个数据库（0-15），如需更多可修改Redis配置

**使用方法**：
1. 生产环境配置`REDIS_DBNUM = 5`
2. 测试环境配置`REDIS_DBNUM = 6`
3. 重启应用后，两个环境将使用不同的Redis数据库，避免缓存数据互相干扰

**技术说明**：
- 项目已完全移除代码中的自定义Redis数据库编号参数
- 所有Redis操作均通过`Redis_client()`函数获取统一配置的客户端
- 简化了接口设计，提高代码可维护性和一致性

## 通用组件

### 页面筛选栏实现方法

当需要在页面中实现筛选功能时，可以使用以下提示词快速实现：

```
1. 删除当前页面ID为data_search的所有内容，包括HTML、CSS和JavaScript（仅限当前页面）
2. 参照interface\admin\rtc_room_list.html，将顶部的筛选内容实现到当前页面中
```

**实现说明**：
- 这种方法可以快速复用已有的筛选栏组件
- 筛选栏会根据当前用户角色（医生、售前、售后）自动调整状态
- 包含科室+医生、销售部门+人员、患者搜索等功能区块
- 自动处理权限和数据加载逻辑

**参考模板**：`rtc_room_list.html`页面

## 最近更新
- 2023-12-20：简化页面筛选栏实现方法
  - 删除复杂的智能角色自适应筛选栏文档
  - 提供简洁明了的两步实现提示词
  - 使用rtc_room_list.html作为参考模板
  - 优化开发流程，提高开发效率
- 2023-11-26：系统设置界面进一步优化
  - 实现配置区块高度自适应，各区块高度一致
  - 添加预留配置区块，便于后期功能扩展
  - 使用CSS Flex布局和JavaScript动态控制实现高度自适应
- 2023-11-25：优化系统设置界面，采用两列布局提升空间利用效率和用户体验
  - 将原有的垂直单列布局改为响应式两列布局
  - 利用Layui栅格系统实现，确保在各种设备上都能良好展示

## 开发与部署指南
待补充...
