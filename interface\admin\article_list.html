<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 文章管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
</head>

<body>
    <div class="layui-layout layui-layout-admin">
        <!-- 头部 -->
        <div class="layui-header">
            <div class="layui-logo">幸年堂</div>
            <ul class="layui-nav layui-layout-right">
                <li class="layui-nav-item" style="background-color:transparent!important;padding-right:10px">
                    <a href="javascript:;" style="display:flex;align-items:center;color:#fff"><img
                            src="/dist/images/user.png" class="layui-nav-img"><span class="user_name"></span></a>
                    <dl class="layui-nav-child pop_menu">
                        <dd><i class="iconfont">&#xe62f;</i><a href="change_password.html#7">用户资料</a></dd>
                        <dd><i class="iconfont">&#xe601;</i><a href="javascript:void(0)" class="refresh_cache">刷新缓存</a>
                        </dd>
                        <dd><i class="iconfont">&#xe634;</i><a href="login_out.html">退出登录</a></dd>
                    </dl>
                </li>
            </ul>
        </div>
        <!-- 导航 -->
        <ul class="layui-nav layui-nav-tree layui-nav-side" id="menuContainer" lay-bar="disabled" lay-accordion>
            <div style="margin: 50px 0;color: #bbb;text-align: center;">菜单加载中...</div>
        </ul>
        <!-- 主体内容 -->
        <div class="layui-body">
            <div class="body_child">
                <div class="layui-panel">
                    <div class="layui-card-header">
                        <div class="layui-row" style="padding-top:10px;">
                            <div class="layui-col-md11 layui-col-sm10" style="display: flex;">
                                <div>文章管理</div>
                            </div>
                            <div class="layui-col-md1 layui-col-sm2" style="text-align: right;">
                                <button class="layui-btn layui-btn-primary layui-border-red perm_check_btn"
                                    lay-event="add" onclick="add()">
                                    <i class="layui-icon">&#xe654;</i> 新增文章
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="layui-padding-3">
                        <div id="data_search">
                            <div class="layui-form">
                                <div class="layui-row">
                                    <div class="layui-col-md3" style="min-width:380px;">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label">搜索</label>
                                            <div class="layui-input-block">
                                                <input type="text" name="key" placeholder="请输入文章标题或内容" autocomplete="off"
                                                    class="layui-input" lay-affix="clear">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-md3">
                                        <div class="layui-form-item">
                                            <div class="layui-input-inline" style="margin-left: 10px;">
                                                <button class="layui-btn" lay-submit lay-filter="search"
                                                    style="width: 120px;">筛选</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <table id="mytable" lay-filter="mytable" class="layui-table" lay-skin="row"></table>
                    </div>
                </div>
                <br>
                <br>
            </div>
        </div>
    </div>

    <script type="text/html" id="TPL-bar">
        <button class="layui-btn layui-btn-xs perm_check_btn" lay-event="detail">详情</button>
        <button class="layui-btn layui-btn-xs perm_check_btn" lay-event="edit">编辑</button>
        <button class="layui-btn layui-btn-xs layui-btn-danger perm_check_btn" lay-event="del">删除</button>
    </script>

    <script>
        layui.use(['element', 'layer', 'util'], function () {
            var element = layui.element;
            var form = layui.form;
            var layer = layui.layer;
            var util = layui.util;
            var table = layui.table;
            var $ = layui.$;
            var pid = request.get("pid");

            // 页面顶部插入缓存的用户数据
            let local_userinfo = localStorage.getItem('local_userinfo');
            let user_name = "";
            if (local_userinfo) {
                local_userinfo = JSON.parse(local_userinfo);
                user_name = local_userinfo.Name + " ( " + local_userinfo.Roles + " )";
            } else {
                user_name = "未登录";
            }
            $('.user_name').text(user_name);
            render_menu($);
            $('.refresh_cache').click(function () {
                refresh_cache($);
            });

            // 用户ID映射
            let userMap = {};
            // 分类ID映射
            let categoryMap = {};

            // 获取用户映射
            $.ajax({
                url: serverUrl + '/admin/user/list_low',
                type: 'POST',
                async: false,
                success: function (res) {
                    if (res.code === 200 && res.data) {
                        res.data.forEach(function (item) {
                            userMap[item.ID] = item.Name;
                        });
                    }
                }
            });

            // 获取分类映射
            $.ajax({
                url: serverUrl + '/admin/article_category/list',
                type: 'POST',
                async: false,
                success: function (res) {
                    if (res.code === 200 && res.data) {
                        res.data.forEach(function (item) {
                            categoryMap[item.ID] = item.Title;
                        });
                    }
                }
            });

            //引入TABLE模块
            layer.load(2);
            table.render({
                elem: '#mytable'
                , even: true
                , url: serverUrl + "/admin/article/list"
                , method: 'post'
                , where: {
                    pid: pid
                }
                , response: {
                    statusName: 'code'
                    , statusCode: 200
                    , msgName: 'msg'
                    , countName: 'count'
                    , dataName: 'data'
                }
                , cols: [[
                    { field: 'ID', title: 'ID', align: 'center', width: 80 }
                    , { 
                        field: 'Pid', title: '文章分类', align: 'center', width: 120, templet: function (d) {
                            return categoryMap[d.Pid] || '未知分类';
                        }
                    }
                    , { 
                        field: 'User_id', title: '发布人', align: 'center', width: 120, templet: function (d) {
                            return userMap[d.User_id] || '未知用户';
                        }
                    }
                    , { field: 'Title', title: '文章标题', align: 'left' }
                    , { 
                        field: 'CoverPic', title: '封面图片', align: 'center', width: 150, templet: function (d) {
                            if (d.CoverPic) {
                                return '<img src="' + global_article_pic_path + d.CoverPic + '" onclick="layer.photos({photos: {title: \'查看图片\', data: [{src: \'' + (global_article_pic_path + d.CoverPic) + '\'}]}, footer: false})" class="table_list_img">';
                            } else {
                                return '无';
                            }
                        }
                    }
                    , { field: 'Visits', title: '浏览次数', align: 'center', width: 100 }
                    , { field: 'Sort', title: '排序', align: 'center', width: 80 }
                    , {
                        field: 'Create_time', title: '创建时间', width: 180, align: 'center',
                        templet: function (d) {
                            return d.Create_time.replace('T', ' ').replace('Z', '');
                        }
                    }
                    , { title: '操作', align: 'center', toolbar: '#TPL-bar', width: 280, fixed: 'right' }
                ]]
                , page: true
                , limit: 10
                , done: function () {
                    layer.closeAll('loading');
                }
            });

            //监听工具条
            table.on('tool(mytable)', function (obj) {
                var data = obj.data;
                if (obj.event === 'edit') {
                    layer.open({
                        type: 2,
                        title: '编辑文章',
                        area: ['900px', '800px'],
                        shadeClose: true,
                        content: 'article_edit.html?id=' + data.ID
                    });
                } else if (obj.event === 'detail') {
                    layer.open({
                        type: 2,
                        title: '查看文章',
                        area: ['900px', '800px'],
                        shadeClose: true,
                        content: 'article_detail.html?id=' + data.ID
                    });
                } else if (obj.event === 'del') {
                    layer.confirm('确定要删除该文章吗？', function (index) {
                        layer.close(index);
                        layer.load(2);
                        $.ajax({
                            url: serverUrl + '/admin/article/del',
                            type: 'POST',
                            data: {
                                id: data.ID
                            },
                            success: function (res) {
                                layer.closeAll('loading');
                                if (res.code === 200) {
                                    layer.msg('删除成功');
                                    obj.del();
                                } else {
                                    layer.msg(res.msg || '删除失败');
                                }
                            },
                            error: function () {
                                layer.closeAll('loading');
                                layer.msg('网络错误，请稍后重试');
                            }
                        });
                    });
                }
            });

            //监听搜索
            form.on('submit(search)', function (data) {
                layer.load(2);
                data.field.pid = pid;
                table.reload('mytable', {
                    where: data.field
                    , page: {
                        curr: 1
                    }
                });
                return false;
            });

            // 添加文章函数定义
            window.add = function () {
                layer.open({
                    type: 2,
                    title: '新增文章',
                    shadeClose: true,
                    area: ['900px', '800px'],
                    content: 'article_add.html'
                });
            };
        });
    </script>
</body>

</html> 