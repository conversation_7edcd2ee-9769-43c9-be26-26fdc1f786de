<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 赠品详情</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
</head>

<body>
    <div class="layui-padding-3">
        <form class="layui-form" lay-filter="form">
            <div class="layui-row layui-col-space15">
                <!-- 第一列 -->
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">赠品名称</label>
                        <div class="layui-input-block">
                            <div class="detail-text" id="name"></div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">出厂日期</label>
                        <div class="layui-input-block">
                            <div class="detail-text" id="factory_date"></div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">参考价格</label>
                        <div class="layui-input-block">
                            <div class="detail-text" id="price"></div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">创建时间</label>
                        <div class="layui-input-block">
                            <div class="detail-text" id="Create_time"></div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button type="button" class="layui-btn layui-btn-primary" onclick="closeModalWindow()">关闭</button>
                </div>
            </div>
        </form>
    </div>

    <script>
        layui.use(['form'], function () {
            var form = layui.form;
            var $ = layui.$;

            // 获取URL中的ID参数
            var id = request.get('id');
            if (!id) {
                layer.msg('ID不能为空', { icon: 2 });
                return;
            }

            // 加载赠品详情数据
            $.ajax({
                url: serverUrl + '/admin/gift/detail',
                type: 'POST',
                data: { id: id },
                success: function (res) {
                    if (res.code === 200) {
                        var data = res.data;
                        $('#name').text(data.Name || '');
                        $('#factory_date').text(data.Factory_date ? data.Factory_date.split('T')[0] : '');
                        $('#price').text(data.Price || '');
                        $('#Create_time').text(data.Create_time ? data.Create_time.replace('T', ' ').replace('Z', '') : '');
                    } else {
                        layer.msg(res.msg || '加载失败', { icon: 2 });
                    }
                },
                error: function (res) {
                    layer.msg(res.responseJSON.msg, { icon: 2, time: 1000 });
                }
            });
        });

        function closeModalWindow() {
            var index = parent.layer.getFrameIndex(window.name);
            parent.layer.close(index);
        }
    </script>
</body>

</html> 