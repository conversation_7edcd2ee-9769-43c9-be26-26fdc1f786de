<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 药材详情</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
</head>

<body>
    <div class="layui-padding-3">
        <div class="layui-row layui-col-space15">
            <!-- 第一列 -->
            <div class="layui-col-md6">
                <div class="layui-form-item">
                    <label class="layui-form-label">药材名称</label>
                    <div class="layui-input-block">
                        <div class="detail-text" id="name"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">药材分类</label>
                    <div class="layui-input-block">
                        <div class="detail-text" id="category"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">规格</label>
                    <div class="layui-input-block">
                        <div class="detail-text" id="spec"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">批次</label>
                    <div class="layui-input-block">
                        <div class="detail-text" id="batch"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">批号</label>
                    <div class="layui-input-block">
                        <div class="detail-text" id="code"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">单位</label>
                    <div class="layui-input-block">
                        <div class="detail-text" id="unit"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">性味</label>
                    <div class="layui-input-block">
                        <div class="detail-text" id="property"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">产地</label>
                    <div class="layui-input-block">
                        <div class="detail-text" id="origin"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">采收时间</label>
                    <div class="layui-input-block">
                        <div class="detail-text" id="harvestTime"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">效期(天)</label>
                    <div class="layui-input-block">
                        <div class="detail-text" id="validity_days"></div>
                    </div>
                </div>
            </div>

            <!-- 第二列 -->
            <div class="layui-col-md6">
                <div class="layui-form-item">
                    <label class="layui-form-label">储存条件</label>
                    <div class="layui-input-block">
                        <div class="detail-text" id="storage"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">常用剂量</label>
                    <div class="layui-input-block">
                        <div class="detail-text" id="dosage"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">用法</label>
                    <div class="layui-input-block">
                        <div class="detail-text" id="directions"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">药效</label>
                    <div class="layui-input-block">
                        <div class="detail-text" id="effect"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">适应症</label>
                    <div class="layui-input-block">
                        <div class="detail-text" id="indication"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">禁忌症</label>
                    <div class="layui-input-block">
                        <div class="detail-text" id="contraindication"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">副作用</label>
                    <div class="layui-input-block">
                        <div class="detail-text" id="sideeffect"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">相互作用</label>
                    <div class="layui-input-block">
                        <div class="detail-text" id="interaction"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">参考价格</label>
                    <div class="layui-input-block">
                        <div class="detail-text" id="price"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">建档时间</label>
                    <div class="layui-input-block">
                        <div class="detail-text" id="create_time"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部按钮 -->
        <div class="layui-form-item" style="text-align: center; ">
            <button type="button" class="layui-btn layui-btn-primary" onclick="closeModalWindow()">关闭</button>
        </div>
    </div>

    <script>
        layui.use(['layer'], function () {
            var layer = layui.layer;
            var $ = layui.$;

            // 获取URL参数
            var id = request.get('id');
            if (!id) {
                layer.msg('缺少必要参数', { icon: 2 });
                return;
            }

            // 加载数据
            layer.load(2);
            
            // 先加载分类数据
            var categoryMap = {};
            $.ajax({
                url: serverUrl + '/admin/drug_category/list',
                type: 'POST',
                async: false,
                success: function(res) {
                    if (res.code === 200) {
                        res.data.forEach(function(item) {
                            categoryMap[item.ID] = item.Name;
                        });
                    }
                }
            });

            // 加载药材详情
            $.ajax({
                url: serverUrl + '/admin/drug/detail',
                type: 'POST',
                data: { id: id },
                success: function (res) {
                    layer.closeAll('loading');
                    if (res.code === 200) {
                        var data = res.data;
                        $('#name').text(data.Name || '--');
                        $('#category').text(data.Pid ? (categoryMap[data.Pid] || '--') : '--');
                        $('#batch').text(data.Batch || '--');
                        $('#code').text(data.Code || '--');
                        $('#unit').text(data.Unit || '--');
                        $('#spec').text(data.Spec || '--');
                        $('#property').text(data.Property || '--');
                        $('#origin').text(data.Origin || '--');
                        $('#harvestTime').text(data.HarvestTime || '--');
                        $('#validity_days').text(data.Validity_Days || '--');
                        $('#storage').text(data.Storage || '--');
                        $('#dosage').text(data.Dosage || '--');
                        $('#directions').text(data.Directions || '--');
                        $('#effect').text(data.Effect || '--');
                        $('#indication').text(data.Indication || '--');
                        $('#contraindication').text(data.Contraindication || '--');
                        $('#sideeffect').text(data.Sideeffect || '--');
                        $('#interaction').text(data.Interaction || '--');
                        $('#price').text(data.Price ? (data.Price + '元/' + data.Unit) : '--');
                        $('#create_time').text(data.Create_time ? data.Create_time.replace('T', ' ').replace('Z', '') : '--');
                    } else {
                        layer.msg(res.msg || '加载失败', { icon: 2 });
                    }
                },
                error: function (res) {
                    layer.closeAll('loading');
                    layer.msg(res.responseJSON.msg, { icon: 2, time: 1000 });
                }
            });
        });

        function closeModalWindow() {
            var index = parent.layer.getFrameIndex(window.name);
            parent.layer.close(index);
        }
    </script>
</body>

</html> 