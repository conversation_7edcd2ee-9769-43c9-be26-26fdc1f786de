<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 赠品库存详情</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
</head>

<body>
    <div class="layui-padding-3">
        <div class="layui-row layui-col-space15">
            <!-- 第一列 -->
            <div class="layui-col-md6">
                <div class="layui-form-item">
                    <label class="layui-form-label">赠品ID</label>
                    <div class="layui-input-block">
                        <div class="detail-text" id="gift_id"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">赠品名称</label>
                    <div class="layui-input-block">
                        <div class="detail-text" id="gift_name"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">库存数量</label>
                    <div class="layui-input-block">
                        <div class="detail-text" id="quantity"></div>
                    </div>
                </div>
            </div>

            <!-- 第二列 -->
            <div class="layui-col-md6">
                <div class="layui-form-item">
                    <label class="layui-form-label">进货价</label>
                    <div class="layui-input-block">
                        <div class="detail-text" id="price"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">建档时间</label>
                    <div class="layui-input-block">
                        <div class="detail-text" id="create_time"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部按钮 -->
        <div class="layui-form-item" style="text-align: center; ">
            <button type="button" class="layui-btn layui-btn-primary" onclick="closeModalWindow()">关闭</button>
        </div>
    </div>

    <script>
        layui.use(['layer'], function () {
            var layer = layui.layer;
            var $ = layui.$;

            // 获取URL参数
            var id = request.get('id');
            if (!id) {
                layer.msg('缺少必要参数', { icon: 2 });
                return;
            }

            // 加载数据
            layer.load(2);
            $.ajax({
                url: serverUrl + '/admin/warehouse_gifts/detail',
                type: 'POST',
                data: { id: id },
                success: function (res) {
                    layer.closeAll('loading');
                    if (res.code === 200) {
                        var data = res.data;
                        $('#gift_id').text(data.Gift_id || '--');
                        $('#quantity').text(data.Quantity || '--');
                        $('#price').text(data.Price ? (data.Price + '元') : '--');
                        $('#create_time').text(data.Create_time ? data.Create_time.replace('T', ' ').replace('Z', '') : '--');
                        
                        // 获取赠品名称
                        $.ajax({
                            url: serverUrl + '/admin/tools/warehouse2name',
                            type: 'POST',
                            data: { 
                                wid: id,
                                type: 1
                            },
                            success: function(nameRes) {
                                if (nameRes.code === 200) {
                                    $('#gift_name').text(nameRes.data.Name || '--');
                                } else {
                                    $('#gift_name').text('--');
                                }
                            },
                            error: function() {
                                $('#gift_name').text('--');
                            }
                        });
                    } else {
                        layer.msg(res.msg || '加载失败', { icon: 2 });
                    }
                },
                error: function (res) {
                    layer.closeAll('loading');
                    layer.msg(res.responseJSON.msg, { icon: 2, time: 1000 });
                }
            });
        });

        function closeModalWindow() {
            var index = parent.layer.getFrameIndex(window.name);
            parent.layer.close(index);
        }
    </script>
</body>

</html>