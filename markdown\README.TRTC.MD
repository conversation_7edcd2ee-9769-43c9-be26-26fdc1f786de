# 慕生堂在线诊室系统

## 项目说明
本项目是一个基于Golang和LAYUI的互联网医院系统，其涵盖患者、病历、线上诊室（TRTC）、订单、处方、中药调剂、药材库存管理、药材供应商管理、成品药管理、赠品管理、仓储订单管理、售后、统计、角色RBAC管理等几个大模块

## 当前MARKDOWN文档主要针对模块为：线上视频诊室（TRTC）

## 视频聊天室功能
### 第一阶段：医生端（WEB）
- 支持三人视频通话（医生、医助、患者）
- 医生可以录制与患者的视频（不含医助视频）
- 录制的视频保存在本地，文件名格式：诊室_[房间ID]_[时间戳].webm
- 房间人数限制为3人，超出将提示房间已满
- 进入房间条件：诊室表(rtc_room)中doc_id、asst_id、pat_pro_id三个ID同时存在

### 角色说明
系统中定义了三种角色：
- 医生（role_id: 4）：使用WEB端，可以控制问诊开始/结束和视频录制
- 医助（role_id: 3）：使用小程序端，可以参与视频通话
- 患者（role_id: 0）：使用小程序端，可以参与视频通话

### TRTC用户标识说明
为了在TRTC中同时传递角色信息和用户ID，采用复合格式的userId：
- 医生端：`4_医生用户ID`（例如：4_1 表示角色为4的用户ID为1的医生）
- 医助端：`3_医助用户ID`（例如：3_5 表示角色为3的用户ID为5的医助）
- 患者端：`0_患者ID`（例如：0_10 表示角色为0的用户ID为10的患者）

这种格式设计的原因：
1. TRTC SDK限制了可传递的参数，需要在有限的参数中传递更多信息
2. 通过复合ID可以同时识别用户角色和具体用户
3. 使用0作为患者角色ID，避免与医生、医助等职能角色的用户ID冲突
4. 便于在音视频通话中快速识别用户身份，特别是在录制时区分患者流
5. 支持跨表用户管理，医生/医助和患者可以使用不同的用户表

使用方式：
1. 服务端生成UserSig时使用复合ID（roleId_userId）
2. 客户端通过分割复合ID获取角色信息
3. 录制时通过角色ID筛选需要录制的视频流
4. 用户管理时可以通过角色ID区分不同类型的用户

### 视频录制规则
1. 只有医生可以控制录制功能
2. 录制条件：
   - 医生已开始问诊
   - 患者已进入房间（通过role_id判断）
   - 医助是否在场不影响录制
3. 录制内容：
   - 只录制医生和患者的视频画面
   - 不录制医助的视频画面
   - 录制文件包含所有参与者的音频
4. 录制控制：
   - 患者未进入时，录制按钮处于禁用状态
   - 患者离开时，如果正在录制会自动停止
   - 问诊结束时，如果正在录制会自动停止

### 第二阶段：小程序端（待开发）
- 医助端小程序：
  * 使用role_id=3进入房间
  * 可以看到所有参与者的视频
  * 自己的视频不会被录制
- 患者端小程序：
  * 使用role_id=0进入房间
  * 可以看到所有参与者的视频
  * 自己的视频会被录制

## 技术栈
- 后端：Golang
- 前端：HTML, JavaScript, CSS, Layui
- 视频通话：腾讯云TRTC（自由集成方案）
- 小程序：微信小程序（第二阶段）

## TRTC房间机制说明
1. 房间创建：
   - 诊室创建时，会生成一个唯一的房间ID
   - 房间状态为0表示待诊，1表示问诊中，2表示已结束

2. 用户进入流程：
   - 医生端（WEB）：
     * 点击"开始问诊"按钮进入房间
     * 使用UserSig鉴权进入TRTC房间
     * 可以控制问诊开始/结束和视频录制
   - 医助和患者（小程序）：
     * 通过扫码或链接进入对应房间
     * 使用各自的UserSig鉴权
     * 自动加入TRTC音视频通话

3. 实时通信：
   - 基于TRTC的实时音视频通道
   - 支持多人实时语音/视频通话
   - 自动的断线重连机制
   - 房间状态同步机制

## 目录结构
- `/interface/admin/rtc_room_detail.html` - 医生端视频界面
- `/internal/app/admin/admin_extra.go` - 后端业务逻辑
- `/internal/app/admin/trtc.go` - TRTC相关接口
- `/pkg/common/base64url.go、/pkg/common/TLSSigAPI.go` - 腾讯云官方UserSig生成实现（勿修改）
- `/internal/routes/admin.go` - 后端路由配置
- `/internal/app/patient/trtc.go` - 患者端TRTC相关接口

## 开发进度
- [x] 第一阶段：医生端WEB开发
  - [x] 视频通话界面
  - [x] 视频录制功能（本地保存）
  - [x] 房间管理和状态控制
  - [x] UserSig生成和鉴权
  - [x] 问诊控制（开始/结束）
- [ ] 第二阶段：小程序端开发（待开始）

## 注意事项
1. 视频录制仅录制医生与患者的视频
2. 房间人数限制为3人
3. 视频文件保存在本地，按诊室ID和时间命名
4. TRTC配置信息（SDKAppID和密钥）已在配置文件中设置

## 更新日志
- 2025-02-08：
  * 完善了UserSig生成逻辑，使用官方实现
  * 添加了问诊控制按钮（开始/结束）
  * 修改视频保存为本地保存方式
  * 优化了按钮状态管理
  * 完善了TRTC房间机制
  * 修复了重复进入房间的问题
  * 优化了问诊结束时的视频关闭逻辑

## 库存管理模块
### 数据库设计
系统包含两个核心表：

1. 供应商信息表(supplier)
- 存储供应商基本信息
- 包含社会统一代码、名称、地址等基础信息
- 包含银行账户、联系人等业务信息
- 支持启用/禁用状态管理

2. 药品品种表(medicine)
- 存储药品基本信息
- 支持中草药、西药、中成药三种类型
- 包含批次、批号、单位、效期等信息
- 支持打包/散装两种发药规则
- 支持启用/禁用状态管理
