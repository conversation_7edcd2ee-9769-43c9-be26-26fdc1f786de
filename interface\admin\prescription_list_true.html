<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 处方列表</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
    <script src="/dist/js/xm-select.js"></script>
    <style>
        .layui-table-cell {
            height: auto;
            line-height: 28px;
        }

        xm-select {
            border: 0 !important;
        }

        xm-select>.xm-body {
            min-width: 200px !important;
            padding: 10px !important;
        }
    </style>
</head>

<body>
    <div class="layui-layout layui-layout-admin">
        <!-- 头部 -->
        <div class="layui-header">
            <div class="layui-logo">幸年堂</div>
            <ul class="layui-nav layui-layout-right">
                <li class="layui-nav-item" style="background-color:transparent!important;padding-right:10px">
                    <a href="javascript:;" style="display:flex;align-items:center;color:#fff"><img
                            src="/dist/images/user.png" class="layui-nav-img"><span class="user_name"></span></a>
                    <dl class="layui-nav-child pop_menu">
                        <dd><i class="iconfont">&#xe62f;</i><a href="change_password.html#7">用户资料</a></dd>
                        <dd><i class="iconfont">&#xe601;</i><a href="javascript:void(0)" class="refresh_cache">刷新缓存</a>
                        </dd>
                        <dd><i class="iconfont">&#xe634;</i><a href="login_out.html">退出登录</a></dd>
                    </dl>
                </li>
            </ul>
        </div>
        <!-- 导航 -->
        <ul class="layui-nav layui-nav-tree layui-nav-side" id="menuContainer" lay-bar="disabled" lay-accordion>
            <div style="margin: 50px 0;color: #bbb;text-align: center;">菜单加载中...</div>
        </ul>
        <!-- 主体内容 -->
        <div class="layui-body">
            <div class="body_child">
                <div class="layui-panel">
                    <div class="layui-card-header">
                        <div class="layui-row" style="padding-top:10px;">
                            <div class="layui-col-md11 layui-col-sm10" style="display: flex;">
                                <div>处方列表</div>
                                <div style="font-size: 14px;margin-left: 20px;color: #999;">想看针对病历的处方开具情况？<a
                                        href="/admin/prescription_list.html" style="margin-left: 10px;" class="layui-badge layui-bg-red">返回病历列表</a></div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-padding-3" style="padding-top: 0 !important;min-height: 800px;">



                        <!-- 通用顶部筛选 -->
                        <div id="top_data_search">
                            <div class="layui-form" style="margin: 20px 0 0 0;">
                                <div class="layui-row">
                                    <!-- 科室+医生组合 -->
                                    <div class="layui-col-md4">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label">科室</label>
                                            <div class="layui-input-block" style="display: flex; gap: 10px;">
                                                <select name="doc_dep_id" id="doc_dep_id" lay-filter="doc_dep_id"
                                                    style="width: 48%;"></select>
                                                <select name="doc_id" id="doc_id" style="width: 48%;"></select>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 售前/售后部门+人员组合 -->
                                    <div class="layui-col-md4">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label">销售</label>
                                            <div class="layui-input-block" style="display: flex; gap: 10px;">
                                                <div id="asst_dep_id" style="width: 48%;"></div>
                                                <select name="asst_id" id="asst_id" style="width: 48%;"></select>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 患者搜索 -->
                                    <div class="layui-col-md3">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label">患者</label>
                                            <div class="layui-input-block">
                                                <input type="text" name="wd" id="dropdown_input"
                                                    lay-filter="searchFilter" placeholder="请输入电话或姓名" autocomplete="off"
                                                    class="layui-input" lay-affix="clear">
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 筛选按钮 -->
                                    <div class="layui-col-md1" style="text-align: center;">
                                        <div class="layui-form-item">
                                            <div class="layui-input-block">
                                                <button class="layui-btn" lay-submit lay-filter="search"
                                                    style="min-width: 100px;">筛选</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>








                        <table id="mytable" lay-filter="mytable" class="layui-table" lay-skin="row"></table>
                    </div>
                </div>
                <br>
                <br>
            </div>
        </div>
    </div>


    <script type="text/html" id="TPL-bar">
            <a class="layui-btn layui-btn-xs perm_check_btn" lay-event="show" res_id="56">详情</a>
    </script>

    <script>
        // 声明全局变量，用于存储xm-select实例
        var keshiAsstSelect;

        layui.use(['element', 'layer', 'util'], function () {
            var element = layui.element;
            var form = layui.form;
            var layer = layui.layer;
            var util = layui.util;
            var laydate = layui.laydate;
            var pat_pro_id = "";
            var table = layui.table;
            var dropdown = layui.dropdown;
            var $ = layui.$;
            // 渲染日期
            laydate.render({
                elem: '#ID-laydate-demo'
            });
            // 页面顶部插入缓存的用户数据
            let local_userinfo = localStorage.getItem('local_userinfo');
            let user_name = "";
            let currentUserIsSales = false;
            let currentUserIsDoctor = false;
            let currentUserDeptId = 0;
            let currentUserId = 0;
            let currentUserRoleIds = "";

            if (local_userinfo) {
                local_userinfo = JSON.parse(local_userinfo);
                user_name = local_userinfo.Name + " ( " + local_userinfo.Roles + " )";

                // 获取当前用户的角色ID、部门ID和用户ID
                currentUserRoleIds = local_userinfo.Role_ids || "";
                currentUserDeptId = local_userinfo.Department_id || 0;
                currentUserId = local_userinfo.Id || 0;

                // 判断是否为售前(3)或售后(9)用户
                if (currentUserRoleIds.split(',').includes(global_asst_role_id) || currentUserRoleIds.split(',').includes(global_after_asst_role_id)) {
                    currentUserIsSales = true;
                }

                // 判断是否为医生(4)用户
                if (currentUserRoleIds.split(',').includes(global_doctor_role_id)) {
                    currentUserIsDoctor = true;
                }
            } else {
                user_name = "未登录";
            }
            $('.user_name').text(user_name);

            render_menu($);
            $('.refresh_cache').click(function () {
                refresh_cache($);
            });
            //引入TABLE模块
            layer.load(2);
            table.render({
                elem: '#mytable'
                , even: true
                , url: serverUrl + "/admin/prescription/list"
                , method: 'post'
                , response: {
                    statusName: 'code'
                    , statusCode: 200
                    , msgName: 'msg'
                    , countName: 'count'
                    , dataName: 'data'
                }
                , cols: [[
                    { field: 'ID', title: '处方ID', align: 'center', width: 120 ,templet: function (d) {
                        return 'C' + d.ID;
                    }}
                    , {
                        field: 'Pat_id', title: '患者帐号', align: 'center', width: 120
                    }
                    , {
                        field: 'Pat_pro_id', title: '患者', align: 'center', width: 120, templet: function (d) {
                            return '<div class="pat_pro_id" data-id="' + d.Pat_pro_id + '">-</div>';
                        }
                    }
                    , {
                        title: '医生', align: 'center', width: 120, templet: function (d) {
                            return '<div class="user_need_ajax" data-id="' + d.Doc_id + '">-</div>';
                        }
                    }
                    , {
                        title: '医助', align: 'center', width: 120, templet: function (d) {
                            return '<div class="user_need_ajax" data-id="' + d.Asst_id + '">-</div>';
                        }
                    }
                    , { field: 'Diagnosis', title: '诊断结果', align: 'left' }
                    , { field: 'Tx_plan', title: '治疗计划', align: 'left' }
                    , {
                        field: 'Status', title: '订单状态', align: 'center', width: 200, templet: function (d) {
                            //0状态占位符 1已开方，待审核 2已驳回 3签章过半待调剂 4已调剂 5已入成品库 6已出库发药 7已异常废弃
                            let status_index = d.Status;
                            if (status_index == 7) {
                                return '<div style="color:red;"><i class="layui-icon layui-icon-close-fill"></i>已异常废弃</div>';
                            } else if (status_index == 2) {
                                return '<div style="color:#ff8000;"><i class="layui-icon layui-icon-close-fill"></i>已驳回</div>';
                            } else {
                                return Pre_Status[d.Status];
                            }
                        }
                    }
                    , {
                        field: 'Create_time', title: '建立时间', align: 'center', templet: function (d) {
                            return Utc2time(d.Create_time);
                        }
                    }
                    , { title: '操作', align: 'center', width: 150, toolbar: '#TPL-bar' }
                ]]
                , done: function (res, curr, count) {
                    // 解决进度条组件在TABLE中不显示的BUG
                    element.render("progress");
                    //检查菜单权限
                    render_button($);

                    // 获取部门/科室数据 - 使用本地存储
                    let local_departments = localStorage.getItem('local_departments') ? JSON.parse(localStorage.getItem('local_departments')) : [];
                    $('.department_need_ajax').each(function () {
                        let id = $(this).data('id');
                        let department = id2department(id, local_departments, 0);
                        $(this).text(department);
                    });
                    layer.closeAll('loading');

                    //高效-医助、医生等职能部分用户遍历并ID合并
                    let user_arr = [];
                    $('.user_need_ajax').each(function () {
                        let id = $(this).data('id');
                        user_arr.push(id);
                    });
                    user_arr = [...new Set(user_arr)];//去重
                    layer.load(2);
                    $.ajax({
                        url: '/admin/user/list_low',
                        data: {
                            user_id_list: user_arr.join(','),
                        },
                        type: 'post',
                        success: function (res) {
                            layer.closeAll('loading');
                            if (res.code === 200) {
                                let data = res.data;
                                for (let i = 0; i < data.length; i++) {
                                    let id = data[i].ID;
                                    let name = data[i].Name;
                                    $('.user_need_ajax[data-id="' + id + '"]').text(name);
                                }
                            } else {
                                layer.msg(res.msg, { icon: 2, time: 1000 });
                            }
                        }, error: function (res) {
                            layer.closeAll('loading');
                            layer.msg(res.responseJSON.msg, { icon: 2, time: 1000 });
                        }
                    });

                    //高效-患者ID2姓名
                    let pat_pro_id_arr = [];
                    $('.pat_pro_id').each(function () {
                        let id = $(this).data('id');
                        pat_pro_id_arr.push(id);
                    });
                    pat_pro_id_arr = [...new Set(pat_pro_id_arr)];//去重
                    layer.load(2);
                    $.ajax({
                        url: '/admin/patient_profile/patient_profile_ids2name',
                        data: {
                            ids: pat_pro_id_arr.join(','),
                        },
                        type: 'post',
                        success: function (res) {
                            layer.closeAll('loading');
                            if (res.code === 200) {
                                let data = res.data;
                                for (let i = 0; i < data.length; i++) {
                                    let id = data[i].ID;
                                    let name = data[i].Name;
                                    $('.pat_pro_id[data-id="' + id + '"]').text(name);
                                }
                            }
                        }, error: function (res) {
                            layer.closeAll('loading');
                            layer.msg(res.responseJSON.msg, { icon: 2, time: 1000 });
                        }
                    });
                },
                page: true,
                limit: 12,
            })
            //监听工具条
            table.on('tool(mytable)', function (obj) {
                var data = obj.data;
                var layEvent = obj.event;
                if (layEvent == "show") {
                    window.location.href = 'prescription_show.html?id=' + data.ID + '#/admin/prescription_list.html';
                }
            });
            form.on('input-affix(searchFilter)', function (data) {
                pat_pro_id = "";
            });
            form.on('submit(search)', function (data) {
                // 获取xm-select中选中的部门ID
                if (keshiAsstSelect) {
                    let selectedDepts = keshiAsstSelect.getValue();
                    // 如果有选中部门，添加到表单数据
                    if (selectedDepts.length > 0) {
                        // 提取ID值并合并为逗号分隔的字符串
                        let deptIds = selectedDepts.map(item => item.Id || item.id).join(',');
                        data.field.asst_dep_id = deptIds;
                    }
                }

                data.field.pat_pro_id = pat_pro_id;

                // 删除可能存在的多余字段
                if (data.field.select !== undefined) {
                    delete data.field.select;
                }

                table.reload('mytable', {
                    where: data.field,
                    page: {
                        curr: 1 //重新从第 1 页开始
                    }
                });
                return false;
            });
            //新增用户
            $('.create_btn').on('click', function () {
                window.location.href = 'order_add.html';
            });

            // 统一获取部门数据，使用本地存储
            layer.load(2);
            // 从本地存储获取部门数据
            let allDepartments = localStorage.getItem('local_departments') ? JSON.parse(localStorage.getItem('local_departments')) : [];
            layer.closeAll('loading');

            // 1. 渲染部门下拉框（用于Department下拉菜单）
            let treeData = format_to_treedata_department(allDepartments, global_default_store_id);
            treeData = renderDropdownItems(treeData);
            dropdown.render({
                elem: '#Department',
                id: 'DropdownID',
                data: [],
                content: '<div class="dropdown-menu">' + treeData + '</div>',
                ready: function (elemPanel, elem) {
                    elemPanel.on('click', '.dropdown-item-leaf', function () {
                        $('#Department').val($(this).text());
                        dropdown.close('DropdownID');
                    });
                }
            });

            // 定义加载医生列表的函数
            function loadDoctorsByDepartment(keshi_id) {
                if (keshi_id) {
                    layer.load(2);
                    $.ajax({
                        url: '/admin/user/list_low',
                        data: {
                            role_id: 4,
                            department_id: keshi_id,
                        },
                        type: 'post',
                        success: function (res) {
                            layer.closeAll('loading');
                            if (res.code === 200) {
                                let data = res.data;
                                let html = '<option value="">选择医生</option>';
                                if (data && data.length > 0) {
                                    for (let i = 0; i < data.length; i++) {
                                        html += '<option value="' + data[i].ID + '">' + data[i].Name + '</option>';
                                    }

                                    // 如果当前用户是医生，并且在当前科室中，设置默认选中和只读状态
                                    if (currentUserIsDoctor && currentUserDeptId == keshi_id && currentUserId > 0) {
                                        $('#doc_id').html(html);
                                        $('#doc_id').val(currentUserId);
                                        $('#doc_id').attr('disabled', 'disabled');
                                        // 使用内联样式而不是添加类，避免影响布局
                                        $('#doc_id').css('background-color', '#f2f2f2');
                                        form.render('select');
                                    } else {
                                        $('#doc_id').html(html);
                                        form.render('select');
                                    }
                                } else {
                                    layer.msg('该科室暂无医生', { icon: 2, time: 1000 });
                                    html = '<option value="">该科室暂无医生</option>';
                                    $('#doc_id').html(html);
                                    form.render('select');
                                }
                            } else {
                                layer.msg(res.msg, { icon: 2, time: 1000 });
                            }
                        }, error: function (res) {
                            layer.closeAll('loading');
                            layer.msg(res.responseJSON.msg, { icon: 2, time: 1000 });
                        }
                    });
                } else {
                    $('#doc_id').html('<option value="">选择医生</option>');
                    form.render('select');
                }
            }

            // 2. 单独请求医疗部门数据，用于科室下拉框
            // 这里需要单独调用 /admin/department/list 接口获取医疗部门数据
            $.ajax({
                url: '/admin/department/list',
                data: {
                    name: '医疗部门',
                    store_id: global_default_store_id,
                },
                type: 'post',
                success: function (medicalRes) {
                    if (medicalRes.code === 200) {
                        let medicalData = medicalRes.data;
                        let medicalHtml = '<option value="">选择科室</option>';
                        for (let i = 0; i < medicalData.length; i++) {
                            medicalHtml += '<option value="' + medicalData[i].id + '">' + medicalData[i].name + '</option>';
                        }
                        $('#doc_dep_id').html(medicalHtml);

                        // 如果当前用户是医生，设置默认选中和只读状态
                        if (currentUserIsDoctor && currentUserDeptId > 0) {
                            // 设置科室下拉框为只读
                            $('#doc_dep_id').val(currentUserDeptId);
                            $('#doc_dep_id').attr('disabled', 'disabled');
                            // 使用内联样式而不是添加类，避免影响布局
                            $('#doc_dep_id').css('background-color', '#f2f2f2');

                            // 自动加载该科室下的医生列表
                            loadDoctorsByDepartment(currentUserDeptId);
                        }

                        form.render('select');

                        // 监听科室选择事件
                        form.on('select(doc_dep_id)', function (data) {
                            let keshi_id = data.value;
                            loadDoctorsByDepartment(keshi_id);
                        });
                    } else {
                        layer.msg(medicalRes.msg, { icon: 2, time: 1000 });
                    }
                },
                error: function (res) {
                    layer.msg(res.responseJSON.msg, { icon: 2, time: 1000 });
                }
            });



            // 3. 获取所有售前和售后部门的子部门
            let allSaleDepts = [];
            global_sale_department_ids.forEach(deptId => {
                allDepartments.forEach(dept => {
                    if (dept.Pid === deptId) {
                        allSaleDepts.push(dept);
                    }
                });
            });

            // 再找出这些子部门的所有子部门
            let allChildDepts = [...allSaleDepts];
            allSaleDepts.forEach(dept => {
                function findChildren(parentId) {
                    allDepartments.forEach(d => {
                        if (d.Pid === parentId && !allChildDepts.some(cd => cd.Id === d.Id)) {
                            allChildDepts.push(d);
                            findChildren(d.Id);
                        }
                    });
                }
                findChildren(dept.Id);
            });

            // 创建一个映射，用于快速查找部门
            let deptMap = {};
            allDepartments.forEach(dept => {
                deptMap[dept.Id] = dept;
            });

            // 过滤部门数据 - 只保留用户有权限的部门及其父级部门
            let filteredDepartments = [];
            // 默认只展开售前、售后2个大层级节点
            let expandedKeys = global_sale_department_ids;
            // 判断当前用户是否为销售数据管理员
            let isDataMaster = local_userinfo && local_userinfo.Department_id === global_sale_data_master;
            let userDepIds = [];
            if (isDataMaster) {
                userDepIds = local_userinfo.Dep_ids.split(',').map(id => parseInt(id.trim(), 10)).filter(id => !isNaN(id));
            }
            if (isDataMaster && userDepIds.length > 0) {
                // 数据管理员时，由于所负载节点比较少，所以默认展开所有
                expandedKeys = true;
                // 收集用户可访问的部门及其所有父级部门的ID
                let relevantDeptIds = new Set();
                // 递归查找父级部门
                function collectParentDepts(deptId) {
                    const dept = deptMap[deptId];
                    if (dept) {
                        relevantDeptIds.add(dept.Id);
                        if (dept.Pid && dept.Pid !== 0) {
                            collectParentDepts(dept.Pid);
                        }
                    }
                }
                // 处理每个用户可访问的部门
                userDepIds.forEach(deptId => {
                    collectParentDepts(deptId);
                });
                // 只保留用户有权限的部门及其父级部门
                filteredDepartments = allChildDepts.filter(dept =>
                    relevantDeptIds.has(dept.Id)
                );
            } else {
                // 非数据管理员，显示所有部门及其父级部门
                filteredDepartments = allChildDepts;
            }

            // 按照排序值降序排列
            filteredDepartments.sort((a, b) => b.Sort - a.Sort);

            // 将部门数据转换成树形结构
            let departmentTree = convertToTree(filteredDepartments);

            // 初始化xm-select，使用全局变量
            // 判断当前用户是否为销售岗位
            if (currentUserIsSales && currentUserDeptId > 0) {
                // 使用id2department函数获取部门名称
                let deptName = id2department(currentUserDeptId, allDepartments, 0);
                // 渲染为禁用状态的xm-select
                keshiAsstSelect = xmSelect.render({
                    el: '#asst_dep_id',
                    data: [{
                        Name: deptName,
                        Id: currentUserDeptId
                    }],
                    initValue: [currentUserDeptId],//就显示当前的销售岗位
                    model: {
                        label: {
                            type: 'text'
                        }
                    },
                    prop: {
                        name: 'Name',
                        value: 'Id'
                    },
                    disabled: true,
                });

                // 合并部门数据用于后续查询
                let salesDepartments = filteredDepartments;

                // 加载该部门下的人员列表
                loadDepartmentUsers(currentUserDeptId, salesDepartments);

            } else {
                // 非销售岗位用户，正常初始化xm-select
                keshiAsstSelect = xmSelect.render({
                    el: '#asst_dep_id',
                    theme: {
                        color: '#1677ff',
                    },
                    height: 'auto',
                    data: departmentTree,
                    model: {
                        label: {
                            type: 'text',
                        }
                    },
                    clickClose: false, // 多选模式下点击不关闭
                    filterable: true,
                    // 添加默认提示文字
                    tips: '请选择部门',
                    prop: {
                        name: 'Name',
                        value: 'Id',
                        children: 'children'
                    },
                    // 设置表单提交时的名称为空，避免自动提交
                    name: '',
                    toolbar: {
                        show: true,
                        list: ['ALL', 'CLEAR']
                    },
                    tree: {
                        show: true,
                        strict: true, // 保持严格模式，确保父子节点联动
                        expandedKeys: expandedKeys,
                        // 启用级联选择，确保父子节点联动
                        cascade: true,
                        // 自动展开父节点
                        autoExpandParent: true
                    },
                    on: function (data) {
                        if (data.change && data.change.length > 0) {
                            // 使用setTimeout确保在DOM更新后获取最新的选中值
                            setTimeout(function () {
                                // 获取当前所有选中的部门ID
                                if (keshiAsstSelect) {
                                    let selectedDepts = keshiAsstSelect.getValue();

                                    // 如果有选中的部门，加载这些部门的用户
                                    if (selectedDepts.length > 0) {
                                        loadDepartmentUsersAll(selectedDepts, filteredDepartments);
                                    } else {
                                        // 清空用户列表
                                        $('#asst_id').html('<option value="">选择人员</option>');
                                        form.render('select');
                                    }
                                }
                            }, 0);
                        }
                    }
                });
            }

            // 转换扁平数据为树形结构
            function convertToTree(data) {
                let result = [];
                let map = {};

                // 创建所有节点的映射
                data.forEach(function (item) {
                    map[item.Id] = {
                        ...item,
                        children: []
                    };
                });

                // 确保所有必要的父节点都存在
                let addedParentIds = new Set(); // 用于跟踪已添加的父节点ID

                data.forEach(function (item) {
                    if (item.Pid !== 0 && !map[item.Pid] && !addedParentIds.has(item.Pid)) {
                        // 如果父节点不在映射中且尚未添加，尝试从原始数据中找到它
                        const parentDept = allDepartments.find(d => d.Id === item.Pid);
                        if (parentDept) {
                            // 添加父节点到映射
                            map[parentDept.Id] = {
                                ...parentDept,
                                children: []
                            };
                            // 将父节点添加到数据数组
                            data.push(parentDept);
                            // 记录已添加的父节点ID
                            addedParentIds.add(parentDept.Id);
                        }
                    }
                });

                // 移除重复的部门
                let uniqueData = [];
                let idSet = new Set();
                data.forEach(function (item) {
                    if (!idSet.has(item.Id)) {
                        uniqueData.push(item);
                        idSet.add(item.Id);
                    }
                });
                data = uniqueData;

                // 构建树结构
                data.forEach(function (item) {
                    let node = map[item.Id];
                    if (item.Pid !== 0 && map[item.Pid]) {
                        // 将当前节点添加到父节点的children中
                        map[item.Pid].children.push(node);
                    } else {
                        // 顶级节点直接添加到结果数组
                        result.push(node);
                    }
                });

                // 按Sort字段排序
                function sortBySort(arr) {
                    arr.sort(function (a, b) {
                        return b.Sort - a.Sort; // 降序排列
                    });
                    arr.forEach(function (item) {
                        if (item.children && item.children.length > 0) {
                            sortBySort(item.children);
                        }
                    });
                    return arr;
                }

                return sortBySort(result);
            }

            // 4. 医生列表函数已在前面定义

            // 5. 定义加载部门用户的函数
            function loadDepartmentUsers(keshi_id, salesDepartments) {
                layer.load(2);
                // 根据部门ID获取对应的角色ID（售前=3，售后=9）
                let role_id = 3; // 默认为售前
                let selectedDept = allDepartments.find(dept => dept.Id == keshi_id);
                if (selectedDept && selectedDept.Name.includes('售后')) {
                    role_id = 9; // 售后角色ID
                }

                $.ajax({
                    url: '/admin/user/list_low',
                    data: {
                        role_id: role_id,
                        department_id: keshi_id,
                    },
                    type: 'post',
                    success: function (res) {
                        layer.closeAll('loading');
                        if (res.code === 200) {
                            let data = res.data;
                            //let html = '<option value="">选择人员</option>';
                            let html = '';
                            if (data && data.length > 0) {
                                for (let i = 0; i < data.length; i++) {
                                    html += '<option value="' + data[i].ID + '">' + data[i].Name + '</option>';
                                }

                                // 如果当前用户是售前或售后，并且在当前部门中，设置默认选中和只读状态
                                if (currentUserIsSales && currentUserDeptId == keshi_id && currentUserId > 0) {
                                    $('#asst_id').html(html);
                                    $('#asst_id').val(currentUserId);
                                    $('#asst_id').attr('disabled', 'disabled');
                                    // 使用内联样式而不是添加类，避免影响布局
                                    $('#asst_id').css('background-color', '#f2f2f2');
                                    form.render('select');
                                } else {
                                    $('#asst_id').html(html);
                                    form.render('select');
                                }
                            } else {
                                layer.msg('该部门下暂无人员', { icon: 2, time: 1000 });
                                html = '<option value="">该部门下暂无人员</option>';
                                $('#asst_id').html(html);
                                form.render('select');
                            }
                        } else {
                            layer.msg(res.msg, { icon: 2, time: 1000 });
                        }
                    },
                    error: function (res) {
                        layer.closeAll('loading');
                        layer.msg(res.responseJSON.msg, { icon: 2, time: 1000 });
                    }
                });
            }

            // 加载多个部门的用户
            function loadDepartmentUsersAll(selectedDepts, filteredDepartments) {
                layer.load(2);

                // 获取所有选中部门的ID
                let deptIds = selectedDepts.map(item => item.Id || item.id);

                // 创建查询条件，适应多个部门
                let queryParams = {
                    department_ids: deptIds.join(',')
                };

                $.ajax({
                    url: '/admin/user/list_low',
                    data: queryParams,
                    type: 'post',
                    success: function (res) {
                        layer.closeAll('loading');
                        if (res.code === 200) {
                            let data = res.data;
                            //let html = '<option value="">选择人员</option>';
                            let html = '';
                            if (data && data.length > 0) {
                                // 按姓名排序
                                data.sort((a, b) => a.Name.localeCompare(b.Name, 'zh'));

                                for (let i = 0; i < data.length; i++) {
                                    html += '<option value="' + data[i].ID + '">' + data[i].Name + '</option>';
                                }

                                $('#asst_id').html(html);
                                form.render('select');
                            } else {
                                html = '<option value="">所选部门下暂无人员</option>';
                                $('#asst_id').html(html);
                                form.render('select');
                            }
                        } else {
                            layer.msg(res.msg, { icon: 2, time: 1000 });
                        }
                    },
                    error: function (res) {
                        layer.closeAll('loading');
                        layer.msg(res.responseJSON.msg, { icon: 2, time: 1000 });
                    }
                });
            }

            // 6. 初始化加载
            // 如果当前用户是医生，自动触发科室选择事件加载对应的医生列表
            if (currentUserIsDoctor && currentUserDeptId > 0) {
                loadDoctorsByDepartment(currentUserDeptId);
            }

            // 如果当前用户是售前或售后，自动触发部门选择事件加载对应的人员列表
            if (currentUserIsSales && currentUserDeptId > 0) {
                loadDepartmentUsers(currentUserDeptId);
            }

            // 7. 监听选择事件 - 科室选择事件已在前面添加
            // 部门选择事件已在xm-select的on事件中处理


            //模糊搜索用户的下拉框组件
            var inst = dropdown.render({
                elem: '#dropdown_input',
                trigger: 'keyup click',
                data: [],
                style: 'min-width: 220px; box-shadow: 1px 1px 11px rgb(0 0 0 / 11%);',
                click: function (data) {
                    if (!isEmpty(data)) {
                        this.elem.val(data.name + ' / ' + data.phone);
                        pat_pro_id = data.id;
                    }
                }
            });
            $(inst.config.elem).on('input propertychange', function () {
                var elem = $(this);
                var value = elem.val().trim();
                if (value.length > 0) {
                    $.ajax({
                        url: "/admin/patient_profile/patient_profile_phone2id",
                        type: 'post',
                        dataType: 'json',
                        data: {
                            "wd": value,
                        },
                        success: function (response) {
                            let data = response.data;
                            if (data && data.length > 0) {
                                dropdown.reloadData(inst.config.id, {
                                    data: data,
                                    templet: function (d) {
                                        var exp = new RegExp(value, 'gi');
                                        return (d.name + ' / ' + d.phone).replace(exp, function (str) {
                                            return '<span style="color: red;">' + str + '</span>';
                                        });
                                    }
                                });
                            } else {
                                dropdown.reloadData(inst.config.id, {
                                    data: [],
                                });
                            }
                        },
                        error: function (err) {
                            dropdown.reloadData(inst.config.id, {
                                data: [],
                            });
                        }
                    });
                } else {
                    dropdown.reloadData(inst.config.id, {
                        data: [],
                    });
                }
            });
        });
    </script>
</body>

</html>