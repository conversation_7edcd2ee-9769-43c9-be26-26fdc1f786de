<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 销售数据部管理者赋权</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
    <script src="/dist/js/xm-select.js"></script>
    <style>
        .xm-label,
        .auto-row {
            display: none;
        }

        xm-select {
            border: 0 !important;
        }
    </style>
</head>

<body>
    <div class="layui-card">
        <div id="department-select" style="padding:10px;"></div>
    </div>

    <script>
        layui.use(['element', 'layer', 'util'], function () {
            var element = layui.element;
            var layer = layui.layer;
            var util = layui.util;
            var $ = layui.$;
            var user_id = request.get("id");

            // 保存用户已绑定的部门ID列表
            var userBindDepartments = [];
            // 保存部门数据
            var departmentData = [];
            // 保存要展开的节点ID
            var expandedKeys = [];

            // 加载数据并初始化选择器的函数
            function initDepartmentTree() {
                var loadingCount = 0;

                // 显示加载中
                layer.load(2);
                loadingCount++;

                // 获取销售管理者与部门映射表数据
                $.ajax({
                    url: '/admin/department/salemaster_bind_department_list',
                    type: 'post',
                    data: { user_id: user_id },
                    dataType: 'json',
                    success: function (res) {
                        if (res.code === 200 && res.data) {
                            // 保存已绑定部门ID - 处理新的数据格式（逗号分隔的字符串）
                            // 注意：现在返回的是对象，字段名是Dep_ids
                            var depIdsStr = res.data.Dep_ids || "";
                            if (depIdsStr) {
                                // 将逗号分隔的字符串转换为数组，并确保每个ID是数字类型
                                userBindDepartments = depIdsStr.split(',').map(function (id) {
                                    return parseInt(id.trim(), 10);
                                }).filter(function (id) {
                                    // 过滤掉NaN值
                                    return !isNaN(id);
                                });
                            } else {
                                userBindDepartments = [];
                            }
                        }
                        loadingCount--;
                        if (loadingCount === 0) {
                            layer.closeAll('loading');
                            renderDepartmentTree();
                        }
                    },
                    error: function (data) {
                        loadingCount--;
                        if (loadingCount === 0) {
                            layer.closeAll('loading');
                        }
                        layer.msg(data.responseJSON ? data.responseJSON.msg : '获取绑定数据失败');
                    }
                });

                // 显示加载中
                layer.load(2);
                loadingCount++;

                // 获取部门(科室)数据
                $.ajax({
                    url: '/normal/department_cache_get',
                    type: 'post',
                    dataType: 'json',
                    success: function (res) {
                        if (res.code === 200 && res.data) {
                            // 保存部门数据 - 只保留销售类科室
                            // 使用全局变量 global_sale_department_ids 过滤科室
                            var salesDeptIds = global_sale_department_ids || [20, 21]; // 默认值防止全局变量未定义

                            // 过滤出销售类科室及其子科室
                            departmentData = filterSalesDepartments(res.data, salesDeptIds);
                        }
                        loadingCount--;
                        if (loadingCount === 0) {
                            layer.closeAll('loading');
                            renderDepartmentTree();
                        }
                    },
                    error: function (data) {
                        loadingCount--;
                        if (loadingCount === 0) {
                            layer.closeAll('loading');
                        }
                        layer.msg(data.responseJSON ? data.responseJSON.msg : '获取部门数据失败');
                    }
                });
            }

            // 过滤出销售类科室及其子科室
            function filterSalesDepartments(allDepts, salesDeptIds) {
                // 找出所有销售部门的ID及其子部门ID
                var salesAndChildrenIds = [];

                // 递归获取所有子部门ID
                function getAllChildrenIds(deptId) {
                    salesAndChildrenIds.push(deptId);
                    allDepts.forEach(function (dept) {
                        if (dept.Pid === deptId) {
                            getAllChildrenIds(dept.Id);
                        }
                    });
                }

                // 获取销售部门及其所有子部门的ID
                salesDeptIds.forEach(function (deptId) {
                    getAllChildrenIds(deptId);
                });

                // 过滤出销售部门及其子部门
                return allDepts.filter(function (dept) {
                    return salesAndChildrenIds.includes(dept.Id);
                });
            }

            // 渲染部门树选择器
            function renderDepartmentTree() {
                // 确保两个接口都已返回数据
                if (departmentData.length === 0) {
                    return;
                }

                // 将部门数据转换成树形结构
                var departmentTree = convertToTree(departmentData);

                // 确定需要展开的节点
                expandedKeys = findNodesToExpand(departmentData, userBindDepartments);

                // 初始化xm-select
                var departmentSelect = xmSelect.render({
                    el: '#department-select',
                    theme: {
                        color: '#1677ff',
                    },
                    height: 'auto',
                    data: departmentTree,
                    initValue: userBindDepartments,
                    model: {
                        type: 'relative',
                    },
                    filterable: true,
                    prop: {
                        name: 'Name',
                        value: 'Id',
                        children: 'children'
                    },
                    toolbar: {
                        show: true,
                        list: ['ALL', 'CLEAR', 'REVERSE']
                    },
                    tree: {
                        show: true,
                        strict: true,
                        clickCheck: false,
                        expandedKeys: expandedKeys, // 展开已选中节点的上级和下级
                        lazy: false
                    },
                    height: 'auto',
                    autoRow: true,
                    cascade: true, // 启用级联选择，选择父节点会自动选择子节点
                    on: function (data) {
                        // 选中变化事件
                        if (data.isAdd !== undefined) {
                            var actionType = data.isAdd ? 'bind' : 'unbind';
                            var depId = data.change[0];
                            console.log(actionType, depId);

                            // 获取当前已选中的ID集合并打印
                            // 直接使用回调函数中的arr参数，这是当前所有选中项
                            var selectedArr = data.arr;
                            var selectedIds = selectedArr.map(function (item) { return item.Id; });
                            // 将ID数组转换为逗号分隔的字符串，以匹配新的数据格式
                            var selectedIdsStr = selectedIds.join(',');
                            // console.log('当前已选中的ID集合:', selectedIds);
                            // console.log('当前已选中的ID字符串:', selectedIdsStr);

                            $.ajax({
                                url: '/admin/department/salemaster_bind_department_bind',
                                type: 'post',
                                dataType: 'json',
                                data: {
                                    dep_ids: selectedIdsStr, // 使用上面已获取的selectedIdsStr
                                    user_id: user_id
                                },
                                success: function (res) {
                                    if (res.code === 200) {
                                        layer.msg('部门绑定更新成功');
                                    } else {
                                        layer.msg(res.msg || '部门绑定更新失败');
                                    }
                                },
                                error: function (data) {
                                    layer.msg(data.responseJSON ? data.responseJSON.msg : '部门绑定更新失败');
                                }
                            });
                        }
                    }
                });
            }

            // 找出需要展开的节点（已选中节点的所有上级和下级）
            function findNodesToExpand(allDepts, selectedIds) {
                var nodesToExpand = [];
                var deptMap = {};

                // 创建ID到部门的映射，方便查找
                allDepts.forEach(function (dept) {
                    deptMap[dept.Id] = dept;
                });

                // 找出选中节点的所有上级节点
                function findAllParents(deptId) {
                    var dept = deptMap[deptId];
                    if (dept && dept.Pid && dept.Pid !== 0) {
                        nodesToExpand.push(dept.Pid);
                        findAllParents(dept.Pid);
                    }
                }

                // 找出选中节点的所有下级节点
                function findAllChildren(deptId) {
                    nodesToExpand.push(deptId);
                    allDepts.forEach(function (dept) {
                        if (dept.Pid === deptId) {
                            findAllChildren(dept.Id);
                        }
                    });
                }

                // 处理所有选中的节点
                selectedIds.forEach(function (deptId) {
                    // 添加选中节点本身到展开列表
                    nodesToExpand.push(parseInt(deptId));

                    // 找出所有上级节点
                    findAllParents(deptId);

                    // 找出所有下级节点
                    allDepts.forEach(function (dept) {
                        if (dept.Pid === parseInt(deptId)) {
                            findAllChildren(dept.Id);
                        }
                    });
                });

                // 去重
                return [...new Set(nodesToExpand)];
            }

            // 将扁平数据转换为树形结构
            function convertToTree(data) {
                var result = [];
                var map = {};

                // 创建所有节点的映射
                data.forEach(function (item) {
                    map[item.Id] = Object.assign({}, item, { children: [] });
                });

                // 构建树结构
                data.forEach(function (item) {
                    var node = map[item.Id];
                    if (item.Pid !== 0 && map[item.Pid]) {
                        // 将当前节点添加到父节点的children中
                        map[item.Pid].children.push(node);
                    } else {
                        // 顶级节点直接添加到结果数组
                        result.push(node);
                    }
                });

                // 按Sort字段排序
                function sortBySort(arr) {
                    arr.sort(function (a, b) {
                        return a.Sort - b.Sort;
                    });
                    arr.forEach(function (item) {
                        if (item.children && item.children.length > 0) {
                            sortBySort(item.children);
                        }
                    });
                    return arr;
                }

                return sortBySort(result);
            }

            // 初始化
            initDepartmentTree();
        });
    </script>
</body>

</html>