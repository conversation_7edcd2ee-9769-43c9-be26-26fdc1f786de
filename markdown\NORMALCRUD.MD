# 增删改查代码生成指南

## 1. 任务概述
- **目标**：基于现有模板生成完整的增删改查功能模块（前端+后端）
- **技术栈**：Golang后端 + LayUI前端
- **项目路径**：E:\works\go\

## 2. 核心规范（最重要）

### 2.1 命名规范
| 组件 | 格式 | 示例（模块名：article） |
|------|------|------------------------|
| 后端函数 | {Module}_list/add/edit/del/detail | Article_list() |
| API路由 | /admin/{module}/list/add/edit/del/detail | /admin/article/list |
| 前端页面 | {module}_list/add/edit/detail.html | article_list.html |

### 2.2 数据交互规范
- **成功响应**：`code: 200`
- **失败响应**：`code: 500`
- **API字段**：首字母大写（如：Title, Create_time）
- **表单字段**：必须与后端API返回字段完全匹配（包括大小写）

## 3. 实现步骤

### 3.1 分析数据表结构
- 表名通常与模块名相同
- 识别必填字段、搜索字段
- 特殊字段处理（如：sort, pid）

### 3.2 后端实现
1. 在`internal/app/admin/admin_extra_2.go`中实现5个核心函数
2. 在`internal/routes/admin.go`中添加路由配置
3. 权限控制模板：
   ```go
   func {module}_list(w http.ResponseWriter, r *http.Request) {
       api_id := config.NormalPerm
       _, isLogin := common.Check_Perm(w, r, api_id)
       if !isLogin {
           return
       }
       // 业务逻辑
   }
   ```
4. 获取用户ID：
   ```go
   func {module}_add(w http.ResponseWriter, r *http.Request) {
       api_id := config.NormalPerm
       session, isLogin := common.Check_Perm(w, r, api_id)
       if !isLogin {
           return
       }
       user_id := session.Values["id"] // 注意是session.Values["id"]，不是session.Values["user_id"]
       // 业务逻辑
   }
   ```

### 3.3 前端实现
1. 创建4个页面：list/add/edit/detail.html
2. 页面头部必须与模仿页面保持一致（仅修改title）
3. 表单验证使用`lay-verify="required"`
4. AJAX请求模板：
   ```javascript
   $.ajax({
       url: serverUrl + '/admin/{module}/{action}',
       type: 'POST',
       data: data.field,
       success: function(res) {
           if (res.code == 200) {
               // 成功处理
           } else {
               layer.msg(res.msg, { icon: 2, time: 1000 });
           }
       },
       error: function(err) {
           layer.msg(err.responseJSON ? err.responseJSON.msg : '请求失败', { icon: 2, time: 1000 });
       }
   });
   ```

## 4. 参考文件
- **后端参考**：`internal/app/admin/normal_template.go`中的Article_category_*相关函数
- **前端参考**：`interface/admin/article_category_*.html`

## 5. 特殊功能实现

### 5.1 搜索功能
- 默认参数名：`key`
- 实现方式：WHERE field1 LIKE ? OR field2 LIKE ?

### 5.2 父级关联（Pid字段）
- 自动从对应list接口获取父级数据
- 在add/edit页面中渲染为下拉选择框

### 5.3 排序字段（sort）
- add页面不显示sort输入框
- edit页面允许修改sort值
- 后端自动处理新增记录的sort值（最大值+10）

### 5.4 图片上传功能
- 仅当明确指定需要图片上传功能时才实现
- 每个模块最多支持上传1张图片
- 图片上传实现步骤：
  1. 在add和edit页面添加图片上传组件
  2. 使用统一的图片上传接口：`/admin/upload_normal_pic`
  3. 上传目录使用模块名作为category参数
  4. 图片上传组件示例代码：
     ```javascript
     // 初始化文件上传
     upload.render({
         elem: '#upload-image-btn',
         url: '/admin/upload_normal_pic',
         multiple: true,  // 虽然设置为true，但实际业务逻辑中只使用一张图片
         data: {
             category: '{module_name}',  // 使用模块名作为category
         },
         done: function(res) {
             if (res.code === 200) {
                 const fileInfo = res.data[0];
                 // 显示图片预览
                 appendImagePreview('image-container', fileInfo.filepath, fileInfo.filename);
                 // 将图片路径保存到隐藏字段中
                 $('input[name="Image"]').val(fileInfo.filepath);
                 layer.msg('上传成功', { icon: 1, time: 1000 });
             } else {
                 layer.msg(res.msg, { icon: 2, time: 1000 });
             }
         }
     });
     ```
  5. 图片预览函数实现：
     ```javascript
     // 图片预览函数
     function appendImagePreview(containerId, filepath, filename) {
         const container = $(`#${containerId}`);
         // 清空现有内容，确保只有一张图片
         container.empty();
         
         const imageItem = $(`
             <div class="image-preview-item" data-filename="${filename}" data-filepath="${filepath}">
                 <img src="${filepath}">
                 <div class="delete-btn">删除</div>
             </div>
         `);

         // 点击图片查看大图
         imageItem.find('img').on('click', function() {
             layer.photos({
                 photos: {
                     title: '查看图片',
                     data: [{ src: filepath }]
                 },
                 footer: false
             });
         });

         // 删除图片
         imageItem.find('.delete-btn').on('click', function() {
             layer.confirm('确定删除该图片吗？', { icon: 3, title: '提示' }, function(index) {
                 // 清空图片路径
                 $('input[name="Image"]').val('');
                 container.empty();
                 layer.close(index);
             });
         });

         container.append(imageItem);
     }
     ```
  6. 在HTML中添加必要的结构：
     ```html
     <!-- 图片上传区域 -->
     <div class="layui-form-item">
         <label class="layui-form-label">图片</label>
         <div class="layui-input-block">
             <input type="hidden" name="Image" value="">
             <div class="layui-upload layui-padding-4" style="border: 1px solid #eee;">
                 <button type="button" class="layui-btn upload_big_btn" id="upload-image-btn">
                     <div class="btn_big_font">
                         <i class="layui-icon layui-icon-upload btn_big_font"></i> 上传图片
                     </div>
                     <div>jpg、png、jpeg、bmp、gif格式，1M以内</div>
                 </button>
                 <div class="image-preview-container" id="image-container"></div>
             </div>
         </div>
     </div>
     ```
  7. 在edit页面中，需要加载已有图片：
     ```javascript
     // 如果已有图片路径，显示图片预览
     if (data.Image && data.Image !== '') {
         // 从路径中提取文件名
         const filename = data.Image.split('/').pop();
         appendImagePreview('image-container', data.Image, filename);
     }
     ```

## 6. 常见问题与解决方案

### 6.1 详情页面显示样式
详情页面必须严格按照参考模板的样式实现。特别注意：
- **不要使用input标签显示只读内容**，而应使用div标签
- 正确示例：
  ```html
  <div class="detail-text" id="description">内容</div>
  ```
- 错误示例：
  ```html
  <input type="text" name="Title" readonly class="layui-input">
  ```
- 思维链：
  1. 查看参考模板article_category_detail.html
  2. 发现详情页使用div.detail-text显示内容
  3. 在新模块的详情页中也使用相同的结构
  4. 这样可以保持UI风格一致，并提供更好的阅读体验

### 6.2 编辑页面ID处理
编辑页面提交数据时必须正确处理ID参数：
- 从URL获取的ID必须合并到提交的表单数据中
- 正确示例：
  ```javascript
  form.on('submit(formDemo)', function(data) {
      // 确保ID被包含在提交数据中
      var id = request.get('id');
      data.field.id = id;      
      $.ajax({
          url: serverUrl + '/admin/module/edit',
          type: 'POST',
          data: data.field,
          // 其他代码
      });
      return false;
  });
  ```
- 思维链：
  1. 编辑页面通过URL参数获取ID
  2. 表单提交时需要将此ID包含在数据中
  3. 如果不包含ID，后端会返回"ID不能为空"错误
  4. 确保ID字段名与后端期望的格式一致（通常是首字母大写的"Id"）

### 6.3 图片上传字段处理
处理图片上传时需要注意以下几点：
- 图片路径应存储在隐藏字段中，并在表单提交时一起发送
- 图片预览区域应清晰显示已上传的图片
- 删除图片时应清空对应的隐藏字段值
- 思维链：
  1. 图片上传成功后，将返回的filepath保存到隐藏的input字段中
  2. 表单提交时，这个隐藏字段会作为普通表单字段一起提交
  3. 在编辑页面加载数据时，检查是否有图片路径，如有则显示预览
  4. 删除图片时，清空隐藏字段的值，确保表单提交时不会包含已删除的图片路径

## 7. 前端开发补充规范

### 7.1 jQuery使用规范
- jQuery的`$`符号必须在`layui.use()`内部使用
- 正确示例：
  ```javascript
  layui.use(['form', 'layer'], function(){
      var $ = layui.$;  // 在这里声明$
      // 这里可以安全使用$
  });
  ```

### 7.2 URL参数获取
- 必须使用系统封装的request对象获取URL参数
- 正确示例：`var id = request.get('id');`
- 错误示例：~~`var id = getQueryString('id');`~~

### 7.3 表单字段命名规则
- 所有表单字段的name属性必须与后端API返回的字段名完全匹配（包括大小写）
- 示例：如果API返回字段为"Title"，则表单字段应写为：
  ```html
  <input type="text" name="Title" class="layui-input">
  ```

### 7.4 图片上传样式规范
- 图片预览容器必须使用统一的样式类
- 必须添加以下CSS样式到页面头部：
  ```css
  .image-preview-container {
      display: flex;
      flex-wrap: wrap;
      gap: 15px;
      margin-top: 15px;
  }

  .image-preview-item {
      position: relative;
      width: 150px;
      height: 150px;
  }

  .image-preview-item img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 4px;
      cursor: pointer;
  }

  .image-preview-item .delete-btn {
      position: absolute;
      bottom: 5px;
      left: 50%;
      transform: translateX(-50%);
      padding: 2px 10px;
      background-color: rgba(255, 255, 255, 0.9);
      border: 1px solid #ddd;
      border-radius: 3px;
      cursor: pointer;
  }

  .delete-btn:hover {
      background-color: #f56c6c;
      color: white;
  }
  ```

## 8. 数据表结构信息
执行代码生成任务前，请提供以下信息：

### 8.1 数据库表名
- 默认使用模块名作为表名（除非特别说明）
- 例如：模块名为book，则表名为book

### 8.2 字段信息
提供类似如下格式的字段信息：
```
字段名      数据类型    字段说明
id          int         ID
title       varchar     标题
create_time timestamp   创建时间
```

### 8.3 搜索配置
指定：
1. 搜索关键词参数名（默认为'key'）
2. 需要进行模糊匹配的字段（单个或多个）

### 8.4 图片上传配置
如果需要图片上传功能，请明确指定：
1. 图片字段名（通常为"Image"，首字母大写）
2. 图片上传目录（通常使用模块名作为category）
3. 思维链：
   - 确认是否需要图片上传功能
   - 如需要，在add和edit页面添加图片上传组件
   - 使用模块名作为category参数
   - 确保图片路径正确保存到数据库中

## 9. 前端模板严格遵循规范

### 9.1 头部模板规范
必须严格按照参考模板格式实现，各前端页面的头部标签，仅允许修改title内容：
```html
<head>
    <meta charset="utf-8">
    <title>{修改这里的标题}</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
</head>
```

### 9.2 列表页父ID筛选规范
- 列表页可能需要支持父ID（Pid）筛选
- 该功能默认不启用，除非明确指定："列表页有父ID传值，其参数名为pid"
- 实现方式：
  ```javascript
  // 前端
  var pid = request.get('pid');
  // 后端
  pid := r.FormValue("pid")
  if pid != "" {
     attSql = " and pid = ? "
     params = append(params, pid)
  }
  ``` 