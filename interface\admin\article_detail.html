<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 文章详情</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
    <style>
        .detail-image {
            max-width: 300px;
            max-height: 200px;
            border-radius: 4px;
            cursor: pointer;
        }

        .detail-content {
            padding: 10px;
            line-height: 24px;
            background-color: #f8f8f8;
            border-radius: 4px;
            min-height: 100px;
            white-space: pre-wrap;
        }
    </style>
</head>

<body>
    <div class="layui-padding-3">
        <div class="layui-form">

            <div class="layui-card">
                <div class="layui-card-body">

                    <div class="layui-row">
                        <!-- 第1行：文章ID 占 4；文章分类 占8 -->
                        <div class="layui-col-xs4 layui-col-sm4 layui-col-md4">
                            <div class="layui-form-item">
                                <label class="layui-form-label">ID</label>
                                <div class="layui-input-block">
                                    <div class="detail-text" id="id"></div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-xs8 layui-col-sm8 layui-col-md8">
                            <div class="layui-form-item">
                                <label class="layui-form-label">文章分类</label>
                                <div class="layui-input-block">
                                    <div class="detail-text" id="pid"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="layui-row">
                        <!-- 第2行：文章标题 占8；发布人 占4 -->
                        <div class="layui-col-xs8 layui-col-sm8 layui-col-md8">
                            <div class="layui-form-item">
                                <label class="layui-form-label">文章标题</label>
                                <div class="layui-input-block">
                                    <div class="detail-text" id="title"></div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-xs4 layui-col-sm4 layui-col-md4">
                            <div class="layui-form-item">
                                <label class="layui-form-label">发布人</label>
                                <div class="layui-input-block">
                                    <div class="detail-text" id="user_id"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="layui-row">
                        <!-- 第4行：浏览次数 占3；排序 占3；创建时间 占6 -->
                        <div class="layui-col-xs3 layui-col-sm3 layui-col-md3">
                            <div class="layui-form-item">
                                <label class="layui-form-label">浏览次数</label>
                                <div class="layui-input-block">
                                    <div class="detail-text" id="visits"></div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-xs3 layui-col-sm3 layui-col-md3">
                            <div class="layui-form-item">
                                <label class="layui-form-label">排序</label>
                                <div class="layui-input-block">
                                    <div class="detail-text" id="sort"></div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-xs6 layui-col-sm6 layui-col-md6">
                            <div class="layui-form-item">
                                <label class="layui-form-label">创建时间</label>
                                <div class="layui-input-block">
                                    <div class="detail-text" id="create_time"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="layui-row">
                        <!-- 第3行：封面图片 占12 -->
                        <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                            <div class="layui-form-item">
                                <label class="layui-form-label">封面图片</label>
                                <div class="layui-input-block">
                                    <div id="coverPic-container"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="layui-row">
                        <!-- 第5行：文章内容 占12 -->
                        <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                            <div class="layui-form-item layui-form-text">
                                <label class="layui-form-label">文章内容</label>
                                <div class="layui-input-block">
                                    <div class="detail-content" id="contents"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="layui-row">
                        <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
                            <div class="layui-form-item">
                                <div class="layui-input-block">
                                    <button class="layui-btn layui-btn-primary" onclick="closeWindow()">关闭</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
        layui.use(['layer'], function () {
            var layer = layui.layer;
            var $ = layui.$;

            // 获取URL参数
            var id = request.get('id');
            if (!id) {
                layer.msg('缺少ID参数', { icon: 2, time: 2000 }, function () {
                    closeWindow();
                });
                return;
            }

            // 加载文章详情
            layer.load(2);
            $.ajax({
                url: serverUrl + '/admin/article/detail',
                type: 'POST',
                data: { id: id },
                success: function (res) {
                    layer.closeAll('loading');
                    if (res.code === 200 && res.data) {
                        var data = res.data;

                        // 填充详情
                        $('#id').text(data.ID);
                        $('#title').text(data.Title);
                        $('#contents').text(data.Contents);
                        $('#visits').text(data.Visits);
                        $('#sort').text(data.Sort);
                        $('#create_time').text(data.Create_time ? data.Create_time.replace('T', ' ').replace('Z', '') : '');

                        // 加载分类名称
                        loadCategoryName(data.Pid);

                        // 加载用户名称
                        loadUserName(data.User_id);

                        // 显示封面图片
                        if (data.CoverPic && data.CoverPic !== '') {
                            data.CoverPic = global_article_pic_path + data.CoverPic;
                            var img = $('<img class="detail-image" src="' + data.CoverPic + '">');
                            img.on('click', function () {
                                layer.photos({
                                    photos: {
                                        title: '查看图片',
                                        data: [{ src: data.CoverPic }]
                                    },
                                    footer: false
                                });
                            });
                            $('#coverPic-container').append(img);
                        } else {
                            $('#coverPic-container').text('无封面图片');
                        }
                    } else {
                        layer.msg(res.msg || '获取文章详情失败', { icon: 2, time: 2000 }, function () {
                            closeWindow();
                        });
                    }
                },
                error: function (err) {
                    layer.closeAll('loading');
                    layer.msg(err.responseJSON ? err.responseJSON.msg : '请求失败', { icon: 2, time: 2000 }, function () {
                        closeWindow();
                    });
                }
            });

            // 加载分类名称
            function loadCategoryName(pid) {
                $.ajax({
                    url: serverUrl + '/admin/article_category/list',
                    type: 'POST',
                    success: function (res) {
                        if (res.code === 200 && res.data) {
                            var category = res.data.find(function (item) {
                                return item.ID == pid;
                            });
                            $('#pid').text(category ? category.Title : '未知分类');
                        } else {
                            $('#pid').text('未知分类');
                        }
                    },
                    error: function () {
                        $('#pid').text('未知分类');
                    }
                });
            }

            // 加载用户名称
            function loadUserName(userId) {
                $.ajax({
                    url: serverUrl + '/admin/user/list_low',
                    type: 'POST',
                    success: function (res) {
                        if (res.code === 200 && res.data) {
                            var user = res.data.find(function (item) {
                                return item.ID == userId;
                            });
                            $('#user_id').text(user ? user.Name : '未知用户');
                        } else {
                            $('#user_id').text('未知用户');
                        }
                    },
                    error: function () {
                        $('#user_id').text('未知用户');
                    }
                });
            }
        });

        // 关闭窗口
        function closeWindow() {
            var index = parent.layer.getFrameIndex(window.name);
            parent.layer.close(index);
        }
    </script>
</body>

</html>