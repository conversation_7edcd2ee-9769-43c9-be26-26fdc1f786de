<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 文员编辑</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
    <style>
        body {
            width: 480px;
            padding: 30px 50px 0 50px;
        }

        #sign_img {
            border-radius: 5px;
            width: 150px;
            min-height: 50px;
        }

        #avatar_img {
            border-radius: 50%;
            border: 1px solid #ccc;
            width: 80px;
            height: 80px;
        }

        #Department i {
            margin-right: 5px;
        }

        .preview-text {
            color: #1E9FFF;
            font-size: 12px;
            margin-left: 10px;
            cursor: pointer;
        }

        .preview-img {
            display: inline-block;
            margin-left: 20px;
        }
    </style>
</head>

<body>
    <form class="layui-form" lay-filter="form_edit" action="" onsubmit="return false">
        <div class="layui-form-item">
            <label class="layui-form-label">电话/帐号</label>
            <div class="layui-input-block">
                <input type="text" name="phone" required lay-verify="required" placeholder="请输入输入框内容" autocomplete="off"
                    class="layui-input" onkeyup='isPhone(this)'>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">密码</label>
            <div class="layui-input-block">
                <div class="layui-input-wrap">
                    <div class="layui-input-prefix layui-input-split">
                        <i class="layui-icon layui-icon-password"></i>
                    </div>
                    <input type="password" placeholder="不填即不更改密码" class="layui-input" name="pwd">
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">姓名</label>
            <div class="layui-input-block">
                <input type="text" name="name" required lay-verify="required" placeholder="请输入输入框内容" autocomplete="off"
                    class="layui-input">
            </div>
        </div>

        <div class="layui-form-item" style="display: flex;align-items: center;">
            <label class="layui-form-label">头像上传</label>
            <button type="button" class="layui-btn layui-btn-normal" id="avatar_file">选择头像文件</button>
            <div class="preview-img">
                <img id="avatar_img">
            </div>
        </div>


        <div class="layui-form-item">
            <label class="layui-form-label">部门/科室</label>
            <div class="layui-input-block">
                <button id="Department" class="layui-btn layui-btn-primary"
                    style="display: flex;min-width: 120px;align-items: center;justify-content: center;">
                    <i class='iconfont'>&#xe686;</i>
                    <div>选择选项</div>
                </button>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">排序</label>
            <div class="layui-input-block">
                <input type="number" name="sort" required lay-verify="required" placeholder="请输入输入框内容"
                    autocomplete="off" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">帐号状态</label>
            <div class="layui-input-block">
                <input type="radio" name="status" value="1" title="启用" checked>
                <input type="radio" name="status" value="0" title="禁用">
            </div>
        </div>
        <div class="layui-form-item" style="margin-top: 50px;">
            <div class="layui-input-block form_bottom_button">
                <button class="layui-btn" lay-submit lay-filter="formSubmitBtn">立即提交</button>
                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
            </div>
        </div>
    </form>
    <script>
        layui.use(['form', 'upload', 'jquery', 'layer'], function () {
            var form = layui.form;
            var upload = layui.upload;
            var $ = layui.jquery;
            var layer = layui.layer;
            var dropdown = layui.dropdown;
            var index = parent.layer.getFrameIndex(window.name);
            var id = window.location.search.split('=')[1];
            var department_id;
            // 获取ID参数
            var id = request.get('id');
            var dep_id = request.get('dep_id');
            if (!id || !dep_id) {
                layer.msg('ID和部门ID均不得为空', { icon: 2, time: 1000 });
                return;
            }
            var avatarFile = null;
            var signatureFile = null;

            // 头像上传组件
            upload.render({
                elem: '#avatar_file',
                auto: false,
                accept: 'file',
                choose: function (obj) {
                    obj.preview(function (index, file) {
                        avatarFile = file;
                        $('#avatar_img').attr('src', URL.createObjectURL(file));
                    });
                },
            });

            // 签名上传组件
            upload.render({
                elem: '#sign_file',
                auto: false,
                accept: 'file',
                choose: function (obj) {
                    obj.preview(function (index, file) {
                        signatureFile = file;
                        $('#sign_img').attr('src', URL.createObjectURL(file));
                    });
                },
            });

            // 使用本地存储的科室数据
            function fetchDepartmentData() {
                // 从本地存储获取科室数据
                let local_departments = localStorage.getItem('local_departments') ? JSON.parse(localStorage.getItem('local_departments')) : [];

                if (local_departments.length > 0) {
                    console.log('本地科室数据:', local_departments);
                    console.log('当前部门ID (dep_id):', dep_id);

                    // 只筛选PID为dep_id的科室数据
                    let filteredDepartments = local_departments.filter(item => item.Pid === parseInt(dep_id));
                    console.log('筛选后的科室数据:', filteredDepartments);

                    // 按Sort字段降序排序
                    filteredDepartments.sort((a, b) => b.Sort - a.Sort);

                    // 构建下拉菜单数据
                    let dropdownData = filteredDepartments.map(dept => {
                        return {
                            title: dept.Name,
                            id: dept.Id
                        };
                    });

                    console.log('下拉菜单数据:', dropdownData);

                    // 检查当前部门ID是否属于PID为dep_id的部门
                    let isValidDepartment = filteredDepartments.some(item => item.Id === parseInt(department_id));

                    // 找到当前选中的科室
                    let department = local_departments.find(item => item.Id === parseInt(department_id));

                    if (department && isValidDepartment) {
                        // 如果是有效的部门，显示部门名称
                        let departmentName = department.Name;
                        let departmentValue = `
                        <i class='iconfont'>&#xe686;</i>
                        <div>${departmentName}</div>
                        `;
                        $('#Department').html(departmentValue);
                        // 移除警告信息（如果有）
                        $('#department_warning').remove();
                    } else {
                        // 如果不是有效的部门，显示"请选择..."
                        let departmentValue = `
                        <i class='iconfont'>&#xe686;</i>
                        <div>请选择...</div>
                        `;
                        $('#Department').html(departmentValue);

                        // 添加警告信息
                        if (department && !isValidDepartment) {
                            // 移除已有的警告（如果有）
                            $('#department_warning').remove();
                            // 添加新的警告
                            $('#Department').parent().append(
                                '<div id="department_warning" class="layui-form-mid layui-word-aux" style="color: #FF5722; margin-top: 5px;">' +
                                '当前用户隶属部门非文员角色应该隶属的部门，请重新选择</div>'
                            );
                        } else if (!department) {
                            // 如果没有选择部门，也显示提示（但不是警告）
                            // 移除已有的警告（如果有）
                            $('#department_warning').remove();
                            // 添加提示
                            $('#Department').parent().append(
                                '<div id="department_warning" class="layui-form-mid layui-word-aux" style="margin-top: 5px;">' +
                                '请选择部门</div>'
                            );
                        }
                    }

                    // 渲染LAYUI下拉菜单
                    dropdown.render({
                        elem: '#Department',
                        id: 'DropdownID',
                        data: dropdownData,
                        // 确保下拉菜单有足够的宽度
                        style: 'min-width: 200px; box-shadow: 1px 1px 11px rgb(0 0 0 / 11%);',
                        // 添加调试信息
                        templet: function(d) {
                            console.log('下拉菜单项:', d);
                            return d.title;
                        },
                        click: function(obj) {
                            console.log('选中的部门:', obj);
                            let value = `
                                <i class='iconfont'>&#xe686;</i>
                                <div>${obj.title}</div>
                            `;
                            department_id = parseInt(obj.id);
                            console.log('设置department_id为:', department_id);
                            $('#Department').html(value);

                            // 移除警告信息（如果有）
                            $('#department_warning').remove();
                        }
                    });
                } else {
                    layer.msg('未找到科室数据，请刷新页面重试', { icon: 2, time: 1000 });
                }
            }

            // 请求用户数据并填充表单
            function fetchUserData() {
                layer.load(2);
                $.ajax({
                    url: '/admin/user/list',
                    type: 'post',
                    data: { id: id },
                    success: function (res) {
                        layer.closeAll('loading');
                        if (res.code === 200) {
                            var data = res.data[0];
                            form.val('form_edit', {
                                phone: data.Phone,
                                name: data.Name,
                                sort: data.Sort,
                                is_recommend: data.Is_recommend,
                                status: data.Status,
                            });
                            department_id = data.Department_id;
                            fetchDepartmentData();

                            // 加载头像
                            let avatarUrl = "/static/uploads/icons/avatarurl_" + id + ".png";
                            let avatarImg = new Image();
                            avatarImg.src = avatarUrl;
                            avatarImg.onload = function () {
                                $('#avatar_img').attr('src', avatarUrl + "?" + Math.random());
                            }

                            // 加载签名
                            let signUrl = "/static/uploads/icons/sign_" + id + ".png";
                            let signImg = new Image();
                            signImg.src = signUrl;
                            signImg.onload = function () {
                                $('#sign_img').attr('src', signUrl + "?" + Math.random());
                            }
                        } else {
                            layer.msg(res.msg, { icon: 2, time: 1000 });
                        }
                    },
                    error: function (err) {
                        layer.closeAll('loading');
                        layer.msg(err.responseJSON ? err.responseJSON.msg : '请求失败', { icon: 2, time: 1000 });
                    },
                });
            }

            // 初始化科室数据和用户数据
            // 检查是否有本地存储的科室数据
            let local_departments = localStorage.getItem('local_departments');
            if (!local_departments) {
                // 如果没有本地存储的科室数据，先获取科室数据
                layer.load(2);
                $.ajax({
                    url: '/normal/department_cache_get',
                    type: 'post',
                    success: function (res) {
                        layer.closeAll('loading');
                        if (res.code === 200) {
                            // 缓存部门数据到本地存储
                            localStorage.setItem('local_departments', JSON.stringify(res.data));
                            // 获取用户数据
                            fetchUserData();
                        } else {
                            layer.msg(res.msg, { icon: 2, time: 1000 });
                        }
                    },
                    error: function (err) {
                        layer.closeAll('loading');
                        layer.msg(err.responseJSON ? err.responseJSON.msg : '请求失败', { icon: 2, time: 1000 });
                    }
                });
            } else {
                // 如果已有本地存储的科室数据，直接获取用户数据
                fetchUserData();
            }

            // 图片预览功能
            $('#avatar_img, #sign_img').on('click', function () {
                var imgId = this.id === 'avatar_img' ? 'avatar_img' : 'sign_img';
                var imgSrc = $('#' + imgId).attr('src');
                if (imgSrc) {
                    layer.open({
                        type: 1,
                        title: false,
                        closeBtn: 1,
                        shadeClose: true,
                        area: ['500px', 'auto'],
                        content: '<div style="text-align: center;"><img src="' + imgSrc + '" style="max-width: 100%; max-height: 100%;"></div>'
                    });
                }
            });

            // 表单提交
            form.on('submit(formSubmitBtn)', function (data) {
                // 从本地存储获取科室数据
                let local_departments = localStorage.getItem('local_departments') ? JSON.parse(localStorage.getItem('local_departments')) : [];
                console.log('表单提交 - 本地科室数据:', local_departments);
                console.log('表单提交 - 当前部门ID (dep_id):', dep_id);
                console.log('表单提交 - 选择的部门ID (department_id):', department_id);

                // 只筛选PID为dep_id的科室数据
                let filteredDepartments = local_departments.filter(item => item.Pid === parseInt(dep_id));
                console.log('表单提交 - 筛选后的科室数据:', filteredDepartments);

                // 检查当前部门ID是否属于PID为dep_id的部门
                let isValidDepartment = filteredDepartments.some(item => item.Id === parseInt(department_id));
                console.log('表单提交 - 部门ID是否有效:', isValidDepartment);

                // 如果部门ID无效，显示警告并阻止提交
                if (!isValidDepartment) {
                    layer.msg('请选择有效的部门', { offset: 't', anim: 'slideDown', skin: 'layer_msg_normal_style', time: 1000 });
                    return false;
                }

                layer.load(2);
                var formData = new FormData();

                if (avatarFile) {
                    formData.append('avatar_file', avatarFile);
                }
                if (signatureFile) {
                    formData.append('sign_file', signatureFile);
                }

                for (var key in data.field) {
                    if (data.field.hasOwnProperty(key)) {
                        formData.append(key, data.field[key]);
                    }
                }
                formData.append('id', id);
                formData.append('department_id', department_id);

                $.ajax({
                    url: '/admin/user/edit',
                    type: 'post',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function (res) {
                        layer.closeAll('loading');
                        if (res.code === 200) {
                            layer.msg(res.msg, { offset: 't', anim: 'slideDown', skin: 'layer_msg_normal_style', time: 1000 }, function () {
                                parent.layer.close(index);
                                parent.layui.table.reload('user_list');
                            });
                        } else {
                            layer.msg(res.msg, { offset: 't', anim: 'slideDown', skin: 'layer_msg_normal_style', time: 1000 });
                        }
                    },
                    error: function (err) {
                        layer.closeAll('loading');
                        layer.msg(err.responseJSON ? err.responseJSON.msg : '请求失败', { offset: 't', anim: 'slideDown', skin: 'layer_msg_normal_style', time: 1000 });
                    },
                });

                return false;
            });
        });
    </script>
</body>

</html>