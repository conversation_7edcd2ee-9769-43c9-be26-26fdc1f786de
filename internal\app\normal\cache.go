package normal

import (
	"context"
	"encoding/json"
	"fmt"
	"mstproject/pkg/common"
	"mstproject/pkg/config"
	"mstproject/pkg/database"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"time"
	"unicode/utf8"

	"github.com/redis/go-redis/v9"
)

type User_more struct {
	Id      int    `db:"id"`
	Name    string `db:"NAME"`
	Phone   string `db:"phone"`
	Role_id string `db:"role_id"`
	Role    string `db:"role"`
	Perm_id string `db:"perm_id"`
	Perm    string `db:"perm"`
}

type User_less struct {
	Id                int       `db:"id"`
	DepartmentID      int       `db:"department_id"` // 确保添加这个字段
	Phone             string    `db:"phone"`
	Name              string    `db:"name"`
	ConsultationFee   float64   `db:"consultationFee"`
	ProfessionalTitle string    `db:"professionalTitle"`
	Introduction      *string   `db:"introduction"`
	Proficient        *string   `db:"proficient"`
	Sort              int       `db:"sort"`
	CreateTime        time.Time `db:"create_time"`
}

type Role struct {
	Id   int    `db:"id"`
	Role string `db:"role"`
}
type Menu struct {
	Id   int    `db:"id"`
	Pid  int    `db:"pid"`
	Name string `db:"name"`
	Path string `db:"path"`
}
type Perm struct {
	Id   int    `db:"id"`
	Name string `db:"name"`
}
type Api struct {
	Id      int    `db:"id"`
	Pid     int    `db:"pid"`
	APIName string `db:"api_name"`
	APIPath string `db:"api_path"`
	BuiltIn int    `db:"built_in"`
}

func cacheUser(redis_client *redis.Client, ctx context.Context, user User_more) error {
	key := fmt.Sprintf("user:%d", user.Id)
	value, err := json.Marshal(user)
	if err != nil {
		common.HandleErrorResponse(nil, http.StatusInternalServerError, "用户信息序列化失败", err)
		return err
	}
	if err = redis_client.Set(ctx, key, value, 0).Err(); err != nil {
		common.HandleErrorResponse(nil, http.StatusInternalServerError, "缓存用户信息失败", err)
		return err
	}
	return nil
}

func cacheUserByField(redis_client *redis.Client, ctx context.Context, user User_more, field string, fieldType string) error {
	var key string
	if fieldType == "name" {
		key = fmt.Sprintf("name:index:%s", field)
	} else if fieldType == "phone" {
		key = fmt.Sprintf("phone:index:%s", field)
	} else {
		return fmt.Errorf("invalid field type")
	}

	return redis_client.Set(ctx, key, user.Id, 0).Err()
}

func cacheUserByRole(redis_client *redis.Client, ctx context.Context, user User_more) error {
	roles := strings.Split(user.Role_id, ",")
	for _, role := range roles {
		key := fmt.Sprintf("role:index:%s", strings.TrimSpace(role))
		if err := redis_client.SAdd(ctx, key, user.Id).Err(); err != nil {
			return err
		}
	}
	return nil
}
func searchUsers(redis_client *redis.Client, ctx context.Context, wd string, indexType string) []User_more {
	var matchedUsers []User_more
	cursor := uint64(0)
	count := int64(20) //每次扫描数

	for {
		keys, nextCursor, err := redis_client.Scan(ctx, cursor, fmt.Sprintf("%s:index:*%s*", indexType, wd), count).Result()
		if err != nil {
			fmt.Println("SCAN error:", err)
			return matchedUsers
		}

		for _, key := range keys {
			userID, err := redis_client.Get(ctx, key).Result()
			if err != nil {
				continue
			}
			val, err := redis_client.Get(ctx, fmt.Sprintf("user:%s", userID)).Result()
			if err != nil {
				continue
			}
			var user User_more
			if err := json.Unmarshal([]byte(val), &user); err == nil {
				matchedUsers = append(matchedUsers, user)
			}
		}

		if nextCursor == 0 {
			break
		}

		cursor = nextCursor
	}

	// 按ID从大到小排序并限制返回的条数
	sort.Slice(matchedUsers, func(i, j int) bool {
		return matchedUsers[i].Id > matchedUsers[j].Id
	})

	if len(matchedUsers) > config.CacheTipsCount {
		matchedUsers = matchedUsers[:config.CacheTipsCount]
	}
	return matchedUsers
}

func filterUsersByRole(users []User_more, redis_client *redis.Client, ctx context.Context, pm_role_id string) []User_more {
	var filteredUsers []User_more
	roleKey := fmt.Sprintf("role:index:%s", pm_role_id)
	userIDs, err := redis_client.SMembers(ctx, roleKey).Result()
	if err == nil {
		userIDSet := make(map[string]struct{})
		for _, id := range userIDs {
			userIDSet[id] = struct{}{}
		}
		for _, user := range users {
			if _, exists := userIDSet[fmt.Sprintf("%d", user.Id)]; exists {
				filteredUsers = append(filteredUsers, user)
			}
		}
	}
	return filteredUsers
}

// REDIS - 部门
func Department_cache_get(w http.ResponseWriter, r *http.Request) {
	id, err := strconv.Atoi(r.FormValue("id"))
	if err != nil {
		id = 0
	}
	redis_client, err := common.Redis_client(w)
	if err != nil {
		common.HandleErrorResponse(w, http.StatusBadRequest, "REDIS连接失败", err)
		return
	}
	defer redis_client.Close()
	ctx := context.Background()
	result, err := common.Redis_Cache_Get(ctx, redis_client, id, "department")
	if err != nil || result == nil || common.Is_empty_array(result) {
		fmt.Println("缓存不存在，自动调用 Department_cache_set_internal 写入缓存...")
		// 自动调用内部方法
		if err = Department_cache_set_internal(); err != nil {
			common.HandleErrorResponse(w, http.StatusBadRequest, "科室信息缓存失败", err)
			return
		}
		result, err = common.Redis_Cache_Get(ctx, redis_client, id, "department")
		if err != nil || result == nil {
			common.HandleErrorResponse(w, http.StatusBadRequest, "科室信息获取失败，且缓存仍为空", err)
			return
		}
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "科室信息获取成功",
		"data": result,
	})
}
func Department_cache_set_internal() error {
	redis_client, err := common.Redis_client(nil) // 不需要 w 参数，因为是内部调用
	if err != nil {
		return fmt.Errorf("REDIS连接失败: %v", err)
	}
	defer redis_client.Close()
	ctx := context.Background()

	type Department struct {
		Id      int    `db:"id"`
		Pid     int    `db:"pid"`
		Name    string `db:"name"`
		Details string `db:"details"`
		Sort    int    `db:"sort"`
	}
	sql := "select id,pid,name,sort,details from department order by sort desc"
	var departments []Department
	if err = database.GetAll(sql, &departments); err != nil {
		return fmt.Errorf("数据库查询失败: %v", err)
	}

	// 清除旧的缓存
	keys, err := redis_client.Keys(ctx, "department:*").Result()
	if err != nil {
		return fmt.Errorf("获取 Redis 键失败: %v", err)
	}
	// 使用 DEL 命令删除找到的键，本机REDIS版本太旧，不然用unlink效果更好，非阻塞
	if len(keys) > 0 {
		_, delErr := redis_client.Del(ctx, keys...).Result()
		if delErr != nil {
			fmt.Printf("删除键失败: %v\n", delErr)
		}
	}

	// 写入新的缓存
	for _, department := range departments {
		if err = common.Redis_Cache_Item(redis_client, ctx, fmt.Sprintf("department:%d", department.Id), department, 0); err != nil {
			return fmt.Errorf("写入缓存失败: %v", err)
		}
	}

	fmt.Println("科室信息缓存成功")
	return nil
}

func Department_cache_set(w http.ResponseWriter, r *http.Request) {
	api_id := config.NormalPerm
	if _, isLogin := common.Check_Perm(w, r, api_id); !isLogin {
		return
	}
	// 调用内部缓存写入逻辑
	if err := Department_cache_set_internal(); err != nil {
		common.HandleErrorResponse(w, http.StatusBadRequest, "缓存写入失败", err)
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "科室信息缓存成功",
	})
}

// REDIS - API
func Api_cache_get(w http.ResponseWriter, r *http.Request) {
	id, err := strconv.Atoi(r.FormValue("id"))
	if err != nil {
		id = 0
	}
	redis_client, err := common.Redis_client(w)
	if err != nil {
		common.HandleErrorResponse(w, http.StatusBadRequest, "REDIS连接失败", err)
		return
	}
	defer redis_client.Close()
	ctx := context.Background()
	result, err := common.Redis_Cache_Get(ctx, redis_client, id, "api")
	if err != nil || result == nil || common.Is_empty_array(result) {
		// 缓存不存在时，自动调用 Api_cache_set_internal 更新缓存
		fmt.Println("API缓存不存在，自动调用 Api_cache_set_internal 写入缓存...")
		if err = Api_cache_set_internal(); err != nil {
			common.HandleErrorResponse(w, http.StatusBadRequest, "API缓存写入失败", err)
			return
		}
		result, err = common.Redis_Cache_Get(ctx, redis_client, id, "api")
		if err != nil || result == nil {
			common.HandleErrorResponse(w, http.StatusBadRequest, "API信息获取失败，且缓存仍为空", err)
			return
		}
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "API信息获取成功",
		"data": result,
	})
}
func Api_cache_set(w http.ResponseWriter, r *http.Request) {
	api_id := config.NormalPerm
	if _, isLogin := common.Check_Perm(w, r, api_id); !isLogin {
		return
	}
	if err := Api_cache_set_internal(); err != nil {
		common.HandleErrorResponse(w, http.StatusBadRequest, "API缓存写入失败", err)
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "接口信息缓存成功",
	})
}
func Api_cache_set_internal() error {
	redis_client, err := common.Redis_client(nil) // 不需要 w 参数
	if err != nil {
		return fmt.Errorf("REDIS连接失败: %v", err)
	}
	defer redis_client.Close()
	sql := `SELECT id, pid, api_name, api_path, built_in FROM rbac__api`
	var apis []Api
	if err = database.GetAll(sql, &apis); err != nil {
		return fmt.Errorf("数据库查询失败: %v", err)
	}
	ctx := context.Background()
	// 清除旧的缓存
	keys, err := redis_client.Keys(ctx, "api:*").Result()
	if err != nil {
		return fmt.Errorf("获取 Redis 键失败: %v", err)
	}
	// 使用 DEL 命令删除找到的键，本机REDIS版本太旧，不然用unlink效果更好，非阻塞
	if len(keys) > 0 {
		_, delErr := redis_client.Del(ctx, keys...).Result()
		if delErr != nil {
			fmt.Printf("删除键失败: %v\n", delErr)
		}
	}
	for _, api := range apis {
		if err = common.Redis_Cache_Item(redis_client, ctx, fmt.Sprintf("api:%d", api.Id), api, 0); err != nil {
			return fmt.Errorf("缓存写入失败: %v", err)
		}
	}

	fmt.Println("API信息缓存成功")
	return nil
}

// CACHE - User_more_cache
func User_more_cache_set(w http.ResponseWriter, r *http.Request) {
	api_id := config.NormalPerm
	if _, isLogin := common.Check_Perm(w, r, api_id); !isLogin {
		return
	}

	if err := User_more_cache_set_internal(); err != nil {
		common.HandleErrorResponse(w, http.StatusBadRequest, "用户信息缓存写入失败", err)
		return
	}

	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "用户信息缓存成功",
	})
}

func User_more_cache_set_internal() error {
	redis_client, err := common.Redis_client(nil)
	if err != nil {
		return fmt.Errorf("REDIS连接失败: %v", err)
	}
	defer redis_client.Close()

	var users []User_more
	sql := `
			SELECT
				a.id,
				a.phone,
				a.NAME,
				IFNULL(GROUP_CONCAT(DISTINCT c.id SEPARATOR ','), -1) AS role_id,
				IFNULL(GROUP_CONCAT(DISTINCT c.role SEPARATOR ','), '角色未绑') AS role,
				CASE 
					WHEN FIND_IN_SET('1', GROUP_CONCAT(DISTINCT c.id SEPARATOR ',')) > 0 THEN 0
					ELSE IFNULL(GROUP_CONCAT(DISTINCT d.perm_id SEPARATOR ','), -1) 
				END AS perm_id,
				CASE 
					WHEN FIND_IN_SET('1', GROUP_CONCAT(DISTINCT c.id SEPARATOR ',')) > 0 THEN '超管'
					ELSE IFNULL(GROUP_CONCAT(DISTINCT e.name SEPARATOR ','), '权限未绑') 
				END AS perm
			FROM
				rbac_user AS a
			LEFT JOIN rbac_user_roles AS b ON a.id = b.user_id
			LEFT JOIN rbac_role AS c ON c.id = b.role_id
			LEFT JOIN rbac_role_perm AS d ON d.role_id = c.id
			LEFT JOIN rbac_perm AS e ON e.id = d.perm_id
			GROUP BY
				a.id, a.phone, a.NAME;
    `
	if err = database.GetAll(sql, &users); err != nil {
		return fmt.Errorf("数据库查询失败: %v", err)
	}

	ctx := context.Background()
	for _, user := range users {
		if err = cacheUser(redis_client, ctx, user); err != nil {
			return fmt.Errorf("缓存用户失败: %v", err)
		}
		if err = cacheUserByField(redis_client, ctx, user, user.Name, "name"); err != nil {
			return fmt.Errorf("缓存用户姓名失败: %v", err)
		}
		if err = cacheUserByField(redis_client, ctx, user, user.Phone, "phone"); err != nil {
			return fmt.Errorf("缓存用户电话失败: %v", err)
		}
		if err = cacheUserByRole(redis_client, ctx, user); err != nil {
			return fmt.Errorf("缓存用户角色失败: %v", err)
		}
	}

	fmt.Println("用户信息缓存成功")
	return nil
}

func User_more_cache_get(w http.ResponseWriter, r *http.Request) {
	api_id := config.NormalPerm
	if _, isLogin := common.Check_Perm(w, r, api_id); !isLogin {
		return
	}

	redis_client, err := common.Redis_client(w)
	if err != nil {
		common.HandleErrorResponse(w, http.StatusBadRequest, "REDIS连接失败", err)
		return
	}
	defer redis_client.Close()

	wd := r.FormValue("wd")
	pm_role_id := r.FormValue("pm_role_id")
	ctx := context.Background()
	var matchedUsers []User_more

	if common.IsAllChinese(wd) && utf8.RuneCountInString(wd) < 5 {
		matchedUsers = searchUsers(redis_client, ctx, wd, "name")
	} else if common.IsPhoneNumber(wd) {
		matchedUsers = searchUsers(redis_client, ctx, wd, "phone")
	}

	if pm_role_id != "" {
		matchedUsers = filterUsersByRole(matchedUsers, redis_client, ctx, pm_role_id)
	}

	if len(matchedUsers) == 0 {
		fmt.Println("用户缓存不存在，自动触发 User_more_cache_set_internal 写入缓存...")
		if err := User_more_cache_set_internal(); err != nil {
			common.HandleErrorResponse(w, http.StatusBadRequest, "用户缓存写入失败", err)
			return
		}

		if common.IsAllChinese(wd) && utf8.RuneCountInString(wd) < 5 {
			matchedUsers = searchUsers(redis_client, ctx, wd, "name")
		} else if common.IsPhoneNumber(wd) {
			matchedUsers = searchUsers(redis_client, ctx, wd, "phone")
		}

		if pm_role_id != "" {
			matchedUsers = filterUsersByRole(matchedUsers, redis_client, ctx, pm_role_id)
		}
	}

	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "用户信息查询成功",
		"data": matchedUsers,
	})
}

// CACHE - RBAC-ROLE
func Role_cache_set(w http.ResponseWriter, r *http.Request) {
	api_id := config.NormalPerm
	if _, isLogin := common.Check_Perm(w, r, api_id); !isLogin {
		return
	}

	if err := Role_cache_set_internal(); err != nil {
		common.HandleErrorResponse(w, http.StatusBadRequest, "角色信息缓存写入失败", err)
		return
	}

	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "角色信息缓存成功",
	})
}

func Role_cache_set_internal() error {
	redis_client, err := common.Redis_client(nil)
	if err != nil {
		return fmt.Errorf("REDIS连接失败: %v", err)
	}
	defer redis_client.Close()

	sql := `SELECT id, role FROM rbac_role`
	var roles []Role
	if err = database.GetAll(sql, &roles); err != nil {
		return fmt.Errorf("数据库查询失败: %v", err)
	}

	ctx := context.Background()
	// 清除旧的缓存
	keys, err := redis_client.Keys(ctx, "role:*").Result()
	if err != nil {
		return fmt.Errorf("获取 Redis 键失败: %v", err)
	}
	// 使用 DEL 命令删除找到的键，本机REDIS版本太旧，不然用unlink效果更好，非阻塞
	if len(keys) > 0 {
		_, delErr := redis_client.Del(ctx, keys...).Result()
		if delErr != nil {
			fmt.Printf("删除键失败: %v\n", delErr)
		}
	}
	for _, role := range roles {
		if err = common.Redis_Cache_Item(redis_client, ctx, fmt.Sprintf("role:%d", role.Id), role, 0); err != nil {
			return fmt.Errorf("缓存写入失败: %v", err)
		}
	}

	fmt.Println("角色信息缓存成功")
	return nil
}
func Role_cache_get(w http.ResponseWriter, r *http.Request) {
	id, err := strconv.Atoi(r.FormValue("id"))
	if err != nil {
		id = 0
	}

	redis_client, err := common.Redis_client(w)
	if err != nil {
		common.HandleErrorResponse(w, http.StatusBadRequest, "REDIS连接失败", err)
		return
	}
	defer redis_client.Close()

	ctx := context.Background()
	result, err := common.Redis_Cache_Get(ctx, redis_client, id, "role")
	if err != nil || result == nil || common.Is_empty_array(result) {
		fmt.Println("角色缓存不存在，自动触发 Role_cache_set_internal 写入缓存...")
		if err = Role_cache_set_internal(); err != nil {
			common.HandleErrorResponse(w, http.StatusBadRequest, "角色缓存写入失败", err)
			return
		}

		result, err = common.Redis_Cache_Get(ctx, redis_client, id, "role")
		if err != nil || result == nil {
			common.HandleErrorResponse(w, http.StatusBadRequest, "角色信息获取失败，且缓存仍为空", err)
			return
		}
	}

	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "角色信息获取成功",
		"data": result,
	})
}

// CACHE - USER
func User_cache_set(w http.ResponseWriter, r *http.Request) {
	api_id := config.NormalPerm
	if _, isLogin := common.Check_Perm(w, r, api_id); !isLogin {
		return
	}

	if err := User_cache_set_internal(); err != nil {
		common.HandleErrorResponse(w, http.StatusBadRequest, "用户信息缓存写入失败", err)
		return
	}

	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "用户信息缓存成功",
	})
}

func User_cache_set_internal() error {
	redis_client, err := common.Redis_client(nil)
	if err != nil {
		return fmt.Errorf("REDIS连接失败: %v", err)
	}
	defer redis_client.Close()

	sql := `SELECT id, department_id, phone, name, consultationFee, professionalTitle, introduction, proficient, sort, create_time FROM rbac_user`
	var users []User_less
	if err = database.GetAll(sql, &users); err != nil {
		return fmt.Errorf("数据库查询失败: %v", err)
	}

	ctx := context.Background()
	// 清除旧的缓存
	keys, err := redis_client.Keys(ctx, "user:*").Result()
	if err != nil {
		return fmt.Errorf("获取 Redis 键失败: %v", err)
	}
	// 使用 DEL 命令删除找到的键，本机REDIS版本太旧，不然用unlink效果更好，非阻塞
	if len(keys) > 0 {
		_, delErr := redis_client.Del(ctx, keys...).Result()
		if delErr != nil {
			fmt.Printf("删除键失败: %v\n", delErr)
		}
	}
	for _, user := range users {
		if err = common.Redis_Cache_Item(redis_client, ctx, fmt.Sprintf("user:%d", user.Id), user, 0); err != nil {
			return fmt.Errorf("缓存写入失败: %v", err)
		}
	}

	fmt.Println("用户信息缓存成功")
	return nil
}
func User_cache_get(w http.ResponseWriter, r *http.Request) {
	session, _ := common.SessionStore.Get(r, "admin_sessions")
	id, err := strconv.Atoi(r.FormValue("id"))
	if err != nil {
		id = 0
	}

	if session.Values["id"] != 1 && id != session.Values["id"] {
		common.JSONResponse(w, http.StatusForbidden, map[string]interface{}{
			"code": 403,
			"msg":  "您非超级管理员，您只能查看自己的信息",
		})
		return
	}

	redis_client, err := common.Redis_client(w)
	if err != nil {
		common.HandleErrorResponse(w, http.StatusBadRequest, "REDIS连接失败", err)
		return
	}
	defer redis_client.Close()

	ctx := context.Background()
	result, err := common.Redis_Cache_Get(ctx, redis_client, id, "user")
	if err != nil || result == nil || common.Is_empty_array(result) {
		fmt.Println("用户缓存不存在，自动触发 User_cache_set_internal 写入缓存...")
		if err = User_cache_set_internal(); err != nil {
			common.HandleErrorResponse(w, http.StatusBadRequest, "用户缓存写入失败", err)
			return
		}

		result, err = common.Redis_Cache_Get(ctx, redis_client, id, "user")
		if err != nil || result == nil {
			common.HandleErrorResponse(w, http.StatusBadRequest, "用户信息获取失败，且缓存仍为空", err)
			return
		}
	}

	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "用户信息获取成功",
		"data": result,
	})
}

// CACHE - PERM
func Perm_cache_set(w http.ResponseWriter, r *http.Request) {
	api_id := config.NormalPerm
	if _, isLogin := common.Check_Perm(w, r, api_id); !isLogin {
		return
	}

	if err := Perm_cache_set_internal(); err != nil {
		common.HandleErrorResponse(w, http.StatusBadRequest, "权限信息缓存写入失败", err)
		return
	}

	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "权限信息缓存成功",
	})
}

func Perm_cache_set_internal() error {
	redis_client, err := common.Redis_client(nil)
	if err != nil {
		return fmt.Errorf("REDIS连接失败: %v", err)
	}
	defer redis_client.Close()

	// 修改查询语句，使用新的字段 name
	sql := `SELECT id, name FROM rbac_perm`
	var perms []Perm
	if err = database.GetAll(sql, &perms); err != nil {
		return fmt.Errorf("数据库查询失败: %v", err)
	}

	ctx := context.Background()
	// 清除旧的缓存
	keys, err := redis_client.Keys(ctx, "perm:*").Result()
	if err != nil {
		return fmt.Errorf("获取 Redis 键失败: %v", err)
	}
	// 使用 DEL 命令删除找到的键，本机REDIS版本太旧，不然用unlink效果更好，非阻塞
	if len(keys) > 0 {
		_, delErr := redis_client.Del(ctx, keys...).Result()
		if delErr != nil {
			fmt.Printf("删除键失败: %v\n", delErr)
		}
	}
	for _, perm := range perms {
		// 修改存储的字段名，这里存储的是 name
		if err = common.Redis_Cache_Item(redis_client, ctx, fmt.Sprintf("perm:%d", perm.Id), perm, 0); err != nil {
			return fmt.Errorf("缓存写入失败: %v", err)
		}
	}

	fmt.Println("权限信息缓存成功")
	return nil
}

func Perm_cache_get(w http.ResponseWriter, r *http.Request) {
	id, err := strconv.Atoi(r.FormValue("id"))
	if err != nil {
		id = 0
	}

	redis_client, err := common.Redis_client(w)
	if err != nil {
		common.HandleErrorResponse(w, http.StatusBadRequest, "REDIS连接失败", err)
		return
	}
	defer redis_client.Close()

	ctx := context.Background()
	// 修改 Redis 获取的字段名，这里获取的是 name
	result, err := common.Redis_Cache_Get(ctx, redis_client, id, "perm")
	if err != nil || result == nil || common.Is_empty_array(result) {
		fmt.Println("权限缓存不存在，自动触发 Perm_cache_set_internal 写入缓存...")
		if err = Perm_cache_set_internal(); err != nil {
			common.HandleErrorResponse(w, http.StatusBadRequest, "权限缓存写入失败", err)
			return
		}

		result, err = common.Redis_Cache_Get(ctx, redis_client, id, "perm")
		if err != nil || result == nil {
			common.HandleErrorResponse(w, http.StatusBadRequest, "权限信息获取失败，且缓存仍为空", err)
			return
		}
	}

	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "权限信息获取成功",
		"data": result,
	})
}

// CACHE - MENU
func Menu_cache_set(w http.ResponseWriter, r *http.Request) {
	api_id := config.NormalPerm
	if _, isLogin := common.Check_Perm(w, r, api_id); !isLogin {
		return
	}

	if err := Menu_cache_set_internal(); err != nil {
		common.HandleErrorResponse(w, http.StatusBadRequest, "菜单信息缓存写入失败", err)
		return
	}

	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "菜单信息缓存成功",
	})
}

func Menu_cache_set_internal() error {
	redis_client, err := common.Redis_client(nil)
	if err != nil {
		return fmt.Errorf("REDIS连接失败: %v", err)
	}
	defer redis_client.Close()

	sql := `SELECT id, pid, name, path FROM rbac__menu`
	var menus []Menu
	if err = database.GetAll(sql, &menus); err != nil {
		return fmt.Errorf("数据库查询失败: %v", err)
	}

	ctx := context.Background()
	// 清除旧的缓存
	keys, err := redis_client.Keys(ctx, "menu:*").Result()
	if err != nil {
		return fmt.Errorf("获取 Redis 键失败: %v", err)
	}
	// 使用 DEL 命令删除找到的键，本机REDIS版本太旧，不然用unlink效果更好，非阻塞
	if len(keys) > 0 {
		_, delErr := redis_client.Del(ctx, keys...).Result()
		if delErr != nil {
			fmt.Printf("删除键失败: %v\n", delErr)
		}
	}
	for _, menu := range menus {
		if err = common.Redis_Cache_Item(redis_client, ctx, fmt.Sprintf("menu:%d", menu.Id), menu, 0); err != nil {
			return fmt.Errorf("缓存写入失败: %v", err)
		}
	}

	fmt.Println("菜单信息缓存成功")
	return nil
}
func Menu_cache_get(w http.ResponseWriter, r *http.Request) {
	id, err := strconv.Atoi(r.FormValue("id"))
	if err != nil {
		id = 0
	}

	redis_client, err := common.Redis_client(w)
	if err != nil {
		common.HandleErrorResponse(w, http.StatusBadRequest, "REDIS连接失败", err)
		return
	}
	defer redis_client.Close()

	ctx := context.Background()
	result, err := common.Redis_Cache_Get(ctx, redis_client, id, "menu")
	if err != nil || result == nil || common.Is_empty_array(result) {
		fmt.Println("菜单缓存不存在，自动触发 Menu_cache_set_internal 写入缓存...")
		if err = Menu_cache_set_internal(); err != nil {
			common.HandleErrorResponse(w, http.StatusBadRequest, "菜单缓存写入失败", err)
			return
		}

		result, err = common.Redis_Cache_Get(ctx, redis_client, id, "menu")
		if err != nil || result == nil {
			common.HandleErrorResponse(w, http.StatusBadRequest, "菜单信息获取失败，且缓存仍为空", err)
			return
		}
	}

	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "菜单信息获取成功",
		"data": result,
	})
}

// 无鉴权，多端通用，根据ID求姓名
func Id2name_cache_get(w http.ResponseWriter, r *http.Request) {
	id, err := strconv.Atoi(r.FormValue("id"))
	if err != nil {
		id = 0
	}

	redis_client, err := common.Redis_client(w)
	if err != nil {
		common.HandleErrorResponse(w, http.StatusBadRequest, "REDIS连接失败", err)
		return
	}
	defer redis_client.Close()

	ctx := context.Background()
	result, err := common.Redis_Cache_Get(ctx, redis_client, id, "id2name")
	if err != nil || result == nil || common.Is_empty_array(result) {
		fmt.Println("id2name缓存不存在，自动触发 id2name_cache_set_internal 写入缓存...")
		if err = id2name_cache_set_internal(); err != nil {
			common.HandleErrorResponse(w, http.StatusBadRequest, "id2name缓存写入失败", err)
			return
		}

		result, err = common.Redis_Cache_Get(ctx, redis_client, id, "id2name")
		if err != nil || result == nil {
			common.HandleErrorResponse(w, http.StatusBadRequest, "id2name信息获取失败，且缓存仍为空", err)
			return
		}
	}

	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"data": result,
	})
}

// CACHE - ID2NAME
func id2name_cache_set_internal() error {
	redis_client, err := common.Redis_client(nil)
	if err != nil {
		return fmt.Errorf("REDIS连接失败: %v", err)
	}
	defer redis_client.Close()

	sql := `SELECT id, name FROM rbac_user`
	var users []User_less
	if err = database.GetAll(sql, &users); err != nil {
		return fmt.Errorf("数据库查询失败: %v", err)
	}

	ctx := context.Background()
	// 清除旧的缓存
	keys, err := redis_client.Keys(ctx, "id2name:*").Result()
	if err != nil {
		return fmt.Errorf("获取 Redis 键失败: %v", err)
	}
	// 使用 DEL 命令删除找到的键，本机REDIS版本太旧，不然用unlink效果更好，非阻塞
	if len(keys) > 0 {
		_, delErr := redis_client.Del(ctx, keys...).Result()
		if delErr != nil {
			fmt.Printf("删除键失败: %v\n", delErr)
		}
	}
	for _, user := range users {
		if err = common.Redis_Cache_Item(redis_client, ctx, fmt.Sprintf("id2name:%d", user.Id), user.Name, 30*time.Minute); err != nil {
			return fmt.Errorf("缓存写入失败: %v", err)
		}
	}

	fmt.Println("id2name信息缓存成功")
	return nil
}

// 获取指定部门的员工ID列表
func Dp_ids_cache_get(deptID int) ([]int, error) {
	// 使用通用的Redis_client函数获取Redis客户端
	redis_client, err := common.Redis_client(nil)
	if err != nil {
		return nil, fmt.Errorf("REDIS连接失败: %v", err)
	}
	defer redis_client.Close()
	ctx := context.Background()

	// 构建Redis键
	key := fmt.Sprintf("dp_ids:%d", deptID)

	// 从Redis获取数据
	val, err := redis_client.Get(ctx, key).Result()
	if err == redis.Nil {
		// 缓存未命中，从数据库获取并写入缓存
		return setDpIdsCacheForDept(redis_client, ctx, deptID)
	} else if err != nil {
		return nil, fmt.Errorf("从Redis读取键失败: %s, 错误: %v", key, err)
	}

	// 解析JSON数据
	var userIDs []int
	if err := json.Unmarshal([]byte(val), &userIDs); err != nil {
		return nil, fmt.Errorf("解析员工ID数据失败: %v", err)
	}

	return userIDs, nil
}

// 为指定部门设置员工ID缓存并返回结果
func setDpIdsCacheForDept(redis_client *redis.Client, ctx context.Context, deptID int) ([]int, error) {
	fmt.Printf("部门ID(%d)->员工IDS ： 缓存未命中，从数据库获取并写入缓存\n", deptID)
	// 从数据库查询部门下的员工ID
	var userIDs []int
	sql := "SELECT id FROM rbac_user WHERE department_id = ?"
	err := database.GetAll(sql, &userIDs, deptID)
	if err != nil {
		return []int{}, fmt.Errorf("查询部门员工ID失败: %v", err)
	}

	// 如果有员工数据，将数据写入缓存
	if len(userIDs) > 0 {
		// 将int数组转换为string
		userIDsJSON, _ := json.Marshal(userIDs)
		// 使用dp_ids:部门ID作为键
		key := fmt.Sprintf("dp_ids:%d", deptID)

		// 缓存到Redis，设置24小时过期
		err = common.Redis_Cache_Item(redis_client, ctx, key, string(userIDsJSON), 24*time.Hour)
		if err != nil {
			fmt.Printf("为部门ID %d 缓存员工ID失败: %v\n", deptID, err)
		}
	}

	return userIDs, nil
}
