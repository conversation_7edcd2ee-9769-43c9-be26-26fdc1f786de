<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 库存详情</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
</head>

<body>
    <div class="layui-padding-3">
        <div class="layui-row layui-col-space15">
            <!-- 第一列 -->
            <div class="layui-col-md6">
                <div class="layui-form-item">
                    <label class="layui-form-label">药材名称</label>
                    <div class="layui-input-block">
                        <div class="detail-text" id="drug_name"></div>
                    </div>
                </div>
                <div class="layui-row layui-col-space15">
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">库存数量</label>
                            <div class="layui-input-block">
                                <div class="detail-text" id="quantity"></div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">下限预警值</label>
                            <div class="layui-input-block">
                                <div class="detail-text" id="min_stock"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">批号</label>
                    <div class="layui-input-block">
                        <div class="detail-text" id="code"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">规格</label>
                    <div class="layui-input-block">
                        <div class="detail-text" id="spec"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">单位</label>
                    <div class="layui-input-block">
                        <div class="detail-text" id="unit"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">最新供应商</label>
                    <div class="layui-input-block">
                        <div class="detail-text" id="last_supplier"></div>
                    </div>
                </div>

            </div>

            <!-- 第二列 -->
            <div class="layui-col-md6">
                <div class="layui-form-item">
                    <label class="layui-form-label">进货价</label>
                    <div class="layui-input-block">
                        <div class="detail-text" id="cost_price"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">出货价</label>
                    <div class="layui-input-block">
                        <div class="detail-text" id="price"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">市场价</label>
                    <div class="layui-input-block">
                        <div class="detail-text" id="market_price"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">参于盘点</label>
                    <div class="layui-input-block">
                        <div class="detail-text" id="is_for_check"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">有效期</label>
                    <div class="layui-input-block">
                        <div class="detail-text" id="exp_date"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">建档时间</label>
                    <div class="layui-input-block">
                        <div class="detail-text" id="create_time"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部按钮 -->
        <div class="layui-form-item" style="text-align: center; ">
            <button type="button" class="layui-btn layui-btn-primary" onclick="closeModalWindow()">关闭</button>
        </div>
    </div>

    <script>
        layui.use(['layer'], function () {
            var layer = layui.layer;
            var $ = layui.$;

            // 获取URL参数
            var id = request.get('id');
            if (!id) {
                layer.msg('缺少必要参数', { icon: 2 });
                return;
            }

            // 加载数据
            layer.load(2);
            $.ajax({
                url: serverUrl + '/admin/warehouse_drug/detail',
                type: 'POST',
                data: { id: id },
                success: function (res) {
                    layer.closeAll('loading');
                    if (res.code === 200) {
                        var data = res.data;
                        $('#drug_name').text(data.Drug_name || '--');
                        $('#code').text(data.Code || '--');
                        $('#spec').text(data.Spec || '--');
                        $('#unit').text(data.Unit || '--');
                        $('#last_supplier').text(data.Last_supplier || '--');
                        $('#quantity').text(data.Quantity || '--');
                        $('#min_stock').text(data.Min_stock || '--');
                        $('#cost_price').text(data.Cost_price ? (data.Cost_price + '元/' + data.Unit) : '--');
                        $('#price').text(data.Price ? (data.Price + '元/' + data.Unit) : '--');
                        $('#market_price').text(data.Market_price ? (data.Market_price + '元/' + data.Unit) : '--');
                        $('#is_for_check').text(data.Is_for_check === 1 ? '是' : '否');
                        $('#exp_date').text(data.Exp_date ? data.Exp_date.split('T')[0] : '--');
                        $('#create_time').text(data.Create_time ? data.Create_time.replace('T', ' ').replace('Z', '') : '--');
                    } else {
                        layer.msg(res.msg || '加载失败', { icon: 2 });
                    }
                },
                error: function (res) {
                    layer.closeAll('loading');
                    layer.msg(res.responseJSON.msg, { icon: 2, time: 1000 });
                }
            });
        });

        function closeModalWindow() {
            var index = parent.layer.getFrameIndex(window.name);
            parent.layer.close(index);
        }
    </script>
</body>

</html>