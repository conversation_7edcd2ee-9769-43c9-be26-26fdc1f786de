<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 成品库存列表</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
    <style>
        /* 患者信息单元格样式 */
        .patient-info-cell {
            text-align: center;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 150px;
        }

        /* 表格行高度控制 */
        .layui-table-cell {
            height: auto;
            line-height: 28px;
            padding: 6px 15px;
            position: relative;
            box-sizing: border-box;
        }

        /* 地址单元格特殊处理 */
        .layui-table td[data-field="Patient_Address"] .layui-table-cell {
            min-width: 300px !important;
            width: 300px !important;
            max-width: 300px !important;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        /* 强制控制其他列宽度，防止错位 */
        .layui-table td[data-field="Patient_Age"] .layui-table-cell {
            width: 80px !important;
            min-width: 80px !important;
        }
        .layui-table td[data-field="Patient_Phone"] .layui-table-cell {
            width: 120px !important;
            min-width: 120px !important;
        }
        .layui-table td[data-field="Quantity"] .layui-table-cell {
            width: 100px !important;
            min-width: 100px !important;
        }
        .layui-table td[data-field="Status"] .layui-table-cell {
            width: 80px !important;
            min-width: 80px !important;
        }
    </style>
</head>

<body>
    <div class="layui-layout layui-layout-admin">
        <!-- 头部 -->
        <div class="layui-header">
            <div class="layui-logo">幸年堂</div>
            <ul class="layui-nav layui-layout-right">
                <li class="layui-nav-item" style="background-color:transparent!important;padding-right:10px">
                    <a href="javascript:;" style="display:flex;align-items:center;color:#fff"><img
                            src="/dist/images/user.png" class="layui-nav-img"><span class="user_name"></span></a>
                    <dl class="layui-nav-child pop_menu">
                        <dd><i class="iconfont">&#xe62f;</i><a href="change_password.html#7">用户资料</a></dd>
                        <dd><i class="iconfont">&#xe601;</i><a href="javascript:void(0)" class="refresh_cache">刷新缓存</a>
                        </dd>
                        <dd><i class="iconfont">&#xe634;</i><a href="login_out.html">退出登录</a></dd>
                    </dl>
                </li>
            </ul>
        </div>
        <!-- 导航 -->
        <ul class="layui-nav layui-nav-tree layui-nav-side" id="menuContainer" lay-bar="disabled" lay-accordion>
            <div style="margin: 50px 0;color: #bbb;text-align: center;">菜单加载中...</div>
        </ul>
        <!-- 主体内容 -->
        <div class="layui-body">
            <div class="body_child">
                <div class="layui-panel">
                    <div class="layui-card-header">
                        <div class="layui-row" style="padding-top:10px;">
                            <div class="layui-col-md11 layui-col-sm10" style="display: flex;">
                                <div>成品库存列表</div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-padding-3">
                        <div id="data_search">
                            <div class="layui-form">
                                <div class="layui-row">
                                    <div class="layui-col-md3" style="min-width:380px;">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label">搜索</label>
                                            <div class="layui-input-block">
                                                <input type="text" name="key" placeholder="请输入处方ID" autocomplete="off"
                                                    class="layui-input" lay-affix="clear">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-md3">
                                        <div class="layui-form-item">
                                            <div class="layui-input-inline" style="margin-left: 10px;">
                                                <button class="layui-btn" lay-submit lay-filter="search"
                                                    style="width: 120px;">筛选</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- 添加Tab导航 -->
                            <div class="layui-tab layui-tab-brief" lay-filter="warehouseType">
                                <ul class="layui-tab-title">
                                    <li class="layui-this">全部</li>
                                    <li>正常入库</li>
                                    <li>退货入库</li>
                                    <li>异常入库</li>
                                </ul>
                            </div>
                        </div>
                        <table id="mytable" lay-filter="mytable" class="layui-table" lay-skin="row"></table>
                    </div>
                </div>
                <br>
                <br>
            </div>
        </div>
    </div>

    <script>
        layui.use(['element', 'layer', 'util'], function () {
            var element = layui.element;
            var form = layui.form;
            var layer = layui.layer;
            var util = layui.util;
            var table = layui.table;
            var $ = layui.$;

            // 定义全局变量currentKind，用于记录当前选中的状态
            let currentKind = -1; // 默认为全部

            // 监听Tab切换事件
            element.on('tab(warehouseType)', function (data) {
                // 根据选项卡索引设置currentKind
                switch (data.index) {
                    case 0: currentKind = -1; break; // 全部
                    case 1: currentKind = 0; break;  // 正常入库
                    case 2: currentKind = 1; break;  // 退货入库
                    case 3: currentKind = 2; break;  // 异常入库
                }

                // 获取搜索框的值
                let searchKey = $('input[name="key"]').val();

                // 重新加载表格数据
                layer.load(2);
                table.reload('mytable', {
                    where: {
                        kind: currentKind !== -1 ? currentKind : undefined,
                        key: searchKey || undefined  // 如果searchKey有值就携带，没有就不传
                    }
                    , page: {
                        curr: 1
                    }
                });

                // 在Tab切换后应用列宽
                setTimeout(function() {
                    applyColumnWidths();
                }, 200);
            });

            // 页面顶部插入缓存的用户数据
            let local_userinfo = localStorage.getItem('local_userinfo');
            let user_name = "";
            if (local_userinfo) {
                local_userinfo = JSON.parse(local_userinfo);
                user_name = local_userinfo.Name + " ( " + local_userinfo.Roles + " )";
            } else {
                user_name = "未登录";
            }
            $('.user_name').text(user_name);
            render_menu($);
            $('.refresh_cache').click(function () {
                refresh_cache($);
            });

            //引入TABLE模块
            layer.load(2);
            table.render({
                elem: '#mytable'
                , even: true
                , url: serverUrl + "/admin/warehouse_finisheddrug/list"
                , method: 'post'
                , response: {
                    statusName: 'code'
                    , statusCode: 200
                    , msgName: 'msg'
                    , countName: 'count'
                    , dataName: 'data'
                }
                , cols: [[
                    { field: 'ID', title: 'ID',width:100, align: 'center' }
                    , {
                        field: 'Kind', title: '类型',width:100, align: 'center', templet: function (d) {
                            let kindText = '';
                            switch (d.Kind) {
                                case 0: kindText = '正常入库'; break;
                                case 1: kindText = '退货入库'; break;
                                case 2: kindText = '异常入库'; break;
                                default: kindText = '未知';
                            }
                            return kindText;
                        }
                    }
                    , {
                        field: 'Ord_id',width:100, title: '订单ID', align: 'center', templet: function (d) {
                            return "D" + d.Ord_id;
                        }
                    }
                    , {
                        field: 'Pre_id',width:100, title: '处方ID', align: 'center', templet: function (d) {
                            return "C" + d.Pre_id;
                        }
                    }
                    , { field: 'Patient_ID',width:90, title: '患者ID', align: 'center' }
                    , { field: 'Patient_Name',width:90, title: '患者姓名', align: 'center' }
                    , {
                        field: 'Patient_Sex',width:80, title: '性别', align: 'center', templet: function (d) {
                            return d.Patient_Sex === 1 ? '男' : '女';
                        }
                    }
                    , { field: 'Patient_Age', title: '年龄', align: 'center', width: 80, minWidth: 80 }
                    , { field: 'Patient_Phone', title: '电话', align: 'center', width: 100, minWidth: 120 }
                    , { field: 'Patient_Address', title: '地址', align: 'center', width: 200, minWidth: 300 }
                    , {
                        field: 'Quantity', title: '数量', align: 'center', width: 100, minWidth: 100, templet: function (d) {
                            return d.Quantity + ' ' + Drug_unit[d.Unit];
                        }
                    }
                    , {
                        field: 'Status', title: '状态', align: 'center', width: 80, minWidth: 80, templet: function (d) {
                            let statusText = '';
                            switch (d.Status) {
                                case 0: statusText = '占位符'; break;
                                case 1: statusText = '已入'; break;
                                case 2: statusText = '已出'; break;
                                default: statusText = '未知';
                            }
                            return statusText;
                        }
                    }
                    , { field: 'Notes', title: '备注',width:100, align: 'center' }
                    , {
                        field: 'Create_time', title: '时间', align: 'center',
                        templet: function (d) {
                            return d.Create_time.replace('T', ' ').replace('Z', '');
                        }
                    }
                ]]
                , page: true
                , limit: 12
                , done: function (res) {
                    layer.closeAll('loading');

                    // 强制应用列宽样式，解决后渲染导致的列宽错位问题
                    applyColumnWidths();

                    // 获取患者信息
                    if (res.data && res.data.length > 0) {
                        // 遍历数据，获取处方ID，调用接口获取患者信息
                        res.data.forEach(function (item, index) {
                            if (item.Pre_id) {
                                // 获取当前行的DOM元素
                                let tr = $('.layui-table-body').find('tr[data-index="' + index + '"]');

                                // 发起AJAX请求获取患者信息
                                $.ajax({
                                    url: serverUrl + "/admin/pre_id2patient_profile",
                                    type: "post",
                                    data: { pre_id: item.Pre_id },
                                    success: function (patientRes) {
                                        if (patientRes.code === 200 && patientRes.data) {
                                            // 计算年龄
                                            let age = '';
                                            if (patientRes.data.Born_date) {
                                                const birthDate = new Date(patientRes.data.Born_date);
                                                const today = new Date();
                                                age = today.getFullYear() - birthDate.getFullYear();

                                                // 如果今年的生日还没过，年龄减1
                                                const monthDiff = today.getMonth() - birthDate.getMonth();
                                                if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
                                                    age--;
                                                }
                                            }

                                            // 使用更可靠的方法定位单元格
                                            // 根据字段名称查找对应的单元格
                                            let patientIdCell = tr.find('td[data-field="Patient_ID"]');
                                            let patientNameCell = tr.find('td[data-field="Patient_Name"]');
                                            let patientSexCell = tr.find('td[data-field="Patient_Sex"]');
                                            let patientAgeCell = tr.find('td[data-field="Patient_Age"]');
                                            let patientPhoneCell = tr.find('td[data-field="Patient_Phone"]');
                                            let patientAddressCell = tr.find('td[data-field="Patient_Address"]');

                                            // 为单元格添加样式类
                                            patientIdCell.addClass('patient-info-cell');
                                            patientNameCell.addClass('patient-info-cell');
                                            patientSexCell.addClass('patient-info-cell');
                                            patientAgeCell.addClass('patient-info-cell');
                                            patientPhoneCell.addClass('patient-info-cell');
                                            patientAddressCell.addClass('patient-info-cell');

                                            // 更新单元格内容
                                            patientIdCell.find('.layui-table-cell').html(patientRes.data.ID || '');
                                            patientNameCell.find('.layui-table-cell').html(patientRes.data.Name || '');
                                            patientSexCell.find('.layui-table-cell').html(patientRes.data.Sex === 1 ? '男' : '女');
                                            patientAgeCell.find('.layui-table-cell').html(age || '');
                                            patientPhoneCell.find('.layui-table-cell').html(patientRes.data.Phone || '');

                                            // 地址可能较长，添加title属性以便鼠标悬停时显示完整内容
                                            if (patientRes.data.Address) {
                                                patientAddressCell.find('.layui-table-cell').html(removeAllPipe(patientRes.data.Address));
                                            }
                                        }
                                    }
                                });
                            }
                        });
                    }
                }
            });

            //监听工具条
            table.on('tool(mytable)', function (obj) {
                var data = obj.data;
                if (obj.event === 'detail') {
                    layer.open({
                        type: 2,
                        title: '成品库存详情',
                        area: ['600px', '500px'],
                        shadeClose: true,
                        content: 'warehouse_finisheddrug_detail.html?id=' + data.ID
                    });
                }
            });

            //监听搜索
            form.on('submit(search)', function (data) {
                layer.load(2);
                table.reload('mytable', {
                    where: {
                        key: data.field.key || undefined,
                        kind: currentKind !== -1 ? currentKind : undefined
                    }
                    , page: {
                        curr: 1
                    }
                });
                return false;
            });

            // 监听分页事件，确保分页后列宽保持一致
            table.on('sort(mytable)', function() {
                applyColumnWidths();
            });

            // 监听分页事件，确保分页后列宽保持一致
            table.on('page(mytable)', function() {
                applyColumnWidths();
            });

            // 定义应用列宽的函数
            function applyColumnWidths() {
                setTimeout(function() {
                    // 强制设置地址列宽度
                    $('td[data-field="Patient_Address"]').css({
                        'min-width': '300px',
                        'width': '200px'
                    });

                    // 强制设置其他列宽度
                    $('td[data-field="Patient_Age"]').css({
                        'width': '80px',
                        'min-width': '80px'
                    });

                    $('td[data-field="Patient_Phone"]').css({
                        'width': '120px',
                        'min-width': '120px'
                    });

                    $('td[data-field="Quantity"]').css({
                        'width': '100px',
                        'min-width': '100px'
                    });

                    $('td[data-field="Status"]').css({
                        'width': '80px',
                        'min-width': '80px'
                    });

                    // 强制表头也应用相同的宽度
                    $('th[data-field="Patient_Address"]').css({
                        'min-width': '300px',
                        'width': '300px'
                    });

                    $('th[data-field="Patient_Age"]').css({
                        'width': '80px',
                        'min-width': '80px'
                    });

                    $('th[data-field="Patient_Phone"]').css({
                        'width': '120px',
                        'min-width': '120px'
                    });

                    $('th[data-field="Quantity"]').css({
                        'width': '100px',
                        'min-width': '100px'
                    });

                    $('th[data-field="Status"]').css({
                        'width': '80px',
                        'min-width': '80px'
                    });
                }, 100);
            }
        });
    </script>
</body>

</html>