我需要AI帮我写前后端增删改查代码，其编写逻辑，是模仿我已经写好的功能来写的，接下来我会介绍应该如何仿写，我把下面的访写的规则介绍完后，你需要理解我的需求，然后帮我用Chain-of-Thought来写AI提示词，这样AI能够更好的理解，并帮助我仿写优秀可靠的代码，请将提示词保存进项目根目录：“增删改查通用提示词.MD”
--------------------------------------------------------
前端框架：layui
后端语言：golang
我需要AI帮我写一个我经常复用的通用模块的增删改查。
涉及前端页面有“E:\works\go\interface\admin\”目录下的：
列表：*_list.html
新增：*_add.html
编辑：*_edit.html
详情：*_detail.html
上述前端文件调用的后端接口规则为：
/admin/*/list
/admin/*/add
/admin/*/del
/admin/*/edit
/admin/*/detail

涉及后端文件有2个，分别是：
1、E:\works\go\internal\app\admin\admin_extra_2.go - 你需要写增删改查逻辑的函数文件
2、E:\works\go\internal\routes\admin.go - 路由文件
3、E:\works\go\internal\app\admin\normal_template.go - 写增删改查逻辑的参考文件

增删改查参考文件（normal_template.go）相应函数命名规则介绍：
列表：Article_category_list()
新增：Article_category_add()
修改：Article_category_edit()
删除：Article_category_del()
详情：Article_category_detail()

1、你需要参考上面的函数，将我提出模块，以上述的命名规则和功能，然后你参考好后，需要将我的需求（增删改查功能）写进指定函数存放文件E:\works\go\internal\app\admin\admin_extra_2.go中去。
比如我让你写的模块名字是：book，那你就需要模仿上面的函数命名方式，写出以下函数：
列表：Book_list()
新增：Book_add()
修改：Book_edit()
删除：Book_del()
详情：Book_detail()
然后写进admin_extra_2.go中

2、路由函数的新增
写好Book相应的功能函数后，需要在路由文件（E:\works\go\internal\routes\admin.go）中新增对应的路由。
还按照上述的Book为例，那它的规则即为如下方式：
（这里你最好写一个注释，如：//book模块的增删改查路由）
mux.HandleFunc("/admin/book/list", handlers.Book_list)
mux.HandleFunc("/admin/book/add", handlers.Book_add)
mux.HandleFunc("/admin/book/edit", handlers.Book_edit)
mux.HandleFunc("/admin/book/del", handlers.Book_del)
mux.HandleFunc("/admin/book/detail", handlers.Book_detail)
--------------------------------------------------------
此至，你有不明白的问题吗？有的话可以先问我，如果都弄明白了，就帮我写AI提示词，并保存文件“增删改查通用提示词.MD”





忘记一件重要的事情：我还会告诉AI我要新写的模块对应的数据表相关信息
数据表的表名，一般是我要写的这个模块的模块名，比如我要让AI写的模块名是book，那我的这个表就是book（一般情况下会是这样，除非我有特殊说明）
然后我还会提供字段（因为前端在显示列表、详情、修改时，都会涉及字段），字段的样式我会这样提供：
id          int         ID
title       varchar     文章分类名称
description varchar     描述
sort        int         递增的排序
create_time timestamp   建立时间
--------------------
当然，每次提供的表、字段，都会不同，所以我感觉这些没必要跟AI说，但我希望AI能做好接收的准备，我会每次在有需求时，会将表、表结构告诉他。
请将我上述的意思，以Chain-of-Thought的形式补充告知AI



再加1条，前端list页面，会有个搜索框，这个搜索key所针对的后端模糊搜索字段，我也会告知需要匹配后端的哪个字段（有可能是1个字段，也有可能是2个字段）。
把我上面的这个再完善好后，就可以生成MD文档了。