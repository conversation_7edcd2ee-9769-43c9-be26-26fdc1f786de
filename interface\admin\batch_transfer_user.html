<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 批量转移用户</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
    <script src="/dist/js/xm-select.js"></script>
    <style>
        .info-panel {
            background-color: #f9f9f9;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
        }

        .info-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .info-item {
            margin-bottom: 10px;
        }

        .info-label {
            display: inline-block;
            width: 100px;
            text-align: right;
            padding-right: 10px;
            color: #666;
        }

        .btn-container {
            text-align: center;
            margin-top: 20px;
        }

        .layui-btn {
            min-width: 100px;
        }

        /* 错误提示样式 */
        .error-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(5px);
            z-index: 9999;
            display: flex;
            justify-content: center;
            align-items: center;
            opacity: 1;
            transition: opacity 0.3s ease;
        }

        .error-card {
            width: 400px;
            text-align: center;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.95);
            transform: translateY(0);
            transition: all 0.3s ease;
        }

        /* 页面模糊效果 */
        .page-blur {
            filter: blur(5px);
            pointer-events: none;
            user-select: none;
        }

        /* 美化xm-select树形结构 */
        .xm-tree-icon.xm-tree-icon-parent {
            border-width: 5px;
        }

        .xm-tree-icon.xm-tree-icon-parent.expand {
            border-color: transparent transparent transparent #4096ff;
        }

        .xm-tree-icon.xm-tree-icon-leaf {
            border: 0;
            margin-right: 3px;
        }

        /* 选中态样式增强 */
        .xm-tree-show .xm-option.selected {
            background-color: #e6f4ff !important;
        }

        /* 调整tree节点间距 */
        .xm-tree-show {
            padding: 5px 0 !important;
        }

        .xm-tree-show .xm-option {
            padding-left: 30px !important;
        }

        .xm-tree-show .xm-tree-item .xm-option {
            padding-left: 35px !important;
        }

        .xm-tree-show .xm-tree-item .xm-tree-item .xm-option {
            padding-left: 60px !important;
        }

        .warning_tips {
            background-color: #fff7e6;
            border: 1px solid #ffe7ba;
            padding: 10px 15px;
            border-radius: 4px;
            margin-bottom: 15px;
            color: #d46b08;
            font-size: 14px;
            line-height: 1.5;
        }

        .msg_box_style {
            margin-bottom: 15px;
            display: flex;
            padding: 20px;
            justify-content: space-around;
            align-items: center;
            font-size: 15px;
        }

        /* 用户列表样式 */
        .user-list {
            max-height: 200px;
            overflow-y: auto;
            margin-bottom: 15px;
            border: 1px solid #e6e6e6;
            border-radius: 4px;
            padding: 10px;
        }

        .user-item {
            padding: 5px 10px;
            border-bottom: 1px dashed #eee;
            display: flex;
            justify-content: space-between;
        }

        .user-item:last-child {
            border-bottom: none;
        }

        .user-status {
            width: 20px;
            text-align: center;
        }
    </style>
</head>

<body>
    <div class="layui-padding-3">
        <!-- 目标转移信息 -->
        <div class="info-panel">
            <div class="warning_tips">
                您当前的操作，是将选中的用户批量转移到其他部门和销售人员，这将意味着这些用户的所有数据，如订单、病历、处方等将一并被转移。
            </div>
            <div class="layui-form">
                <div class="info-item">
                    <label class="info-label">部门：</label>
                    <div class="layui-inline" style="width: 260px;">
                        <div id="dep_tree" style="min-width: 100px;"></div>
                    </div>
                </div>
                <div class="info-item">
                    <label class="info-label">销售人员：</label>
                    <div class="layui-inline" style="width: 260px;">
                        <select name="asst_id" id="asst_id" lay-filter="asst_id">
                            <option value="">请先选择部门</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- 用户列表 -->
        <div class="info-panel">
            <div class="info-title">待转移用户列表</div>
            <div class="user-list" id="userList">
                <!-- 用户列表将通过JavaScript动态生成 -->
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="btn-container">
            <button type="button" class="layui-btn" id="confirm_transfer">确认批量转移</button>
            <button type="button" class="layui-btn layui-btn-primary" id="cancel_btn">取消</button>
        </div>
    </div>

    <!-- 进度条容器 -->
    <div id="uploadProgress"
        style="display: none; position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 20px; border-radius: 4px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); width: 400px; z-index: 999;">
        <div style="margin-bottom: 10px;">正在处理用户转移...</div>
        <div class="layui-progress layui-progress-big" lay-filter="upload-progress" lay-showPercent="true">
            <div class="layui-progress-bar" lay-percent="0%"></div>
        </div>
    </div>

    <!-- 添加结果统计对话框的模板 -->
    <script type="text/html" id="resultDialogTpl">
        <div style="padding: 20px;">
            <div class="msg_box_style">
                <p>总计处理：{{d.total}}条</p>
                <p>成功转移：<span style="color: #5FB878;">{{d.success}}条</span></p>
                <p>转移失败：<span style="color: #FF5722;">{{d.fail}}条</span></p>
            </div>
            <div style="text-align: center;">
                <button type="button" class="layui-btn" onclick="closeDialog()">确定</button>
            </div>
        </div>
    </script>

    <!-- 错误提示覆盖层 -->
    <div class="error-overlay" style="display: none;">
        <div class="error-card layui-card">
            <div class="layui-card-body">
                <p style="font-size: 16px; padding: 20px 10px; color: #333;" id="error-message"></p>
                <div style="padding: 10px 0 20px 0;">
                    <button class="layui-btn layui-btn-normal return-btn" style="padding: 0 30px;">返回</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        layui.use(['form', 'layer', 'jquery', 'element'], function() {
            var form = layui.form;
            var layer = layui.layer;
            var $ = layui.jquery;
            var element = layui.element;

            // 全局变量
            var departmentData = JSON.parse(localStorage.getItem('local_departments') || '[]');
            var userIds = request.get('user_ids') ? request.get('user_ids').split(',') : [];
            var selectedDepId = null;
            var selectedStaffId = null;

            // 初始化检查
            if (userIds.length === 0) {
                showError('参数错误，未选择任何用户');
                return false;
            }

            // 加载用户列表
            loadUserList(userIds);

            // 初始化部门树
            initDepartmentTree();

            // 显示错误信息
            function showError(message) {
                $('#error-message').text(message);
                $('.error-overlay').show();
                $('body > .layui-padding-3').addClass('page-blur');
            }

            // 显示用户数量信息
            function loadUserList(ids) {
                if (!ids || ids.length === 0) return;

                // 直接显示用户数量
                var userCount = ids.length;
                var html = '<div class="user-item" style="text-align: center; font-size: 16px; padding: 15px;">' +
                    '您当前要转移的用户有 <b>' + userCount + '</b> 名' +
                    '</div>';
                $('#userList').html(html);

                // 为每个用户ID创建隐藏的数据项，用于后续处理
                ids.forEach(function(id) {
                    $('#userList').append('<div class="user-item" data-id="' + id + '" style="display: none;"></div>');
                });
            }

            // 初始化部门树函数
            function initDepartmentTree() {
                // 过滤出售前和售后部门数据
                var filteredData = filterSaleDepartmentData(departmentData);

                if (filteredData.length === 0) {
                    showError('未找到相关部门数据');
                    return;
                }

                // 初始化xm-select组件
                var departmentSelect = xmSelect.render({
                    el: '#dep_tree',
                    model: { label: { type: 'text' } },
                    radio: true,
                    clickClose: true,
                    empty: '没有可选择的部门',
                    searchTips: '输入关键词搜索',
                    toolbar: {
                        show: true,
                        list: ['CLEAR']
                    },
                    filterable: true,
                    height: 'auto',
                    data: filteredData,
                    tree: {
                        show: true,
                        strict: true,
                        expandedKeys: global_sale_department_ids,
                        autoExpandParent: true
                    },
                    theme: {
                        color: '#4096ff',
                    },
                    on: function(data) {
                        if(data.isAdd && data.change.length > 0) {
                            // 设置选中的部门ID
                            selectedDepId = data.change[0].value;
                            console.log("选中的部门ID：", selectedDepId);

                            // 加载该部门下的销售人员
                            loadStaff(selectedDepId);

                            return data.change.slice(0, 1); // 确保只能选一个
                        }
                    }
                });
            }

            // 过滤部门数据，只保留售前和售后部门及其子节点
            function filterSaleDepartmentData(data) {
                // 构建树形结构
                var treeData = [];
                var idMapping = {};

                // 创建ID到对象的映射
                data.forEach(item => {
                    if (isSaleDepartmentOrDescendant(item, data)) {
                        idMapping[item.Id] = {
                            name: item.Name,
                            value: item.Id,
                            pid: item.Pid,
                            children: []
                        };
                    }
                });

                // 构建树形结构
                data.forEach(item => {
                    if (isSaleDepartmentOrDescendant(item, data)) {
                        // 当前节点
                        const node = idMapping[item.Id];

                        // 如果是顶级节点，直接添加到树中
                        if (global_sale_department_ids.includes(item.Id)) {
                            treeData.push(node);
                        }
                        // 否则添加到父节点的children数组
                        else if (idMapping[item.Pid]) {
                            idMapping[item.Pid].children.push(node);
                        }
                    }
                });

                // 对各级节点按Sort字段排序
                function sortTree(nodes) {
                    if (!nodes || !nodes.length) return nodes;

                    // 查找对应的原始数据以获取Sort值
                    nodes.sort((a, b) => {
                        const aSort = data.find(item => item.Id === a.value)?.Sort || 0;
                        const bSort = data.find(item => item.Id === b.value)?.Sort || 0;
                        return bSort - aSort; // 降序排列
                    });

                    // 递归排序子节点
                    nodes.forEach(node => {
                        if (node.children && node.children.length) {
                            sortTree(node.children);
                        }
                    });

                    return nodes;
                }

                // 对树进行排序
                sortTree(treeData);

                return treeData;
            }

            // 判断节点是否是售前或售后部门或其后代
            function isSaleDepartmentOrDescendant(node, data) {
                if (global_sale_department_ids.includes(node.Id)) return true;

                // 向上查找父节点
                let currentId = node.Pid;
                let loopProtection = 0;

                while (currentId && loopProtection < 100) {
                    if (global_sale_department_ids.includes(currentId)) return true;

                    const parentNode = data.find(item => item.Id === currentId);
                    if (!parentNode) break;

                    currentId = parentNode.Pid;
                    loopProtection++;
                }

                return false;
            }

            // 判断部门ID所属的顶级部门是售前还是售后
            function getDepartmentType(depId, data) {
                // 如果就是顶级部门
                if (depId === global_sale_department_pre_sale) return 'pre_sale'; // 售前
                if (depId === global_sale_department_after_sale) return 'after_sale'; // 售后

                // 向上查找父节点
                let currentId = depId;
                let loopProtection = 0;

                while (currentId && loopProtection < 100) {
                    const currentDept = data.find(item => item.Id === currentId);
                    if (!currentDept) break;

                    // 找到了顶级部门
                    if (currentDept.Pid === global_sale_department_pre_sale) return 'pre_sale'; // 售前
                    if (currentDept.Pid === global_sale_department_after_sale) return 'after_sale'; // 售后

                    // 如果当前部门就是售前或售后部门
                    if (currentDept.Id === global_sale_department_pre_sale) return 'pre_sale'; // 售前
                    if (currentDept.Id === global_sale_department_after_sale) return 'after_sale'; // 售后

                    // 继续向上查找
                    currentId = currentDept.Pid;
                    loopProtection++;
                }

                return 'unknown'; // 未知部门类型
            }

            // 加载销售人员
            function loadStaff(depId) {
                if (!depId) {
                    $('#asst_id').html('<option value="">请先选择部门</option>');
                    form.render('select');
                    return;
                }

                layer.load(2);
                $.ajax({
                    url: '/admin/user/list_low',
                    type: 'post',
                    data: {
                        department_id: depId
                    },
                    success: function(res) {
                        layer.closeAll('loading');
                        if (res.code === 200 && res.data && res.data.length > 0) {
                            var html = '<option value="">请选择销售人员</option>';
                            for (var i = 0; i < res.data.length; i++) {
                                html += '<option value="' + res.data[i].ID + '">' + res.data[i].Name + '</option>';
                            }
                            $('#asst_id').html(html);
                            form.render('select');
                        } else {
                            $('#asst_id').html('<option value="">该部门下暂无人员</option>');
                            form.render('select');
                            layer.msg('该部门下暂无人员', {icon: 0, time: 1500});
                        }
                    },
                    error: function(xhr) {
                        layer.closeAll('loading');
                        $('#asst_id').html('<option value="">请求失败</option>');
                        form.render('select');
                        layer.msg('请求失败：' + xhr.responseText, {icon: 2, time: 2000});
                    }
                });
            }

            // 监听销售人员选择
            form.on('select(asst_id)', function(data) {
                selectedStaffId = data.value;
                console.log("选中的销售人员ID：", selectedStaffId);
            });

            // 确认转移按钮点击事件
            $('#confirm_transfer').click(function() {
                if (!selectedDepId) {
                    layer.msg('请选择部门', {icon: 2, time: 1500});
                    return false;
                }

                if (!selectedStaffId) {
                    layer.msg('请选择销售人员', {icon: 2, time: 1500});
                    return false;
                }

                // 执行批量转移操作
                batchTransferUsers();
            });

            // 取消按钮点击事件
            $('#cancel_btn').click(function() {
                var index = parent.layer.getFrameIndex(window.name);
                parent.layer.close(index);
            });

            // 错误提示中的返回按钮点击事件
            $('.return-btn').click(function() {
                var index = parent.layer.getFrameIndex(window.name);
                parent.layer.close(index);
            });

            // 执行批量转移操作
            function batchTransferUsers() {
                // 判断部门类型
                let departmentType = getDepartmentType(selectedDepId, departmentData);
                let isPreSale = departmentType === 'pre_sale';

                console.log("选中的部门类型:", departmentType, "是否售前:", isPreSale);

                // 显示进度条
                $('#uploadProgress').show();
                element.progress('upload-progress', '0%');

                // 初始化计数器
                var successCount = 0;
                var failCount = 0;
                var totalCount = userIds.length;
                var currentIndex = 0;

                // 串行处理每个用户
                function processNextUser() {
                    if (currentIndex >= totalCount) {
                        // 所有用户处理完成，显示结果对话框
                        $('#uploadProgress').hide();
                        showResultDialog(successCount, failCount, totalCount);
                        return;
                    }

                    var userId = userIds[currentIndex];

                    // 更新进度条
                    var progress = Math.round((currentIndex + 1) / totalCount * 100);
                    element.progress('upload-progress', progress + '%');

                    // 更新进度条上方的文本
                    $('#uploadProgress div:first').text('正在处理用户转移... (' + (currentIndex + 1) + '/' + totalCount + ')');

                    $.ajax({
                        url: '/admin/patient_account/Transfer_user',
                        type: 'post',
                        data: {
                            pat_id: userId,
                            asst_dep_id: selectedDepId,
                            asst_id: selectedStaffId,
                            is_pre_sale: isPreSale ? 1 : 0 // 添加部门类型参数
                        },
                        success: function(res) {
                            if (res.code === 200) {
                                successCount++;
                            } else {
                                failCount++;
                                console.error('用户ID:' + userId + ' 转移失败: ' + res.msg);
                            }
                            currentIndex++;
                            processNextUser();
                        },
                        error: function(xhr) {
                            failCount++;
                            console.error('用户ID:' + userId + ' 网络错误: ' + xhr.responseText);
                            currentIndex++;
                            processNextUser();
                        }
                    });
                }

                // 开始处理第一个用户
                processNextUser();
            }

            // 显示结果统计对话框
            function showResultDialog(successCount, failCount, totalCount) {
                var content = layui.laytpl($('#resultDialogTpl').html()).render({
                    total: totalCount,
                    success: successCount,
                    fail: failCount
                });

                layer.open({
                    type: 1,
                    title: '批量转移完成',
                    content: content,
                    area: ['500px', 'auto'],
                    closeBtn: 0,
                    shadeClose: false
                });
            }

            // 关闭对话框并返回
            window.closeDialog = function() {
                layer.closeAll();
                var index = parent.layer.getFrameIndex(window.name);
                parent.layer.close(index);
                parent.layui.table.reload('mytable');
            };
        });
    </script>
</body>

</html>
