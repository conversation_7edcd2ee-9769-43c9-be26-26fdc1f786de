<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 新建订单</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/layui/layarea_lc.js"></script>
    <script src="/dist/js/main.js"></script>
    <style>
        #floating-submit {
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .image-preview-container {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-top: 15px;
        }

        .image-preview-item {
            position: relative;
            width: 150px;
            height: 150px;
        }

        .image-preview-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 4px;
            cursor: pointer;
        }

        .image-preview-item .delete-btn {
            position: absolute;
            bottom: 5px;
            left: 50%;
            transform: translateX(-50%);
            padding: 2px 10px;
            background-color: rgba(255, 255, 255, 0.9);
            border: 1px solid #ddd;
            border-radius: 3px;
            cursor: pointer;
        }

        .delete-btn:hover {
            background-color: #f56c6c;
            color: white;
        }

        h3 {
            margin: 30px 0 20px 0;
            border-left: 3px solid #666;
            padding-left: 15px;
        }

        .multi_record_check_result {
            display: none;
        }
    </style>
</head>

<body>
    <div class="layui-layout layui-layout-admin">
        <!-- 头部 -->
        <div class="layui-header">
            <div class="layui-logo">幸年堂</div>
            <ul class="layui-nav layui-layout-right">
                <li class="layui-nav-item" style="background-color:transparent!important;padding-right:10px">
                    <a href="javascript:;" style="display:flex;align-items:center;color:#fff"><img
                            src="/dist/images/user.png" class="layui-nav-img"><span class="user_name"></span></a>
                    <dl class="layui-nav-child pop_menu">
                        <dd><i class="iconfont">&#xe62f;</i><a href="change_password.html#7">用户资料</a></dd>
                        <dd><i class="iconfont">&#xe601;</i><a href="javascript:void(0)" class="refresh_cache">刷新缓存</a>
                        </dd>
                        <dd><i class="iconfont">&#xe634;</i><a href="login_out.html">退出登录</a></dd>
                    </dl>
                </li>
            </ul>
        </div>
        <!-- 导航 -->
        <ul class="layui-nav layui-nav-tree layui-nav-side" id="menuContainer" lay-bar="disabled" lay-accordion>
            <div style="margin: 50px 0;color: #bbb;text-align: center;">菜单加载中...</div>
        </ul>


        <!-- 主体内容 -->
        <div class="layui-body">
            <form class="layui-form" lay-filter="form_add" onsubmit="return false">
                <div class="body_child">
                    <div class="layui-panel">
                        <div class="layui-card-header">
                            <div class="layui-row" style="padding-top:10px;">
                                <div class="layui-col-md11 layui-col-sm10">② 订单创建</div>
                            </div>
                        </div>

                        <div class="layui-padding-5" style="padding-top: 0 !important;min-height: 800px;">

                            <h3>患者基础信息</h3>
                            <!-- 填充患者基础信息，接口/admin/patient_profile/detail -->
                            <div class="layui-form">
                                <div id="patient_data" class="layui-row">
                                    <i
                                        class='layui-icon layui-icon-loading-1 layui-anim layui-anim-rotate layui-anim-loop'></i>
                                </div>
                            </div>

                            <h3>病历及基础信息</h3>

                            <!-- 病历ID显示 -->
                            <div class="layui-row">
                                <div class="layui-col-md12">
                                    <div style="margin: 0 0 30px 40px;display: flex;align-items: center;">
                                        <span style="font-size: 18px;font-weight: bold;"></span>病历ID：</span>
                                        <span style="font-size: 18px;font-weight: bold;" class="record_ids"></span>
                                    </div>
                                </div>
                            </div>

                            <!-- 如果病历有多个，则显示多病历检测同医生、同医助部分 -->

                            <div class="layui-row layui-form multi_record_check_result">
                                <div style=" display: flex;">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">主病历：</label>
                                        <div class="layui-input-block">
                                            <select name="record_ids_default" id="record_ids_default">
                                                <option value="">请选择</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="layui-form-mid layui-word-aux" style="margin-left: 10px;">
                                        * 请选择一个病历作为主病历
                                    </div>
                                </div>
                            </div>

                            <div class="layui-row layui-form">
                                <div class="layui-form-item" id="area-picker">
                                    <label class="layui-form-label">收货地址：</label>
                                    <div class="layui-input-block">
                                        <div class="layui-input-inline" style="width: 200px;">
                                            <select name="province" class="province-selector" lay-filter="province-1">
                                                <option value="">请选择省</option>
                                            </select>
                                        </div>
                                        <div class="layui-input-inline" style="width: 200px;">
                                            <select name="city" class="city-selector" lay-filter="city-1">
                                                <option value="">请选择市</option>
                                            </select>
                                        </div>
                                        <div class="layui-input-inline" style="width: 200px;">
                                            <select name="county" class="county-selector" lay-filter="county-1">
                                                <option value="">请选择区</option>
                                            </select>
                                        </div>
                                        <div class="layui-input-inline" style="width:360px;">
                                            <input type="text" name="address" placeholder="请输入具体地址" autocomplete="off"
                                                class="layui-input">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <h3>金额及截图</h3>

                            <div class="layui-row layui-form">
                                <div class="layui-col-md3">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">订单总额：</label>
                                        <div class="layui-input-block">
                                            <input type="number" name="total_money" class="layui-input" min="1"
                                                max="10000">
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-col-md3">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">已付定金：</label>
                                        <div class="layui-input-block">
                                            <input type="number" name="pre_pay" class="layui-input" min="0" max="10000">
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-col-md3">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">待收款项：</label>
                                        <div class="layui-input-inline">
                                            <div class="layui-form-mid owing"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="layui-row layui-form">
                                <div class="layui-col-md3">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">实付状态：</label>
                                        <div class="layui-input-wrap">
                                            订金待审
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-col-md3">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">发货时间：</label>
                                        <div class="layui-input-inline">
                                            <div class="layui-input-wrap">
                                                <div class="layui-input-prefix">
                                                    <i class="layui-icon layui-icon-date"></i>
                                                </div>
                                                <input type="text" class="layui-input" id="delivery_time"
                                                    placeholder="请选择发货时间" name="delivery_time">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>


                            <!-- 图片上传相关HTML开始 -->
                            <div class="layui-form-item">
                                <div class="layui-col-md6">
                                    <!-- 收款凭证 -->
                                    <div class="layui-upload layui-padding-4" style="border: 1px solid #eee;">
                                        <!-- 左侧按钮，右侧审核文字，文字居右侧对齐 -->
                                        <div class="layui-upload-btn"
                                            style="display: flex;align-items: center;justify-content: flex-start;">

                                            <button type="button" class="layui-btn upload_big_btn"
                                                id="ID-upload-demo-btn-1">
                                                <div class="btn_big_font"><i
                                                        class="layui-icon layui-icon-upload btn_big_font"></i> 代收款凭证上传
                                                </div>
                                                <div>jpg、png、jpeg、bmp、gif格式，2M以内</div>
                                                <div>可点选上传和拖拽上传</div>
                                            </button>

                                        </div>
                                        <div class="image-preview-container" id="photo_cr_pic_container">
                                            <!-- 图片预览将在这里动态插入 -->
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-col-md6">
                                    <!-- 收款截图 -->
                                    <div class="layui-upload layui-padding-4"
                                        style="border: 1px solid #eee;margin-left: 8px;">
                                        <!-- 左侧按钮，右侧审核文字，文字居右侧对齐 -->
                                        <div class="layui-upload-btn"
                                            style="display: flex;align-items: center;justify-content: flex-start;">
                                            <button type="button" class="layui-btn upload_big_btn"
                                                id="ID-upload-demo-btn-2">
                                                <div class="btn_big_font"><i
                                                        class="layui-icon layui-icon-upload btn_big_font"></i> 收款截图上传
                                                </div>
                                                <div>jpg、png、jpeg、bmp、gif格式，2M以内</div>
                                                <div>可点选上传和拖拽上传</div>
                                            </button>
                                        </div>
                                        <div class="image-preview-container" id="photo_pay_pic_container">
                                            <!-- 图片预览将在这里动态插入 -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- 图片上传相关HTML结束 -->


                            <!-- 赠品表格，赠品ID，赠品名称，数量，价格，备注 -->
                            <div style="display: flex;align-items: center;justify-content: space-between;">
                                <h3>赠品选择</h3>
                                <button class="layui-btn layui-btn-primary layui-border-green"
                                    id="add_gift_btn">添加赠品数据</button>
                            </div>
                            <table class="layui-table">
                                <thead>
                                    <tr>
                                        <th>库存ID</th>
                                        <th>赠品ID</th>
                                        <th>赠品名称</th>
                                        <th>数量</th>
                                        <th>单价</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="gift_table_body">
                                    <!-- 表格内容将在这里动态插入 -->
                                </tbody>
                            </table>


                            <div class="layui-row" style="display: flex;justify-content: center;margin-top: 100px;">
                                <button class="layui-btn" lay-submit lay-filter="formSubmitBtn"
                                    style="margin-right: 50px;">确认新建</button>
                                <button type="reset" class="layui-btn layui-btn-primary"
                                    onclick="history.go(-1)">取消</button>
                            </div>


                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>


    <script>
        layui.use(['element', 'layer', 'util'], function () {
            var element = layui.element;
            var form = layui.form;
            var layer = layui.layer;
            var util = layui.util;
            var laydate = layui.laydate;
            var table = layui.table;
            var upload = layui.upload;
            var dropdown = layui.dropdown;
            var $ = layui.$;
            var layarea_lc = layui.layarea_lc;
            var record_ids = request.get('record_ids');
            if (!record_ids) {
                layer.msg('病历ID不存在', { icon: 2, time: 2000 });
                return false;
            }
            // 初始化日期选择器
            laydate.render({
                elem: '#delivery_time',
                type: 'date'
            });
            
            // 初始化地区选择器
            layarea_lc.render({
                elem: '#area-picker',
                name: 'name',
                change: function (res) {
                    console.log(res);
                }
            });
            
            //根据处方ID求出来的病历ID来填充患者基础信息
            var render_patient_info = function (record_ids) {
                layer.load(2);
                $.ajax({
                    url: serverUrl + "/admin/patient_profile/get_patient_ids_by_record_ids",
                    type: "post",
                    data: { record_ids: record_ids },
                    success: function (res) {
                        layer.closeAll('loading');
                        let data = res.data;
                        let html = '';
                        if (data.length > 1) {
                            layer.msg("如订单出现多个患者信息，则属于历史遗留，后续已对患者规则进行了限制：1个订单，只允许1个帐号下的1个患者", {
                                icon: 0,
                                time: 10000
                            });
                        }
                        for (let i = 0; i < data.length; i++) {
                            html += `
                                        <!-- 第1大行 -->
                                        <div class="layui-col-xs3">
                                            <div class="info-item">
                                                <span class="info-label">手机号码：</span>
                                                <span class="info-content">${data[i].Phone || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">患者姓名：</span>
                                                <span class="info-content">${data[i].Name || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">患者性别：</span>
                                                <span class="info-content">${data[i].Sex == 0 ? '女' : '男' || '-'}</span>
                                            </div>
                                        </div>

                                        <div class="layui-col-xs3">
                                            <div class="info-item">
                                                <span class="info-label">关系：</span>
                                                <span class="info-content">${FamilyRelation[data[i].Relation] || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">身高：</span>
                                                <span class="info-content">${data[i].Height || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">体重：</span>
                                                <span class="info-content">${data[i].Weight || '-'}</span>
                                            </div>
                                        </div>

                                        <div class="layui-col-xs3">
                                            <div class="info-item">
                                                <span class="info-label">身份证号：</span>
                                                <span class="info-content">${data[i].Idcard || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">年龄：</span>
                                                <span class="info-content">${date2age(data[i].Born_date)}岁</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">医保卡：</span>
                                                <span class="info-content">${data[i].Ins_card_num || '-'}</span>
                                            </div>
                                        </div>

                                        <div class="layui-col-xs3">
                                            <div class="info-item">
                                                <span class="info-label">患者来源：</span>
                                                <span class="info-content">${Patient_From[data[i].Patient_from] || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">患者等级：</span>
                                                <span class="info-content">${data[i].Level + '级' || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">微信号：</span>
                                                <span class="info-content">${data[i].Weixin || '-'}</span>
                                            </div>
                                        </div>

                                        
                                        <div class="layui-col-xs6">
                                            <div class="info-item">
                                                <span class="info-label">患者备注：</span>
                                                <span class="info-content">${data[i].Customer_notes || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">既往病史：</span>
                                                <span class="info-content">${data[i].Medical_history || '-'}</span>
                                            </div>
                                        </div>
                                        <div class="layui-col-xs6">
                                            <div class="info-item">
                                                <span class="info-label">主诉：</span>
                                                <span class="info-content">${data[i].Chief_complaint || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">过敏史：</span>
                                                <span class="info-content">${data[i].Allergies || '-'}</span>
                                            </div>
                                        </div>
                                        <div class="layui-col-xs12">
                                            <div class="info-item">
                                                <span class="info-label">详细地址：</span>
                                                <span class="info-content">${data[i].Address.replace("|", " ") || '-'}</span>
                                            </div>
                                        </div>
                            `;
                        }
                        $('#patient_data').html(html);
                    },
                    error: function () {
                        layer.msg('获取患者信息失败', { icon: 2 });
                    }
                });
            };
            render_patient_info(record_ids);
            // 判断是否是多病历检测
            $.ajax({
                url: '/admin/order/exist_by_record_check',
                type: 'POST',
                data: { record_ids: record_ids },
                success: function (res) {
                    if (!res.data) {
                        layer.confirm('经检测这些病历并非隶属于同一位医生或医助，请选择一个病历当作订单的主病历作为列表展示用。<br><font color="red">注：其它病历也会与该订单关联。</font>', { icon: 3, title: '提示' }, function (index) {
                            layer.close(index);
                            var idsArray = record_ids.split(',');
                            for (let i = 0; i < idsArray.length; i++) {
                                $('#record_ids_default').append(
                                    $('<option>')
                                        .val(idsArray[i])  // 值保持原样
                                        .text('B' + idsArray[i]) // ID前添加B
                                );
                            }
                            form.render('select');
                            $('.multi_record_check_result').show(300);
                        });
                    }
                }
            });
            //各超级链接，中间以逗号隔开
            var record_ids_links = record_ids.split(',').map(function (id) {
                return `<a href="/admin/patient_records_show.html?id=${id}#/admin/patient_records_list.html" class="btn_arg_pm" target="_blank">B${id}</a>`;
            }).join('');
            $('.record_ids').html(record_ids_links);
            // 渲染日期
            render_menu($);
            render_button($);
            // 删除图片处理函数
            function deleteImage(element, filename, filepath) {
                const item = $(element).parent();
                handleDeleteImage(item, filename, filepath);
            }

            // 删除确认和AJAX处理
            function handleDeleteImage(item, filename, filepath) {
                layer.confirm('确定删除该图片吗？', { icon: 3, title: '提示' }, function (index) {
                    $.ajax({
                        url: '/admin/normal_pic_del',
                        type: 'POST',
                        data: {
                            filename: filename,
                            category: filepath.split('/')[4]
                        },
                        success: function (res) {
                            if (res.code === 200) {
                                item.remove();
                                layer.msg('删除成功', { icon: 1, time: 1000 });
                            }
                        },
                        error: function (data) {
                            layer.confirm('删除失败: ' + data.responseJSON.msg + '，是否移除该图片？',
                                { icon: 3, title: '提示' },
                                function (index) {
                                    item.remove();
                                    layer.msg('移除成功', { icon: 1, time: 1000 });
                                }
                            );
                        }
                    });
                    layer.close(index);
                });
            }

            function appendTableRow(tableId, filepath, filename) {
                const containerId = tableId.replace('_table', '_container');
                const container = $(containerId);
                const imageItem = $(`
                    <div class="image-preview-item" data-filename="${filename}" data-filepath="${filepath}">
                        <img src="${filepath}">
                        <div class="delete-btn">删除</div>
                    </div>
                `);

                // 绑定图片点击事件
                imageItem.find('img').on('click', function () {
                    layer.photos({
                        photos: {
                            title: '查看图片',
                            data: [{ src: filepath }]
                        },
                        footer: false
                    });
                });

                // 绑定删除按钮事件
                imageItem.find('.delete-btn').on('click', function () {
                    deleteImage(this, filename, filepath);
                });

                container.append(imageItem);
            }

            function initFileUpload(buttonId, tableId, category = "") {
                upload.render({
                    elem: buttonId,
                    url: '/admin/upload_normal_pic',
                    multiple: true,
                    data: {
                        category: category,
                    },
                    before: function (obj) {
                        // 2025-04-21 移除图片数量限制
                    },
                    done: function (res) {
                        if (res.code === 200) {
                            const fileInfo = res.data[0];
                            const filename = fileInfo.filename;
                            const filepath = fileInfo.filepath;
                            appendTableRow(tableId, filepath, filename);
                            layer.msg('上传成功', { icon: 1, time: 1000 });
                        } else {
                            layer.msg('上传失败: ' + res.msg, { icon: 2, time: 1000 });
                        }
                    },
                    error: function () {
                        layer.msg('上传失败', { icon: 2, time: 1000 });
                    }
                });
            }

            // 初始化上传
            initFileUpload('#ID-upload-demo-btn-1', '#photo_cr_pic_table', 'photo_cr_pic');
            initFileUpload('#ID-upload-demo-btn-2', '#photo_pay_pic_table', 'photo_pay_pic');

            // 添加赠品数据
            $('#add_gift_btn').on('click', function () {
                layer.open({
                    type: 2,
                    title: '添加赠品数据',
                    area: ['900px', '720px'],
                    shadeClose: true,
                    content: '/admin/warehouse_gifts_add_toolspage.html'
                });
            });

            // 提交表单
            form.on('submit(formSubmitBtn)', function (data) {
                let field = data.field;
                field.record_ids = record_ids;
                // 处理地址信息
                field.address = field.province + '|' + field.city + '|' + field.county + '|' + field.address;
                
                // 1. 收集图片URL数据
                let photo_cr_pics = [];
                $('#photo_cr_pic_container .image-preview-item').each(function () {
                    photo_cr_pics.push($(this).data('filename'));
                });
                field.cr_pic = photo_cr_pics.join('\n');

                let photo_pay_pics = [];
                $('#photo_pay_pic_container .image-preview-item').each(function () {
                    photo_pay_pics.push($(this).data('filename'));
                });
                field.pay_pic = photo_pay_pics.join('\n');

                // 2. 收集赠品数据
                let gifts = [];
                $('#gift_table_body tr').each(function () {
                    let nums = $(this).find('td:eq(3)').find('input').val() || 0;
                    let gift = {
                        wh_gift_id: $(this).find('td:eq(0)').text(),  // 库存ID
                        nums: nums,
                        // user_notes: $(this).find('td:eq(6)').find('input').val()  // 备注
                    };
                    gifts.push(gift);
                });
                field.gifts = gifts;
                layer.load(2);
                $.ajax({
                    url: serverUrl + '/admin/order/add',
                    type: 'POST',
                    data: field,
                    success: function (res) {
                        layer.closeAll('loading');
                        if (res.code === 200) {
                            layer.confirm('订单新建成功，是否要返回列表页？', { icon: 3, title: '提示' }, function (index) {
                                layer.close(index);
                                window.location.href = 'order_list.html#/admin/order_list.html';
                            });
                        } else {
                            layer.msg(res.msg, { icon: 2, time: 2000 });
                        }
                    },
                    error: function (data) {
                        layer.closeAll('loading');
                        layer.msg('提交失败：' + data.responseJSON.msg, { icon: 2, time: 3000 });
                    }
                });

                return false;
            });

            // 处理订单总额输入
            $('input[name="total_money"]').on('input', function () {
                var value = this.value;

                // 去除开头的0
                if (value.startsWith('0')) {
                    value = value.replace(/^0+/, '');
                }

                // 限制范围
                if (value > 10000) value = 10000;
                if (value < 1) value = 1;

                this.value = value;

                // 检查并调整已付定金
                var prePayInput = $('input[name="pre_pay"]');
                if (Number(prePayInput.val()) > Number(value)) {
                    prePayInput.val(value);
                }

                // 计算并更新待收款项
                updateRemainingBalance();
            });
            // 处理已付定金输入
            $('input[name="pre_pay"]').on('input', function () {
                var value = this.value;
                var totalMoney = $('input[name="total_money"]').val();
                if (!totalMoney) {
                    this.value = '';
                    return;
                }
                if (value.startsWith('0') && value.length > 1) {
                    value = value.replace(/^0+/, '');
                }
                if (value < 0) value = 0;
                if (value > Number(totalMoney)) value = totalMoney;
                this.value = value;
                updateRemainingBalance();
            });
            function updateRemainingBalance() {
                var totalMoney = Number($('input[name="total_money"]').val()) || 0;
                var prePay = Number($('input[name="pre_pay"]').val()) || 0;
                var remainingBalance = totalMoney - prePay;
                $('.owing').text(remainingBalance);
            }
            $('input[type="number"]').on('paste', function (e) {
                var pastedData = e.originalEvent.clipboardData.getData('text');
                if (!/^\d*$/.test(pastedData)) {
                    e.preventDefault();
                    layer.msg('只能输入数字', { icon: 2 });
                }
            });

        });
    </script>
</body>

</html>