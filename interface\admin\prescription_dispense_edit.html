<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 处方调剂</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
    <style>
        h3 {
            margin: 30px 0 20px 0;
            border-left: 3px solid #666;
            padding-left: 15px;
        }

        /* 重置打印区域的背景色 */
        .print-area {
            background-color: #fff !important;
        }

        .layui-card {
            background-color: #fff !important;
        }

        /* 修改打印预览的样式 */
        @media print {
            body {
                background-color: #fff !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            .print-area {
                background-color: #fff !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            .layui-card {
                background-color: #fff !important;
                box-shadow: none !important;
            }
        }

        .check_button {
            width: 120px;
            text-align: center;
        }

        .nums {
            width: 100px;
            display: inline;
            margin-right: 10px;
        }

        .type_btn button {
            width: 120px;
        }
    </style>
</head>

<body>
    <div class="layui-layout layui-layout-admin">
        <!-- 头部 -->
        <div class="layui-header">
            <div class="layui-logo">幸年堂</div>
            <ul class="layui-nav layui-layout-right">
                <li class="layui-nav-item" style="background-color:transparent!important;padding-right:10px">
                    <a href="javascript:;" style="display:flex;align-items:center;color:#fff"><img
                            src="/dist/images/user.png" class="layui-nav-img"><span class="user_name"></span></a>
                    <dl class="layui-nav-child pop_menu">
                        <dd><i class="iconfont">&#xe62f;</i><a href="change_password.html#7">用户资料</a></dd>
                        <dd><i class="iconfont">&#xe601;</i><a href="javascript:void(0)" class="refresh_cache">刷新缓存</a>
                        </dd>
                        <dd><i class="iconfont">&#xe634;</i><a href="login_out.html">退出登录</a></dd>
                    </dl>
                </li>
            </ul>
        </div>
        <!-- 导航 -->
        <ul class="layui-nav layui-nav-tree layui-nav-side" id="menuContainer" lay-bar="disabled" lay-accordion>
            <div style="margin: 50px 0;color: #bbb;text-align: center;">菜单加载中...</div>
        </ul>

        <!-- 主体内容 -->
        <div class="layui-body">
            <div class="body_child">
                <div class="layui-panel">
                    <div class="layui-card-header">
                        <div class="layui-row" style="padding-top:10px;">
                            <div class="layui-col-md11 layui-col-sm10">处方调剂</div>
                        </div>
                    </div>

                    <div class="layui-padding-3" style="min-height: 600px;">

                        <h3>患者基础信息</h3>
                        <!-- 填充患者基础信息，接口/admin/patient_profile/detail -->
                        <div class="layui-form">
                            <div id="patient_data" class="layui-row">
                                <i
                                    class='layui-icon layui-icon-loading-1 layui-anim layui-anim-rotate layui-anim-loop'></i>
                            </div>
                        </div>


                        <h3>处方信息</h3>
                        <div class="layui-row">
                            <div class="layui-col-md4">
                                <div class="info-item">
                                    <div class="info-label">处方状态：</div>
                                    <div class="info-content" id="status"></div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">诊断信息：</div>
                                    <div class="info-content" id="diagnosis"></div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">治疗计划：</div>
                                    <div class="info-content" id="tx_plan"></div>
                                </div>
                            </div>
                            <div class="layui-col-md4">
                                <div class="info-item">
                                    <div class="info-label">疗程：</div>
                                    <div class="info-content">
                                        <span id="tx_day"></span> 天，
                                        药品类型：<span id="tx_type"></span>
                                    </div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">使用方法：</div>
                                    <div class="info-content" id="dosage_detail">
                                        每 <span id="dosage_1"></span> 天
                                        <span id="dosage_2"></span> 次，
                                        每次 <span id="dosage_3"></span>
                                        * <span id="dosage_4"></span>
                                    </div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">医嘱：</div>
                                    <div class="info-content" id="askfor"></div>
                                </div>
                            </div>
                            <div class="layui-col-md4">
                                <div class="info-item">
                                    <div class="info-label">禁忌：</div>
                                    <div class="info-content" id="dosage_5"></div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">创建时间：</div>
                                    <div class="info-content" id="create_time"></div>
                                </div>
                            </div>
                        </div>

                        <h3>处方单 <i class="layui-icon layui-icon-tips layui-font-14"
                                title="这个处方单不对，记得以前做处方时，最后会加个斜线，代表没有药品了" style="margin-left: 5px;"></i></h3>

                        <div class="layui-container print-area" id="prescriptionArea">
                            <div class="layui-card-body">
                                <div class="layui-card"
                                    style="width: 1200px;padding: 20px;margin: 0 auto;box-shadow: 0 0 10px #dddddd;">
                                    <div class="layui-card-body">
                                        <!-- 处方标题 -->
                                        <div
                                            style="display: flex;justify-content: space-between;align-items: center;margin-bottom: 20px;">
                                            <h2>RP</h2>
                                            <i class="layui-icon layui-icon-print"
                                                style="font-size: 25px;margin-right: 10px;"
                                                onclick="printPreview()"></i>
                                        </div>

                                        <!-- 药品表格 -->
                                        <table class="layui-table" lay-skin="line">
                                            <tbody id="RP_table_body">
                                            </tbody>
                                        </table>

                                        <!-- 签名区域 -->
                                        <div class="layui-form sign_box">
                                            <div class="layui-row layui-col-space30">
                                                <div class="layui-col-xs3">
                                                    <div class="layui-form-item">
                                                        <label class="layui-form-label">医生：</label>
                                                        <div class="layui-input-block sign_pic" id="verify_1">

                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="layui-col-xs3">
                                                    <div class="layui-form-item">
                                                        <label class="layui-form-label">药师：</label>
                                                        <div class="layui-input-block sign_pic" id="verify_2">
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="layui-col-xs3">
                                                    <div class="layui-form-item">
                                                        <label class="layui-form-label">调配：</label>
                                                        <div class="layui-input-block sign_pic" id="verify_3">
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="layui-col-xs3">
                                                    <div class="layui-form-item">
                                                        <label class="layui-form-label">复核发药：</label>
                                                        <div class="layui-input-block sign_pic" id="verify_4">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div style="display: flex;justify-content: space-between;align-items: center;">
                            <h3>处方药品信息</h3>
                            <div class="layui-row" style="display: flex;justify-content: flex-end;">
                                <button class="layui-btn layui-btn-primary layui-border-green"
                                    id="add_meds_btn">药材修改</button>
                                <button class="layui-btn perm_check_btn check_button" res_id="157">核对</button>
                            </div>
                        </div>

                        <table class="layui-table">
                            <thead>
                                <tr>
                                    <th style="width: 10%;">库存ID</th>
                                    <th style="width: 10%;">药材ID</th>
                                    <th style="width: 20%;">药材名称</th>
                                    <th style="width: 15%;">数量</th>
                                    <th style="width: 20%;">抓药克数</th>
                                    <th style="width: 25%;">工艺制法</th>
                                </tr>
                            </thead>
                            <tbody id="prescription_drug_table_body">
                                <!-- 药品数据将在这里动态插入 -->
                            </tbody>
                        </table>

                        <div class="layui-row" style="display: flex;justify-content: center;margin-top:50px;">
                            <button class="layui-btn perm_check_btn layui-btn-disabled" res_id="157"
                                style="padding: 0 50px;margin-right: 30px;" id="confirm_btn">确认制药</button>
                            <button type="reset" class="layui-btn layui-btn-primary"
                                onclick="history.go(-1)">取消</button>
                        </div>


                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        layui.use(['element', 'layer', 'util'], function () {
            var element = layui.element;
            var layer = layui.layer;
            var util = layui.util;
            var $ = layui.$;
            // 页面顶部插入缓存的用户数据
            let local_userinfo = localStorage.getItem('local_userinfo');
            let user_name = "";
            if (local_userinfo) {
                local_userinfo = JSON.parse(local_userinfo);
                user_name = local_userinfo.Name + " ( " + local_userinfo.Roles + " )";
            } else {
                user_name = "未登录";
            }
            $('.user_name').text(user_name);
            render_menu($);
            $('.refresh_cache').click(function () {
                refresh_cache($);
            });

            // 获取处方ID
            var prescription_id = request.get('id');
            if (!prescription_id) {
                layer.msg('处方ID不能为空', { icon: 2 });
                return;
            }

            // 核对按钮点击事件
            let isChecked = false;
            $('.check_button').on('click', function () {
                const $btn = $(this);
                if (!isChecked) {
                    // 当点击"核对"时，显示确认框
                    layer.confirm('确定要核对该处方吗？', {
                        btn: ['确定', '取消'],
                        title: '确认'
                    }, function (index) {
                        // 用户点击确定
                        isChecked = true;
                        $btn.html('<i class="layui-icon layui-icon-lock"></i>取消核对');
                        $('#confirm_btn').removeClass('layui-btn-disabled');
                        layer.close(index);
                    }, function (index) {
                        // 用户点击取消
                        layer.close(index);
                    });
                } else {
                    // 当是取消核对时，直接执行
                    isChecked = false;
                    $btn.html('核对');
                    $('#confirm_btn').addClass('layui-btn-disabled');
                }
            });

            //根据处方ID求出来的病历ID来填充患者基础信息
            var render_patient_info = function (record_ids) {
                layer.load(2);
                $.ajax({
                    url: serverUrl + "/admin/patient_profile/get_patient_ids_by_record_ids",
                    type: "post",
                    data: { record_ids: record_ids },
                    success: function (res) {
                        layer.closeAll('loading');
                        let data = res.data;
                        let html = '';
                        for (let i = 0; i < data.length; i++) {
                            html += `
                                        <!-- 第1大行 -->
                                        <div class="layui-col-xs3">
                                            <div class="info-item">
                                                <span class="info-label">手机号码：</span>
                                                <span class="info-content">${data[i].Phone || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">患者姓名：</span>
                                                <span class="info-content">${data[i].Name || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">患者性别：</span>
                                                <span class="info-content">${data[i].Sex == 0 ? '女' : '男' || '-'}</span>
                                            </div>
                                        </div>

                                        <div class="layui-col-xs3">
                                            <div class="info-item">
                                                <span class="info-label">关系：</span>
                                                <span class="info-content">${FamilyRelation[data[i].Relation] || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">身高：</span>
                                                <span class="info-content">${data[i].Height || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">体重：</span>
                                                <span class="info-content">${data[i].Weight || '-'}</span>
                                            </div>
                                        </div>

                                        <div class="layui-col-xs3">
                                            <div class="info-item">
                                                <span class="info-label">身份证号：</span>
                                                <span class="info-content">${data[i].Idcard || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">年龄：</span>
                                                <span class="info-content">${date2age(data[i].Born_date)}岁</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">医保卡：</span>
                                                <span class="info-content">${data[i].Ins_card_num || '-'}</span>
                                            </div>
                                        </div>

                                        <div class="layui-col-xs3">
                                            <div class="info-item">
                                                <span class="info-label">患者来源：</span>
                                                <span class="info-content">${Patient_From[data[i].Patient_from] || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">患者等级：</span>
                                                <span class="info-content">${data[i].Level + '级' || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">微信号：</span>
                                                <span class="info-content">${data[i].Weixin || '-'}</span>
                                            </div>
                                        </div>


                                        <div class="layui-col-xs6">
                                            <div class="info-item">
                                                <span class="info-label">患者备注：</span>
                                                <span class="info-content">${data[i].Customer_notes || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">既往病史：</span>
                                                <span class="info-content">${data[i].Medical_history || '-'}</span>
                                            </div>
                                        </div>
                                        <div class="layui-col-xs6">
                                            <div class="info-item">
                                                <span class="info-label">主诉：</span>
                                                <span class="info-content">${data[i].Chief_complaint || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">过敏史：</span>
                                                <span class="info-content">${data[i].Allergies || '-'}</span>
                                            </div>
                                        </div>
                                        <div class="layui-col-xs12">
                                            <div class="info-item">
                                                <span class="info-label">详细地址：</span>
                                                <span class="info-content">${data[i].Address.replace("|", " ") || '-'}</span>
                                            </div>
                                        </div>
                            `;
                        }
                        $('#patient_data').html(html);
                    },
                    error: function (res) {
                        layer.msg(res.responseJSON.msg, { icon: 2, time: 1000 });
                    }
                });
            }
            // 加载处方调剂
            $.ajax({
                url: '/admin/prescription/detail',
                type: 'POST',
                data: { id: prescription_id },
                success: function (res) {
                    if (res.code === 200) {
                        var data = res.data;

                        // 填充基本信息
                        $('#diagnosis').text(data.Diagnosis || '-');
                        $('#tx_plan').text(data.Tx_plan || '-');
                        $('#askfor').text(data.Askfor || '-');
                        $('#tx_day').text(data.Tx_day || '-');
                        $('#tx_type').text(data.Tx_type || '-');

                        // 处理用法用量
                        if (data.Dosage) {
                            var dosage = data.Dosage.split('|');
                            $('#dosage_1').text(dosage[0] || '-');
                            $('#dosage_2').text(dosage[1] || '-');
                            $('#dosage_3').text(dosage[2] || '-');
                            $('#dosage_4').text(dosage[3] || '-');
                            $('#dosage_5').text(dosage[4] || '-');
                        }

                        // 填充患者基础信息
                        render_patient_info(data.Record_id);

                        $('#status').text(Pre_Status[data.Status] || '未知状态');

                        // 格式化时间
                        $('#create_time').text(data.Create_time ? data.Create_time.replace('T', ' ').replace('Z', '') : '-');

                        // 医生签名
                        let Verify_1 = data.Verify_1;
                        let Verify_1_str = '';
                        if (Verify_1 == 2) {
                            Verify_1_str = "<div class='layui-form-mid'><font color='red'><i class=\"iconfont\">&#xe637;</i> " + data.Verify_1_desc + "</font></div>";
                        } else if (Verify_1 == 0) {
                            Verify_1_str = "<div class='layui-form-mid'><font color='blue'>待审核</font></div>";
                        } else {
                            Verify_1_str = "<img src='/static/uploads/icons/sign_" + data.Verify_1_user_id + ".png?" + new Date().getTime() + "'/>";
                        }
                        $('#verify_1').html(Verify_1_str);

                        // 审方签名
                        let Verify_2 = data.Verify_2;
                        let Verify_2_str = '';
                        if (Verify_2 == 2) {
                            Verify_2_str = "<div class='layui-form-mid'><font color='red'><i class=\"iconfont\">&#xe637;</i> " + data.Verify_2_desc + "</font></div>";
                        } else if (Verify_2 == 0) {
                            Verify_2_str = "<div class='layui-form-mid'><font color='blue'>待审核</font></div>";
                        } else {
                            Verify_2_str = "<img src='/static/uploads/icons/sign_" + data.Verify_2_user_id + ".png?" + new Date().getTime() + "'/>";
                        }
                        $('#verify_2').html(Verify_2_str);

                        // 调配签名
                        let Verify_3 = data.Verify_3;
                        let Verify_3_str = '';
                        if (Verify_3 == 2) {
                            Verify_3_str = "<div class='layui-form-mid'><font color='red'><i class=\"iconfont\">&#xe637;</i> " + data.Verify_3_desc + "</font></div>";
                        } else if (Verify_3 == 0) {
                            Verify_3_str = "<div class='layui-form-mid'><font color='blue'>待审核</font></div>";
                        } else {
                            Verify_3_str = "<img src='/static/uploads/icons/sign_" + data.Verify_3_user_id + ".png?" + new Date().getTime() + "'/>";
                        }
                        $('#verify_3').html(Verify_3_str);

                        // 复核发药签名
                        let Verify_4 = data.Verify_4;
                        let Verify_4_str = '';
                        if (Verify_4 == 2) {
                            Verify_4_str = "<div class='layui-form-mid'><font color='red'><i class=\"iconfont\">&#xe637;</i> " + data.Verify_4_desc + "</font></div>";
                        } else if (Verify_4 == 0) {
                            Verify_4_str = "<div class='layui-form-mid'><font color='blue'>待审核</font></div>";
                        } else {
                            Verify_4_str = "<img src='/static/uploads/icons/sign_" + data.Verify_4_user_id + ".png?" + new Date().getTime() + "'/>";
                        }
                        $('#verify_4').html(Verify_4_str);

                        // 加载药品信息
                        loadPrescriptionDrugs(prescription_id);
                    } else {
                        layer.msg(res.msg || '加载失败', { icon: 2 });
                    }
                },
                error: function (res) {
                    layer.msg(res.responseJSON.msg, { icon: 2, time: 1000 });
                }
            });
            // 添加药品按钮点击事件
            $('#add_meds_btn').on('click', function () {
                // 先获取当前处方的药品数据
                $.ajax({
                    url: '/admin/prescription/drug',
                    type: 'POST',
                    data: { pre_id: prescription_id },
                    success: function (res) {
                        if (res.code === 200) {
                            // 格式化药品数据
                            var existingDrugs = res.data.map(function (item) {
                                return {
                                    id: item.ID,
                                    wh_drug_id: item.Wh_drug_id,
                                    drug_id: item.Drug_id,
                                    quantity: item.Quantity,
                                    name: item.Name,
                                    price: item.Price
                                };
                            });

                            // 设置全局变量，供iframe使用
                            window.drugModalData = {
                                prescriptionId: prescription_id,
                                existingDrugs: existingDrugs
                            };

                            // 打开模态框
                            layer.open({
                                type: 2,
                                title: '药材数据修改',
                                area: ['1200px', '800px'],
                                shadeClose: true,
                                content: '/admin/warehouse_drug_edit_toolspage.html'
                            });
                        } else {
                            layer.msg('获取药品数据失败：' + res.msg, { icon: 2 });
                        }
                    }
                });
            });
            // 加载处方药品信息
            function loadPrescriptionDrugs(prescription_id) {
                $.ajax({
                    url: '/admin/prescription/drug',
                    type: 'POST',
                    data: { pre_id: prescription_id },
                    success: function (res) {
                        if (res.code === 200) {
                            var tableBody = $('#prescription_drug_table_body');
                            var RP_table_body = $('#RP_table_body');
                            tableBody.empty();
                            RP_table_body.empty();

                            res.data.forEach(function (item) {
                                var row = `
                                    <tr data-id="${item.ID}">
                                        <td>${item.Wh_drug_id || '-'}</td>
                                        <td>${item.Drug_id || '-'}</td>
                                        <td>${item.Name || '-'}</td>
                                        <td>${item.Quantity || '-'}g</td>
                                        <td>
                                            <input type="text" class="layui-input nums" name="nums" placeholder="请输入"/>g
                                        </td>
                                        <td class="type_btn">
                                            <button class="layui-btn layui-btn-primary process-btn" data-type="0">打粉</button>
                                            <button class="layui-btn layui-btn-primary process-btn" data-type="1">煮水</button>
                                            <input type="hidden" name="process" value=""/>
                                        </td>
                                    </tr>
                                `;
                                tableBody.append(row);
                            });

                            // 工艺按钮点击事件
                            $('.process-btn').on('click', function () {
                                const $btn = $(this);
                                const $row = $btn.closest('tr');
                                // 移除同一行所有按钮的高亮
                                $row.find('.process-btn').removeClass('layui-bg-orange').addClass('layui-btn-primary');
                                // 高亮当前按钮
                                $btn.removeClass('layui-btn-primary').addClass('layui-bg-orange');
                                // 更新隐藏的工艺值
                                $row.find('input[name="process"]').val($btn.data('type'));
                            });

                            // 动态生成3列N行的RP_table_body - 处方单
                            var rowsPerColumn = Math.ceil(res.data.length / 3); // 每列的行数
                            for (let i = 0; i < rowsPerColumn; i++) {
                                var RP_row = `
                                    <tr>
                                        <td>${res.data[i] ? res.data[i].Name + ' ' + res.data[i].Nums + 'g' : ''}</td>
                                        <td>${res.data[i + rowsPerColumn] ? res.data[i + rowsPerColumn].Name + ' ' + res.data[i + rowsPerColumn].Nums + 'g' : ''}</td>
                                        <td>${res.data[i + 2 * rowsPerColumn] ? res.data[i + 2 * rowsPerColumn].Name + ' ' + res.data[i + 2 * rowsPerColumn].Nums + 'g' : ''}</td>
                                    </tr>
                                `;
                                RP_table_body.append(RP_row);
                            }
                        } else {
                            layer.msg(res.msg || '加载药品信息失败', { icon: 2 });
                        }
                    },
                    error: function (res) {
                        layer.msg(res.responseJSON.msg, { icon: 2, time: 1000 });
                    }
                });
            }

            // 确认制药按钮点击事件
            $('#confirm_btn').on('click', function () {
                if ($(this).hasClass('layui-btn-disabled')) {
                    return;
                }

                // 收集所有行的数据
                let formData = [];
                let isValid = true;

                $('#prescription_drug_table_body tr').each(function () {
                    const $row = $(this);
                    const nums = parseInt($row.find('input[name="nums"]').val()) || 0;  // 转换为整数
                    const process = $row.find('input[name="process"]').val();
                    const drug_id = parseInt($row.find('td:eq(1)').text()) || 0;  // 转换为整数
                    const wh_drug_id = parseInt($row.find('td:eq(0)').text()) || 0;  // 转换为整数

                    if (!nums || !process) {
                        isValid = false;
                        return false; // break the loop
                    }

                    formData.push({
                        id: $row.data('id'),
                        nums: nums,  // 现在是整数
                        process: process,
                        drug_id: drug_id,  // 现在是整数
                        wh_drug_id: wh_drug_id  // 现在是整数
                    });
                });

                if (!isValid) {
                    layer.msg('请填写所有药品的克数和选择工艺制法', { icon: 2 });
                    return;
                }

                // 确认框
                layer.confirm('确定要提交制药信息吗？', {
                    btn: ['确定', '取消'],
                    title: '确认'
                }, function (index) {
                    layer.load(2);
                    // 提交数据
                    $.ajax({
                        url: '/admin/prescription/dispense_save',
                        type: 'POST',
                        data: {
                            prescription_id: prescription_id,
                            drugs: JSON.stringify(formData)
                        },
                        success: function (res) {
                            layer.closeAll('loading');
                            if (res.code === 200) {
                                layer.msg(res.msg, { icon: 1 });
                                setTimeout(function () {
                                    window.location.href = document.referrer || 'prescription_list.html';
                                }, 1000);
                            } else {
                                layer.msg(res.msg || '提交失败', { icon: 2 });
                            }
                        },
                        error: function (res) {
                            layer.closeAll('loading');
                            layer.msg(res.responseJSON.msg, { icon: 2, time: 5000 });
                        }
                    });
                    layer.close(index);
                });
            });
        });

        function printPreview() {
            layui.use(['layer'], function () {
                var layer = layui.layer;

                // 加载html2canvas
                var script = document.createElement('script');
                script.src = '/dist/js/html2canvas.min.js';
                script.onload = function () {
                    // 将处方区域转换为图片
                    html2canvas(document.getElementById('prescriptionArea'), {
                        backgroundColor: '#ffffff', // 设置背景为白色
                        useCORS: true,             // 允许跨域
                        scale: 2,                  // 提高清晰度
                        logging: false,            // 关闭日志
                        removeContainer: true      // 移除临时容器
                    }).then(function (canvas) {
                        // 创建图片预览
                        var img = canvas.toDataURL('image/png');
                        layer.open({
                            type: 1,
                            title: '打印预览',
                            area: ['800px', '600px'],
                            content: '<div style="background-color: #fff;"><img src="' + img + '" style="width: 100%; height: auto;"/></div>',
                            btn: ['打印', '取消'],
                            yes: function (index) {
                                // 创建打印窗口
                                var printWindow = window.open('', '_blank');
                                printWindow.document.write(`
                                    <html>
                                    <head>
                                        <title>处方单打印预览</title>
                                        <style>
                                            body {
                                                margin: 0;
                                                padding: 0;
                                                background-color: #fff;
                                            }
                                            img {
                                                width: 100%;
                                                height: auto;
                                            }
                                        </style>
                                    </head>
                                    <body>
                                        <img src="${img}">
                                    </body>
                                    </html>
                                `);
                                printWindow.document.close();

                                // 等待图片加载完成后打印
                                printWindow.onload = function () {
                                    printWindow.print();
                                    printWindow.close();
                                };
                                layer.close(index);
                            }
                        });
                    });
                };
                document.body.appendChild(script);
            });
        }
    </script>
</body>

</html>