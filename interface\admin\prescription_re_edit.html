<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 处方重开</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
    <style>
        h3 {
            margin: 30px 0 20px 0;
            border-left: 3px solid #666;
            padding-left: 15px;
        }

        table,
        th {
            text-align: center !important;
        }

        .info-content {
            color: #333;
            font-weight: 500;
        }

        .text-area-content {
            background: #f9f9f9;
            padding: 10px;
            border-radius: 4px;
            min-height: 60px;
            white-space: pre-wrap;
        }

        /* 驳回信息样式 */
        #Verify_reject {
            margin-bottom: 20px;
        }

        #Verify_reject .layui-card {
            box-shadow: none;
            border: 1px solid #f0f0f0;
        }

        #Verify_reject .layui-card-header {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        #Verify_reject .layui-badge {
            padding: 5px 10px;
            font-weight: normal;
        }

        #Verify_reject .layui-table {
            margin: 0;
        }

        #Verify_reject .layui-table th {
            font-weight: bold;
            color: #666;
        }
        
        /* 毛玻璃效果样式 */
        .blur-effect {
            filter: blur(5px);
            pointer-events: none;
            user-select: none;
            opacity: 0.7;
        }
        
        /* 成品药选择提示层样式 */
        .finished-drug-message {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 10;
            background-color: rgba(255, 255, 255, 0.9);
            padding: 20px 40px;
            border-radius: 8px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .finished-drug-message h2 {
            font-size: 24px;
            color: #1E9FFF;
            margin-bottom: 10px;
        }
    </style>
</head>

<body>
    <div class="layui-layout layui-layout-admin">
        <!-- 头部 -->
        <div class="layui-header">
            <div class="layui-logo">幸年堂</div>
            <ul class="layui-nav layui-layout-right">
                <li class="layui-nav-item" style="background-color:transparent!important;padding-right:10px">
                    <a href="javascript:;" style="display:flex;align-items:center;color:#fff"><img
                            src="/dist/images/user.png" class="layui-nav-img"><span class="user_name"></span></a>
                    <dl class="layui-nav-child pop_menu">
                        <dd><i class="iconfont">&#xe62f;</i><a href="change_password.html#7">用户资料</a></dd>
                        <dd><i class="iconfont">&#xe601;</i><a href="javascript:void(0)" class="refresh_cache">刷新缓存</a>
                        </dd>
                        <dd><i class="iconfont">&#xe634;</i><a href="login_out.html">退出登录</a></dd>
                    </dl>
                </li>
            </ul>
        </div>
        <!-- 导航 -->
        <ul class="layui-nav layui-nav-tree layui-nav-side" id="menuContainer" lay-bar="disabled" lay-accordion>
            <div style="margin: 50px 0;color: #bbb;text-align: center;">菜单加载中...</div>
        </ul>

        <!-- 主体内容 -->
        <div class="layui-body">
            <div class="body_child">
                <div class="layui-panel">
                    <div class="layui-card-header">
                        <div class="layui-row" style="padding-top:10px;">
                            <div class="layui-col-md11 layui-col-sm10">处方重开</div>
                        </div>
                    </div>

                    <div class="layui-padding-3" style="min-height: 600px;">
                        <form class="layui-form" lay-filter="form_edit" onsubmit="return false">
                            <input type="hidden" name="id" id="prescription_id">

                            <h3>驳回信息</h3>
                            <div class="layui-form">
                                <div class="layui-row">
                                    <div class="layui-bg-red layui-padding-1" id="Verify_reject">
                                    </div>
                                </div>
                            </div>

                            <h3>患者基础信息</h3>
                            <!-- 填充患者基础信息，接口/admin/patient_profile/detail -->
                            <div class="layui-form">
                                <div id="patient_data" class="layui-row">
                                    <i
                                        class='layui-icon layui-icon-loading-1 layui-anim layui-anim-rotate layui-anim-loop'></i>
                                </div>
                            </div>


                            <h3>诊断信息及医嘱</h3>
                            <div class="layui-form-item">
                                <div class="layui-row">
                                    <!-- 诊断信息 -->
                                    <div class="layui-col-md6 layui-col-sm12">
                                        <label class="layui-form-label">诊断信息</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="diagnosis" placeholder="请输入诊断信息"
                                                class="layui-input" autocomplete="off">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="layui-form-item">
                                <!-- 治疗计划 -->
                                <div class="layui-col-md6 layui-col-sm12">
                                    <label class="layui-form-label">治疗计划</label>
                                    <div class="layui-input-block">
                                        <input type="text" name="tx_plan" placeholder="请输入治疗计划" class="layui-input"
                                            autocomplete="off">
                                    </div>
                                </div>
                            </div>

                            <div class="layui-form-item">
                                <!-- 医嘱 -->
                                <div class="layui-col-md6 layui-col-sm12">
                                    <label class="layui-form-label">医嘱</label>
                                    <div class="layui-input-block">
                                        <input type="text" name="askfor" placeholder="请输入医嘱" class="layui-input"
                                            autocomplete="off">
                                    </div>
                                </div>
                            </div>

                            <h3>用药信息</h3>
                            <!-- 第一行：用药信息 -->
                            <div class="layui-form-item">
                                <div class="layui-inline">
                                    <label class="layui-form-label">共：</label>
                                    <div class="layui-input-inline">
                                        <select name="tx_day" lay-verify="required">
                                            <option value=""></option>
                                            <option value="1">1</option>
                                            <option value="2">2</option>
                                            <option value="3">3</option>
                                            <option value="4">4</option>
                                            <option value="5">5</option>
                                            <option value="6">6</option>
                                            <option value="7">7</option>
                                            <option value="8">8</option>
                                            <option value="9">9</option>
                                            <option value="10">10</option>
                                            <option value="11">11</option>
                                            <option value="12">12</option>
                                            <option value="13">13</option>
                                            <option value="14">14</option>
                                            <option value="15">15</option>
                                        </select>
                                    </div>
                                    <div class="layui-form-mid" style="margin-left: 10px;">天</div>
                                </div>
                                <div class="layui-inline">
                                    <label class="layui-form-label">药品类型：</label>
                                    <div class="layui-input-inline" style="width: 120px;">
                                        <select name="tx_type" lay-verify="required">
                                            <option value=""></option>
                                            <option value="膏滋">膏滋</option>
                                            <option value="丸剂">丸剂</option>
                                            <option value="汤剂">汤剂</option>
                                            <option value="散剂">散剂</option>
                                            <option value="无糖膏">无糖膏</option>
                                            <option value="水丸">水丸</option>
                                            <option value="内服粉剂">内服粉剂</option>
                                            <option value="外用粉剂">外用粉剂</option>
                                            <option value="中药材">中药材</option>
                                            <option value="清膏">清膏</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="layui-inline" style="margin-left: 30px;">
                                    <label class="layui-form-label">共：</label>
                                    <div class="layui-input-inline" style="width: 120px;">
                                        <input type="text" name="totalDoses" class="layui-input" autocomplete="off">
                                    </div>
                                    <div class="layui-form-mid">剂</div>
                                </div>
                            </div>

                            <!-- 第二行：使用方式和禁忌 -->
                            <div class="layui-form-item">
                                <div class="layui-inline">
                                    <label class="layui-form-label">每</label>
                                    <div class="layui-input-inline" style="width: 50px;">
                                        <input type="text" name="dosage_1" class="layui-input" autocomplete="off">
                                    </div>
                                    <div class="layui-form-mid">天</div>
                                    <div class="layui-input-inline" style="width: 50px;">
                                        <input type="text" name="dosage_2" class="layui-input" autocomplete="off">
                                    </div>
                                    <div class="layui-form-mid">次</div>
                                </div>

                                <div class="layui-inline">
                                    <label class="layui-form-label">每次</label>
                                    <div class="layui-input-inline" style="width: 50px;">
                                        <input type="text" name="dosage_3" class="layui-input" autocomplete="off">
                                    </div>
                                    <div class="layui-input-inline" style="width: 130px;">
                                        <input type="text" name="dosage_4" class="layui-input" placeholder="请输入单位"
                                            autocomplete="off">
                                    </div>
                                </div>

                                <div class="layui-inline">
                                    <label class="layui-form-label">禁忌：</label>
                                    <div class="layui-input-inline" style="width: 300px;">
                                        <input type="text" name="dosage_5" class="layui-input" autocomplete="off">
                                    </div>
                                </div>
                            </div>

                            <!-- 提交按钮 -->
                            <div class="layui-row" style="text-align: center;margin: 50px 0 30px 0;">
                                <button class="layui-btn" lay-submit lay-filter="formSubmitBtn"
                                    style="width: 200px;">1、修改处方基础数据</button>
                            </div>
                        </form>
                    </div>



                </div>
            </div>
            <div class="body_child">
                <div class="layui-panel">
                    <div class="layui-card-header">
                        <div class="layui-row">
                            <div class="layui-col-md11 layui-col-sm10">处方药品信息 <a href="javascript:;" id="choose_finished_drug" style="margin-left: 15px; font-size: 14px; color: #1E9FFF;">选择成品药？</a></div>
                        </div>
                    </div>
                    <div class="layui-padding-5" style="padding-top: 0 !important;min-height: 300px; position: relative;">
                        <!-- 原有药品列表部分 -->
                        <div id="original_drug_content">
                            <div style="display: flex;align-items: center;justify-content: space-between;">
                                <h3>药品列表</h3>
                            </div>
                            <table class="layui-table">
                                <thead>
                                    <tr>
                                        <th>库存ID</th>
                                        <th>药品ID</th>
                                        <th>药品名称</th>
                                        <th>单价</th>
                                        <th>库存量</th>
                                        <th>数量</th>
                                    </tr>
                                </thead>
                                <tbody id="prescription_drug_table_body">
                                    <!-- 表格内容将在这里动态插入 -->
                                </tbody>
                            </table>

                            <div class="layui-row" style="text-align: center;margin: 50px 0 0 0;">
                                <button class="layui-btn" id="add_meds_btn" style="width: 200px;">2、修改药品信息</button>
                            </div>
                        </div>
                        
                        <div class="layui-row" style="text-align: center;margin: 50px 0 0 0;">
                            <button class="layui-btn layui-btn-primary layui-border-red layui-btn-lg"
                                id="re_verify">3、确认无误，申请重审</button>
                            <button class="layui-btn layui-btn-primary layui-btn-lg" id="return_btn"
                                style="margin-left: 20px;" onclick="window.history.go(-1);">取消</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 定义全局变量存储 layui 的 jquery 对象
        var globalLayuiJquery;
        var finishedDrugId = null; // 存储选中的成品药ID
        var finishedDrugInfo = null; // 存储选中的成品药详细信息

        // 定义全局函数
        function loadPrescriptionDrugs(prescription_id) {
            if (!globalLayuiJquery) {
                console.error('layui.jquery 未初始化');
                return;
            }

            globalLayuiJquery.ajax({
                url: '/admin/prescription/drug',
                type: 'POST',
                data: { pre_id: prescription_id },
                success: function (res) {
                    if (res.code === 200) {
                        var tableBody = globalLayuiJquery('#prescription_drug_table_body');
                        tableBody.empty();

                        res.data.forEach(function (item) {
                            var row = `
                                <tr>
                                    <td>${item.Wh_drug_id || '-'}</td>
                                    <td>${item.Drug_id || '-'}</td>
                                    <td>${item.Name || '-'}</td>
                                    <td>${item.Price || 0}</td>
                                    <td>${item.Wh_quantity || 0}</td>
                                    <td>${item.Quantity || 0}</td>
                                </tr>
                            `;
                            tableBody.append(row);
                        });
                    } else {
                        layer.msg(res.msg || '加载药品信息失败', { icon: 2 });
                    }
                },
                error: function (res) {
                    layer.closeAll('loading');
                    layer.msg(res.responseJSON.msg, { icon: 2, time: 3000 });
                }
            });
        }

        layui.use(['element', 'layer', 'util', 'form'], function () {
            var element = layui.element;
            var layer = layui.layer;
            var util = layui.util;
            var form = layui.form;
            var $ = layui.$;

            // 存储 layui 的 jquery 对象到全局变量
            globalLayuiJquery = $;

            // 页面顶部插入缓存的用户数据
            let local_userinfo = localStorage.getItem('local_userinfo');
            let user_name = "";
            if (local_userinfo) {
                local_userinfo = JSON.parse(local_userinfo);
                user_name = local_userinfo.Name + " ( " + local_userinfo.Roles + " )";
            } else {
                user_name = "未登录";
            }
            $('.user_name').text(user_name);
            render_menu($);

            $('.refresh_cache').click(function () {
                refresh_cache($);
            });

            // 成品药选择按钮点击事件
            $('#choose_finished_drug').on('click', function() {
                // 打开模态框
                layer.open({
                    type: 2,
                    title: '选择成品药',
                    area: ['900px', '600px'],
                    shadeClose: true,
                    content: '/admin/warehouse_finisheddrug_choose.html',
                    success: function(layero, index) {
                        // 可以在这里向iframe传递数据
                        var iframeWindow = window[layero.find('iframe')[0]['name']];
                        // 如果需要在iframe加载完成后执行某些操作，可以在这里添加
                    },
                    end: function() {
                        // 模态框关闭后的处理
                        if (finishedDrugId) {
                            // 如果选择了成品药，应用毛玻璃效果并显示提示信息
                            $('#original_drug_content').addClass('blur-effect');
                            
                            // 创建并显示成品药选择提示
                            if ($('#finished_drug_message').length === 0) {
                                var messageHtml = '<div id="finished_drug_message" class="finished-drug-message"><h2>您已选择成品药</h2><button type="button" class="layui-btn layui-btn-primary" id="cancel_finished_drug">取消选择</button></div>';
                                $('#original_drug_content').parent().append(messageHtml);
                                
                                // 绑定取消按钮事件
                                $('#cancel_finished_drug').on('click', function() {
                                    layer.confirm('确定取消选择成品药吗？', {
                                        btn: ['确定', '取消']
                                    }, function(index) {
                                        // 重置成品药相关变量
                                        finishedDrugId = null;
                                        finishedDrugInfo = null;
                                        
                                        // 移除毛玻璃效果，隐藏提示信息
                                        $('#original_drug_content').removeClass('blur-effect');
                                        $('#finished_drug_message').hide();
                                        
                                        layer.close(index);
                                    });
                                });
                            } else {
                                $('#finished_drug_message').show();
                            }
                        }
                    }
                });
            });

            // 取消选择成品药
            $('#cancel_finished_drug').on('click', function() {
                layer.confirm('确定取消选择成品药吗？', {
                    btn: ['确定', '取消']
                }, function(index) {
                    // 重置成品药相关变量
                    finishedDrugId = null;
                    finishedDrugInfo = null;
                    
                    // 移除毛玻璃效果，隐藏提示信息
                    $('#original_drug_content').removeClass('blur-effect');
                    $('#finished_drug_message').hide();
                    
                    layer.close(index);
                });
            });
            // 加载成品药详细信息
            function loadFinishedDrugInfo(drugId) {
                $.ajax({
                    url: '/admin/warehouse_drugs/finished_detail',
                    type: 'POST',
                    data: { id: drugId },
                    success: function(res) {
                        if (res.code === 200) {
                            finishedDrugInfo = res.data;
                            
                            // 显示成品药信息
                            var tableBody = $('#finished_drug_table_body');
                            tableBody.empty();
                            
                            var row = `
                                <tr>
                                    <td>${finishedDrugInfo.ID || '-'}</td>
                                    <td>${finishedDrugInfo.Name || '-'}</td>
                                    <td>${finishedDrugInfo.Specification || '-'}</td>
                                    <td>${finishedDrugInfo.Manufacturer || '-'}</td>
                                    <td>${finishedDrugInfo.Price || '0.00'}</td>
                                </tr>
                            `;
                            tableBody.append(row);
                            
                            // 显示遮罩层，隐藏原有内容
                            $('#finished_drug_overlay').show();
                            $('#original_drug_content').hide();
                            
                            // 更新处方基础数据（可选）
                            // 如果成品药包含诊断信息等，可以自动填充到表单
                            if (finishedDrugInfo.Recommended_diagnosis) {
                                $('input[name="diagnosis"]').val(finishedDrugInfo.Recommended_diagnosis);
                            }
                            
                            // 如果成品药包含推荐治疗计划
                            if (finishedDrugInfo.Recommended_tx_plan) {
                                $('input[name="tx_plan"]').val(finishedDrugInfo.Recommended_tx_plan);
                            }
                            
                            // 如果成品药包含医嘱
                            if (finishedDrugInfo.Recommended_askfor) {
                                $('input[name="askfor"]').val(finishedDrugInfo.Recommended_askfor);
                            }
                            
                            form.render(); // 重新渲染表单
                        } else {
                            layer.msg(res.msg || '获取成品药信息失败', { icon: 2 });
                        }
                    },
                    error: function() {
                        layer.msg('获取成品药信息失败，请稍后重试', { icon: 2 });
                    }
                });
            }
            
            // 获取处方ID
            var prescription_id = request.get('id');
            if (!prescription_id) {
                layer.msg('处方ID不能为空', { icon: 2 });
                return;
            }
            //确认重审？
            $('#re_verify').click(function () {
                layer.confirm('确认无误，申请重审？', {
                    btn: ['确认', '取消'] //按钮
                }, function () {
                    $.ajax({
                        url: '/admin/prescription/re_verify',
                        type: 'POST',
                        data: { id: prescription_id },
                        success: function (res) {
                            if (res.code === 200) {
                                layer.msg('申请成功，请耐心等待审核结果', { icon: 1 });
                                setTimeout(function () {
                                    window.location.href = 'prescription_list_reject.html';
                                }, 1000);
                            } else {
                                layer.msg(res.msg || '申请失败', { icon: 2 });
                            }
                        },
                        error: function () {
                            layer.msg('服务器错误', { icon: 2 });
                        }
                    });
                }, function () {
                    layer.msg('已取消', { icon: 1 });
                });
            });
            //根据处方ID求出来的病历ID来填充患者基础信息
            var render_patient_info = function (record_ids) {
                layer.load(2);
                $.ajax({
                    url: serverUrl + "/admin/patient_profile/get_patient_ids_by_record_ids",
                    type: "post",
                    data: { record_ids: record_ids },
                    success: function (res) {
                        layer.closeAll('loading');
                        let data = res.data;
                        let html = '';
                        for (let i = 0; i < data.length; i++) {
                            html += `
                                        <!-- 第1大行 -->
                                        <div class="layui-col-xs3">
                                            <div class="info-item">
                                                <span class="info-label">手机号码：</span>
                                                <span class="info-content">${data[i].Phone || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">患者姓名：</span>
                                                <span class="info-content">${data[i].Name || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">患者性别：</span>
                                                <span class="info-content">${data[i].Sex == 0 ? '女' : '男' || '-'}</span>
                                            </div>
                                        </div>

                                        <div class="layui-col-xs3">
                                            <div class="info-item">
                                                <span class="info-label">关系：</span>
                                                <span class="info-content">${FamilyRelation[data[i].Relation] || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">身高：</span>
                                                <span class="info-content">${data[i].Height || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">体重：</span>
                                                <span class="info-content">${data[i].Weight || '-'}</span>
                                            </div>
                                        </div>

                                        <div class="layui-col-xs3">
                                            <div class="info-item">
                                                <span class="info-label">身份证号：</span>
                                                <span class="info-content">${data[i].Idcard || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">年龄：</span>
                                                <span class="info-content">${date2age(data[i].Born_date)}岁</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">医保卡：</span>
                                                <span class="info-content">${data[i].Ins_card_num || '-'}</span>
                                            </div>
                                        </div>

                                        <div class="layui-col-xs3">
                                            <div class="info-item">
                                                <span class="info-label">患者来源：</span>
                                                <span class="info-content">${Patient_From[data[i].Patient_from] || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">患者等级：</span>
                                                <span class="info-content">${data[i].Level + '级' || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">微信号：</span>
                                                <span class="info-content">${data[i].Weixin || '-'}</span>
                                            </div>
                                        </div>

                                        
                                        <div class="layui-col-xs6">
                                            <div class="info-item">
                                                <span class="info-label">患者备注：</span>
                                                <span class="info-content">${data[i].Customer_notes || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">既往病史：</span>
                                                <span class="info-content">${data[i].Medical_history || '-'}</span>
                                            </div>
                                        </div>
                                        <div class="layui-col-xs6">
                                            <div class="info-item">
                                                <span class="info-label">主诉：</span>
                                                <span class="info-content">${data[i].Chief_complaint || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">过敏史：</span>
                                                <span class="info-content">${data[i].Allergies || '-'}</span>
                                            </div>
                                        </div>
                                        <div class="layui-col-xs12">
                                            <div class="info-item">
                                                <span class="info-label">详细地址：</span>
                                                <span class="info-content">${data[i].Address.replace("|", " ") || '-'}</span>
                                            </div>
                                        </div>
                            `;
                        }
                        $('#patient_data').html(html);
                    },
                    error: function () {
                        layer.msg('获取患者信息失败', { icon: 2 });
                    }
                });
            }

            // 加载处方详情
            $.ajax({
                url: '/admin/prescription/detail',
                type: 'POST',
                data: { id: prescription_id },
                success: function (res) {
                    if (res.code === 200) {
                        var data = res.data;
                        if (data.Status !== 2) {
                            layer.msg('该处方状态非被驳回状态，该页不可访问！', { icon: 2 }, function () {
                                setTimeout(function () {
                                    window.location.href = 'prescription_list_reject.html';
                                }, 1000);
                            });
                            return;
                        };
                        // 处理驳回信息
                        let rejectHtml = `
                            <div class="layui-row layui-col-space15">
                                <div class="layui-col-xs12">
                                    <div class="layui-card">
                                        <div class="layui-card-header" style="font-weight: bold;">
                                            <i class="layui-icon layui-icon-close-fill" style="color: #FF5722; font-size: 16px;"></i> 
                                            处方驳回原因
                                        </div>
                                        <div class="layui-card-body">
                                            <table class="layui-table" lay-skin="nob">
                                                <thead>
                                                    <tr>
                                                        <th width="100">审核环节</th>
                                                        <th width="120">审核人</th>
                                                        <th>驳回原因</th>
                                                        <th width="180">时间</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                        `;

                        let userIds = []; // 收集需要查询的用户ID
                        let hasReject = false;

                        // 检查四种审核状态
                        const auditTypes = {
                            1: "医生审核",
                            2: "调配审核",
                            3: "药师审核",
                            4: "发药审核"
                        };

                        for (let i = 1; i <= 4; i++) {
                            if (data[`Verify_${i}`] === 2) {
                                hasReject = true;
                                userIds.push(data[`Verify_${i}_user_id`]);

                                // 从desc中分离时间和原因
                                let desc = data[`Verify_${i}_desc`] || '';
                                let time = '';
                                let reason = desc;

                                // 假设时间格式在desc最后：" - 2025-02-03 23:12"
                                let timeMatch = desc.match(/\s-\s(\d{4}-\d{2}-\d{2}\s\d{2}:\d{2})$/);
                                if (timeMatch) {
                                    time = timeMatch[1];
                                    reason = desc.replace(/\s-\s\d{4}-\d{2}-\d{2}\s\d{2}:\d{2}$/, '');
                                }

                                rejectHtml += `
                                    <tr>
                                        <td>
                                            <span class="layui-badge layui-bg-red">${auditTypes[i]}</span>
                                        </td>
                                        <td>
                                            <span class="user_need_ajax" data-id="${data[`Verify_${i}_user_id`]}">-</span>
                                        </td>
                                        <td style="color: #666;">
                                            ${reason || '-'}
                                        </td>
                                        <td style="color: #999; font-size: 13px;">
                                            ${time || '-'}
                                        </td>
                                    </tr>
                                `;
                            }
                        }

                        rejectHtml += `
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;

                        // 如果没有驳回信息
                        if (!hasReject) {
                            rejectHtml = `
                                <div class="layui-row">
                                    <div class="layui-col-xs12">
                                        <div class="layui-card">
                                            <div class="layui-card-body" style="text-align: center; color: #999;">
                                                暂无驳回信息
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            `;
                        }

                        $('#Verify_reject').html(rejectHtml);

                        // 如果有需要查询的用户ID，则调用接口获取用户信息
                        if (userIds.length > 0) {
                            // 去重
                            userIds = [...new Set(userIds)];

                            $.ajax({
                                url: '/admin/user/list_low',
                                type: 'post',
                                data: {
                                    id: userIds
                                },
                                success: function (res) {
                                    if (res.code === 200) {
                                        let data = res.data;
                                        // 遍历返回的用户数据，更新页面上的用户名
                                        for (let i = 0; i < data.length; i++) {
                                            let id = data[i].ID;
                                            let name = data[i].Name;
                                            $('.user_need_ajax[data-id="' + id + '"]').text(name);
                                        }
                                    }
                                },
                                error: function () {
                                    layer.msg('获取审核人信息失败', { icon: 2 });
                                }
                            });
                        }

                        // 设置表单值
                        form.val('form_edit', {
                            'id': data.ID,
                            'diagnosis': data.Diagnosis,
                            'tx_plan': data.Tx_plan,
                            'askfor': data.Askfor,
                            'tx_day': data.Tx_day,
                            'tx_type': data.Tx_type,
                            'totalDoses': data.TotalDoses
                        });

                        // 处理用法用量
                        if (data.Dosage) {
                            var dosage = data.Dosage.split('|');
                            form.val('form_edit', {
                                'dosage_1': dosage[0],
                                'dosage_2': dosage[1],
                                'dosage_3': dosage[2],
                                'dosage_4': dosage[3],
                                'dosage_5': dosage[4]
                            });
                        }

                        // 填充患者基础信息
                        render_patient_info(data.Record_id);

                        // 重新渲染表单
                        form.render();

                        // 加载药品信息
                        loadPrescriptionDrugs(prescription_id);
                    } else {
                        layer.msg(res.msg || '加载失败', { icon: 2 });
                    }
                },
                error: function (res) {
                    layer.closeAll('loading');
                    layer.msg(res.responseJSON.msg, { icon: 2, time: 3000 });
                }
            });

            // 添加药品按钮点击事件
            $('#add_meds_btn').on('click', function () {
                // 先获取当前处方的药品数据
                $.ajax({
                    url: '/admin/prescription/drug',
                    type: 'POST',
                    data: { pre_id: prescription_id },
                    success: function (res) {
                        if (res.code === 200) {
                            // 格式化药品数据
                            var existingDrugs = res.data.map(function (item) {
                                return {
                                    id: item.ID,
                                    wh_drug_id: item.Wh_drug_id,
                                    quantity: item.Quantity,
                                    name: item.Name,
                                    price: item.Price
                                };
                            });

                            // 设置全局变量，供iframe使用
                            window.drugModalData = {
                                prescriptionId: prescription_id,
                                existingDrugs: existingDrugs
                            };

                            // 打开模态框
                            layer.open({
                                type: 2,
                                title: '管理处方药品',
                                area: ['900px', '720px'],
                                shadeClose: true,
                                content: '/admin/warehouse_drug_edit_toolspage.html'
                            });
                        } else {
                            layer.msg('获取药品数据失败：' + res.msg, { icon: 2 });
                        }
                    }
                });
            });

            // 绑定删除按钮事件
            function bindDeleteEvent() {
                $('#prescription_drug_table_body').on('click', '.delete-row', function () {
                    $(this).closest('tr').remove();
                });
            }

            // 初始绑定删除事件
            bindDeleteEvent();

            // 监听数量输入变化
            $(document).on('input', '.single-dose', function () {
                var value = $(this).val();

                // 限制最小值
                if (value < 0.1) {
                    $(this).val(0.1);
                }

                // 限制小数点后一位
                if (value.toString().split('.')[1]?.length > 1) {
                    $(this).val(parseFloat(value).toFixed(1));
                }
            });

            // 表单提交
            form.on('submit(formSubmitBtn)', function (data) {
                let field = data.field;
                field.id = prescription_id;  // 添加处方ID

                // 收集药品数据
                let drugs = [];
                $('#prescription_drug_table_body tr').each(function () {
                    let drug = {
                        wh_drug_id: $(this).find('td:eq(0)').text(),  // 库存ID
                        drug_id: $(this).find('td:eq(1)').text(),  // 药品ID
                        single_dose: parseFloat($(this).find('td:eq(5)').text())  // 数量
                    };

                    // 检查库存是否充足
                    // let stock = parseFloat($(this).find('td:eq(4)').text());
                    // if (stock < 1) {
                    //     layer.msg('药品库存ID：' + drug.wh_drug_id + ' 库存不足，请检查', { icon: 2 });
                    //     return false;
                    // }

                    drugs.push(drug);
                });

                // 检查是否选择了药品
                if (drugs.length === 0) {
                    layer.msg('请至少选择一个药品', { icon: 2 });
                    return false;
                }

                // 检查必填字段
                if (!field.diagnosis) {
                    layer.msg('请输入诊断信息', { icon: 2 });
                    return false;
                }
                if (!field.tx_type) {
                    layer.msg('请选择药品类型', { icon: 2 });
                    return false;
                }
                if (!field.tx_day) {
                    layer.msg('请选择疗程天数', { icon: 2 });
                    return false;
                }

                // 收集用法用量数据
                let dosage = [];
                for (let key in field) {
                    if (key.startsWith('dosage_')) {
                        dosage.push(field[key] || '');
                    }
                }
                field.dosage = dosage.join('|');
                field.drugs = drugs;

                // 发送请求
                layer.load(2);
                $.ajax({
                    url: '/admin/prescription/edit',
                    type: 'POST',
                    data: field,
                    success: function (res) {
                        layer.closeAll('loading');
                        if (res.code === 200) {
                            layer.msg('处方基础数据修改成功', { icon: 1 });
                        } else {
                            layer.msg(res.msg || '修改失败', { icon: 2 });
                        }
                    },
                    error: function (xhr) {
                        layer.closeAll('loading');
                        layer.msg(xhr.responseJSON?.msg || '提交失败', { icon: 2 });
                    }
                });
                return false;
            });
        });
    </script>
</body>

</html>