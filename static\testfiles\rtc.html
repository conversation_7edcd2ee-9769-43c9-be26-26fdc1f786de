<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>WebRTC Video Chat</title>
</head>
<body>
    <h1>WebRTC Video Chat</h1>
    <input type="text" id="roomName" placeholder="Room Name">
    <button onclick="joinRoom()">Join Room</button>

    <video id="localVideo" autoplay muted></video>
    <video id="remoteVideo" autoplay></video>

    <script>
        let localStream;
        let pc;
        let roomName;

        async function joinRoom() {
            roomName = document.getElementById('roomName').value;
            if (!roomName) {
                alert('Please enter a room name');
                return;
            }

            // Get local stream
            localStream = await navigator.mediaDevices.getUserMedia({ video: true, audio: true });

            document.getElementById('localVideo').srcObject = localStream;

            // Create RTCPeerConnection
            pc = new RTCPeerConnection({
                iceServers: []
            });

            pc.onicecandidate = event => {
                if (event.candidate) {
                    // Handle ice candidate (in this example, send it to server)
                    sendIceCandidateToServer(event.candidate);
                }
            };

            pc.ontrack = event => {
                document.getElementById('remoteVideo').srcObject = event.streams[0];
            };

            // Create offer and set local description
            const offer = await pc.createOffer();
            await pc.setLocalDescription(offer);

            // Send offer to server (you need to implement this function)
            sendOfferToServer(offer);
        }

        async function sendOfferToServer(offer) {
            const response = await fetch('/rtc/createtoken?apiKey=1&apiSecret=1&room=room1&identity=1', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    apiKey: '1',
                    apiSecret: '1',
                    room: roomName,
                    identity: '1',
                    offer: offer
                })
            });
            const data = await response.json();
            if (data.code === 200) {
                // Handle token and answer (you need to implement this logic)
                const token = data.data;
                handleToken(token);
            } else {
                alert('Failed to create token: ' + data.msg);
            }
        }

        async function handleToken(token) {
            // Use the token to set up the room (you need to implement this logic)
            // This may involve signaling server to exchange offer, answer, and ice candidates
        }

        async function sendIceCandidateToServer(candidate) {
            // Send the ice candidate to the server (you need to implement this function)
        }
    </script>
</body>
</html>