帮我创建页面：/interface/admin/warehouse_finisheddrug_list.html
模仿页面：/interface/admin/warehouse_gifts_log_list.html

创建页面信息：

页面名字：成品出入库日志
读取后端（/admin/warehouse_finisheddrug_log/list）后所渲染的字段如下：
字段：
id	int	ID
pid	int	父ID（仓库表）
order_id	int	对应的订单ID
user_id	int	操作者ID
kind	tinyint	操作类型 0出库 1入库 2删除库存
change_data	decimal	出入库数量
old_data	decimal	修改前的数据
new_data	decimal	修改后的数据
notes	varchar	备注
create_time	timestamp	日志创建时间

注意：
1、严格按照被模仿的页面的样式来制作
2、API返回的值，首字母均大写，请在渲染数据时注意这一点