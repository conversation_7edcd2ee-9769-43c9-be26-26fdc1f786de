<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 患者排号</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src='/dist/js/fullcalendar.min.js'></script>
    <script src='/dist/js/zh-cn.global.js'></script>
    <script src="/dist/js/main.js"></script>
    <style>
        #calendar {
            padding: 10px;
            width: 83vw;
            margin: 0 auto;
        }

        .fc .fc-button-primary:disabled,
        .fc .fc-button-primary {
            border-color: #1677ff;
            display: inline-block;
            vertical-align: middle;
            height: 38px;
            line-height: 38px;
            border: 1px solid transparent;
            padding: 0 18px;
            white-space: nowrap;
            text-align: center;
            font-size: 14px;
            border-radius: 5px;
            cursor: pointer;
            -moz-user-select: none;
            -webkit-user-select: none;
            -ms-user-select: none;
            color: #fff;
            background-color: #1677ff;
            box-shadow: 0 2px 0 rgba(5, 145, 255, 0.1);
            outline: 0;
            -webkit-appearance: none;
            transition: all .3s;
            -webkit-transition: all .3s;
            box-sizing: border-box;
            border: 0;
        }

        .fc .fc-button-primary:hover {
            opacity: .8;
            filter: alpha(opacity=80);
            color: #fff;
            background-color: #1677ff;
            border: 0;
        }

        .fc .fc-button-primary:active {
            opacity: 1;
            background-color: #0958d9 !important;
        }

        .fc .fc-button-primary:not(:disabled).fc-button-active,
        .fc .fc-button-primary:not(:disabled):active {
            background-color: #0958d9 !important;
        }

        .fc .fc-button:focus {
            box-shadow: none !important;
        }

        .fc-event-title {
            font-size: 14px;
        }

        .fc-event-time {
            font-size: 14px;
            line-height: 24px;
        }

        .fc-daygrid-event-dot {
            border: 6px solid var(--fc-event-border-color);
            border-radius: 50%;
        }

        .fc-h-event {
            border: 0;
            padding: 0 10px;
        }
    </style>
</head>

<body>
    <div class="layui-layout layui-layout-admin">
        <!-- 头部 -->
        <div class="layui-header">
            <div class="layui-logo">幸年堂</div>
            <ul class="layui-nav layui-layout-right">
                <li class="layui-nav-item" style="background-color:transparent!important;padding-right:10px">
                    <a href="javascript:;" style="display:flex;align-items:center;color:#fff"><img
                            src="/dist/images/user.png" class="layui-nav-img"><span class="user_name"></span></a>
                    <dl class="layui-nav-child pop_menu">
                        <dd><i class="iconfont">&#xe62f;</i><a href="change_password.html#7">用户资料</a></dd>
                        <dd><i class="iconfont">&#xe601;</i><a href="javascript:void(0)" class="refresh_cache">刷新缓存</a>
                        </dd>
                        <dd><i class="iconfont">&#xe634;</i><a href="login_out.html">退出登录</a></dd>
                    </dl>
                </li>
            </ul>
        </div>
        <!-- 导航 -->
        <ul class="layui-nav layui-nav-tree layui-nav-side" id="menuContainer" lay-bar="disabled" lay-accordion>
            <div style="margin: 50px 0;color: #bbb;text-align: center;">菜单加载中...</div>
        </ul>
        <!-- 主体内容 -->
        <div class="layui-body">
            <div class="body_child">
                <div class="layui-panel">
                    <div class="layui-card-header">
                        <div class="layui-row" style="padding-top:10px;">
                            <div class="layui-col-md11 layui-col-sm10">② 完善诊室信息</div>
                        </div>
                    </div>
                    <div class="layui-padding-3" style="padding-top: 0 !important;min-height: 700px;">
                        <div class="layui-padding-3">
                            <div id='calendar'></div>
                        </div>
                    </div>
                </div>
                <br>
                <br>
            </div>
        </div>
    </div>

    <script type="text/html" id="TPL-bar">
           <a class="layui-btn layui-btn-xs perm_check_btn" lay-event="create_rtc_room" res_id="151">创建线上诊室</a>
    </script>

    <script>
        var record_id = request.get('record_id');
        var doc_id = request.get('doc_id');
        var pat_pro_id = request.get('pat_pro_id');
        var department_id = request.get('department_id');
        var pat_name = '';
        var openAddPersonDialog = function ($, calendar, date, laydate) {
            let choose_time = "";
            layer.open({
                type: 1,
                area: ['390px', '540px'],
                title: '添加问诊数据',
                content: `
                        <div style="padding:20px 30px;font-size:17px;line-height:30px;text-align:center;"   >
                            <h3 style="margin:0;padding:0">为患者<b style="color:#1677ff;">`+ pat_name + `</b>添加问诊数据</h3>
                            <span style="color:#666;font-size:14px;">请根据当前排班情况选择合适时段添加问诊数据</span>
                            <div class="layui-inline" id="ID-laydate-static" style="margin:30px auto 0 auto;"></div>
                        </div>
                    `,
                btn: ['添加', '取消'],
                shadeClose: true,
                btnAlign: 'r',
                success: function (layero, index) {
                    laydate.render({
                        elem: '#ID-laydate-static',
                        type: 'time',
                        fullPanel: true,
                        position: 'static',
                        showBottom: false,
                        change: function (value, endDate) {
                            choose_time = date + " " + value;
                        }
                    });
                },
                yes: function (index, layero) {
                    layer.confirm(`是否要添加该条预约？`, { icon: 3, title: '确认添加' }, function (index) {
                        if (choose_time == '') {
                            layer.msg('请选择预约时间！');
                            return false;
                        }
                        $.ajax({
                            url: '/admin/rtc_room/add',
                            data: {
                                department_id: department_id,
                                record_id: record_id,
                                pat_pro_id: pat_pro_id,
                                doc_id: doc_id,
                                scheduled_time: choose_time,
                            },
                            type: 'post',
                            success: function (res) {
                                layer.closeAll();
                                if (res.code == 200) {
                                    layer.msg(res.msg);
                                    calendar.refetchEvents();
                                }
                            }, error: function (data) {
                                layer.closeAll();
                                layer.msg(data.responseJSON.msg, { icon: 2 });
                            }
                        })
                    });
                },
                btn2: function (index, layero) { }
            });
        }
        layui.use(['element', 'layer', 'util'], function () {
            var element = layui.element;
            var form = layui.form;
            var layer = layui.layer;
            var util = layui.util;
            var laydate = layui.laydate;
            var table = layui.table;
            var dropdown = layui.dropdown;
            var doc_name = '';
            var $ = layui.$;
            // 渲染日期
            laydate.render({
                elem: '#ID-laydate-type-datetime-1',
                type: 'datetime',
                fullPanel: true // 2.8+
            });
            // 页面顶部插入缓存的用户数据
            let local_userinfo = localStorage.getItem('local_userinfo');
            let user_name = "";
            if (local_userinfo) {
                local_userinfo = JSON.parse(local_userinfo);
                user_name = local_userinfo.Name + " ( " + local_userinfo.Roles + " )";
            } else {
                user_name = "未登录";
            }
            $('.user_name').text(user_name);
            render_menu($);
            $('.refresh_cache').click(function () {
                refresh_cache($);
            });
            //获取该病历的详细信息
            if (!record_id || !doc_id || !pat_pro_id) {
                layer.msg('参数缺少，请从正确途径进入！');
                return false;
            }
            // 患者ID2NAME
            $.ajax({
                url: '/admin/patient_profile/patient_profile_id2name',
                data: {
                    id: pat_pro_id,
                },
                type: 'post',
                success: function (res) {
                    pat_name = res.data;
                }
            });
            // 医生ID2NAME
            $.ajax({
                url: '/normal/id2name_cache_get',
                data: {
                    id: doc_id,
                },
                type: 'post',
                success: function (res) {
                    doc_name = res.data;
                    data_plugin();
                }
            });
            //引入日历插件
            var data_plugin = function () {
                var calendarEl = document.getElementById('calendar');
                var datasources = [];
                var calendar = new FullCalendar.Calendar(calendarEl, {
                    headerToolbar: {
                        left: 'prevYear,prev,next,nextYear today',
                        center: 'title',
                        right: 'dayGridMonth,dayGridWeek,dayGridDay'
                    },
                    locale: 'zh-cn',
                    initialView: 'dayGridMonth',
                    editable: false,
                    droppable: false,
                    events: function (fetchInfo, successCallback, failureCallback) {
                        const startDate = fetchInfo.startStr;
                        const endDate = fetchInfo.endStr;
                        layer.load(2);
                        $.ajax({
                            url: '/admin/rtc_room/schedule',
                            data: {
                                doc_id: doc_id,
                                scheduled_time1: startDate,
                                scheduled_time2: endDate,
                            },
                            type: 'post',
                            dataType: 'json',
                            success: function (data) {
                                layer.closeAll('loading');
                                datasources = data.data;
                                if(!datasources){
                                    layer.msg("当前月份无任何预约数据");
                                    return;
                                }
                                var events = datasources.map(item => ({
                                    id: item.ID,
                                    title: item.Name + " (" + item.Phone + ")",
                                    start: item.Scheduled_time.replace('T', ' ').replace('Z', ''),
                                    allDay: false,
                                    backgroundColor: item.Status === 0 ? '#0d6efd' : '#6c757d',
                                }));
                                successCallback(events);
                            },
                            error: function (xhr, textStatus, errorThrown) {
                                layer.closeAll('loading');
                                layer.msg('获取数据失败！', { icon: 2 });
                                console.log(textStatus, errorThrown);
                                failureCallback(errorThrown);
                            }
                        });
                    },
                    dateClick: function (info) {
                        const date = info.dateStr;
                        openAddPersonDialog($, calendar, date, laydate);
                    },
                    eventClick: function (info) {
                        const eventId = Number(info.event.id);
                        let status_arr = ['待诊', '已诊', '已取消', '已过期'];
                        let asst_id = datasources.find(item => item.ID === eventId).Asst_id;
                        let patient_name = datasources.find(item => item.ID === eventId).Name;
                        let record_id = datasources.find(item => item.ID === eventId).Record_id;
                        let status = datasources.find(item => item.ID === eventId).Status;
                        // 根据医助ID求医助姓名
                        $.ajax({
                            url: '/normal/id2name_cache_get',
                            data: {
                                id: asst_id,
                            },
                            type: 'post',
                            success: function (res) {
                                let asst_name = res.data;
                                layer.open({
                                    type: 1,
                                    area: ['360px', '410px'],
                                    title: '当前诊室数据',
                                    content: `
                                        <div style="padding:20px 30px;font-size:17px;line-height:2.5;">
                                            <div>医生：`+ doc_name + `</div>
                                            <div>患者：`+ patient_name + `</div>
                                            <div>医助：`+ asst_name + `</div>
                                            <div>病历：<a href="/admin/patient_records_edit.html?id=`+ record_id +`#/admin/patient_records_list.html" style="font-size:18px;color:#1677ff;">B`+ record_id + `</a></div>
                                            <div>状态：`+ status_arr[status] + `</div>
                                            <div>预约时间：`+ datasources.find(item => item.ID === eventId).Scheduled_time.replace('T', ' ').replace('Z', '') + `</div>
                                        </div>
                                    `,
                                    btn: ['删除', '取消'],
                                    shadeClose: true,
                                    btnAlign: 'r',
                                    yes: function (index, layero) {
                                        layer.confirm(`是否要删除该条预约？`, { icon: 3, title: '删除确认' }, function (index) {
                                            $.ajax({
                                                url: '/admin/rtc_room/del',
                                                data: {
                                                    id: eventId,
                                                },
                                                type: 'post',
                                                success: function (res) {
                                                    console.log(res);
                                                    layer.msg(res.msg);
                                                    if (res.code == 200) {
                                                        info.event.remove();
                                                    }
                                                }
                                            })
                                            layer.closeAll();
                                        });
                                    },
                                    btn2: function (index, layero) { }
                                });
                            }
                        });
                    },
                    datesSet: function (info) {
                        const dateText = calendar.formatDate(info.start, {
                            year: 'numeric',
                            month: 'long',
                        });
                        // 动态更新标题内容
                        const titleElement = document.querySelector('.fc-toolbar-title');
                        if (titleElement) {
                            titleElement.textContent = `${doc_name} ${dateText} 预约情况`;
                        }
                    }
                });
                calendar.render();
            }
        });
    </script>
</body>

</html>