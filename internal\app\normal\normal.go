package normal

import (
	"archive/zip"
	"fmt"
	"io"
	"mstproject/pkg/common"
	"mstproject/pkg/config"
	"mstproject/pkg/database"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"strings"
)

func UploadWebInterface(w http.ResponseWriter, r *http.Request) {
	api_id := config.SystemPerm
	if _, isLogin := common.Check_Perm(w, r, api_id); !isLogin {
		return
	}

	// 无r.FormValue填充r.MultipartForm，所以先解析表单数据
	if err := r.ParseMultipartForm(10 << 20); err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}
	// 判断有没有文本域contents的传值，这是文字类型，如果用户传的有这个值，就单独将内容保存到服务器的文件contents.txt中（追加），同时在文字的末尾追加日期、换行符
	if contents := r.FormValue("contents"); contents != "" {
		f, err := os.OpenFile(
			"/www/wwwroot/mushengtang/interface/logs/worklog.txt", os.O_WRONLY|os.O_CREATE|os.O_APPEND, 0666,
		)
		if err != nil {
			common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
				"code": 500,
				"msg":  err.Error(),
			})
			return
		}
		defer f.Close()
		_, err = f.WriteString(contents + "\n\n" + common.GetTime() + "\n====================================================\n\n\n")
		if err != nil {
			common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
				"code": 500,
				"msg":  err.Error(),
			})
			return
		}
	}
	// 获取上传的文件
	file, handler, err := r.FormFile("file")
	if err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}
	defer file.Close()

	// 保存文件到临时目录
	tempPath := filepath.Join(os.TempDir(), handler.Filename)
	outFile, err := os.Create(tempPath)
	if err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}
	defer outFile.Close()

	// 将文件内容写入临时文件
	_, err = io.Copy(outFile, file)
	if err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}

	// 确保在解压文件前关闭输出文件
	err = outFile.Close()
	if err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}

	// 解压文件
	webPort := config.BasePort
	ext_path := ""
	if webPort == "8086" {
		ext_path = "/www/wwwroot/mushengtang/interface"
	} else {
		ext_path = "/www/wwwroot/dev.mushengtang.com/interface"
	}
	err = unzip(tempPath, ext_path)
	if err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}

	// 清理临时文件
	err = os.Remove(tempPath)
	if err != nil {
		println(err.Error())
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}

	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "WEB前端上传成功",
	})

	common.Add_log("WEB前端上传成功", r)
}

func unzip(src, dest string) error {
	r, err := zip.OpenReader(src)
	if err != nil {
		return err
	}
	defer r.Close()

	for _, f := range r.File {
		rc, err := f.Open()
		if err != nil {
			return err
		}
		defer rc.Close()

		// 创建文件路径
		fpath := filepath.Join(dest, f.Name)
		if f.FileInfo().IsDir() {
			os.MkdirAll(fpath, os.ModePerm)
		} else {
			var fdir string
			if lastIndex := strings.LastIndex(f.Name, "/"); lastIndex > -1 {
				fdir = fpath[:lastIndex]
			}
			os.MkdirAll(fdir, os.ModePerm)
			f, err := os.OpenFile(
				fpath, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, f.Mode(),
			)
			if err != nil {
				return err
			}
			defer f.Close()

			_, err = io.Copy(f, rc)
			if err != nil {
				return err
			}
		}
	}
	return nil
}

func Set_rtc_room_status_to_expired(w http.ResponseWriter, r *http.Request) {
	/*
		注：
			最初做的是病历过期，病历表patient_records，字段scheduled_time
			后需求改变，病历不可自动过期
			现在改为诊室过期（诊室有过期需求），诊室表rtc_room，字段scheduled_time；诊室过期对应status值为3
			不过诊室过期按天算，不按具体时间算
			即：如果预约时间大于等于今天，不过期，否则设置过期。
		sql := "SELECT record_id FROM rtc_room WHERE `status` = 0 AND scheduled_time < CURDATE()"
		record_id := []string{}
		err := database.GetAll(sql, &record_id)
		if err != nil {
		 common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
		     "code": 500,
		     "msg":  "查询已过期问诊单失败",
		     "err":  err.Error(),
		     "sql":  common.DebugSql(sql, []interface{}{}),
		 })
		 return
		}
		if len(record_id) > 0 {
		 // 事务开始
		 sql1 := "UPDATE rtc_room set `status` = 3 where record_id in (" + strings.Join(record_id, ",") + ")"
		 sql2 := "UPDATE patient_records set `status` = 2 where id in (" + strings.Join(record_id, ",") + ")"
		 sqls := []database.SQLExec{
		     {Query: sql1},
		     {Query: sql2},
		 }
		 RowsAffected, err := database.ExecuteTransaction_with_affectedRows(sqls)
		 if err != nil {
		     common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
		         "code": 500,
		         "msg":  "设置已过期问诊单状态为已过期失败",
		         "err":  err.Error(),
		     })
		     return
		 }
		 if RowsAffected > 0 {
		     common.Add_log_wxapp(fmt.Sprintf("问诊单自动过期，涉及病历：%s，影响行数：%d", strings.Join(record_id, ","), RowsAffected), 0, r)
		 }
		 common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		     "code": 200,
		     "msg":  "问诊单自动过期成功",
		     "rows": RowsAffected,
		     // "sql1": common.DebugSql(sql1, record_id),
		     // "sql2": common.DebugSql(sql2, record_id),
		 })
		} else {
		 common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		     "code": 200,
		     "msg":  "没有已过期的问诊单",
		 })
		}
	*/
	// 查询所有待诊状态(status=0)且预约时间小于今天的诊室
	sql := "SELECT id FROM rtc_room WHERE `status` = 0 AND scheduled_time < CURDATE()"
	room_ids := []string{}
	err := database.GetAll(sql, &room_ids)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "查询已过期诊室失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, []interface{}{}),
		})
		return
	}

	if len(room_ids) > 0 {
		// 更新过期诊室的状态为已过期(status=3)
		update_sql := "UPDATE rtc_room SET `status` = 3 WHERE id IN (" + strings.Join(room_ids, ",") + ")"
		result, err := database.Query(update_sql)
		if err != nil {
			common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
				"code": 500,
				"msg":  "设置已过期诊室状态失败",
				"err":  err.Error(),
				"sql":  common.DebugSql(update_sql, []interface{}{}),
			})
			return
		}

		RowsAffected, _ := result.RowsAffected()
		if RowsAffected > 0 {
			common.Add_log_sys_auto(fmt.Sprintf("诊室自动过期，涉及诊室ID：%s，影响行数：%d", strings.Join(room_ids, ","), RowsAffected), r)
		}

		common.JSONResponse(w, http.StatusOK, map[string]interface{}{
			"code": 200,
			"msg":  "诊室自动过期成功",
			"rows": RowsAffected,
		})
	} else {
		common.JSONResponse(w, http.StatusOK, map[string]interface{}{
			"code": 200,
			"msg":  "没有已过期的诊室",
		})
	}
}

// 员工绑定微信
func Bind_wx(w http.ResponseWriter, r *http.Request) {
	user_id, err := common.CheckInt(r.FormValue("user_id"))
	if err != nil || user_id < 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "用户ID不能为空",
		})
		return
	}
	openid := r.FormValue("openid")
	if openid == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "微信OpenID不能为空",
		})
		return
	}
	//先清除该OPENID在其它地方的绑定
	sql := "update rbac_user set openid = '0' where openid = ?"
	_, err = database.Query(sql, openid)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "解除其它地方的绑定失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, openid),
		})
		return
	}
	sql = "update rbac_user set openid = ? where id = ?"
	result, err := database.Query(sql, openid, user_id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "绑定微信失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, openid, user_id),
		})
		return
	}
	RowsAffected, _ := result.RowsAffected()
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":         200,
		"msg":          "绑定微信成功：" + strconv.Itoa(int(RowsAffected)) + "条",
		"RowsAffected": RowsAffected,
	})
	if RowsAffected > 0 {
		useragent := r.FormValue("useragent")
		common.Add_log_worker_in_wx(fmt.Sprintf("职能部门员工绑定微信OpenID：%s", openid), user_id, r, useragent)
	}
}

// 进入音视频房间前直接绑定OPENID
func Bind_wx_in_share_room(w http.ResponseWriter, r *http.Request) {
	user_id := r.FormValue("user_id")
	if user_id == "" {
		common.JSONResponse(w, http.StatusOK, map[string]interface{}{
			"code": 500,
			"msg":  "用户ID不能为空",
		})
		return
	}
	openid := r.FormValue("openid")
	if openid == "" {
		common.JSONResponse(w, http.StatusOK, map[string]interface{}{
			"code": 500,
			"msg":  "微信OpenID不能为空",
		})
		return
	}
	// 0. 加塞个逻辑，如果当前微信已经绑定过职能部门员工，则不允许再绑定患者角色
	sql := "select id from rbac_user where openid = ?"
	// fmt.Print(common.DebugSql(sql, openid))
	var has_master_id int
	err := database.GetOne(sql, &has_master_id, openid)
	if err == nil && has_master_id > 0 {
		// 当前微信存在于职能部门员工中
		common.JSONResponse(w, http.StatusOK, map[string]interface{}{
			"code": 500,
			"msg":  "该微信已绑定于管理角色，无法再绑定患者角色，请先解绑管理角色再绑定患者角色",
		})
		return
	}
	// 1. 先查询该用户是否已经绑定过微信，且ID不是当前传递的ID - 意味着是否医助建重复了帐号或者发错了链接
	sql = "select id from patient_account where id <> ? and openid = ?"
	var has_id int
	err = database.GetOne(sql, &has_id, user_id, openid)
	if err == nil && has_id > 0 {
		common.JSONResponse(w, http.StatusOK, map[string]any{
			"code": 500,
			"msg":  "当前微信已绑定用户（ID：" + strconv.Itoa(has_id) + "）与您分享用户ID（" + user_id + "）不匹配",
		})
		return
	}
	//2. 查询该用户是否已经绑定过微信
	sql = "select openid from patient_account where id = ?"
	var db_openid string
	err = database.GetOne(sql, &db_openid, user_id)
	if err != nil {
		common.JSONResponse(w, http.StatusOK, map[string]interface{}{
			"code": 500,
			"msg":  "查询患者绑定微信失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, openid),
		})
		return
	}
	if db_openid != "0" {
		// 3. 正好该诊室对应库中的OPENID与当前微信OPENID一致，证明以前已绑过，直接进入下一步
		if db_openid == openid {
			common.JSONResponse(w, http.StatusOK, map[string]interface{}{
				"code": 200,
				"msg":  "当前患者已绑定了该微信，直接进入下一步",
			})
			return
		} else {
			// 4. 不一样，那一定是医助发错患者了
			common.JSONResponse(w, http.StatusOK, map[string]interface{}{
				"code": 500,
				"msg":  "当前分享帐号ID已绑定微信，且不是当前微信用户，请医助确认分享对象的正确性",
			})
			return
		}
	} else {
		// 5. 未绑定过微信，则绑定当前微信
		sql = "update patient_account set openid = ? where id = ?"
		result, err := database.Query(sql, openid, user_id)
		if err != nil {
			common.JSONResponse(w, http.StatusOK, map[string]interface{}{
				"code": 500,
				"msg":  "绑定微信失败",
				"err":  err.Error(),
				"sql":  common.DebugSql(sql, openid, user_id),
			})
			return
		}
		RowsAffected, _ := result.RowsAffected()
		common.JSONResponse(w, http.StatusOK, map[string]interface{}{
			"code":         200,
			"msg":          "绑定微信成功：" + strconv.Itoa(int(RowsAffected)) + "条",
			"RowsAffected": RowsAffected,
		})
		if RowsAffected > 0 {
			useragent := r.FormValue("useragent")
			user_id_int, _ := strconv.Atoi(user_id)
			common.Add_log_worker_in_wx(fmt.Sprintf("患者分享进入时绑定微信，OpenID：%s", openid), user_id_int, r, useragent)
		}
	}
}

// 患者绑定微信
func Bind_wx_patient(w http.ResponseWriter, r *http.Request) {
	user_id := r.FormValue("user_id")
	if user_id == "" {
		common.JSONResponse(w, http.StatusOK, map[string]interface{}{
			"code": 500,
			"msg":  "用户ID不能为空",
		})
		return
	}
	openid := r.FormValue("openid")
	if openid == "" {
		common.JSONResponse(w, http.StatusOK, map[string]interface{}{
			"code": 500,
			"msg":  "微信OpenID不能为空",
		})
		return
	}
	//先查询该患者有没有绑定微信
	sql := "select openid from patient_account where id = ?"
	var db_openid string
	err := database.GetOne(sql, &db_openid, user_id)
	if err != nil {
		common.JSONResponse(w, http.StatusOK, map[string]interface{}{
			"code": 500,
			"msg":  "查询患者绑定微信失败",
			"err":  err.Error(),
			// "sql":  common.DebugSql(sql, openid),
		})
		return
	}
	if db_openid != "0" {
		common.JSONResponse(w, http.StatusOK, map[string]interface{}{
			"code": 200,
			"msg":  "当前患者已绑定过微信，直接进入签权页面",
		})
		return
	}
	//再查询该患者OPENID是否已经绑定过其它用户
	var count int
	sql = "select count(id) from patient_account where openid = ?"
	err = database.GetOne(sql, &count, openid)
	if err != nil {
		common.JSONResponse(w, http.StatusOK, map[string]interface{}{
			"code": 500,
			"msg":  "查询患者绑定微信失败",
			"err":  err.Error(),
			// "sql":  common.DebugSql(sql, openid),
		})
		return
	}
	if count > 0 {
		common.JSONResponse(w, http.StatusOK, map[string]interface{}{
			"code": 200,
			"msg":  "当前微信已绑定过其它用户，直接进入签权页面",
		})
		return
	}
	// 判断是否已经绑定过微信执行完毕
	sql = "update patient_account set openid = ? where id = ?"
	result, err := database.Query(sql, openid, user_id)
	if err != nil {
		common.JSONResponse(w, http.StatusOK, map[string]interface{}{
			"code": 500,
			"msg":  "绑定微信失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, openid, user_id),
		})
		return
	}
	RowsAffected, _ := result.RowsAffected()
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":         200,
		"msg":          "绑定微信成功：" + strconv.Itoa(int(RowsAffected)) + "条",
		"RowsAffected": RowsAffected,
	})
	if RowsAffected > 0 {
		useragent := r.FormValue("useragent")
		user_id_int, _ := strconv.Atoi(user_id)
		common.Add_log_worker_in_wx(fmt.Sprintf("患者（新绑定）绑定微信OpenID：%s", openid), user_id_int, r, useragent)
	}
}

// 自动更新订单最新物流信息 - 按照上次更新时间的倒序排列，每次只更新一条
func Auto_update_order_express_info(w http.ResponseWriter, r *http.Request) {
	// 查询待更新物流信息的订单 ; 状态为2表示已发货未签收
	sql := "SELECT id, pat_pro_id, express, tracking_num FROM orders WHERE status = 2 ORDER BY last_update_time ASC LIMIT 1"

	type OrderInfo struct {
		ID           int    `db:"id"`           // 订单ID
		Pat_pro_id   int    `db:"pat_pro_id"`   // 患者资料ID
		Express      int    `db:"express"`      // 快递公司ID
		Tracking_num string `db:"tracking_num"` // 物流单号
	}

	var orderInfo OrderInfo
	err := database.GetOne(sql, &orderInfo)
	if err != nil {
		// 没有需要更新的订单或发生错误
		common.JSONResponse(w, http.StatusOK, map[string]any{
			"code": 200,
			"msg":  "没有需要更新的订单或查询出错",
			"err":  err.Error(),
		})
		return
	}

	// 使用common包中的QueryExpressInfo函数查询物流信息
	result, err := common.QueryExpressInfo(orderInfo.Express, orderInfo.Tracking_num, orderInfo.ID, orderInfo.Pat_pro_id, true, r)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "查询物流信息失败",
			"err":  err.Error(),
		})
		return
	}
	// fmt.Println(result)
	//请在此处帮我输入http数据
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "物流信息查询成功",
		"data": result,
	})
}

// 获取订单当前状态
func Get_order_status(w http.ResponseWriter, r *http.Request) {
	api_id := config.NormalPerm
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	order_id := r.FormValue("order_id")
	if order_id == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "订单ID不能为空",
		})
		return
	}
	sql := "SELECT status FROM orders WHERE id = ?"
	var status int
	err := database.GetOne(sql, &status, order_id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "查询订单状态失败",
			"err":  err.Error(),
		})
		return
	} else {
		common.JSONResponse(w, http.StatusOK, map[string]interface{}{
			"code": 200,
			"msg":  "ok",
			"data": status,
		})
	}
}
