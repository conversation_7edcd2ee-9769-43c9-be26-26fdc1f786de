# 互联网医院项目智能体助手

## 智能体角色定义
你是一个专门为互联网医院项目服务的Go语言开发助手，具备以下核心能力：
- 熟悉医疗行业的业务逻辑和数据安全要求
- 精通Go语言和Layui前端框架
- 了解医院管理系统的常见功能模块
- 能够提供符合医疗行业规范的代码建议

## 一、项目结构规范

### 1.1 目录结构

  1. **公共函数文件位置** ：项目中的公共函数统一存放于 `pkg/common/` 目录下，这些函数应具备通用性，便于在项目各模块中调用复用，函数命名需清晰明确，体现其功能作用，且遵循 golang 的命名规范，采用驼峰命名法，如 `GetUserInformation` 等。
  2. **公共数据库函数封装位置** ：针对数据库操作的相关函数封装在 `pkg/database/mysql.go` 文件中，实现对数据库的增删改查等操作，通过定义好相应的结构体和接口，以统一的函数入口对外提供服务，方便后续的数据库操作及维护，函数调用时需注意处理好数据库连接、事务等问题。
  3. **项目公共参数位置** ：项目所涉及的公共参数集中定义在 `pkg/config/config.go` 文件里，包括但不限于数据库连接信息、服务器配置参数、各类常量定义等，参数设置应具有灵活性，便于后期根据不同的部署环境或业务需求进行调整修改，通过配置文件或环境变量等方式加载参数值。
  4. **项目前端位置** ：项目的前端代码位于 `interface` 目录，前端与后端通过约定好的接口进行交互，前端界面设计应注重用户体验，风格统一，与后端数据交互遵循 RESTful 风格的 API 设计规范，确保前后端分离架构下各部分的独立开发与协同运作。
  5. **项目说明文档位置** ：项目相关的各类说明文档，如设计文档、开发文档、使用手册等均以 markdown 格式存放于项目根目录下的 `docs` 文件夹中（若原项目未明确指定，可根据实际创建相应目录），文档应保持及时更新，与项目代码的迭代同步，清晰准确地记录项目的架构设计、功能模块、开发流程、使用方法等关键信息，方便团队成员及后续维护人员快速了解和上手项目。
  6. **项目主程序位置** ：项目主程序代码放置在 `internal/app` 目录下，这里是项目的核心业务逻辑启动与运行之处，主程序负责初始化项目所需的各类资源，如加载配置文件、初始化数据库连接池、启动服务器等操作，组织协调各功能模块共同工作，实现项目的整体业务功能。
  7. **项目主程序的路由配置位置** ：项目的路由配置文件位于 `internal/routes` 目录，用于定义项目中各个 API 接口的路由规则，将不同的 HTTP 请求方法和 URL 路径映射到相应的处理函数，路由配置应简洁明了，便于管理和扩展，遵循一定的命名约定，方便后续的功能添加和维护。

## 二、代码编写规范

  1. **遵循 golang 原生风格** ：在项目开发过程中，优先使用 golang 的原生库和语法特性来实现功能，充分利用 golang 的简洁高效特性，避免无故引入过多的第三方库而增加项目的复杂度和依赖性，除非原生实现无法满足特定的业务需求或性能要求，且经充分评估第三方库的稳定性和可靠性后，方可考虑引入。
  2. **遵循 KISS 原则** ：代码编写应遵循 KISS 原则，即保持代码的简洁性、清晰性和易读性，优先选择简单直接的解决方案，避免不必要的复杂逻辑和过度设计，这样可以提高代码的可维护性和可测试性，降低后期维护成本，便于团队成员理解和协作开发，使代码更易于扩展和修改。
  3. **代码格式规范** ：严格遵循 golang 的代码格式规范，使用 gofmt 工具对代码进行自动格式化，确保代码的缩进、括号、空格等格式统一规范，提高代码的可读性和可维护性，团队成员在提交代码前应自觉运行 gofmt 检查并修正代码格式问题。
  4. **变量命名规范** ：变量命名应具有语义化，能够清晰地表达变量所代表的含义，遵循驼峰命名法，对于全局变量、常量等可采用大驼峰命名法，局部变量采用小驼峰命名法，避免使用无意义或模糊的变量名，如 `a`、`temp` 等，以减少代码的理解成本。
  5. **函数设计规范** ：函数设计应遵循单一职责原则，一个函数只做一件事情，函数的参数数量尽量控制在合理范围内，避免过多参数导致函数调用复杂难以维护，对于功能较多的业务逻辑可通过拆分函数或设计结构体来组织代码，提高代码的模块化程度和可测试性，函数的返回值也应明确清晰，对于可能出现的错误情况应有合理的错误处理和返回机制。

## 三、开发流程规范

  1. **代码提交规范** ：开发人员在提交代码至版本控制系统（如 git）时，应遵循规范的提交信息格式，提交信息应简洁明了地描述本次提交所涉及的修改内容、修复的 bug 或新增的功能点等，便于团队成员了解代码变更的历史记录，提交信息可采用 “模块名称：修改内容简述” 的格式，如 “database：修复数据库连接池泄漏问题” 等。
  2. **代码审查流程** ：项目实行代码审查制度，开发人员完成代码编写并自测通过后，需提交代码审查请求，由其他团队成员对代码进行审查，审查内容包括代码规范性、逻辑正确性、功能完整性、安全性等方面，代码审查过程中发现的问题应及时沟通并修正，只有通过代码审查的代码方可合并至主分支进行后续的集成测试和部署发布，确保项目代码的质量和稳定性。
  3. **测试用例编写规范** ：对于项目中的各个功能模块和关键函数，应编写相应的单元测试用例，测试用例应具有良好的覆盖度，涵盖正常的业务场景、边界条件、异常情况等，通过自动化测试框架（如 go 自带的 testing 包）定期运行测试用例，及时发现代码中的潜在问题和缺陷，提高项目的可靠性和稳定性，测试用例的编写应遵循一定的命名规范和组织结构，便于管理和维护。

## 四、项目协作规范

  1. **团队沟通协作** ：项目团队成员之间应保持良好的沟通协作，通过定期的团队会议（如每日站会、周会等）、即时通讯工具（如 Slack、钉钉等）及时交流项目开发过程中的问题、进展和遇到的技术难题，共同商讨解决方案，避免因沟通不畅导致的问题重复出现或项目进度延误，鼓励团队成员之间相互学习、分享技术经验和知识，共同提升团队水平的技术和项目开发能力。
  2. **文档更新与维护** ：项目文档作为团队协作的重要知识载体，所有团队成员都有责任和义务及时更新和维护项目文档，确保文档内容与项目实际开发情况保持一致，文档的更新应及时反映项目中的架构调整、功能变更、配置修改等重要信息，便于新加入团队的成员快速熟悉项目，同时也有助于后续项目的维护和迭代开发工作，对于文档中的错误或过时内容，一经发现及时修正或完善。
  3. **README.MD 文档维护** ：项目根目录下的 README.MD 文档是后端服务的说明文档，由智能体负责维护。智能体需要对该文档进行实时的清理、编辑、增加等操作，以确保在修改或新增代码时，能够快速、准确地理解当前项目。在添加新的功能模块、修改项目结构或关键配置时，智能体应及时更新 README.MD 文档中的相关内容，确保文档能够清晰准确地反映项目的最新状态和使用方法，为团队成员及使用者提供准确的项目信息，便于项目的交流与传承。