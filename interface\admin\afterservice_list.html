<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 角色列表</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
</head>

<body>

    <div class="del_pm"></div>
    
    <div class="layui-layout layui-layout-admin">
        <!-- 头部 -->
        <div class="layui-header">
            <div class="layui-logo">幸年堂</div>
            <ul class="layui-nav layui-layout-right">
                <li class="layui-nav-item" style="background-color:transparent!important;padding-right:10px">
                    <a href="javascript:;" style="display:flex;align-items:center;color:#fff"><img
                            src="/dist/images/user.png" class="layui-nav-img"><span class="user_name"></span></a>
                    <dl class="layui-nav-child pop_menu">
                        <dd><i class="iconfont">&#xe62f;</i><a href="change_password.html#7">用户资料</a></dd>
                        <dd><i class="iconfont">&#xe601;</i><a href="javascript:void(0)" class="refresh_cache">刷新缓存</a>
                        </dd>
                        <dd><i class="iconfont">&#xe634;</i><a href="login_out.html">退出登录</a></dd>
                    </dl>
                </li>
            </ul>
        </div>
        <!-- 导航 -->
        <ul class="layui-nav layui-nav-tree layui-nav-side" id="menuContainer" lay-bar="disabled" lay-accordion>
            <div style="margin: 50px 0;color: #bbb;text-align: center;">菜单加载中...</div>
        </ul>
        <!-- 主体内容 -->
        <div class="layui-body">
            <div class="body_child">
                <div class="layui-panel">
                    <div class="layui-card-header">
                        <div class="layui-row" style="padding-top:10px;">
                            <div class="layui-col-md11 layui-col-sm10">售后病历列表</div>
                            <div class="layui-col-md1 layui-col-sm2">
                                <button type="submit" class="layui-btn layui-btn-fluid create_btn perm_check_btn"
                                    lay-submit="" res_id="52">创建 +
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="layui-padding-3" style="padding-top: 0 !important;min-height: 800px;">








                        <div id="data_search">
                            <div class="layui-form" style="margin: 20px 0;">
                                <div class="layui-row">


                                    <div class="layui-col-md2">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label">门店</label>
                                            <div class="layui-input-block">
                                                <div class="layui-form-mid">门店C - 医科 ID19<i
                                                        class="layui-icon layui-icon-tips layui-font-14"
                                                        title="需要在添加科室时，规范化一个医疗方面科室的名称，方便程序从该名称下获取相应科室列表信息，待讨论"
                                                        style="margin-left: 5px;"></i></div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="layui-col-md2">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label">部门<i
                                                    class="layui-icon layui-icon-tips layui-font-14"
                                                    title="患者不是各部门协同为患者服务的吗，怎么还有部门这个选项？待讨论"
                                                    style="margin-left: 5px;"></i></label>
                                            <div class="layui-input-block">
                                                <div style="display: flex; align-items: center;">
                                                    <input id="Department" name="Department" class="layui-input"
                                                        style="flex: 1; min-width: 120px;" placeholder="选择部门">
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="layui-col-md2">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label">科室</label>
                                            <div class="layui-input-block">
                                                <select name="keshi" id="keshi"></select>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="layui-col-md2">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label">医助</label>
                                            <div class="layui-input-block">
                                                <select name="asst" id="asst"></select>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="layui-col-md2">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label">医生</label>
                                            <div class="layui-input-block">
                                                <select name="doctor" id="doctor">
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-row">
                                    <div class="layui-col-md3">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label">患者等级</label>
                                            <div class="layui-input-block">
                                                <select name="patient_level" id="patient_level">
                                                    <option value="">患者等级</option>
                                                    <option value="1">A级</option>
                                                    <option value="2">B级</option>
                                                    <option value="3">C级</option>
                                                    <option value="4">D级</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-md3">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label">日期</label>
                                            <div class="layui-input-block">
                                                <input type="text" class="layui-input" id="ID-laydate-demo"
                                                    placeholder="选择创建日期">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="layui-col-md2">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label">患者</label>
                                            <div class="layui-input-block">
                                                <input type="text" name="wd" id="dropdown_input" placeholder="请输入电话或姓名"
                                                    autocomplete="off" class="layui-input" lay-affix="clear">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="layui-col-md2">
                                        <div class="layui-form-item">
                                            <div class="layui-input-block">
                                                <button class="layui-btn" lay-submit lay-filter="search"
                                                    style="min-width: 150px;">筛选</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>



                        <table id="mytable" lay-filter="mytable" class="layui-table" lay-skin="row"></table>
                    </div>
                </div>
                <br>
                <br>
            </div>
        </div>
    </div>

    <script type="text/html" id="TPL-bar">
            <a class="layui-btn layui-btn-xs perm_check_btn" lay-event="show" res_id="58">详情</a>
            <a class="layui-btn layui-btn-xs perm_check_btn" lay-event="edit" res_id="53">编辑</a>
            <a class="layui-btn layui-btn-danger layui-btn-xs perm_check_btn" lay-event="delete" res_id="150">删除</a>
    </script>

    <script>
        layui.use(['element', 'layer', 'util'], function () {
            var $ = layui.$;
            var element = layui.element;
            var form = layui.form;
            var layer = layui.layer;
            var util = layui.util;
            var laydate = layui.laydate;
            var table = layui.table;
            var dropdown = layui.dropdown;
            var pat_pro_id = 0;
            var record_ids = request.get('record_ids');
            if (record_ids) {
                $('.del_pm').html(`
                    <i class="iconfont">&#xe650;</i>
                    <div>注：当前页面含其它页面传来的参数进行筛选</div>
                    <a href="patient_records_list.html">解绑该参数</a>
                    <a href="javascript:history.back();">返回原页面</a>
                `);
                $('.del_pm').show();
            }
            // 渲染日期
            laydate.render({
                elem: '#ID-laydate-demo'
            });
            // 页面顶部插入缓存的用户数据
            let local_userinfo = localStorage.getItem('local_userinfo');
            let user_name = "";
            if (local_userinfo) {
                local_userinfo = JSON.parse(local_userinfo);
                user_name = local_userinfo.Name + " ( " + local_userinfo.Roles + " )";
            } else {
                user_name = "未登录";
            }
            $('.user_name').text(user_name);
            render_menu($);
            $('.refresh_cache').click(function () {
                refresh_cache($);
            });
            //引入TABLE模块
            layer.load(2);
            table.render({
                elem: '#mytable'
                , url: serverUrl + "/admin/patient_records/list"
                , method: 'post'
                , even: true
                , where: {
                    record_ids: record_ids,
                }
                , response: {
                    statusName: 'code'
                    , statusCode: 200
                    , msgName: 'msg'
                    , countName: 'count'
                    , dataName: 'data'
                }
                , cols: [[
                    { field: 'ID', title: '病历ID', align: 'center' },
                    { field: 'Pat_pro_id', title: '用户ID', align: 'center' },
                    {
                        field: 'ID', title: '姓名', align: 'center', templet: function (d) {
                            return '-';
                        }
                    },
                    {
                        field: 'ID', title: '性别', align: 'center', templet: function (d) {
                            return '-';
                        }
                    },
                    {
                        field: 'ID', title: '年龄', align: 'center', templet: function (d) {
                            return '-';
                        }
                    },
                    {
                        field: 'ID', title: '就诊类型', align: 'center', templet: function (d) {
                            return '-';
                        }
                    },
                    {
                        field: 'ID', title: '就诊时间', align: 'center', templet: function (d) {
                            return '-';
                        }
                    },
                    {
                        field: 'Department_id', title: '科室', align: 'center'
                    },
                    {
                        field: 'Doc_id', title: '医生', align: 'center'
                    },
                    { title: '操作', align: 'center',width:280, toolbar: '#TPL-bar' }
                ]]
                , done: function (res, curr, count) {
                    // console.log(res);
                    // console.log(curr);
                    // console.log(count);
                    after_table_done_rander();
                    // 点击帐号ID弹出模态框
                    $('.pid_link').on('click', function () {
                        let pid = $(this).data('id');
                        layer.open({
                            type: 2,
                            title: '帐号详情',
                            shade: 0.2,
                            maxmin: true,
                            area: ['500px', '690px'],
                            shadeClose: true,
                            content: 'patient_account_show.html?id=' + pid
                        });
                    });
                },
                page: true,
                limit: 12,
            })
            //监听工具条
            table.on('tool(mytable)', function (obj) {
                var data = obj.data;
                if (obj.event === 'show') {
                    window.location.href = 'patient_records_show.html?id=' + data.ID + '#/admin/patient_records_list.html';
                }else if(obj.event === 'edit') {
                    window.location.href = 'patient_records_edit.html?id=' + data.ID + '#/admin/patient_records_list.html';
                } else if (obj.event === 'delete') {
                    layer.confirm('确定删除吗？', function (index) {
                        $.ajax({
                            url: serverUrl + "/admin/patient_records/del",
                            type: "post",
                            data: {
                                id: data.ID
                            },
                            success: function (res) {
                                if (res.code == 200) {
                                    layer.msg(res.msg, { icon: 1, time: 1000 });
                                    obj.del();
                                } else {
                                    layer.msg(res.msg, { icon: 2, time: 1000 });
                                }
                            }, error: function (err) {
                                layer.closeAll('loading');
                                layer.msg(err.responseJSON ? err.responseJSON.msg : '请求失败', { icon: 2, time: 1000 });
                            },
                        });
                        layer.close(index);
                    });
                }
            });
            form.on('submit(search)', function (data) {
                // 重载表格
                if (data.field.wd.split(' / ').length > 1) {
                    data.field.pat_pro_id = pat_pro_id;
                }
                if (record_ids) {
                    data.field.record_ids = record_ids;
                }
                table.reload('mytable', {
                    where: data.field,
                    page: {
                        curr: 1 //重新从第 1 页开始
                    }
                });
                return false;
            });
            //新增用户按钮
            $('.create_btn').on('click', function () {
                window.location.href = 'patient_records_add.html';
            });
            //顶部 - 渲染部门下拉框
            $.ajax({
                url: '/normal/department_cache_get',
                type: 'post',
                success: function (res) {
                    layer.closeAll('loading');
                    if (res.code === 200) {
                        let data = res.data;
                        // 渲染科室树形结构
                        let treeData = format_to_treedata_department(data, global_default_store_id);
                        treeData = renderDropdownItems(treeData);
                        dropdown.render({
                            elem: '#Department',
                            id: 'DropdownID',
                            data: [],
                            content: '<div class="dropdown-menu">' + treeData + '</div>',
                            ready: function (elemPanel, elem) {
                                elemPanel.on('click', '.dropdown-item-leaf', function () {
                                    $('#Department').val($(this).text());
                                    dropdown.close('DropdownID');
                                });
                            }
                        });
                    } else {
                        layer.msg(res.msg, { icon: 2, time: 1000 });
                    }
                }
            });
            // 顶部 - 渲染科室下拉表
            $.ajax({
                url: '/admin/department/list',
                data: {
                    name: '医疗部门',
                    store_id: global_default_store_id,
                },
                type: 'post',
                success: function (res) {
                    layer.closeAll('loading');
                    if (res.code === 200) {
                        let data = res.data;
                        let html = '<option value="">选择科室</option>';
                        for (let i = 0; i < data.length; i++) {
                            html += '<option value="' + data[i].id + '">' + data[i].name + '</option>';
                        }
                        $('#keshi').html(html);
                        form.render('select');
                    } else {
                        layer.msg(res.msg, { icon: 2, time: 1000 });
                    }
                }
            });
            //顶部 - 渲染医生下拉框
            $.ajax({
                url: serverUrl + "/admin/user/list_low",
                type: "post",
                data: {
                    role_id: 4,
                },
                success: function (res) {
                    let data = res.data;
                    let html = '<option value="">选择医生</option>';
                    for (let i = 0; i < data.length; i++) {
                        html += '<option value="' + data[i].ID + '">' + data[i].Name + '</option>';
                    }
                    $('#doctor').html(html);
                    form.render('select');
                }
            });
            //顶部 - 渲染医助下拉框
            $.ajax({
                url: serverUrl + "/admin/user/list_low",
                type: "post",
                data: {
                    role_id: 3,
                },
                success: function (res) {
                    let data = res.data;
                    let html = '<option value="">选择医助</option>';
                    for (let i = 0; i < data.length; i++) {
                        html += '<option value="' + data[i].ID + '">' + data[i].Name + '</option>';
                    }
                    $('#asst').html(html);
                    form.render('select');
                }
            });
            //模糊搜索用户的下拉框组件
            var inst = dropdown.render({
                elem: '#dropdown_input',
                trigger: 'keyup click',
                data: [],
                style: 'min-width: 220px; box-shadow: 1px 1px 11px rgb(0 0 0 / 11%);',
                click: function (data) {
                    if (!isEmpty(data)) {
                        this.elem.val(data.name + ' / ' + data.phone);
                        pat_pro_id = data.id;
                    }
                }
            });
            $(inst.config.elem).on('input propertychange', function () {
                var elem = $(this);
                var value = elem.val().trim();
                if (value.length > 0) {
                    $.ajax({
                        url: "/admin/patient_profile/patient_profile_phone2id",
                        type: 'post',
                        dataType: 'json',
                        data: {
                            "wd": value,
                        },
                        success: function (response) {
                            let data = response.data;
                            if (data && data.length > 0) {
                                dropdown.reloadData(inst.config.id, {
                                    data: data,
                                    templet: function (d) {
                                        var exp = new RegExp(value, 'gi');
                                        return (d.name + ' / ' + d.phone).replace(exp, function (str) {
                                            return '<span style="color: red;">' + str + '</span>';
                                        });
                                    }
                                });
                            } else {
                                dropdown.reloadData(inst.config.id, {
                                    data: [],
                                });
                            }
                        },
                        error: function (err) {
                            dropdown.reloadData(inst.config.id, {
                                data: [],
                            });
                        }
                    });
                } else {
                    dropdown.reloadData(inst.config.id, {
                        data: [],
                    });
                }
            });





























            var after_table_done_rander = function () {
                render_button($);
                // 表格内AJAX填充，整行填充
                // 获取用户信息
                let patient_profile_array = [];
                $("tbody").find("tr").each(function (d) {
                    patient_profile_array.push($(this).find("td:eq(1)").text());
                });
                patient_profile_array = Array.from(new Set(patient_profile_array));
                for (let i = 0; i < patient_profile_array.length; i++) {
                    $.ajax({
                        url: serverUrl + "/admin/patient_profile/detail",
                        type: "post",
                        data: {
                            id: patient_profile_array[i],
                        },
                        success: function (res) {
                            if (res.code === 200) {
                                let data = res.data;
                                $("tbody").find("tr").each(function () {
                                    let trid = $(this).find("td:eq(1)").text();
                                    if (trid == data.ID) {
                                        $(this).find("td:eq(2)").html('<div class="layui-table-cell laytable-cell-1-0-0" align="center">' + data.Name + '</div>');
                                        $(this).find("td:eq(3)").html('<div class="layui-table-cell laytable-cell-1-0-0" align="center">' + (data.Sex == 1 ? '男' : '女') + '</div>');
                                        $(this).find("td:eq(4)").html('<div class="layui-table-cell laytable-cell-1-0-0" align="center">' + data.Born_date.split('T')[0] + '</div>');
                                        $(this).find("td:eq(6)").html('<div class="layui-table-cell laytable-cell-1-0-0" align="center">' + data.Create_time.split('T')[0] + '</div>');
                                    }
                                })

                            } else {
                                layer.msg(res.msg, { icon: 2, time: 1000 });
                            }
                        }
                    });
                }



                // 获取医生信息
                let doctor_array = [];
                $("tbody").find("tr").each(function (d) {
                    doctor_array.push($(this).find("td:eq(8)").text());
                });
                doctor_array = Array.from(new Set(doctor_array));
                for (let i = 0; i < doctor_array.length; i++) {
                    $.ajax({
                        url: serverUrl + "/normal/user_cache_get",
                        type: "post",
                        data: {
                            id: doctor_array[i],
                        },
                        success: function (res) {
                            if (res.code === 200) {
                                let data = res.data;
                                $("tbody").find("tr").each(function () {
                                    let trid = $(this).find("td:eq(8)").text();
                                    if (trid == data.Id) {
                                        $(this).find("td:eq(8)").html('<div class="layui-table-cell laytable-cell-1-0-0" align="center">' + (data.Name ? data.Name : '未知') + '</div>');
                                    }
                                })
                            }
                        },
                        error: function (err) {
                            $("tbody").find("tr").each(function () {
                                let trid = $(this).find("td:eq(8)").text();
                                if (trid == doctor_array[i]) {
                                    $(this).find("td:eq(8)").html('<div class="layui-table-cell laytable-cell-1-0-0" align="center">无该医生ID</div>');
                                }
                            });
                        }
                    });
                }

                // 获取科室
                let Department_array = [];
                $("tbody").find("tr").each(function (d) {
                    Department_array.push($(this).find("td:eq(7)").text());
                });
                Department_array = Array.from(new Set(Department_array));
                for (let i = 0; i < Department_array.length; i++) {
                    $.ajax({
                        url: serverUrl + "/normal/department_cache_get",
                        type: "post",
                        data: {
                            id: Department_array[i],
                        },
                        success: function (res) {
                            if (res.code === 200) {
                                let data = res.data;
                                $("tbody").find("tr").each(function () {
                                    let trid = $(this).find("td:eq(7)").text();
                                    if (trid == data.Id) {
                                        $(this).find("td:eq(7)").html('<div class="layui-table-cell laytable-cell-1-0-0" align="center">' + (data.Name ? data.Name : '未知') + '</div>');
                                    }
                                })
                            }
                        },
                        error: function (err) {
                            $("tbody").find("tr").each(function () {
                                let trid = $(this).find("td:eq(7)").text();
                                if (trid == Department_array[i]) {
                                    $(this).find("td:eq(7)").html('<div class="layui-table-cell laytable-cell-1-0-0" align="center">无该科室ID</div>');
                                }
                            });
                        }
                    });
                }

                // 就诊类型 - 初次还是N次
                let Records_array = [];
                $("tbody").find("tr").each(function (d) {
                    Records_array.push($(this).find("td:eq(1)").text());
                });
                Records_array = Array.from(new Set(Records_array));
                for (let i = 0; i < Records_array.length; i++) {
                    $.ajax({
                        url: serverUrl + "/admin/patient_records/count",
                        type: "post",
                        data: {
                            id: Records_array[i],
                        },
                        success: function (res) {
                            if (res.code === 200) {
                                let data = res.data;
                                $("tbody").find("tr").each(function () {
                                    let trid = $(this).find("td:eq(1)").text();
                                    if (trid == Records_array[i]) {
                                        $(this).find("td:eq(5)").html('<div class="layui-table-cell laytable-cell-1-0-0" align="center">' + (data == 1 ? '初诊' : data + '次诊') + '</div>');
                                    }
                                })
                            }
                        },
                        error: function (err) {
                            $("tbody").find("tr").each(function () {
                                let trid = $(this).find("td:eq(1)").text();
                                if (trid == Records_array[i]) {
                                    $(this).find("td:eq(5)").html('<div class="layui-table-cell laytable-cell-1-0-0" align="center">' + (data == 1 ? '初诊' : data + '次诊') + '</div>');
                                }
                            })
                        }
                    });
                }
            }









        });
    </script>
</body>

</html>