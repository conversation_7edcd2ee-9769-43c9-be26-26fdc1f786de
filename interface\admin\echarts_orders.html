<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 图表</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
    <script src="/dist/js/echarts.min.js"></script>
    <style>
        body {
            background-color: #f5f7fa;
            margin: 0;
            padding: 0;
            font-family: "PingFang SC", "Microsoft YaHei", sans-serif;
        }

        .chart-container {
            position: relative;
            width: 100%;
            height: 100vh;
            padding: 20px;
            box-sizing: border-box;
        }

        .chart-card {
            width: 100%;
            height: calc(100% - 80px);
            background: rgba(255, 255, 255, 0.85);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.18);
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .chart-card:hover {
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.25);
        }

        #clickdatas {
            width: 100%;
            height: 100%;
            padding: 20px;
            box-sizing: border-box;
        }

        .button-group {
            position: fixed;
            bottom: 30px;
            right: 30px;
            display: flex;
            flex-direction: row;
            gap: 10px;
            z-index: 1000;
        }

        .date-btn {
            background: rgba(255, 255, 255, 0.85);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.18);
            box-shadow: 0 4px 12px rgba(31, 38, 135, 0.1);
            border-radius: 8px;
            padding: 8px 16px;
            font-size: 14px;
            color: #333;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .date-btn:hover {
            background: rgba(255, 255, 255, 0.95);
            transform: translateY(-2px);
        }

        .date-btn.active {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
        }

        .loading-container {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100%;
            flex-direction: column;
        }

        .loading-text {
            margin-top: 20px;
            font-size: 14px;
            color: #666;
        }

        /* 自定义下拉框样式 */
        #chart-type-btn {
            transition: all 0.3s ease;
            width: 100px;
            text-align: center;
            font-size: 12px;
            padding: 0 10px;
            height: 26px;
            line-height: 26px;
        }

        #chart-type-btn:hover, #back-to-dept:hover {
            background-color: rgba(255, 255, 255, 0.95);
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        }

        #chart-type-dropdown {
            width: 100px;
            border-radius: 4px;
            overflow: hidden;
            transition: all 0.2s ease;
        }

        .chart-type-option {
            padding: 8px 0;
            transition: all 0.2s ease;
            text-align: center;
            cursor: pointer;
            font-size: 12px;
            color: #333;
            background-color: white;
            border-bottom: 1px solid #f0f0f0;
            line-height: 1.5;
        }

        .chart-type-option:last-child {
            border-bottom: none;
        }

        .chart-type-option:hover {
            background-color: #f5f5f5;
            color: #1E9FFF;
        }

        .chart-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
            padding: 15px 20px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }
    </style>
</head>

<body>
    <div class="chart-container">
        <div class="chart-card">
            <div class="chart-title">
                订单数据统计
                <span id="drill-info" style="display: none; font-size: 14px; font-weight: normal; margin-left: 10px; color: #666;">
                    <i class="layui-icon layui-icon-right" style="font-size: 12px;"></i>
                    <span id="current-dept-name"></span>
                </span>
                <button id="back-to-dept" class="layui-btn layui-btn-xs layui-btn-primary" style="float: right; display: none; height: 26px; line-height: 26px; font-size: 12px; padding: 0 10px; transition: all 0.3s ease;">
                    <i class="layui-icon layui-icon-left" style="font-size: 12px;"></i> 返回部门视图
                </button>
                <div style="float: right; margin-right: 10px; position: relative;">
                    <button id="chart-type-btn" class="layui-btn layui-btn-xs layui-btn-primary">
                        <span id="chart-type-text">按单量</span>
                        <i class="layui-icon layui-icon-down" style="font-size: 12px; margin-left: 5px;"></i>
                    </button>
                    <div id="chart-type-dropdown" style="display: none; position: absolute; background: white; border-radius: 4px; box-shadow: 0 2px 8px rgba(0,0,0,0.15); z-index: 1000; margin-top: 2px; left: 0; width: 100px; overflow: hidden;">
                        <div class="chart-type-option" data-value="0">按单量</div>
                        <div class="chart-type-option" data-value="1">按金额</div>
                    </div>
                </div>
            </div>
            <div id="clickdatas">
                <div class="loading-container">
                    <i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop" style="font-size: 30px;"></i>
                    <div class="loading-text">数据折线图加载中...</div>
                </div>
            </div>
        </div>

        <div class="button-group">
            <button class="date-btn" data-type="0">最近3天</button>
            <button class="date-btn" data-type="1">最近7天</button>
            <button class="date-btn" data-type="2">最近1月</button>
            <button class="date-btn" data-type="3">最近3月</button>
            <button class="date-btn active" data-type="4">所有</button>
        </div>
    </div>

    <script>
        layui.use(['element', 'layer', 'util'], function () {
            var layer = layui.layer,
                util = layui.util,
                $ = layui.jquery;

            // 获取部门ID参数
            var deptIds = request.get('deptids');
            if (!deptIds) {
                layer.msg('缺少部门ID参数', {icon: 2});
                return;
            }

            // 初始化图表
            var myChart = null;

            // 跟踪当前视图状态
            var viewState = {
                isDrillDown: false,
                currentDeptId: null,
                currentDeptName: '',
                rawData: null,
                currentDateType: '4',
                by_money: '0' // 默认按单量
            };

            // 用户ID到用户名的映射
            var userIdToName = {};

            // 加载数据函数
            function loadChartData(dateType) {
                // 保存当前日期类型
                viewState.currentDateType = dateType;

                // 如果图表已存在，先销毁它
                if (myChart) {
                    try {
                        // 先将myChart设为null，避免后续引用
                        var tempChart = myChart;
                        myChart = null;

                        // 检查DOM元素是否存在
                        var chartDom = document.getElementById('clickdatas');
                        if (chartDom) {
                            // 清空DOM内容，避免removeChild错误
                            chartDom.innerHTML = '';
                        }

                        // 使用try-catch包裹dispose操作
                        try {
                            // 检查tempChart是否有dispose方法
                            if (tempChart && typeof tempChart.dispose === 'function') {
                                tempChart.dispose();
                            }
                        } catch (e) {
                            console.error("销毁图表实例出错:", e);
                            // 即使销毁失败也继续执行
                        }
                    } catch (e) {
                        console.error("销毁图表时出错:", e);
                    }
                }

                // 显示加载中
                $('#clickdatas').html('<div class="loading-container"><i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop" style="font-size: 30px;"></i><div class="loading-text">数据折线图加载中...</div></div>');

                // 如果是下钻视图且已有数据，直接使用缓存的数据渲染
                if (viewState.isDrillDown && viewState.rawData) {
                    renderDrillDownChart(viewState.rawData, viewState.currentDeptId);
                    return;
                }

                // 重置下钻状态
                viewState.isDrillDown = false;
                $('#drill-info').hide();
                $('#back-to-dept').hide();

                // 发送AJAX请求获取数据
                $.ajax({
                    url: '/admin/charts_orders',
                    type: 'POST',
                    data: {
                        deptids: deptIds,
                        date_type: dateType,
                        by_money: viewState.by_money
                    },
                    success: function(res) {
                        if (res.code === 200) {
                            // 检查数据是否为空
                            if (res.data === null) {
                                console.log("后端返回的数据为空");
                                $('#clickdatas').html('<div style="text-align: center;padding-top:100px;color:#999;">暂无数据</div>');
                                return;
                            }

                            // 保存原始数据
                            viewState.rawData = res.data;

                            // 收集所有用户ID
                            var userIds = [];
                            res.data.forEach(function(item) {
                                if (item.AsstId && userIds.indexOf(parseInt(item.AsstId)) === -1) {
                                    userIds.push(parseInt(item.AsstId));
                                }
                            });

                            console.log("原始数据中的用户ID:", userIds);

                            // 如果有用户ID，获取用户名称
                            if (userIds.length > 0) {
                                fetchUserNames(userIds, function() {
                                    renderChart(res.data);
                                });
                            } else {
                                renderChart(res.data);
                            }
                        } else {
                            layer.msg(res.msg || '获取数据失败', {icon: 2});
                            $('#clickdatas').html('<div style="text-align: center;padding-top:100px;color:#999;">暂无数据</div>');
                        }
                    },
                    error: function() {
                        layer.msg('服务器错误，请稍后再试', {icon: 2});
                        $('#clickdatas').html('<div style="text-align: center;padding-top:100px;color:#999;">加载失败</div>');
                    }
                });
            }

            // 获取用户名称
            function fetchUserNames(userIds, callback) {
                if (!userIds || userIds.length === 0) {
                    if (callback) callback();
                    return;
                }

                console.log("获取用户名称 - 用户ID列表:", userIds);

                // 检查是否所有用户ID都已有名称映射
                var needToFetch = false;
                for (var i = 0; i < userIds.length; i++) {
                    if (!userIdToName[userIds[i]]) {
                        needToFetch = true;
                        break;
                    }
                }

                if (!needToFetch) {
                    console.log("所有用户名称已缓存，无需请求");
                    if (callback) callback();
                    return;
                }

                // 发送请求获取用户名称
                $.ajax({
                    url: '/admin/user/list_low',
                    type: 'POST',
                    data: {
                        user_id_list: userIds.join(',')
                    },
                    success: function(res) {
                        console.log("获取用户名称响应:", res);
                        if (res.code === 200 && res.data) {
                            // 更新用户ID到用户名的映射
                            res.data.forEach(function(user) {
                                // 确保ID是整数类型
                                var userId = parseInt(user.ID);
                                // 检查所有可能的字段名
                                if (user.Name !== undefined) {
                                    userIdToName[userId] = user.Name;
                                } else if (user.NAME !== undefined) {
                                    userIdToName[userId] = user.NAME;
                                } else {
                                    // 尝试遍历对象找到名称字段
                                    for (var key in user) {
                                        if (key.toLowerCase() === 'name') {
                                            userIdToName[userId] = user[key];
                                            break;
                                        }
                                    }
                                    // 如果仍然找不到，使用默认值
                                    if (!userIdToName[userId]) {
                                        userIdToName[userId] = '用户' + userId;
                                        console.warn("无法找到用户名称字段，使用默认名称:", userIdToName[userId]);
                                    }
                                }
                            });
                            console.log("更新后的用户名称映射:", userIdToName);
                        }
                        if (callback) callback();
                    },
                    error: function(err) {
                        console.error("获取用户名称失败:", err);
                        if (callback) callback();
                    }
                });
            }

            // 渲染图表函数
            function renderChart(data) {
                if (!data || data.length === 0) {
                    $('#clickdatas').html('<div style="text-align: center;padding-top:100px;color:#999;">暂无数据</div>');
                    return;
                }

                console.log("原始数据:", data);

                // 从本地存储获取部门数据
                var localDepartmentsStr = localStorage.getItem('local_departments');
                var localDepartments = JSON.parse(localDepartmentsStr);

                console.log("本地部门数据:", localDepartments);

                // 创建部门ID到部门名称的映射
                var deptIdToName = {};
                localDepartments.forEach(function(dept) {
                    deptIdToName[dept.Id] = dept.Name;
                });

                // 处理数据，按部门和日期分组
                var departments = {};
                var dates = [];
                var deptNames = {};

                // 收集所有日期和部门
                data.forEach(function(item) {
                    // 处理日期格式 - 从"2025-03-23T00:00:00Z"格式转换为"2025-03-23"
                    var dateStr = item.Date ? item.Date.split('T')[0] : '';

                    // 确保日期格式有效
                    if (dateStr && /^\d{4}-\d{2}-\d{2}$/.test(dateStr) && dates.indexOf(dateStr) === -1) {
                        dates.push(dateStr);
                    }

                    if (!departments[item.Department]) {
                        departments[item.Department] = {};
                        // 使用部门名称映射，如果不存在则使用默认值
                        // 在部门名称后添加ID，避免相同名称被合并
                        deptNames[item.Department] = (deptIdToName[item.Department] || '部门' + item.Department) + '（' + item.Department + '）';
                    }

                    if (!departments[item.Department][dateStr]) {
                        departments[item.Department][dateStr] = 0;
                    }

                    departments[item.Department][dateStr] += item.Count;
                });

                // 对日期进行排序
                dates.sort();

                // 如果日期点太少，可能需要插入中间日期以使图表更连续
                if (dates.length > 1 && dates.length < 10) {
                    var filledDates = [];
                    var startDate = new Date(dates[0]);
                    var endDate = new Date(dates[dates.length - 1]);

                    // 遍历每一天，确保连续性
                    for (var d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
                        var dateString = d.toISOString().split('T')[0];
                        filledDates.push(dateString);
                    }

                    // 使用填充后的日期数组
                    if (filledDates.length > dates.length) {
                        console.log("填充前的日期点数:", dates.length, "填充后的日期点数:", filledDates.length);
                        dates = filledDates;
                    }
                }

                console.log("处理后的日期:", dates);
                console.log("处理后的部门数据:", departments);

                // 准备图表系列数据
                var series = [];
                for (var deptId in departments) {
                    var deptData = [];
                    dates.forEach(function(date) {
                        deptData.push(departments[deptId][date] || 0);
                    });

                    series.push({
                        name: deptNames[deptId],
                        type: 'line',
                        smooth: true,
                        data: deptData,
                        symbolSize: 10, // 增大数据点尺寸
                        symbol: 'circle',
                        showSymbol: true, // 始终显示标记点
                        lineStyle: {
                            width: 1 // 增加线条宽度，使其更容易点击
                        },
                        // 增加线条的点击区域
                        emphasis: {
                            lineStyle: {
                                width: 3
                            },
                            itemStyle: {
                                borderWidth: 2
                            }
                        },
                        // 添加区域填充效果
                        areaStyle: {
                            opacity: 0.1
                        },
                        // 添加部门ID作为自定义属性，用于下钻
                        deptId: deptId
                    });
                }

                console.log("图表系列数据:", series);

                // 初始化图表
                $('#clickdatas').empty();

                try {
                    // 确保DOM元素存在
                    var chartDom = document.getElementById('clickdatas');
                    if (!chartDom) {
                        console.error("找不到图表DOM元素");
                        $('#clickdatas').html('<div style="text-align: center;padding-top:100px;color:#999;">图表容器不存在</div>');
                        return;
                    }

                    // 确保DOM元素有尺寸
                    if (chartDom.offsetHeight === 0 || chartDom.offsetWidth === 0) {
                        console.error("图表DOM元素尺寸为0");
                        chartDom.style.height = '400px'; // 设置一个默认高度
                    }

                    // 先清空容器内容，确保没有残留元素
                    chartDom.innerHTML = '';

                    // 如果已有图表实例，先销毁它
                    if (myChart) {
                        try {
                            var tempChart = myChart;
                            myChart = null;
                            try {
                                if (tempChart && typeof tempChart.dispose === 'function') {
                                    tempChart.dispose();
                                }
                            } catch (e) {
                                console.error("销毁旧图表实例出错:", e);
                            }
                        } catch (e) {
                            console.error("处理旧图表实例时出错:", e);
                        }
                    }

                    // 初始化图表
                    try {
                        myChart = echarts.init(chartDom);
                        console.log("成功初始化主图表实例");
                    } catch (e) {
                        console.error("初始化主图表实例出错:", e);
                        $('#clickdatas').html('<div style="text-align: center;padding-top:100px;color:#999;">图表初始化失败: ' + e.message + '</div>');
                        return;
                    }

                    // 检查是否成功创建了canvas元素
                    if (chartDom.querySelector('canvas') === null) {
                        console.error("echarts未能创建canvas元素");
                        // 尝试重新创建图表
                        chartDom.innerHTML = '';
                        try {
                            // 手动创建一个canvas元素
                            var canvas = document.createElement('canvas');
                            canvas.style.width = '100%';
                            canvas.style.height = '100%';
                            chartDom.appendChild(canvas);

                            // 重新初始化图表
                            if (myChart) {
                                try {
                                    if (typeof myChart.dispose === 'function') {
                                        myChart.dispose();
                                    }
                                } catch (e) {
                                    console.error("重新创建canvas时销毁图表出错:", e);
                                }
                            }
                            myChart = echarts.init(chartDom);
                        } catch (e) {
                            console.error("手动创建canvas元素时出错:", e);
                            $('#clickdatas').html('<div style="text-align: center;padding-top:100px;color:#999;">创建图表元素失败: ' + e.message + '</div>');
                            return;
                        }
                    }
                } catch (e) {
                    console.error("初始化图表时出错:", e);
                    $('#clickdatas').html('<div style="text-align: center;padding-top:100px;color:#999;">图表初始化失败: ' + e.message + '</div>');
                    return;
                }

                // 图表配置
                var option = {
                    // 增强交互性配置
                    animation: true,
                    animationDuration: 300,
                    animationEasing: 'cubicOut',
                    // 增加点击区域
                    axisPointer: {
                        lineStyle: {
                            color: 'rgba(0, 0, 0, 0.2)',
                            width: 1
                        },
                        shadowStyle: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        }
                    },
                    tooltip: {
                        trigger: 'axis',
                        backgroundColor: 'rgba(255, 255, 255, 0.9)',
                        borderColor: '#f5f5f5',
                        borderWidth: 1,
                        textStyle: {
                            color: '#333'
                        },
                        confine: true, // 确保提示框在视图区域内
                        enterable: true, // 鼠标可进入提示框
                        extraCssText: 'box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);', // 添加阴影效果
                        formatter: function(params) {
                            // 获取完整的日期字符串
                            var dateStr = params[0].axisValue;

                            // 确保显示完整的年月日
                            var formattedDate = dateStr;
                            if (dateStr && dateStr.split('-').length === 3) {
                                formattedDate = dateStr; // 已经是完整格式
                            }

                            // 根据当前模式决定单位
                            var unit = $('#chart-type-text').text() === '按金额' ? '元' : '条';
                            console.log("提示框单位 - 按钮文本:", $('#chart-type-text').text(), "单位:", unit);

                            var result = '<div style="font-weight:bold;margin-bottom:5px;font-size:13px;">' + formattedDate + '</div>';
                            params.forEach(function(item) {
                                result += '<div style="display:flex;align-items:center;margin:3px 0;">' +
                                    '<span style="display:inline-block;width:10px;height:10px;background:' + item.color + ';margin-right:5px;border-radius:50%;"></span>' +
                                    '<span>' + item.seriesName + ': ' + item.value + unit + '</span>' +
                                    '</div>';
                            });
                            return result;
                        }
                    },
                    legend: {
                        data: Object.values(deptNames),
                        top: 10,
                        type: 'scroll',
                        textStyle: {
                            color: '#666'
                        }
                    },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '15%', // 固定增加底部空间以容纳旋转的标签
                        top: '15%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        boundaryGap: false,
                        data: dates,
                        axisLine: {
                            lineStyle: {
                                color: '#ddd'
                            }
                        },
                        axisLabel: {
                            color: '#666',
                            formatter: function(value) {
                                // 将日期格式化为更简洁的显示方式
                                if (!value) return '';

                                // 处理日期格式
                                var parts = value.split('-');
                                if (parts.length !== 3) return value;

                                // 只显示月和日，如 "03-23"
                                return parts[1] + '-' + parts[2];
                            },
                            interval: function(index, value) {
                                // 根据日期数量动态调整显示间隔
                                if (dates.length <= 10) return 0; // 少于10个日期点时显示所有
                                if (dates.length <= 20) return index % 2 === 0 ? 0 : -1; // 10-20个日期点时显示一半
                                if (dates.length <= 40) return index % 3 === 0 ? 0 : -1; // 20-40个日期点时显示1/3
                                return index % 5 === 0 ? 0 : -1; // 超过40个日期点时显示1/5
                            },
                            rotate: 45, // 固定旋转角度
                            margin: 10,
                            align: 'right',
                            fontSize: 10
                        }
                    },
                    yAxis: {
                        type: 'value',
                        name: $('#chart-type-text').text() === '按金额' ? '金额' : '病历数量',
                        nameTextStyle: {
                            color: '#666'
                        },
                        axisLine: {
                            show: false
                        },
                        axisTick: {
                            show: false
                        },
                        splitLine: {
                            lineStyle: {
                                color: '#eee',
                                type: 'dashed'
                            }
                        },
                        axisLabel: {
                            color: '#666'
                        }
                    },
                    series: series,
                    color: ['#4facfe', '#00f2fe', '#f093fb', '#f5576c', '#4cd964', '#5ac8fa', '#007aff', '#5856d6', '#ff2d55', '#ff9500']
                };

                // 设置图表
                try {
                    if (myChart) {
                        myChart.setOption(option);
                        console.log("主图表选项设置成功");
                    } else {
                        console.error("图表实例不存在，无法设置选项");
                        $('#clickdatas').html('<div style="text-align: center;padding-top:100px;color:#999;">图表实例不存在</div>');
                    }
                } catch (e) {
                    console.error("设置图表选项时出错:", e);
                    $('#clickdatas').html('<div style="text-align: center;padding-top:100px;color:#999;">图表渲染失败: ' + e.message + '</div>');
                }

                // 添加点击事件，实现下钻功能
                try {
                    if (myChart) {
                        // 移除之前的点击事件
                        myChart.off('click');

                        // 添加新的点击事件
                        myChart.on('click', function(params) {
                            // 处理点击事件 - 支持点击数据点或线条
                            if (params.componentType === 'series' && (params.seriesType === 'line' || params.seriesType === 'scatter')) {
                                // 获取点击的系列索引
                                var seriesIndex = params.seriesIndex;

                                // 获取部门ID
                                var deptId = seriesIndex >= 0 && series[seriesIndex] ? series[seriesIndex].deptId : null;
                                if (deptId) {
                                    // 下钻到该部门的人员数据
                                    drillDownToDepartment(deptId, deptNames[deptId]);
                                }
                            }
                        });

                        // 添加图例点击事件
                        myChart.on('legendselectchanged', function(params) {
                            // 获取被点击的图例名称
                            var legendName = params.name;

                            // 查找对应的系列
                            for (var i = 0; i < series.length; i++) {
                                if (series[i].name === legendName) {
                                    // 获取部门ID
                                    var deptId = series[i].deptId;
                                    if (deptId) {
                                        // 下钻到该部门的人员数据
                                        drillDownToDepartment(deptId, deptNames[deptId]);
                                        break;
                                    }
                                }
                            }
                        });

                        console.log("成功绑定图表点击和图例点击事件");
                    } else {
                        console.error("图表实例不存在，无法绑定点击事件");
                    }
                } catch (e) {
                    console.error("绑定图表点击事件时出错:", e);
                }

                // 响应式调整
                var resizeHandler = function() {
                    if (myChart) {
                        try {
                            myChart.resize();
                        } catch (e) {
                            console.error("调整图表大小时出错:", e);
                        }
                    }
                };

                // 移除之前可能存在的resize事件监听器
                window.removeEventListener('resize', resizeHandler);
                // 添加新的resize事件监听器
                window.addEventListener('resize', resizeHandler);
            }

            // 下钻到部门人员数据
            function drillDownToDepartment(deptId, deptName) {
                try {
                    if (!viewState.rawData) {
                        layer.msg('无法获取详细数据', {icon: 2});
                        return;
                    }

                    console.log("下钻到部门 - 部门ID:", deptId, "部门名称:", deptName);

                    // 如果图表实例存在，先安全地销毁它
                    if (myChart) {
                        try {
                            var tempChart = myChart;
                            myChart = null;

                            // 检查DOM元素是否存在
                            var chartDom = document.getElementById('clickdatas');
                            if (chartDom) {
                                // 清空DOM内容，避免removeChild错误
                                chartDom.innerHTML = '';
                            }

                            try {
                                if (tempChart && typeof tempChart.dispose === 'function') {
                                    tempChart.dispose();
                                }
                            } catch (e) {
                                console.error("下钻前销毁图表实例出错:", e);
                                // 继续执行，不中断流程
                            }
                        } catch (e) {
                            console.error("下钻前处理图表实例时出错:", e);
                        }
                    }

                    // 更新视图状态
                    viewState.isDrillDown = true;
                    viewState.currentDeptId = parseInt(deptId);
                    viewState.currentDeptName = deptName || '部门' + deptId;

                    // 显示下钻信息和返回按钮
                    $('#current-dept-name').text(viewState.currentDeptName);
                    $('#drill-info').show();
                    $('#back-to-dept').show();

                    // 显示加载中状态
                    $('#clickdatas').html('<div class="loading-container"><i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop" style="font-size: 30px;"></i><div class="loading-text">加载人员数据中...</div></div>');
                } catch (e) {
                    console.error("下钻初始化时出错:", e);
                    layer.msg('下钻过程中出现错误', {icon: 2});
                    return;
                }

                // 收集该部门下的所有用户ID
                var userIds = [];
                try {
                    viewState.rawData.forEach(function(item) {
                        if (parseInt(item.Department) === parseInt(deptId) &&
                            item.AsstId !== null && item.AsstId !== undefined &&
                            userIds.indexOf(parseInt(item.AsstId)) === -1) {
                            userIds.push(parseInt(item.AsstId));
                        }
                    });

                    console.log("该部门下的用户ID:", userIds);

                    // 如果没有找到用户ID，可能是数据格式问题，尝试直接从API响应中提取
                    if (userIds.length === 0) {
                        console.warn("未找到用户ID，尝试从API响应中提取");
                        // 从API响应中获取用户ID列表
                        $.ajax({
                            url: '/admin/user/list_low',
                            type: 'POST',
                            data: {
                                department_id: deptId
                            },
                            async: false, // 同步请求，确保在继续之前获取数据
                            success: function(res) {
                                if (res.code === 200 && res.data) {
                                    res.data.forEach(function(user) {
                                        var userId = parseInt(user.ID);
                                        if (userIds.indexOf(userId) === -1) {
                                            userIds.push(userId);
                                        }
                                        // 同时更新用户名称映射
                                        if (user.Name !== undefined) {
                                            userIdToName[userId] = user.Name;
                                        } else if (user.NAME !== undefined) {
                                            userIdToName[userId] = user.NAME;
                                        }
                                    });
                                    console.log("从API获取的用户ID:", userIds);
                                }
                            },
                            error: function(err) {
                                console.error("获取部门用户列表失败:", err);
                            }
                        });
                    }

                    // 如果有用户ID，先获取用户名称再渲染图表
                    if (userIds.length > 0) {
                        fetchUserNames(userIds, function() {
                            renderDrillDownChart(viewState.rawData, deptId);
                        });
                    } else {
                        // 即使没有用户ID，也尝试渲染图表
                        console.warn("该部门下没有找到用户ID，尝试直接渲染");
                        renderDrillDownChart(viewState.rawData, deptId);
                    }
                } catch (e) {
                    console.error("处理用户ID时出错:", e);
                    layer.msg('处理用户数据时出错', {icon: 2});
                    // 尝试直接渲染图表
                    renderDrillDownChart(viewState.rawData, deptId);
                }
            }

            // 渲染下钻图表
            function renderDrillDownChart(data, deptId) {
                console.log("开始渲染下钻图表 - 用户名称映射:", userIdToName);

                if (!data || data.length === 0 || !deptId) {
                    $('#clickdatas').html('<div style="text-align: center;padding-top:100px;color:#999;">暂无数据</div>');
                    return;
                }

                try {
                    // 过滤出当前部门的数据
                    var filteredData = data.filter(function(item) {
                        return parseInt(item.Department) === parseInt(deptId);
                    });

                    console.log("下钻视图 - 部门ID:", deptId);
                    console.log("下钻视图 - 过滤后数据:", filteredData);

                    if (filteredData.length === 0) {
                        // 尝试创建一个空的图表，显示"暂无数据"
                        $('#clickdatas').html('<div id="empty-chart" style="width:100%;height:100%;"></div>');

                        try {
                            var emptyChart = echarts.init(document.getElementById('empty-chart'));
                            emptyChart.setOption({
                                title: {
                                    text: '该部门暂无数据',
                                    left: 'center',
                                    top: 'center',
                                    textStyle: {
                                        color: '#999',
                                        fontSize: 16
                                    }
                                },
                                grid: {
                                    left: '3%',
                                    right: '4%',
                                    bottom: '3%',
                                    top: '3%',
                                    containLabel: true
                                }
                            });
                        } catch (e) {
                            console.error("创建空图表时出错:", e);
                            $('#clickdatas').html('<div style="text-align: center;padding-top:100px;color:#999;">该部门暂无数据</div>');
                        }
                        return;
                    }
                } catch (e) {
                    console.error("过滤部门数据时出错:", e);
                    $('#clickdatas').html('<div style="text-align: center;padding-top:100px;color:#999;">处理部门数据时出错</div>');
                    return;
                }

                // 处理数据，按人员和日期分组
                var users = {};
                var dates = [];
                var userNames = {};

                // 收集所有日期和人员
                filteredData.forEach(function(item) {
                    // 处理日期格式
                    var dateStr = item.Date ? item.Date.split('T')[0] : '';

                    // 确保日期格式有效
                    if (dateStr && /^\d{4}-\d{2}-\d{2}$/.test(dateStr) && dates.indexOf(dateStr) === -1) {
                        dates.push(dateStr);
                    }

                    // 确保AsstId是有效值
                    if (item.AsstId !== null && item.AsstId !== undefined) {
                        var asstId = parseInt(item.AsstId);
                        console.log("处理用户数据 - AsstId:", asstId, "用户名:", userIdToName[asstId]);

                        if (!users[asstId]) {
                            users[asstId] = {};
                            // 使用用户名称映射，如果不存在则使用默认值
                            // 在名称后添加ID，避免相同名称被合并
                            userNames[asstId] = (userIdToName[asstId] || '用户' + asstId) + '（' + asstId + '）';
                            console.log("添加用户 - ID:", asstId, "名称:", userNames[asstId]);
                        }

                        if (!users[asstId][dateStr]) {
                            users[asstId][dateStr] = 0;
                        }

                        users[asstId][dateStr] += item.Count;
                    }
                });

                // 对日期进行排序
                dates.sort();

                // 如果日期点太少，可能需要插入中间日期以使图表更连续
                if (dates.length > 1 && dates.length < 10) {
                    var filledDates = [];
                    var startDate = new Date(dates[0]);
                    var endDate = new Date(dates[dates.length - 1]);

                    // 遍历每一天，确保连续性
                    for (var d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
                        var dateString = d.toISOString().split('T')[0];
                        filledDates.push(dateString);
                    }

                    // 使用填充后的日期数组
                    if (filledDates.length > dates.length) {
                        dates = filledDates;
                    }
                }

                // 准备图表系列数据
                var series = [];

                console.log("下钻视图 - 用户数据:", users);
                console.log("下钻视图 - 用户名称:", userNames);

                // 检查是否有用户数据
                if (Object.keys(users).length === 0) {
                    $('#clickdatas').html('<div style="text-align: center;padding-top:100px;color:#999;">该部门暂无人员数据</div>');
                    return;
                }

                try {
                    for (var userId in users) {
                        var userData = [];
                        dates.forEach(function(date) {
                            userData.push(users[userId][date] || 0);
                        });

                        // 确保用户名称存在
                        var userName = userNames[userId];
                        if (!userName) {
                            userName = '用户' + userId;
                            console.warn("用户名称缺失，使用默认名称:", userName);
                        }

                        series.push({
                            name: userName,
                            type: 'line',
                            smooth: true,
                            data: userData,
                            symbolSize: 10, // 增大数据点尺寸
                            symbol: 'circle',
                            showSymbol: true, // 始终显示标记点
                            lineStyle: {
                                width: 1 // 增加线条宽度，使其更容易点击
                            },
                            // 增加线条的点击区域
                            emphasis: {
                                lineStyle: {
                                    width: 3
                                },
                                itemStyle: {
                                    borderWidth: 2
                                }
                            },
                            areaStyle: {
                                opacity: 0.1
                            }
                        });
                    }
                    console.log("生成的图表系列数据:", series);
                } catch (e) {
                    console.error("生成图表系列数据时出错:", e);
                    $('#clickdatas').html('<div style="text-align: center;padding-top:100px;color:#999;">处理数据时出错</div>');
                    return;
                }

                // 初始化图表
                $('#clickdatas').empty();

                try {
                    // 确保DOM元素存在
                    var chartDom = document.getElementById('clickdatas');
                    if (!chartDom) {
                        console.error("找不到图表DOM元素");
                        $('#clickdatas').html('<div style="text-align: center;padding-top:100px;color:#999;">图表容器不存在</div>');
                        return;
                    }

                    // 确保DOM元素有尺寸
                    if (chartDom.offsetHeight === 0 || chartDom.offsetWidth === 0) {
                        console.error("图表DOM元素尺寸为0");
                        chartDom.style.height = '400px'; // 设置一个默认高度
                    }

                    console.log("初始化下钻图表 - DOM尺寸:", chartDom.offsetWidth, "x", chartDom.offsetHeight);

                    // 先清空容器内容，确保没有残留元素
                    chartDom.innerHTML = '';

                    // 如果已有图表实例，先销毁它
                    if (myChart) {
                        try {
                            var tempChart = myChart;
                            myChart = null;
                            try {
                                if (tempChart && typeof tempChart.dispose === 'function') {
                                    tempChart.dispose();
                                }
                            } catch (e) {
                                console.error("销毁旧图表实例出错:", e);
                            }
                        } catch (e) {
                            console.error("处理旧图表实例时出错:", e);
                        }
                    }

                    // 初始化图表
                    try {
                        myChart = echarts.init(chartDom);
                        console.log("成功初始化下钻图表实例");
                    } catch (e) {
                        console.error("初始化下钻图表实例出错:", e);
                        $('#clickdatas').html('<div style="text-align: center;padding-top:100px;color:#999;">图表初始化失败: ' + e.message + '</div>');
                        return;
                    }

                    // 检查是否成功创建了canvas元素
                    if (chartDom.querySelector('canvas') === null) {
                        console.error("echarts未能创建canvas元素");
                        // 尝试重新创建图表
                        chartDom.innerHTML = '';
                        try {
                            // 手动创建一个canvas元素
                            var canvas = document.createElement('canvas');
                            canvas.style.width = '100%';
                            canvas.style.height = '100%';
                            chartDom.appendChild(canvas);

                            // 重新初始化图表
                            if (myChart) {
                                try {
                                    if (typeof myChart.dispose === 'function') {
                                        myChart.dispose();
                                    }
                                } catch (e) {
                                    console.error("重新创建canvas时销毁图表出错:", e);
                                }
                            }
                            myChart = echarts.init(chartDom);
                        } catch (e) {
                            console.error("手动创建canvas元素时出错:", e);
                            $('#clickdatas').html('<div style="text-align: center;padding-top:100px;color:#999;">创建图表元素失败: ' + e.message + '</div>');
                            return;
                        }
                    }
                } catch (e) {
                    console.error("初始化图表时出错:", e);
                    $('#clickdatas').html('<div style="text-align: center;padding-top:100px;color:#999;">图表初始化失败: ' + e.message + '</div>');
                    return;
                }

                // 图表配置
                var option = {
                    // 增强交互性配置
                    animation: true,
                    animationDuration: 300,
                    animationEasing: 'cubicOut',
                    // 增加点击区域
                    axisPointer: {
                        lineStyle: {
                            color: 'rgba(0, 0, 0, 0.2)',
                            width: 1
                        },
                        shadowStyle: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        }
                    },
                    tooltip: {
                        trigger: 'axis',
                        backgroundColor: 'rgba(255, 255, 255, 0.9)',
                        borderColor: '#f5f5f5',
                        borderWidth: 1,
                        textStyle: {
                            color: '#333'
                        },
                        confine: true, // 确保提示框在视图区域内
                        enterable: true, // 鼠标可进入提示框
                        extraCssText: 'box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);', // 添加阴影效果
                        formatter: function(params) {
                            var dateStr = params[0].axisValue;
                            var formattedDate = dateStr;

                            // 根据当前模式决定单位
                            var unit = $('#chart-type-text').text() === '按金额' ? '元' : '条';
                            console.log("下钻提示框单位 - 按钮文本:", $('#chart-type-text').text(), "单位:", unit);

                            var result = '<div style="font-weight:bold;margin-bottom:5px;font-size:13px;">' + formattedDate + '</div>';
                            params.forEach(function(item) {
                                result += '<div style="display:flex;align-items:center;margin:3px 0;">' +
                                    '<span style="display:inline-block;width:10px;height:10px;background:' + item.color + ';margin-right:5px;border-radius:50%;"></span>' +
                                    '<span>' + item.seriesName + ': ' + item.value + unit + '</span>' +
                                    '</div>';
                            });
                            return result;
                        }
                    },
                    legend: {
                        data: Object.values(userNames),
                        top: 10,
                        type: 'scroll',
                        textStyle: {
                            color: '#666'
                        }
                    },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '15%',
                        top: '15%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        boundaryGap: false,
                        data: dates,
                        axisLine: {
                            lineStyle: {
                                color: '#ddd'
                            }
                        },
                        axisLabel: {
                            color: '#666',
                            formatter: function(value) {
                                if (!value) return '';
                                var parts = value.split('-');
                                if (parts.length !== 3) return value;
                                return parts[1] + '-' + parts[2];
                            },
                            interval: function(index, value) {
                                if (dates.length <= 10) return 0;
                                if (dates.length <= 20) return index % 2 === 0 ? 0 : -1;
                                if (dates.length <= 40) return index % 3 === 0 ? 0 : -1;
                                return index % 5 === 0 ? 0 : -1;
                            },
                            rotate: 45,
                            margin: 10,
                            align: 'right',
                            fontSize: 10
                        }
                    },
                    yAxis: {
                        type: 'value',
                        name: $('#chart-type-text').text() === '按金额' ? '金额' : '病历数量',
                        nameTextStyle: {
                            color: '#666'
                        },
                        axisLine: {
                            show: false
                        },
                        axisTick: {
                            show: false
                        },
                        splitLine: {
                            lineStyle: {
                                color: '#eee',
                                type: 'dashed'
                            }
                        },
                        axisLabel: {
                            color: '#666'
                        }
                    },
                    series: series,
                    color: ['#4facfe', '#00f2fe', '#f093fb', '#f5576c', '#4cd964', '#5ac8fa', '#007aff', '#5856d6', '#ff2d55', '#ff9500']
                };

                // 设置图表
                try {
                    if (myChart) {
                        myChart.setOption(option);
                        console.log("下钻图表设置成功");
                    } else {
                        console.error("下钻图表实例不存在，无法设置选项");
                        $('#clickdatas').html('<div style="text-align: center;padding-top:100px;color:#999;">图表实例不存在</div>');
                        return;
                    }
                } catch (e) {
                    console.error("设置下钻图表选项时出错:", e);
                    $('#clickdatas').html('<div style="text-align: center;padding-top:100px;color:#999;">图表渲染失败: ' + e.message + '</div>');
                    return;
                }

                // 响应式调整
                var resizeHandler = function() {
                    if (myChart) {
                        try {
                            myChart.resize();
                        } catch (e) {
                            console.error("调整图表大小时出错:", e);
                        }
                    }
                };

                // 移除之前可能存在的resize事件监听器
                window.removeEventListener('resize', resizeHandler);
                // 添加新的resize事件监听器
                window.addEventListener('resize', resizeHandler);
            }

            // 获取部门数据
            function fetchDepartmentData(callback) {
                // 直接使用本地存储中的部门数据
                console.log("使用本地缓存的部门数据");
                if (callback) callback();
            }

            // 先获取部门数据，然后加载图表
            fetchDepartmentData(function() {
                // 初始加载所有数据
                loadChartData('4');
            });

            // 绑定按钮点击事件
            $('.date-btn').on('click', function() {
                var type = $(this).data('type');
                $('.date-btn').removeClass('active');
                $(this).addClass('active');
                loadChartData(type);
            });

            // 绑定自定义下拉框切换事件
            $('#chart-type-btn').on('click', function(e) {
                e.stopPropagation();
                $('#chart-type-dropdown').toggle();
            });

            // 点击下拉选项
            $('.chart-type-option').on('click', function(e) {
                e.stopPropagation();
                var value = $(this).data('value');
                var text = $(this).text();

                // 更新按钮文本
                $('#chart-type-text').text(text);

                // 更新状态并加载数据
                console.log("切换图表类型 - 之前:", viewState.by_money, "之后:", value);
                viewState.by_money = value;

                // 强制销毁图表实例，确保完全重新渲染
                if (myChart) {
                    try {
                        myChart.dispose();
                        myChart = null;
                    } catch (e) {
                        console.error("切换图表类型时销毁图表出错:", e);
                    }
                }

                // 重新加载数据
                loadChartData(viewState.currentDateType);

                // 隐藏下拉框
                $('#chart-type-dropdown').hide();
            });

            // 点击页面其他地方关闭下拉框
            $(document).on('click', function() {
                $('#chart-type-dropdown').hide();
            });

            // 绑定返回按钮点击事件
            $('#back-to-dept').on('click', function() {
                try {
                    // 如果图表存在，先安全地销毁它
                    if (myChart) {
                        try {
                            // 先将图表实例保存到临时变量
                            var tempChart = myChart;

                            // 先将myChart设为null，避免后续引用
                            myChart = null;

                            // 检查DOM元素是否存在
                            var chartDom = document.getElementById('clickdatas');
                            if (chartDom) {
                                // 清空DOM内容，避免removeChild错误
                                chartDom.innerHTML = '';
                            }

                            // 直接尝试销毁图表，不使用setTimeout
                            try {
                                if (tempChart && typeof tempChart.dispose === 'function') {
                                    tempChart.dispose();
                                }
                            } catch (e) {
                                console.error("销毁图表实例出错:", e);
                                // 即使销毁失败也继续执行
                            }
                        } catch (e) {
                            console.error("返回时销毁图表出错:", e);
                        }
                    }

                    // 显示加载中状态
                    $('#clickdatas').html('<div class="loading-container"><i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop" style="font-size: 30px;"></i><div class="loading-text">返回部门视图中...</div></div>');

                    // 重置下钻状态
                    viewState.isDrillDown = false;
                    viewState.currentDeptId = null;
                    viewState.currentDeptName = '';

                    // 隐藏下钻信息和返回按钮
                    $('#drill-info').hide();
                    $('#back-to-dept').hide();

                    // 延迟一下再重新加载部门视图，确保DOM已经更新
                    setTimeout(function() {
                        loadChartData(viewState.currentDateType);
                    }, 100);
                } catch (e) {
                    console.error("返回部门视图时出错:", e);
                    layer.msg('返回部门视图时出错', {icon: 2});
                }
            });
        });
    </script>
</body>

</html>