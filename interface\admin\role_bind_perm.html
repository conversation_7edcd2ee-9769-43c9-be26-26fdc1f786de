<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 角色列表</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
    <style>
        body{
            overflow-x: hidden;
        }
    </style>
</head>

<body>
    <div class="layui-padding-3" style="padding-top: 0 !important;min-height: 800px;">
        <div id="ID-tree-demo"></div>
    </div>
    <div style="position: fixed;right: 30px;top: 30px;">
        <button type="button" class="layui-btn layui-btn-sm" lay-on="getChecked">保存绑定</button>
    </div>
    <div style="position: fixed;right: 30px;top: 70px;font-size: 11px;color: #999;">带（M）证明是菜单</div>
    <script>
        layui.use(['element', 'layer', 'util', 'tree'], function () {
            var element = layui.element;
            var layer = layui.layer;
            var tree = layui.tree;
            var util = layui.util;
            var $ = layui.$;
            var index = parent.layer.getFrameIndex(window.name);
            // 页面顶部插入缓存的用户数据
            let local_userinfo = localStorage.getItem('local_userinfo');
            let user_name = "";
            if (local_userinfo) {
                local_userinfo = JSON.parse(local_userinfo);
                user_name = local_userinfo.Name + " ( " + local_userinfo.Roles + " )";
            } else {
                user_name = "未登录";
            }
            $('.user_name').text(user_name);

            // 获取URL传的值role_id
            let role_id = location.search.split('=')[1];

            // 将扁平数据转换为树形结构
            function buildTree(data) {
                const map = {};
                const tree = [];
                data.forEach(item => {
                    if (item.type === 0 && item.pid > 0) {
                        item.name = item.name + ' (M)';
                    }
                    map[item.id] = { ...item, children: [] };
                });
                data.forEach(item => {
                    if (item.pid === 0) {
                        tree.push(map[item.id]); // 根节点
                    } else {
                        if (map[item.pid]) {
                            map[item.pid].children.push(map[item.id]); // 子节点
                        }
                    }
                });
                return tree;
            }
            function renderTree(deduplicatedPermIds) {
                const local_perm_res_data = JSON.parse(localStorage.getItem('local_perm_res_data')) || [];
                const treeData = buildTree(local_perm_res_data);
                tree.render({
                    elem: '#ID-tree-demo',
                    data: treeData,
                    customName: { // 自定义 data 字段名
                        id: 'id',
                        title: 'name',
                        children: 'children'
                    },
                    showLine:false,
                    showCheckbox: true,  // 是否显示复选框
                    onlyIconControl: false,  // 是否仅允许节点左侧图标控制展开收缩
                    id: 'demo-id-1'
                });

                // 使用 deduplicatedPermIds 设置选中项
                tree.setChecked('demo-id-1', deduplicatedPermIds); // 勾选对应 ID 值的节点
            }

            // 获取已选中项IDS
            layer.load(2);
            $.ajax({
                url: '/admin/roles/list',
                type: 'POST',
                data: {
                    id: role_id
                },
                success: function (res) {
                    layer.closeAll('loading');
                    if (res.code == 200) {
                        let data = res.data;
                        let uniquePermIds = new Set();
                        data.forEach(role => {
                            let permIdsArray = role.Perm_ids.split(',');
                            permIdsArray.forEach(permId => {
                                uniquePermIds.add(Number(permId.trim())); // 使用 trim() 去掉可能的空格
                            });
                        });
                        let deduplicatedPermIds = Array.from(uniquePermIds);

                        // 调用渲染树形菜单函数
                        renderTree(deduplicatedPermIds);
                    }
                },
                error: function (err) {
                    layer.closeAll('loading');
                    layer.msg(err.responseJSON.msg);
                }
            });

            // 按钮事件
            util.event('lay-on', {
                getChecked: function (othis) {
                    var checkedData = tree.getChecked('demo-id-1');
                    let perm_ids = extractIds(checkedData);
                    perm_ids = perm_ids.join(',');
                    // 询问用户是否同意绑定
                    layer.confirm('确认绑定吗？', {
                        title: '请确认',
                        btn: ['确认', '取消'],
                    }, function (index) {
                        layer.close(index);
                        layer.load(2);
                        $.ajax({
                            url: '/admin/roles/bind_perm',
                            type: 'POST',
                            data: {
                                id: role_id,
                                perm_ids: perm_ids
                            },
                            success: function (res) {
                                layer.closeAll('loading');
                                if (res.code == 200) {
                                    layer.msg(res.msg);
                                    setTimeout(function () {
                                        parent.layer.close(index);
                                        parent.layui.table.reload('role_list');
                                    }, 500);
                                } else {
                                    layer.msg('绑定失败，请稍后重试');
                                }
                            },
                            error: function (err) {
                                layer.closeAll('loading');
                                layer.msg(err.responseJSON.msg);
                            }
                        });
                    })
                }
            });
        });
    </script>
</body>

</html>