<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 角色列表</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
</head>

<body>
    <div class="layui-layout layui-layout-admin">
        <!-- 头部 -->
        <div class="layui-header">
            <div class="layui-logo">幸年堂</div>
            <ul class="layui-nav layui-layout-right">
                <li class="layui-nav-item" style="background-color:transparent!important;padding-right:10px">
                    <a href="javascript:;" style="display:flex;align-items:center;color:#fff"><img
                            src="/dist/images/user.png" class="layui-nav-img"><span class="user_name"></span></a>
                    <dl class="layui-nav-child pop_menu">
                        <dd><i class="iconfont">&#xe62f;</i><a href="change_password.html#7">用户资料</a></dd>
                        <dd><i class="iconfont">&#xe601;</i><a href="javascript:void(0)" class="refresh_cache">刷新缓存</a>
                        </dd>
                        <dd><i class="iconfont">&#xe634;</i><a href="login_out.html">退出登录</a></dd>
                    </dl>
                </li>
            </ul>
        </div>
        <!-- 导航 -->
        <ul class="layui-nav layui-nav-tree layui-nav-side" id="menuContainer" lay-bar="disabled" lay-accordion>
            <div style="margin: 50px 0;color: #bbb;text-align: center;">菜单加载中...</div>
        </ul>


        <!-- 主体内容 -->
        <div class="layui-body">
            <div class="body_child">
                <div class="layui-panel">
                    <div class="layui-card-header">
                        <div class="layui-row" style="padding-top:10px;">
                            <div class="layui-col-md11 layui-col-sm10">① 选择病历</div>
                        </div>
                    </div>

                    <div class="layui-padding-3" style="padding-top: 0 !important;min-height: 800px;">

                        <div id="data_search" style="margin: 10px;">
                            <div class="layui-form" style="display: flex; align-items: center; gap: 10px;">
                                <input type="text" name="wd" id="dropdown_input" placeholder="请输入电话或姓名"
                                    autocomplete="off" class="layui-input" lay-affix="clear" style="width: 250px;">

                                <button class="layui-btn" lay-submit lay-filter="search"
                                    style="width: 100px;">筛选</button>
                            </div>
                        </div>


                        <table id="mytable" lay-filter="mytable" class="layui-table" lay-skin="row"></table>
                    </div>
                </div>
                <br>
                <br>
            </div>
        </div>
    </div>
    <!-- 以前是{{#  if(d.Status < 4){ }} -->
    <script type="text/html" id="TPL-bar">
            {{#  if(d.Status === 0){ }}
                <a class="layui-btn layui-btn-xs perm_check_btn create_rtc_room" lay-event="create_rtc_room" res_id="151">{{Record_Status[d.Status]}} - 可创建</a>
            {{#  } else { }}
                <span>{{Record_Status[d.Status]}}</span>
            {{#  } }}
    </script>

    <script>
        layui.use(['element', 'layer', 'util'], function () {
            var element = layui.element;
            var form = layui.form;
            var layer = layui.layer;
            var util = layui.util;
            var laydate = layui.laydate;
            var table = layui.table;
            var dropdown = layui.dropdown;
            var pat_pro_id = 0;
            var $ = layui.$;
            // 渲染日期
            laydate.render({
                elem: '#ID-laydate-demo'
            });
            // 页面顶部插入缓存的用户数据
            let local_userinfo = localStorage.getItem('local_userinfo');
            let user_name = "";
            if (local_userinfo) {
                local_userinfo = JSON.parse(local_userinfo);
                user_name = local_userinfo.Name + " ( " + local_userinfo.Roles + " )";
            } else {
                user_name = "未登录";
            }
            $('.user_name').text(user_name);
            render_menu($);
            $('.refresh_cache').click(function () {
                refresh_cache($);
            });
            //引入TABLE模块
            layer.load(2);
            table.render({
                elem: '#mytable'
                , url: serverUrl + "/admin/patient_records/list"
                , method: 'post'
                , even: true
                , response: {
                    statusName: 'code'
                    , statusCode: 200
                    , msgName: 'msg'
                    , countName: 'count'
                    , dataName: 'data'
                }
                , cols: [[
                    { field: 'ID', title: '病历ID', align: 'center' ,templet:function(d){
                        return "B" + d.ID;
                    }},
                    { field: 'Pat_pro_id', title: '用户ID', align: 'center' },
                    {
                        field: 'ID', title: '姓名', align: 'center', templet: function (d) {
                            return '-';
                        }
                    },
                    {
                        title: '性别', align: 'center', templet: function (d) {
                            return '-';
                        }
                    },
                    {
                        title: '年龄', align: 'center', templet: function (d) {
                            return '-';
                        }
                    },
                    {
                        title: '就诊类型', align: 'center', templet: function (d) {
                            return '-';
                        }
                    },
                    {
                        field: 'Create_time', title: '病历时间', align: 'center', templet: function (d) {
                            return d.Create_time ? d.Create_time.split('T')[0] : '-';
                        }
                    },
                    {
                        field: 'Department_id', title: '科室', align: 'center'
                    },
                    {
                        field: 'Doc_id', title: '医生', align: 'center'
                    },
                    { title: '操作', align: 'center', toolbar: '#TPL-bar' }
                ]]
                , done: function (res, curr, count) {
                    // console.log(res);
                    // console.log(curr);
                    // console.log(count);
                    after_table_done_rander();
                    // 点击帐号ID弹出模态框
                    $('.pid_link').on('click', function () {
                        let pid = $(this).data('id');
                        layer.open({
                            type: 2,
                            title: '帐号详情',
                            shade: 0.2,
                            maxmin: true,
                            area: ['500px', '690px'],
                            shadeClose: true,
                            content: 'patient_account_detail.html?id=' + pid
                        });
                    });
                },
                error: function (data) {
                    layer.msg(data.responseJSON.msg);
                },
                page: true,
                limit: 15,
            })
            //监听工具条
            table.on('tool(mytable)', function (obj) {
                var data = obj.data;
                if (obj.event === 'create_rtc_room') {
                    let url = 'medical_online_add_room.html?record_id=' + data.ID + "&department_id=" + data.Department_id + '&pat_pro_id=' + data.Pat_pro_id + '&doc_id=' + data.Doc_id;
                    url += '#/admin/medical_online_add.html';
                    window.location.href = url;
                }
            });
            form.on('submit(search)', function (data) {
                // 重载表格
                if (data.field.wd.split(' / ').length > 1) {
                    data.field.pat_pro_id = pat_pro_id;
                }
                table.reload('mytable', {
                    where: data.field,
                    page: {
                        curr: 1 //重新从第 1 页开始
                    }
                });
                return false;
            });
            //新增用户按钮
            $('.create_btn').on('click', function () {
                window.location.href = 'patient_records_add.html';
            });
            //顶部 - 渲染部门下拉框（使用本地存储）
            let departmentData = localStorage.getItem('local_departments');
            if (departmentData) {
                try {
                    departmentData = JSON.parse(departmentData);
                    // 渲染科室树形结构
                    let treeData = format_to_treedata_department(departmentData, global_default_store_id);
                    treeData = renderDropdownItems(treeData);
                    dropdown.render({
                        elem: '#Department',
                        id: 'DropdownID',
                        data: [],
                        content: '<div class="dropdown-menu">' + treeData + '</div>',
                        ready: function (elemPanel, elem) {
                            elemPanel.on('click', '.dropdown-item-leaf', function () {
                                $('#Department').val($(this).text());
                                dropdown.close('DropdownID');
                            });
                        }
                    });
                } catch (e) {
                    console.error('解析部门数据失败:', e);
                    layer.msg('部门数据解析失败，请刷新缓存', { icon: 2, time: 2000 });
                }
            } else {
                layer.msg('未找到部门数据，请刷新缓存', { icon: 2, time: 2000 });
            }
            // 顶部 - 渲染科室下拉表
            $.ajax({
                url: '/admin/department/list',
                data: {
                    name: '医疗部门',
                    store_id: global_default_store_id,
                },
                type: 'post',
                success: function (res) {
                    layer.closeAll('loading');
                    if (res.code === 200) {
                        let data = res.data;
                        let html = '<option value="">选择科室</option>';
                        for (let i = 0; i < data.length; i++) {
                            html += '<option value="' + data[i].id + '">' + data[i].name + '</option>';
                        }
                        $('#keshi').html(html);
                        form.render('select');
                    } else {
                        layer.msg(res.msg, { icon: 2, time: 1000 });
                    }
                }
            });
            //模糊搜索用户的下拉框组件
            var inst = dropdown.render({
                elem: '#dropdown_input',
                trigger: 'keyup click',
                data: [],
                style: 'min-width: 220px; box-shadow: 1px 1px 11px rgb(0 0 0 / 11%);',
                click: function (data) {
                    if (!isEmpty(data)) {
                        this.elem.val(data.name + ' / ' + data.phone);
                        pat_pro_id = data.id;
                    }
                }
            });
            $(inst.config.elem).on('input propertychange', function () {
                var elem = $(this);
                var value = elem.val().trim();
                if (value.length > 0) {
                    $.ajax({
                        url: "/admin/patient_profile/patient_profile_phone2id",
                        type: 'post',
                        dataType: 'json',
                        data: {
                            "wd": value,
                        },
                        success: function (response) {
                            let data = response.data;
                            if (data && data.length > 0) {
                                dropdown.reloadData(inst.config.id, {
                                    data: data,
                                    templet: function (d) {
                                        var exp = new RegExp(value, 'gi');
                                        return (d.name + ' / ' + d.phone).replace(exp, function (str) {
                                            return '<span style="color: red;">' + str + '</span>';
                                        });
                                    }
                                });
                            } else {
                                dropdown.reloadData(inst.config.id, {
                                    data: [],
                                });
                            }
                        },
                        error: function (err) {
                            dropdown.reloadData(inst.config.id, {
                                data: [],
                            });
                        }
                    });
                } else {
                    dropdown.reloadData(inst.config.id, {
                        data: [],
                    });
                }
            });




            var after_table_done_rander = function () {
                render_button($);
                // 表格内AJAX填充，整行填充
                // 获取用户信息
                let patient_profile_array = [];
                $("tbody").find("tr").each(function (d) {
                    patient_profile_array.push($(this).find("td:eq(1)").text());
                });
                patient_profile_array = Array.from(new Set(patient_profile_array));
                for (let i = 0; i < patient_profile_array.length; i++) {
                    $.ajax({
                        url: serverUrl + "/admin/patient_profile/detail",
                        type: "post",
                        data: {
                            id: patient_profile_array[i],
                        },
                        success: function (res) {
                            if (res.code === 200) {
                                let data = res.data;
                                $("tbody").find("tr").each(function () {
                                    let trid = $(this).find("td:eq(1)").text();
                                    if (trid == data.ID) {
                                        $(this).find("td:eq(2)").html('<div class="layui-table-cell laytable-cell-1-0-0" align="center">' + data.Name + '</div>');
                                        $(this).find("td:eq(3)").html('<div class="layui-table-cell laytable-cell-1-0-0" align="center">' + (data.Sex == 1 ? '男' : '女') + '</div>');
                                        $(this).find("td:eq(4)").html('<div class="layui-table-cell laytable-cell-1-0-0" align="center">' + data.Born_date.split('T')[0] + '</div>');
                                        // 就诊时间已经在表格初始化时设置，不需要再次设置
                                    }
                                })

                            } else {
                                layer.msg(res.msg, { icon: 2, time: 1000 });
                            }
                        }
                    });
                }



                // 获取医生信息（优化版）
                let doctor_array = [];
                $("tbody").find("tr").each(function (d) {
                    doctor_array.push($(this).find("td:eq(8)").text());
                });
                doctor_array = Array.from(new Set(doctor_array));

                // 过滤掉空值和无效值
                doctor_array = doctor_array.filter(id => id && id.trim() !== '' && id !== '0');

                if (doctor_array.length > 0) {
                    $.ajax({
                        url: serverUrl + "/admin/user/list_low",
                        type: "post",
                        data: {
                            user_id_list: doctor_array.join(','),
                        },
                        success: function (res) {
                            if (res.code === 200) {
                                let data = res.data;
                                for (let i = 0; i < data.length; i++) {
                                    let id = data[i].ID;
                                    let name = data[i].Name;
                                    $("tbody").find("tr").each(function () {
                                        let trid = $(this).find("td:eq(8)").text();
                                        if (trid == id) {
                                            $(this).find("td:eq(8)").html('<div class="layui-table-cell laytable-cell-1-0-0" align="center">' + (name ? name : '未知') + '</div>');
                                        }
                                    });
                                }
                            } else {
                                layer.msg(res.msg, { icon: 2, time: 1000 });
                            }
                        },
                        error: function (res) {
                            layer.closeAll('loading');
                            layer.msg(res.responseJSON.msg, { icon: 2, time: 1000 });
                            $("tbody").find("tr").each(function () {
                                let trid = $(this).find("td:eq(8)").text();
                                if (doctor_array.includes(trid)) {
                                    $(this).find("td:eq(8)").html('<div class="layui-table-cell laytable-cell-1-0-0" align="center">无该医生ID</div>');
                                }
                            });
                        }
                    });
                }

                // 获取科室（使用本地存储）
                let Department_array = [];
                $("tbody").find("tr").each(function (d) {
                    Department_array.push($(this).find("td:eq(7)").text());
                });
                Department_array = Array.from(new Set(Department_array));

                // 从本地存储获取部门数据
                let departmentData = localStorage.getItem('local_departments');
                if (departmentData) {
                    try {
                        departmentData = JSON.parse(departmentData);
                        // 遍历表格中的部门ID
                        for (let i = 0; i < Department_array.length; i++) {
                            let deptId = Department_array[i];
                            // 在部门数据中查找匹配的部门
                            let foundDept = null;
                            if (Array.isArray(departmentData)) {
                                foundDept = departmentData.find(dept => dept.Id == deptId || dept.id == deptId);
                            } else if (departmentData.Id == deptId || departmentData.id == deptId) {
                                foundDept = departmentData;
                            }

                            // 更新表格中的部门名称
                            $("tbody").find("tr").each(function () {
                                let trid = $(this).find("td:eq(7)").text();
                                if (trid == deptId) {
                                    if (foundDept) {
                                        $(this).find("td:eq(7)").html('<div class="layui-table-cell laytable-cell-1-0-0" align="center">' + (foundDept.Name || foundDept.name || '未知') + '</div>');
                                    } else {
                                        $(this).find("td:eq(7)").html('<div class="layui-table-cell laytable-cell-1-0-0" align="center">无该科室ID</div>');
                                    }
                                }
                            });
                        }
                    } catch (e) {
                        console.error('解析部门数据失败:', e);
                        // 如果解析失败，显示原始ID
                    }
                } else {
                    console.error('未找到部门数据');
                    // 如果没有部门数据，显示原始ID
                }

                $("tbody").find("tr").each(function (d) {
                    let that = $(this).find("td:eq(9)");
                    let id = $(this).find("td:eq(0)").text();
                    // 根据病历ID，判断当前病历是否可以创建线上诊室，接口/admin/rtc_room/exist_by_record
                    $.ajax({
                        url: serverUrl + "/admin/rtc_room/exist_by_record",
                        type: "post",
                        data: {
                            record_id: id,
                        },
                        success: function (res) {
                            if (res.code === 200) {
                                let data = res.data;
                                if (data > 0) {
                                    that.html('<div class="layui-table-cell laytable-cell-1-0-0" align="center"><a href="rtc_room_list.html?record_id=' + id + '#/admin/rtc_room_list.html" class="btn_arg_pm" style="font-weight: bold;">有待诊数据</a></div>');
                                }
                                //  else {
                                //     that.html('<div class="layui-table-cell laytable-cell-1-0-0" align="center"><a class="layui-btn layui-btn-xs layui-bg-orange perm_check_btn create_rtc_room" lay-event="create_rtc_room" res_id="151">创建线上诊室</a></div>');
                                // }
                            } else {
                                layer.msg(res.msg, { icon: 2, time: 1000 });
                            }
                        },
                        error: function (err) {
                            layer.msg('网络错误，请稍后再试', { icon: 2, time: 1000 });
                        }
                        //重新渲染按钮
                    });
                    // render_button($);
                });

                // 就诊类型 - 初次还是N次
                let Records_array = [];
                $("tbody").find("tr").each(function (d) {
                    Records_array.push($(this).find("td:eq(1)").text());
                });
                Records_array = Array.from(new Set(Records_array));
                for (let i = 0; i < Records_array.length; i++) {
                    $.ajax({
                        url: serverUrl + "/admin/patient_records/count",
                        type: "post",
                        data: {
                            id: Records_array[i],
                        },
                        success: function (res) {
                            if (res.code === 200) {
                                let data = res.data;
                                $("tbody").find("tr").each(function () {
                                    let trid = $(this).find("td:eq(1)").text();
                                    if (trid == Records_array[i]) {
                                        $(this).find("td:eq(5)").html('<div class="layui-table-cell laytable-cell-1-0-0" align="center">' + (data == 1 ? '初诊' : data + '次诊') + '</div>');
                                    }
                                })
                            }
                        },
                        error: function (err) {
                            $("tbody").find("tr").each(function () {
                                let trid = $(this).find("td:eq(1)").text();
                                if (trid == Records_array[i]) {
                                    $(this).find("td:eq(5)").html('<div class="layui-table-cell laytable-cell-1-0-0" align="center">' + (data == 1 ? '初诊' : data + '次诊') + '</div>');
                                }
                            })
                        }
                    });
                }
            }

        });
    </script>
</body>

</html>