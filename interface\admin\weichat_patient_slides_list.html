<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 前端WEB上传</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
    <style>
        .image-container {
            position: relative;
            margin-bottom: 15px;
            overflow: hidden;
            border-radius: 2px;
            cursor: pointer;
        }

        .image-container img {
            width: 100%;
            height: 160px;
            object-fit: cover;
            display: block;
            transition: all 0.3s;
        }

        .image-container:hover img {
            transform: scale(1.05);
        }

        .image-container .delete-btn {
            position: absolute;
            top: 5px;
            right: 5px;
            display: none;
            background-color: rgba(0, 0, 0, 0.5);
            color: #fff;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            text-align: center;
            line-height: 30px;
            cursor: pointer;
            z-index: 10;
        }

        .image-container:hover .delete-btn {
            display: block;
        }

        .layui-container {
            padding: 15px;
        }

        .layui-col-space15 {
            margin: -7.5px;
        }

        .layui-col-space15>* {
            padding: 7.5px;
        }

        /* 大屏幕下8列布局 */
        @media screen and (min-width: 1200px) {
            .layui-col-lg1-5 {
                width: 12.5%;
            }
        }

        .empty-tip {
            text-align: center;
            padding: 30px 0;
            color: #999;
        }

        /* 修改栅格布局为固定5列 */
        .layui-col-fifth {
            width: 20%;
            float: left;
            padding: 7.5px;
            box-sizing: border-box;
        }
        .table_list_img{
            max-width: none!important;
        }
    </style>
</head>

<body>
    <div class="layui-layout layui-layout-admin">
        <!-- 头部 -->
        <div class="layui-header">
            <div class="layui-logo">幸年堂</div>
            <ul class="layui-nav layui-layout-right">
                <li class="layui-nav-item" style="background-color:transparent!important;padding-right:10px">
                    <a href="javascript:;" style="display:flex;align-items:center;color:#fff"><img
                            src="/dist/images/user.png" class="layui-nav-img"><span class="user_name"></span></a>
                    <dl class="layui-nav-child pop_menu">
                        <dd><i class="iconfont">&#xe62f;</i><a href="change_password.html#7">用户资料</a></dd>
                        <dd><i class="iconfont">&#xe601;</i><a href="javascript:void(0)" class="refresh_cache">刷新缓存</a>
                        </dd>
                        <dd><i class="iconfont">&#xe634;</i><a href="login_out.html">退出登录</a></dd>
                    </dl>
                </li>
            </ul>
        </div>
        <!-- 导航 -->
        <ul class="layui-nav layui-nav-tree layui-nav-side" id="menuContainer" lay-bar="disabled" lay-accordion>
            <div style="margin: 50px 0;color: #bbb;text-align: center;">菜单加载中...</div>
        </ul>
        <!-- 主体内容 -->
        <div class="layui-body">
            <div class="body_child">
                <div class="layui-panel">
                    <div class="layui-card-header">
                        <div class="layui-row" style="padding-top:10px;">
                            <div class="layui-col-md11 layui-col-sm10">
                                患者小程序幻灯片
                            </div>
                        </div>
                    </div>
                    <div class="layui-padding-3" style="padding-top: 0 !important;min-height: 710px;">
                        <!-- 添加图片上传模块 -->
                        <div class="layui-row layui-form-item">
                            <div class="layui-col-xs12">
                                <div class="layui-form-item">
                                    <div class="layui-input-block" style="margin-left: 0;">
                                        <input type="hidden" name="CoverPic" value="">
                                        <div class="layui-upload upload-container" id="upload-container">
                                            <button type="button" class="layui-btn upload_big_btn"
                                                id="upload-image-btn">
                                                <div class="btn_big_font">
                                                    <i class="layui-icon layui-icon-upload btn_big_font"></i> 上传图片
                                                </div>
                                                <div>jpg、png、jpeg、bmp、gif格式，1M以内</div>
                                                <div>支持点击、拖拽和 Ctrl+V 上传</div>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="layui-row" id="imageGrid" style="margin: -7.5px;">
                            <div class="empty-tip">加载中...</div>
                        </div>

                    </div>
                </div>
                <br>
                <br>
            </div>
        </div>
    </div>


    <script>
        layui.use(['element', 'layer', 'util', 'upload'], function () {
            var element = layui.element;
            var layer = layui.layer;
            var util = layui.util;
            var upload = layui.upload;
            var treeTable = layui.treeTable;
            var dropdown = layui.dropdown;
            var $ = layui.$;

            // 页面顶部插入缓存的用户数据
            let local_userinfo = localStorage.getItem('local_userinfo');
            let user_name = "";
            if (local_userinfo) {
                local_userinfo = JSON.parse(local_userinfo);
                user_name = local_userinfo.Name + " ( " + local_userinfo.Roles + " )";
            } else {
                user_name = "未登录";
            }
            $('.user_name').text(user_name);
            render_menu($);
            
            $('.refresh_cache').click(function () {
                refresh_cache($);
            });

            // 添加一个数组来跟踪当前的图片列表
            let currentImages = [];

            // 修改加载图片数据函数
            function loadImages() {
                $.ajax({
                    url: '/patient/patient_slides_list',
                    type: 'GET',
                    success: function (res) {
                        if (res.code === 200 && res.data) {
                            currentImages = res.data.split(',');
                            renderImages(currentImages);
                        } else {
                            $('#imageGrid').html('<div class="empty-tip">暂无图片数据</div>');
                            layer.msg('获取图片数据失败', { icon: 2 });
                        }
                    },
                    error: function (err) {
                        console.log('请求失败', err);
                        $('#imageGrid').html('<div class="empty-tip">获取数据失败，请刷新重试</div>');
                        layer.msg('获取图片数据失败', { icon: 2 });
                    }
                });
            }

            // 修改渲染图片函数
            function renderImages(data) {
                if (data.length === 0) {
                    $('#imageGrid').html('<div class="empty-tip">暂无图片数据</div>');
                    return;
                }

                var html = '';
                for (var i = 0; i < data.length; i++) {
                    var filename = data[i];
                    var imageUrl = '/static/uploads/normal_pics/wx_slide_pics/' + filename;

                    html += '<div class="layui-col-fifth">' +
                        '<div class="image-container">' +
                        '<img src="' + imageUrl + '" alt="图片" class="table_list_img">' +
                        '<div class="delete-btn" data-filename="' + filename + '"><i class="layui-icon layui-icon-delete"></i></div>' +
                        '</div>' +
                        '</div>';
                }

                $('#imageGrid').html(html);

                // 绑定删除事件
                $('.delete-btn').on('click', function (e) {
                    e.stopPropagation();
                    var filename = $(this).data('filename');
                    var container = $(this).closest('.layui-col-fifth');

                    layer.confirm('确定要删除这张图片吗？', { icon: 3, title: '提示' }, function (index) {
                        // 从数组中移除图片
                        currentImages = currentImages.filter(img => img !== filename);
                        container.fadeOut(300, function () {
                            $(this).remove();
                            if ($('#imageGrid .image-container').length === 0) {
                                $('#imageGrid').html('<div class="empty-tip">暂无图片数据</div>');
                            }
                        });
                        layer.close(index);
                        save_change();
                    });
                });

                // 绑定图片点击查看大图事件
                $('.image-container').on('click', function () {
                    var imgSrc = $(this).find('img').attr('src');
                    layer.photos({
                        photos: {
                            title: '查看图片',
                            data: [{
                                src: imgSrc
                            }]
                        },
                        footer: false
                    });
                });
            }

            // 修改上传配置
            upload.render({
                elem: '#upload-image-btn',
                url: '/admin/upload_normal_pic',
                multiple: true,
                data: {
                    category: 'wx_slide_pics',
                },
                drag: true,
                before: function (obj) {
                    obj.preview(function (index, file, result) {
                        autoCompressAndUpload(file, {
                            data: { category: 'wx_slide_pics' },
                            success: function (res) {
                                if (res.code === 200) {
                                    // 将新上传的图片添加到数组和显示列表中
                                    const fileInfo = res.data[0];
                                    const filename = fileInfo.filename;
                                    currentImages.push(filename);

                                    // 追加新图片到网格中
                                    const newImageHtml = `
                                        <div class="layui-col-fifth">
                                            <div class="image-container">
                                                <img src="${fileInfo.filepath}" alt="图片" class="table_list_img">
                                                <div class="delete-btn" data-filename="${filename}">
                                                    <i class="layui-icon layui-icon-delete"></i>
                                                </div>
                                            </div>
                                        </div>`;

                                    // 如果是第一张图片，清空"暂无图片"提示
                                    if ($('#imageGrid .empty-tip').length) {
                                        $('#imageGrid').empty();
                                    }
                                    $('#imageGrid').append(newImageHtml);

                                    // 绑定新图片的事件
                                    const newImage = $('#imageGrid .image-container').last();
                                    bindImageEvents(newImage, filename);

                                    layer.msg('上传成功', { icon: 1, time: 1000 });
                                    save_change();
                                } else {
                                    layer.msg(res.msg || '上传失败', { icon: 2 });
                                }
                            },
                            error: function (error) {
                                layer.msg(error.message || '上传出错', { icon: 2 });
                            }
                        });
                    });
                    return false;
                }
            });

            // 修改粘贴上传功能部分的代码
            document.addEventListener('paste', function(event) {
                const items = event.clipboardData && event.clipboardData.items;
                let files = [];
                
                if (items && items.length) {
                    // 遍历剪切板内容
                    for (let i = 0; i < items.length; i++) {
                        if (items[i].type.indexOf('image') !== -1) {
                            const file = items[i].getAsFile();
                            if (file) {
                                files.push(file);
                            }
                        }
                    }
                }
                
                if (files.length === 0) {
                    return;
                }
                
                // 阻止默认粘贴行为
                event.preventDefault();

                // 使用Promise.all处理多个文件上传
                const uploadPromises = files.map(file => {
                    return new Promise((resolve, reject) => {
                        autoCompressAndUpload(file, {
                            data: { category: 'wx_slide_pics' },
                            success: function(res) {
                                if (res.code === 200) {
                                    resolve(res);
                                } else {
                                    reject(new Error(res.msg || '上传失败'));
                                }
                            },
                            error: function(error) {
                                reject(error);
                            }
                        });
                    });
                });

                // 处理所有上传
                Promise.all(uploadPromises)
                    .then(results => {
                        results.forEach(res => {
                            const fileInfo = res.data[0];
                            const filename = fileInfo.filename;
                            currentImages.push(filename);

                            // 追加新图片到网格中
                            const newImageHtml = `
                                <div class="layui-col-fifth">
                                    <div class="image-container">
                                        <img src="${fileInfo.filepath}" alt="图片" class="table_list_img">
                                        <div class="delete-btn" data-filename="${filename}">
                                            <i class="layui-icon layui-icon-delete"></i>
                                        </div>
                                    </div>
                                </div>`;

                            // 如果是第一张图片，清空"暂无图片"提示
                            if ($('#imageGrid .empty-tip').length) {
                                $('#imageGrid').empty();
                            }
                            $('#imageGrid').append(newImageHtml);

                            // 绑定新图片的事件
                            const newImage = $('#imageGrid .image-container').last();
                            bindImageEvents(newImage, filename);
                        });
                        
                        layer.msg('上传成功', {icon: 1, time: 1000});
                        save_change(); // 保存更改
                    })
                    .catch(error => {
                        layer.msg(error.message || '上传出错', {icon: 2});
                    });
            });

            // 提取公共的图片事件绑定函数
            function bindImageEvents(imageElement, filename) {
                imageElement.find('.delete-btn').on('click', function (e) {
                    e.stopPropagation();
                    var container = $(this).closest('.layui-col-fifth');

                    layer.confirm('确定要删除这张图片吗？', { icon: 3, title: '提示' }, function (index) {
                        // 先请求后端删除文件
                        $.ajax({
                            url: '/admin/normal_pic_del',
                            type: 'POST',
                            data: {
                                filename: filename,
                                category: 'wx_slide_pics'
                            },
                            success: function (res) {
                                if (res.code === 200) {
                                    // 从数组中移除图片
                                    currentImages = currentImages.filter(img => img !== filename);
                                    // 从界面移除图片
                                    container.fadeOut(300, function () {
                                        $(this).remove();
                                        if ($('#imageGrid .image-container').length === 0) {
                                            $('#imageGrid').html('<div class="empty-tip">暂无图片数据</div>');
                                        }
                                    });
                                    layer.msg('删除成功', { icon: 1, time: 1000 });
                                    // 删除成功后保存更改
                                    save_change();
                                } else {
                                    layer.msg(res.msg || '删除失败', { icon: 2 });
                                }
                            },
                            error: function (data) {
                                // 如果后端删除失败，询问是否仍要从界面移除
                                layer.confirm('删除失败: ' + data.responseJSON.msg + '，是否移除该图片？',
                                    { icon: 3, title: '提示' },
                                    function (index) {
                                        currentImages = currentImages.filter(img => img !== filename);
                                        container.fadeOut(300, function () {
                                            $(this).remove();
                                            if ($('#imageGrid .image-container').length === 0) {
                                                $('#imageGrid').html('<div class="empty-tip">暂无图片数据</div>');
                                            }
                                        });
                                        layer.msg('移除成功', { icon: 1, time: 1000 });
                                        save_change();
                                        layer.close(index);
                                    }
                                );
                            }
                        });
                        layer.close(index);
                    });
                });

                imageElement.on('click', function () {
                    var imgSrc = $(this).find('img').attr('src');
                    layer.photos({
                        photos: {
                            title: '查看图片',
                            data: [{
                                src: imgSrc
                            }]
                        },
                        footer: false
                    });
                });
            }

            // 修改保存按钮事件，使用新的接口地址
            var save_change = function () {
                const imageList = currentImages.join(',');
                layer.load(2);
                $.ajax({
                    url: '/admin/weichat_patient_slides/edit',
                    type: 'POST',
                    data: {
                        pics: imageList  // 用逗号分隔的图片列表
                    },
                    success: function (res) {
                        layer.closeAll('loading');
                        if (res.code === 200) {
                            layer.msg('保存成功', { icon: 1, time: 1000 });
                        } else {
                            layer.msg(res.msg || '保存失败', { icon: 2 });
                        }
                    },
                    error: function (xhr) {
                        layer.closeAll('loading');
                        layer.msg('保存失败：' + (xhr.responseJSON?.msg || '未知错误'), { icon: 2 });
                    }
                });
            }

            // 初始化加载图片
            loadImages();
        });
    </script>
</body>

</html>