package admin

import (
	"fmt"
	"mstproject/pkg/common"
	"mstproject/pkg/config"
	"mstproject/pkg/database"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"strings"
)

// VisitLog 结构体定义
type VisitLog struct {
	ID          int    `db:"id"`          // ID
	Record_id   int    `db:"record_id"`   // 关联的病历ID
	Asst_id     int    `db:"asst_id"`     // 客服ID
	Pics        string `db:"pics"`        // 图片，多张图片以逗号分隔
	Contents    string `db:"contents"`    // 文字说明
	Create_time string `db:"create_time"` // 创建时间
}

// PhoneLog 结构体定义
type PhoneLog struct {
	ID          int    `db:"id"`          // ID
	Record_id   int    `db:"record_id"`   // 关联的病历ID
	Asst_id     int    `db:"asst_id"`     // 客服ID
	Files       string `db:"files"`       // 录音文件，多个文件以逗号分隔
	Contents    string `db:"contents"`    // 文字说明
	Create_time string `db:"create_time"` // 创建时间
}

// RtcRoom 结构体定义
type RtcRoom struct {
	ID             int    `db:"id"`             // ID
	Record_id      int    `db:"record_id"`      // 关联的病历ID
	Doc_id         int    `db:"doc_id"`         // 医生ID
	Asst_id        int    `db:"asst_id"`        // 医助ID
	Create_time    string `db:"create_time"`    // 创建时间
	Scheduled_time string `db:"scheduled_time"` // 预约时间
	Finish_time    string `db:"finish_time"`    // 完成时间，可能是时间戳格式
}

// 获取图文沟通记录列表
func Visit_logs_list(w http.ResponseWriter, r *http.Request) {
	api_id := config.NormalPerm
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}

	record_id, err := common.CheckInt(r.FormValue("record_id"))
	if err != nil || record_id < 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "病历ID不能为空",
		})
		return
	}

	// 分页参数
	limit, err := strconv.Atoi(r.FormValue("limit"))
	if err != nil || limit < 1 {
		limit = 10 // 默认每页10条
	}
	page, err := strconv.Atoi(r.FormValue("page"))
	if err != nil || page < 1 {
		page = 1 // 默认第1页
	}
	offset := (page - 1) * limit

	// 查询总数
	var count int
	countSql := "SELECT COUNT(id) FROM visit_logs WHERE record_id = ?"
	err = database.GetOne(countSql, &count, record_id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "读取数据总数失败",
			"err":  err.Error(),
		})
		return
	}

	// 查询图文沟通记录列表
	sql := "SELECT id, record_id, asst_id, pics, contents, create_time FROM visit_logs WHERE record_id = ? ORDER BY create_time DESC LIMIT ? OFFSET ?"
	var visit_logs []VisitLog
	err = database.GetAll(sql, &visit_logs, record_id, limit, offset)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败",
			"err":  err.Error(),
		})
		return
	}

	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":  200,
		"msg":   "ok",
		"data":  visit_logs,
		"count": count,
	})
}

// 添加图文沟通记录
func Visit_logs_add(w http.ResponseWriter, r *http.Request) {
	api_id := config.NormalPerm
	session, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}

	record_id, err := common.CheckInt(r.FormValue("record_id"))
	if err != nil || record_id < 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "病历ID不能为空",
		})
		return
	}

	contents := r.FormValue("contents")
	pics := r.FormValue("pics")

	// 添加图文沟通记录
	sql := "INSERT INTO visit_logs (record_id, asst_id, pics, contents) VALUES (?, ?, ?, ?)"
	result, err := database.Query(sql, record_id, session.Values["id"], pics, contents)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]any{
			"code": 500,
			"msg":  "添加记录失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, record_id, session.Values["id"], pics, contents),
		})
		return
	}

	new_id, _ := result.LastInsertId()
	common.JSONResponse(w, http.StatusOK, map[string]any{
		"code":   200,
		"msg":    "添加记录成功",
		"new_id": new_id,
	})
	common.Add_log(fmt.Sprintf("添加图文沟通记录，ID：%d，病历ID：%d，内容：%s", new_id, record_id, contents), r)
}

// 删除图文沟通记录
func Visit_logs_del(w http.ResponseWriter, r *http.Request) {
	// 只有超级管理员有权限删除
	api_id := config.SystemPerm
	session, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}

	// 检查当前用户是否为超级管理员(ID=1)
	user_id := session.Values["id"].(int)
	if user_id != 1 {
		common.JSONResponse(w, http.StatusForbidden, map[string]interface{}{
			"code": 403,
			"msg":  "只有超级管理员可以删除图文沟通记录",
		})
		return
	}

	// 获取要删除的记录ID
	id, err := common.CheckInt(r.FormValue("id"))
	if err != nil || id < 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "记录ID不能为空",
		})
		return
	}

	// 先查询记录，获取图片信息
	var visitLog VisitLog
	querySql := "SELECT id, record_id, pics FROM visit_logs WHERE id = ? LIMIT 1"
	err = database.GetOne(querySql, &visitLog, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "查询记录失败",
			"err":  err.Error(),
		})
		return
	}

	// 删除图片文件
	if visitLog.Pics != "" {
		// 图片存储路径
		uploadDir := "../static/uploads/normal_pics/visit_logs/"

		// 处理图片路径，删除图片文件
		pics := strings.Split(visitLog.Pics, ",")
		for _, pic := range pics {
			if pic == "" {
				continue
			}

			// 构建完整的图片路径
			picPath := filepath.Join(uploadDir, strings.TrimSpace(pic))
			// fmt.Println(picPath)

			// 删除图片文件
			err := os.Remove(picPath)
			if err != nil && !os.IsNotExist(err) {
				// 记录错误但继续执行，不中断流程
				fmt.Printf("删除图片文件失败: %s, 错误: %v\n", picPath, err)
			}
		}
	}

	// 从数据库中删除记录
	deleteSql := "DELETE FROM visit_logs WHERE id = ?"
	_, err = database.Query(deleteSql, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "删除记录失败",
			"err":  err.Error(),
		})
		return
	}

	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "删除成功",
	})
	common.Add_log(fmt.Sprintf("删除图文沟通记录，ID：%d", id), r)
}

// 获取音视频沟通记录列表
func Video_logs_list(w http.ResponseWriter, r *http.Request) {
	api_id := config.NormalPerm
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}

	record_id, err := common.CheckInt(r.FormValue("record_id"))
	if err != nil || record_id < 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "病历ID不能为空",
		})
		return
	}

	// 分页参数
	limit, err := strconv.Atoi(r.FormValue("limit"))
	if err != nil || limit < 1 {
		limit = 10 // 默认每页10条
	}
	page, err := strconv.Atoi(r.FormValue("page"))
	if err != nil || page < 1 {
		page = 1 // 默认第1页
	}
	offset := (page - 1) * limit

	// 查询总数
	var count int
	countSql := "SELECT COUNT(id) FROM rtc_room WHERE record_id = ?"
	err = database.GetOne(countSql, &count, record_id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "读取数据总数失败",
			"err":  err.Error(),
		})
		return
	}

	// 查询音视频沟通记录列表
	sql := "SELECT id, record_id, doc_id, asst_id, create_time, scheduled_time, finish_time FROM rtc_room WHERE record_id = ? ORDER BY create_time DESC LIMIT ? OFFSET ?"
	var rtc_rooms []RtcRoom
	err = database.GetAll(sql, &rtc_rooms, record_id, limit, offset)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败",
			"err":  err.Error(),
		})
		return
	}

	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":  200,
		"msg":   "ok",
		"data":  rtc_rooms,
		"count": count,
		"sql":   common.DebugSql(sql, record_id, limit, offset),
	})
}

// 删除音视频沟通记录
func Video_logs_del(w http.ResponseWriter, r *http.Request) {
	// 只有超级管理员有权限删除
	api_id := config.SystemPerm
	session, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}

	// 检查当前用户是否为超级管理员(ID=1)
	user_id := session.Values["id"].(int)
	if user_id != 1 {
		common.JSONResponse(w, http.StatusForbidden, map[string]interface{}{
			"code": 403,
			"msg":  "只有超级管理员可以删除音视频沟通记录",
		})
		return
	}

	// 获取要删除的记录ID
	id, err := common.CheckInt(r.FormValue("id"))
	if err != nil || id < 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "记录ID不能为空",
		})
		return
	}

	// 先查询记录
	var rtcRoom RtcRoom
	querySql := "SELECT id, record_id FROM rtc_room WHERE id = ? LIMIT 1"
	err = database.GetOne(querySql, &rtcRoom, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "查询记录失败",
			"err":  err.Error(),
		})
		return
	}

	// 从数据库中删除记录
	deleteSql := "DELETE FROM rtc_room WHERE id = ?"
	_, err = database.Query(deleteSql, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "删除记录失败",
			"err":  err.Error(),
		})
		return
	}

	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "删除成功",
	})
	common.Add_log(fmt.Sprintf("删除音视频沟通记录，ID：%d", id), r)
}

// 获取电话沟通记录列表
func Phone_logs_list(w http.ResponseWriter, r *http.Request) {
	api_id := config.NormalPerm
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}

	record_id, err := common.CheckInt(r.FormValue("record_id"))
	if err != nil || record_id < 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "病历ID不能为空",
		})
		return
	}

	// 分页参数
	limit, err := strconv.Atoi(r.FormValue("limit"))
	if err != nil || limit < 1 {
		limit = 10 // 默认每页10条
	}
	page, err := strconv.Atoi(r.FormValue("page"))
	if err != nil || page < 1 {
		page = 1 // 默认第1页
	}
	offset := (page - 1) * limit

	// 查询总数
	var count int
	countSql := "SELECT COUNT(id) FROM phone_logs WHERE record_id = ?"
	err = database.GetOne(countSql, &count, record_id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "读取数据总数失败",
			"err":  err.Error(),
		})
		return
	}

	// 查询电话沟通记录列表
	sql := "SELECT id, record_id, asst_id, files, contents, create_time FROM phone_logs WHERE record_id = ? ORDER BY create_time DESC LIMIT ? OFFSET ?"
	var phone_logs []PhoneLog
	err = database.GetAll(sql, &phone_logs, record_id, limit, offset)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败",
			"err":  err.Error(),
		})
		return
	}

	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":  200,
		"msg":   "ok",
		"data":  phone_logs,
		"count": count,
	})
}

// 添加电话沟通记录
func Phone_logs_add(w http.ResponseWriter, r *http.Request) {
	api_id := config.NormalPerm
	session, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}

	record_id, err := common.CheckInt(r.FormValue("record_id"))
	if err != nil || record_id < 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "病历ID不能为空",
		})
		return
	}

	contents := r.FormValue("contents")
	files := r.FormValue("files")

	// 添加电话沟通记录
	sql := "INSERT INTO phone_logs (record_id, asst_id, files, contents) VALUES (?, ?, ?, ?)"
	result, err := database.Query(sql, record_id, session.Values["id"], files, contents)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]any{
			"code": 500,
			"msg":  "添加记录失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, record_id, session.Values["id"], files, contents),
		})
		return
	}

	new_id, _ := result.LastInsertId()
	common.JSONResponse(w, http.StatusOK, map[string]any{
		"code":   200,
		"msg":    "添加记录成功",
		"new_id": new_id,
	})
	common.Add_log(fmt.Sprintf("添加电话沟通记录，ID：%d，病历ID：%d，内容：%s", new_id, record_id, contents), r)
}

// 删除电话沟通记录
func Phone_logs_del(w http.ResponseWriter, r *http.Request) {
	// 只有超级管理员有权限删除
	api_id := config.SystemPerm
	session, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}

	// 检查当前用户是否为超级管理员(ID=1)
	user_id := session.Values["id"].(int)
	if user_id != 1 {
		common.JSONResponse(w, http.StatusForbidden, map[string]interface{}{
			"code": 403,
			"msg":  "只有超级管理员可以删除电话沟通记录",
		})
		return
	}

	// 获取要删除的记录ID
	id, err := common.CheckInt(r.FormValue("id"))
	if err != nil || id < 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "记录ID不能为空",
		})
		return
	}

	// 先查询记录，获取录音文件信息
	var phoneLog PhoneLog
	querySql := "SELECT id, record_id, files FROM phone_logs WHERE id = ? LIMIT 1"
	err = database.GetOne(querySql, &phoneLog, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "查询记录失败",
			"err":  err.Error(),
		})
		return
	}

	// 删除录音文件
	if phoneLog.Files != "" {
		// 录音文件存储路径
		uploadDir := "../static/uploads/normal_files/phone_recording/"

		// 处理录音文件路径，删除录音文件
		files := strings.Split(phoneLog.Files, ",")
		for _, file := range files {
			if file == "" {
				continue
			}

			// 构建完整的文件路径
			filePath := filepath.Join(uploadDir, strings.TrimSpace(file))

			// 删除文件
			err := os.Remove(filePath)
			if err != nil && !os.IsNotExist(err) {
				// 记录错误但继续执行，不中断流程
				fmt.Printf("删除录音文件失败: %s, 错误: %v\n", filePath, err)
			}
		}
	}

	// 从数据库中删除记录
	deleteSql := "DELETE FROM phone_logs WHERE id = ?"
	_, err = database.Query(deleteSql, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "删除记录失败",
			"err":  err.Error(),
		})
		return
	}

	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "删除成功",
	})
	common.Add_log(fmt.Sprintf("删除电话沟通记录，ID：%d", id), r)
}
