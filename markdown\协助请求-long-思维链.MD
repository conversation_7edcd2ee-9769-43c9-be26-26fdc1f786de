我需要你帮助我修改 E:\works\go\internal\app\admin\admin.go 文件中的 Article_category_list 函数。具体要求如下：
添加分页功能：
请确保实现分页功能，包括处理 page、offset 和 count 等关键环节。
你需要从请求中获取 limit 和 page 参数，并根据这些参数计算 offset。
添加关键词搜索功能：
请添加一个名为 key 的变量，用于接收搜索关键词。
在执行数据库查询时，使用 key 进行模糊匹配，搜索字段包括 title 和 description。
模仿函数：
请参考 Warehouse_drug_log_list 函数的实现细节，确保分页和搜索功能的实现方式与该函数一致。
返回结果：
确保在成功查询后，返回包含分页信息和搜索结果的 JSON 响应。
请根据以上要求修改 Article_category_list 函数，并确保代码的可读性和一致性。