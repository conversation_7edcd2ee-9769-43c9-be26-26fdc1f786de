package common

import (
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"mstproject/pkg/config"
	"mstproject/pkg/database"
	"net/http"
	"net/url"
	"regexp"
	"strconv"
	"strings"
	"time"
)

// 查询物流信息
func Get_KD100_Cookies(com, nu string) ([]*http.Cookie, error) {
	// Create request URL with variables
	requestURL := fmt.Sprintf("https://m.kuaidi100.com/result.jsp?com=%s&nu=%s", com, nu)
	fmt.Println(requestURL)

	// Create request
	req, err := http.NewRequest("GET", requestURL, nil)
	if err != nil {
		return nil, fmt.Errorf("error creating request: %w", err)
	}

	// Set headers
	req.Header.Add("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7")
	req.Header.Add("Accept-Language", "zh-CN,zh;q=0.9")
	req.Header.Add("Connection", "keep-alive")
	req.Header.Add("Sec-Fetch-Dest", "document")
	req.Header.Add("Sec-Fetch-Mode", "navigate")
	req.Header.Add("Sec-Fetch-Site", "none")
	req.Header.Add("Sec-Fetch-User", "?1")
	req.Header.Add("Upgrade-Insecure-Requests", "1")
	req.Header.Add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
	req.Header.Add("sec-ch-ua", `"Chromium";v="134", "Not:A-Brand";v="24", "Google Chrome";v="134"`)
	req.Header.Add("sec-ch-ua-mobile", "?0")
	req.Header.Add("sec-ch-ua-platform", `"Windows"`)

	// Create client with cookie jar
	client := &http.Client{}

	// Make the request
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("error making request: %w", err)
	}
	defer resp.Body.Close()

	// Return cookies
	return resp.Cookies(), nil
}

// QueryExpressInfo 查询快递信息并返回结果，可被其他包调用
func QueryExpressInfo(express_type_int int, nu string, ord_id int, pat_pro_id int, is_sys_auto bool, r *http.Request) (map[string]any, error) {
	// 获取快递公司代码
	com := config.KD100_Express_Info[express_type_int][0]
	phone := strconv.Itoa(rand.Intn(9000) + 1000)
	// 获取快递100的Cookies
	cookies, err := Get_KD100_Cookies(com, nu)
	if err != nil {
		return nil, fmt.Errorf("获取快递100 Cookies失败: %v", err)
	}

	// 创建请求URL和表单数据
	requestURL := "https://m.kuaidi100.com/query"
	formData := url.Values{}
	formData.Set("postid", nu)
	formData.Set("id", "1")
	formData.Set("valicode", "")
	formData.Set("temp", "0."+strconv.FormatInt(time.Now().UnixNano()%1000000000, 10)) // 随机化temp
	formData.Set("type", com)
	formData.Set("phone", phone)
	formData.Set("token", "")
	formData.Set("platform", "MWWW")

	// 创建请求
	req, err := http.NewRequest("POST", requestURL, strings.NewReader(formData.Encode()))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置请求头
	req.Header.Add("Accept", "application/json, text/javascript, */*; q=0.01")
	req.Header.Add("Accept-Language", "zh-CN,zh;q=0.9")
	req.Header.Add("Connection", "keep-alive")
	req.Header.Add("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8")
	req.Header.Add("Origin", "https://m.kuaidi100.com")
	req.Header.Add("Referer", fmt.Sprintf("https://m.kuaidi100.com/result.jsp?com=%s&nu=%s", com, nu))
	req.Header.Add("Sec-Fetch-Dest", "empty")
	req.Header.Add("Sec-Fetch-Mode", "cors")
	req.Header.Add("Sec-Fetch-Site", "same-origin")
	req.Header.Add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
	req.Header.Add("X-Requested-With", "XMLHttpRequest")
	req.Header.Add("sec-ch-ua", `"Chromium";v="134", "Not:A-Brand";v="24", "Google Chrome";v="134"`)
	req.Header.Add("sec-ch-ua-mobile", "?0")
	req.Header.Add("sec-ch-ua-platform", `"Windows"`)

	// 添加Cookies
	for _, cookie := range cookies {
		req.AddCookie(cookie)
	}

	// 创建客户端
	client := &http.Client{}

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	// 解析响应为JSON
	var parsedData map[string]interface{}
	if err := json.Unmarshal(body, &parsedData); err != nil {
		return nil, fmt.Errorf("解析响应为JSON失败: %v", err)
	}

	// 处理物流数据并更新数据库
	dataSlice, ok := parsedData["data"].([]interface{})
	if ok && len(dataSlice) > 0 {
		firstData := dataSlice[0].(map[string]interface{})
		context, ok1 := firstData["context"].(string)
		timeValue, ok2 := firstData["time"].(string)
		state, ok3 := parsedData["state"].(string)
		condition, ok4 := parsedData["condition"].(string)

		if ok1 && ok2 && ok3 && ok4 {
			parsedData["context"] = context
			parsedData["time"] = timeValue
			parsedData["state"] = state
			parsedData["condition"] = condition

			// 先查询是否存在该订单的快递信息记录
			var count int
			checkSql := "SELECT COUNT(id) FROM express_information WHERE ord_id = ?"
			err := database.GetOne(checkSql, &count, ord_id)
			if err != nil {
				return nil, fmt.Errorf("查询订单快递信息失败: %v", err)
			}

			if count > 0 {
				// 如果存在记录，则更新
				updateSql := "UPDATE express_information SET pat_pro_id = ?, express = ?, tracking_num = ?, status = ?, last_contents = ?, express_last_time = ?, last_update_time = NOW() WHERE ord_id = ?"
				_, execErr := database.Query(updateSql, pat_pro_id, express_type_int, nu, state, context, timeValue, ord_id)
				if execErr != nil {
					return nil, fmt.Errorf("更新快递信息失败: %v", execErr)
				}
				fmt.Println("快递信息跟踪状态(更新)：已更新")
			} else {
				// 如果不存在记录，则插入
				insertSql := "INSERT INTO express_information(ord_id, pat_pro_id, express, tracking_num, status, last_contents, express_last_time, last_update_time) VALUES(?, ?, ?, ?, ?, ?, ?, NOW())"
				_, execErr := database.Query(insertSql, ord_id, pat_pro_id, express_type_int, nu, state, context, timeValue)
				if execErr != nil {
					return nil, fmt.Errorf("插入快递信息失败: %v", execErr)
				}
				fmt.Println("快递信息跟踪状态(插入)：已插入")
			}
			// fmt.Println(context)
			// fmt.Println(regexp.MustCompile(`^\[.+\]您的快件已派送成功`).MatchString(context))
			// 快递信息已到达，更新订单状态
			if state == "304" || condition == "F00" || regexp.MustCompile(`^\[.+\]您的快件已派送成功`).MatchString(context) {
				// 快递信息已到达，订单状态更新为已签收
				sql := "UPDATE orders SET status = 3, last_update_time = NOW(),pay_review_status=2, finish_time = NOW() WHERE id = ? and status = 2"
				result, err := database.Query(sql, ord_id)
				if err == nil {
					rowsAffected, _ := result.RowsAffected()
					if rowsAffected > 0 {
						if is_sys_auto {
							Add_log_sys_auto(fmt.Sprintf("自动更新订单，订单 D%d 的物流信息，已签收；尾款待审", ord_id), r)
						} else {
							Add_log(fmt.Sprintf("手动更新，订单 D%d 的物流信息，已签收；尾款待审", ord_id), r)
						}
					} else {
						Add_log(fmt.Sprintf("试图通过快递状态更新订单D%d状态，未被允许，触发场景：用户更新了非待收状的快递信息%s", ord_id, nu), r)
					}
				}
				// 有时候接口返回的状态码错误，但文字描述实际上是签收了，所以该环节强制返回正常签收状态码
				parsedData["state"] = "304"
				parsedData["condition"] = "F00"
			} else {
				// 更新订单的last_update_time字段
				sql := "UPDATE orders SET last_update_time = NOW() WHERE id = ?"
				_, _ = database.Query(sql, ord_id)
			}
		}
	}

	return parsedData, nil
}
