<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 病历详情</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/layui/layarea_lc.js"></script>
    <script src="/dist/js/main.js"></script>
    <style>
        /* 全局样式 */
        body {
            background-color: #f5f7fa;
        }

        /* 隐藏账户相关元素 */
        .has_account {
            display: none;
        }

        /* 输入框和下拉菜单样式 */
        .layui-input-block input,
        .layui-form-select {
            max-width: 100% !important;
        }

        /* 用户选择框样式 */
        .chooseUser {
            box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
        }

        /* 玻璃态卡片样式 */
        .layui-panel {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        /* 卡片标题样式 */
        .layui-card-header {
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            padding: 15px 20px;
        }

        .layui-card-header .layui-row {
            padding-top: 5px;
        }

        .layui-card-header .layui-col-md8 {
            font-size: 18px;
            font-weight: 500;
            color: #333;
            position: relative;
            padding-left: 12px;
        }

        .layui-card-header .layui-col-md8:before {
            content: '';
            position: absolute;
            left: 0;
            top: 5px;
            bottom: 5px;
            width: 4px;
            background: linear-gradient(to bottom, #1E9FFF, #5FB878);
            border-radius: 2px;
        }

        /* 详情文本样式 */
        .detail-text {
            background: rgba(249, 249, 249, 0.8);
            padding: 10px 15px;
            border-radius: 6px;
            min-height: 24px;
            white-space: pre-wrap;
            border: 1px solid rgba(0, 0, 0, 0.05);
            transition: all 0.3s;
            line-height: 1.6;
            color: #333;
            max-height: 80px;
            overflow-y: auto;
        }

        .detail-text:hover {
            background: rgba(249, 249, 249, 1);
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.03);
        }

        /* 表单项样式 */
        .layui-form-item {
            margin-bottom: 20px;
            transition: all 0.3s;
        }

        .layui-form-item:hover {
            transform: translateX(5px);
        }

        .layui-form-label {
            color: #666;
            font-weight: 500;
        }

        /* 图片网格样式 */
        .image-grid {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-top: 5px;
        }

        .image-grid img {
            width: 200px;
            height: 200px;
            object-fit: cover;
            cursor: pointer;
            border-radius: 8px;
            border: none;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s;
        }

        .image-grid img:hover {
            transform: scale(1.03);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
        }

        /* 按钮样式 */
        .layui-btn {
            border-radius: 6px;
            transition: all 0.3s;
        }

        .layui-btn-primary {
            background: linear-gradient(to right, #f5f5f5, #ffffff);
            border-color: #e6e6e6;
        }

        .layui-btn-primary:hover {
            background: linear-gradient(to right, #ffffff, #f5f5f5);
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
            border-color: #d9d9d9;
        }

        /* 错误提示样式 */
        .error-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(5px);
            z-index: 9999;
            display: flex;
            justify-content: center;
            align-items: center;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .error-overlay.show {
            opacity: 1;
        }

        .error-card {
            width: 400px;
            text-align: center;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.95);
            transform: translateY(20px);
            transition: all 0.3s ease;
        }

        .error-overlay.show .error-card {
            transform: translateY(0);
        }

        /* 小标签样式 */
        .smalltext {
            font-size: 13px;
        }

        /* 图文沟通样式 */
        .visit-log-item {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            transition: all 0.3s;
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .visit-log-item:hover {
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .visit-log-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .visit-log-user {
            font-weight: 500;
            color: #1E9FFF;
        }

        .visit-log-time {
            color: #999;
            font-size: 13px;
        }

        .visit-log-content {
            margin-bottom: 15px;
            white-space: pre-wrap;
            line-height: 1.6;
        }

        .visit-log-images {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .visit-log-images img {
            width: 120px;
            height: 120px;
            object-fit: cover;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s;
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
        }

        .visit-log-images img:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
        }

        .image-preview-container {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-top: 10px;
        }

        .image-preview-item {
            position: relative;
            width: 120px;
            height: 120px;
            border-radius: 6px;
            overflow: hidden;
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
        }

        .image-preview-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            cursor: pointer;
            transition: all 0.3s;
        }

        .image-preview-item .delete-btn {
            position: absolute;
            top: 5px;
            right: 5px;
            width: 24px;
            height: 24px;
            background: rgba(0, 0, 0, 0.5);
            color: #fff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            opacity: 0;
            transition: opacity 0.3s;
        }

        .image-preview-item:hover .delete-btn {
            opacity: 1;
        }

        .image-preview-item:hover img {
            transform: scale(1.05);
        }

        .empty-message {
            text-align: center;
            padding: 30px;
            color: #999;
            font-style: italic;
        }

        /* 录音文件预览样式 */
        .audio-preview-container {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-top: 10px;
        }

        .audio-preview-item {
            position: relative;
            width: 300px;
            padding: 15px;
            border-radius: 8px;
            background: rgba(249, 249, 249, 0.8);
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
            transition: all 0.3s;
        }

        .audio-preview-item:hover {
            background: rgba(249, 249, 249, 1);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
            transform: translateY(-2px);
        }

        .audio-preview-item audio {
            width: 100%;
            outline: none;
        }

        .audio-preview-item .delete-btn {
            position: absolute;
            top: 5px;
            right: 5px;
            width: 24px;
            height: 24px;
            background: rgba(0, 0, 0, 0.5);
            color: #fff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            opacity: 0;
            transition: opacity 0.3s;
        }

        .audio-preview-item:hover .delete-btn {
            opacity: 1;
        }

        /* 飘浮导航按钮组样式 */
        .floating-nav {
            position: fixed;
            bottom: 30px;
            right: 30px;
            z-index: 999;
            display: flex;
            flex-direction: column;
            align-items: flex-end;
        }

        .floating-nav-toggle {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, rgba(30, 159, 255, 0.9), rgba(95, 184, 120, 0.9));
            backdrop-filter: blur(10px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2), inset 0 1px 2px rgba(255, 255, 255, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: #fff;
            font-size: 20px;
            transition: all 0.3s ease;
            margin-top: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .floating-nav-toggle:hover {
            transform: scale(1.1);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3), inset 0 1px 3px rgba(255, 255, 255, 0.4);
            background: linear-gradient(135deg, rgba(30, 159, 255, 1), rgba(95, 184, 120, 1));
        }

        .floating-nav-buttons {
            display: flex;
            flex-direction: column;
            margin-bottom: 10px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(20px);
            transition: all 0.3s ease;
        }

        .floating-nav.active .floating-nav-buttons {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .floating-nav.active .floating-nav-toggle i {
            transform: rotate(180deg);
        }

        .floating-nav-btn {
            display: flex;
            align-items: center;
            padding: 10px 15px;
            margin-bottom: 10px;
            border-radius: 30px;
            background: rgba(255, 255, 255, 0.85);
            backdrop-filter: blur(10px);
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), inset 0 1px 1px rgba(255, 255, 255, 0.7);
            color: #666;
            text-decoration: none;
            transition: all 0.2s ease;
            border: 1px solid rgba(255, 255, 255, 0.3);
            width: 140px;
            font-weight: 500;
        }

        .floating-nav-btn i {
            margin-right: 10px;
            font-size: 18px;
            transition: all 0.2s ease;
            color: #1E9FFF;
        }

        .floating-nav-btn span {
            font-size: 14px;
            white-space: nowrap;
        }

        .floating-nav-btn:hover {
            background: rgba(255, 255, 255, 0.95);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15), inset 0 1px 2px rgba(255, 255, 255, 0.8);
            transform: translateX(-5px);
            color: #1E9FFF;
        }

        .floating-nav-btn.active {
            background: linear-gradient(135deg, rgba(30, 159, 255, 0.9), rgba(95, 184, 120, 0.9));
            color: #fff;
            box-shadow: 0 5px 15px rgba(30, 159, 255, 0.3), inset 0 1px 2px rgba(255, 255, 255, 0.3);
            transform: translateX(-5px);
        }

        .floating-nav-btn.active i {
            transform: scale(1.2);
            color: #fff;
        }

        /* 基本设置 */
        html,
        body {
            overflow-x: hidden;
            /* 防止水平滚动条 */
        }
    </style>
</head>

<body>
    <div class="layui-layout layui-layout-admin">
        <!-- 头部 -->
        <div class="layui-header">
            <div class="layui-logo">幸年堂</div>
            <ul class="layui-nav layui-layout-right">
                <li class="layui-nav-item" style="background-color:transparent!important;padding-right:10px">
                    <a href="javascript:;" style="display:flex;align-items:center;color:#fff"><img
                            src="/dist/images/user.png" class="layui-nav-img"><span class="user_name"></span></a>
                    <dl class="layui-nav-child pop_menu">
                        <dd><i class="iconfont">&#xe62f;</i><a href="change_password.html#7">用户资料</a></dd>
                        <dd><i class="iconfont">&#xe601;</i><a href="javascript:void(0)" class="refresh_cache">刷新缓存</a>
                        </dd>
                        <dd><i class="iconfont">&#xe634;</i><a href="login_out.html">退出登录</a></dd>
                    </dl>
                </li>
            </ul>
        </div>
        <!-- 导航 -->
        <ul class="layui-nav layui-nav-tree layui-nav-side" id="menuContainer" lay-bar="disabled" lay-accordion>
            <div style="margin: 50px 0;color: #bbb;text-align: center;">菜单加载中...</div>
        </ul>
        <!-- 主体内容 -->
        <div class="layui-body">
            <div class="body_child">


                <div class="layui-panel" id="section-details">
                    <div class="layui-card-header">
                        <div class="layui-row">
                            <div class="layui-col-md8">用户基础信息</div>
                        </div>
                    </div>
                    <div id="user_info_sample" class="layui-padding-4" style="min-height: 200px; padding: 25px;">
                        <!-- 用户基础信息将在这里动态加载 -->
                    </div>
                </div>


                <div class="layui-panel" id="section-details">
                    <div class="layui-card-header">
                        <div class="layui-row">
                            <div class="layui-col-md8">病历详情</div>
                        </div>
                    </div>
                    <div class="layui-padding-4" style="min-height: 600px; padding: 25px;">


                        <!-- INPUT项 -->
                        <div class="layui-row">


                            <!-- 第一列：患者现状 -->
                            <div class="layui-col-md4">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">科室ID</label>
                                    <div class="layui-input-block">
                                        <div class="detail-text" id="department_id"></div>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">患者主诉</label>
                                    <div class="layui-input-block">
                                        <div class="detail-text" id="chief_complaint"></div>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">复诊主诉</label>
                                    <div class="layui-input-block">
                                        <div class="detail-text" id="re_chief_complaint"></div>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">现病史</label>
                                    <div class="layui-input-block">
                                        <div class="detail-text" id="history_of_present_illness"></div>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">现需治疗</label>
                                    <div class="layui-input-block">
                                        <div class="detail-text" id="now_needs"></div>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">大小便情况</label>
                                    <div class="layui-input-block">
                                        <div class="detail-text" id="urination"></div>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">有无三高</label>
                                    <div class="layui-input-block">
                                        <div class="detail-text" id="triad"></div>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">舌象描述</label>
                                    <div class="layui-input-block">
                                        <div class="detail-text" id="tonguedesc"></div>
                                    </div>
                                </div>
                            </div>


                            <!-- 第二列：病史记录 -->
                            <div class="layui-col-md4">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">既往病史</label>
                                    <div class="layui-input-block">
                                        <div class="detail-text" id="past_medical_history"></div>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">个人史</label>
                                    <div class="layui-input-block">
                                        <div class="detail-text" id="personal_history"></div>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">家族史</label>
                                    <div class="layui-input-block">
                                        <div class="detail-text" id="family_history"></div>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">过敏史</label>
                                    <div class="layui-input-block">
                                        <div class="detail-text" id="allergy_history"></div>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">用药史</label>
                                    <div class="layui-input-block">
                                        <div class="detail-text" id="past_medication_history"></div>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label smalltext">上次用药时间</label>
                                    <div class="layui-input-block">
                                        <div class="detail-text" id="last_medication_time"></div>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label smalltext">上次用药情况</label>
                                    <div class="layui-input-block">
                                        <div class="detail-text" id="last_medical"></div>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">医助</label>
                                    <div class="layui-input-block">
                                        <div class="detail-text" id="asst_name">-</div>
                                    </div>
                                </div>

                            </div>

                            <!-- 第三列：诊疗信息 -->
                            <div class="layui-col-md4">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">诊断信息</label>
                                    <div class="layui-input-block">
                                        <div class="detail-text" id="diagnosis_information"></div>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">治疗方案</label>
                                    <div class="layui-input-block">
                                        <div class="detail-text" id="treatment_plan"></div>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">患者状态</label>
                                    <div class="layui-input-block">
                                        <div class="detail-text" id="status"></div>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">核销时间</label>
                                    <div class="layui-input-block">
                                        <div class="detail-text" id="discharge_time"></div>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">创建时间</label>
                                    <div class="layui-input-block">
                                        <div class="detail-text" id="create_time"></div>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">对应处方</label>
                                    <div class="layui-input-block">
                                        <div class="detail-text" id="pre_id"></div>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">意向剂型</label>
                                    <div class="layui-input-block">
                                        <div class="detail-text"><span id="tx_day"></span>天，<span id="tx_type"></span>
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">医生</label>
                                    <div class="layui-input-block">
                                        <div class="detail-text" id="doc_name">-</div>
                                    </div>
                                </div>
                            </div>
                        </div>



















                        <!-- 舌苔照显示部分 -->
                        <div class="layui-form-item">
                            <label class="layui-form-label">舌苔照</label>
                            <div class="layui-input-block">
                                <div class="image-grid" id="photo_tongue_container"></div>
                            </div>
                        </div>

                        <!-- 检查单显示部分 -->
                        <div class="layui-form-item">
                            <label class="layui-form-label">检查单</label>
                            <div class="layui-input-block">
                                <div class="image-grid" id="photo_sheet_container"></div>
                            </div>
                        </div>

                        <!-- <div style="display: flex; justify-content: center;margin-top: 50px;">
                            <button type="button" class="layui-btn layui-btn-primary" onclick="history.go(-1)">
                                <i class="layui-icon layui-icon-left" style="margin-right: 5px;"></i>返回
                            </button>
                        </div> -->
                    </div>
                </div>
            </div>

            <!-- 图文沟通记录开始 -->
            <div class="body_child">
                <div class="layui-panel" id="section-image-text">
                    <div class="layui-card-header">
                        <div class="layui-row">
                            <div class="layui-col-md8">图文沟通</div>
                            <div class="layui-col-md4" style="text-align: right;">
                                <button type="button" class="layui-btn layui-btn-normal" id="add-visit-log-btn">
                                    <i class="layui-icon layui-icon-add-1"></i> 新增沟通记录
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="layui-padding-4" style="min-height: 300px; padding: 25px;">
                        <div class="layui-row">
                            <div id="visit-logs-container">
                                <!-- 分页区域 -->
                                <div id="visit-logs-table"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 图文沟通记录结束 -->

            <!-- 音视频沟通记录开始 -->
            <div class="body_child">
                <div class="layui-panel" id="section-video">
                    <div class="layui-card-header">
                        <div class="layui-row">
                            <div class="layui-col-md8">音视频沟通</div>
                        </div>
                    </div>
                    <div class="layui-padding-4" style="min-height: 300px; padding: 25px;">
                        <div class="layui-row">
                            <div id="video-logs-container">
                                <!-- 分页区域 -->
                                <div id="video-logs-table"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 音视频沟通记录结束 -->

            <!-- 电话沟通记录开始 -->
            <div class="body_child">
                <div class="layui-panel" id="section-phone">
                    <div class="layui-card-header">
                        <div class="layui-row">
                            <div class="layui-col-md8">电话沟通</div>
                            <div class="layui-col-md4" style="text-align: right;">
                                <button type="button" class="layui-btn layui-btn-normal" id="add-phone-log-btn">
                                    <i class="layui-icon layui-icon-add-1"></i> 新增电话沟通
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="layui-padding-4" style="min-height: 300px; padding: 25px;">
                        <div class="layui-row">
                            <div id="phone-logs-container">
                                <!-- 分页区域 -->
                                <div id="phone-logs-table"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 电话沟通记录结束 -->

        </div>
    </div>

    <!-- 图文沟通模态框 -->
    <script type="text/html" id="visit-log-modal">
        <div class="layui-padding-3">
            <form class="layui-form" id="visit-log-form" lay-filter="visit-log-form">
                <input type="hidden" name="record_id" value="{{d.record_id}}">

                <div class="layui-form-item layui-form-text">
                    <label class="layui-form-label">文字说明</label>
                    <div class="layui-input-block">
                        <textarea name="contents" placeholder="请输入文字说明" class="layui-textarea" style="min-height: 120px;"></textarea>
                    </div>
                </div>

                <!-- 图片上传 -->
                <div class="layui-form-item">
                    <label class="layui-form-label">上传图片</label>
                    <div class="layui-input-block">
                        <input type="hidden" name="pics" value="">
                        <div class="layui-upload upload-container" id="upload-container">
                            <button type="button" class="layui-btn upload_big_btn" id="upload-images-btn">
                                <div class="btn_big_font">
                                    <i class="layui-icon layui-icon-upload btn_big_font"></i> 上传图片
                                </div>
                                <div>jpg、png、jpeg、bmp、gif格式，1M以内</div>
                                <div>支持点击、拖拽和 Ctrl+V 上传</div>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 图片预览区域 -->
                <div class="layui-form-item">
                    <label class="layui-form-label">图片预览</label>
                    <div class="layui-input-block">
                        <div class="image-preview-container" id="image-preview-container"></div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <button class="layui-btn" lay-submit lay-filter="visit-log-submit">提交</button>
                        <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                    </div>
                </div>
            </form>
        </div>
    </script>

    <!-- 电话沟通模态框 -->
    <script type="text/html" id="phone-log-modal">
        <div class="layui-padding-3">
            <form class="layui-form" id="phone-log-form" lay-filter="phone-log-form">
                <input type="hidden" name="record_id" value="{{d.record_id}}">

                <div class="layui-form-item layui-form-text">
                    <label class="layui-form-label">文字说明</label>
                    <div class="layui-input-block">
                        <textarea name="contents" placeholder="请输入文字说明" class="layui-textarea" style="min-height: 120px;"></textarea>
                    </div>
                </div>

                <!-- 录音文件上传 -->
                <div class="layui-form-item">
                    <label class="layui-form-label">上传录音</label>
                    <div class="layui-input-block">
                        <input type="hidden" name="files" value="">
                        <div class="layui-upload upload-container" id="upload-audio-container">
                            <button type="button" class="layui-btn upload_big_btn" id="upload-audio-btn">
                                <div class="btn_big_font">
                                    <i class="layui-icon layui-icon-upload btn_big_font"></i> 上传录音
                                </div>
                                <div>mp3格式，5M以内</div>
                                <div>支持点击上传</div>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 录音文件预览区域 -->
                <div class="layui-form-item">
                    <label class="layui-form-label">录音预览</label>
                    <div class="layui-input-block">
                        <div class="audio-preview-container" id="audio-preview-container"></div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <button class="layui-btn" lay-submit lay-filter="phone-log-submit">提交</button>
                        <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                    </div>
                </div>
            </form>
        </div>
    </script>

    <script>
        layui.config({
            base: './mods/'
            , version: '1.0'
        });
        layui.use(['element', 'layer', 'util', 'upload', 'laytpl', 'table'], function () {
            var element = layui.element;
            var layer = layui.layer;
            var form = layui.form;
            var util = layui.util;
            var treeTable = layui.treeTable;
            var laydate = layui.laydate;
            var dropdown = layui.dropdown;
            var $ = layui.$;
            var upload = layui.upload;
            var laytpl = layui.laytpl;
            var table = layui.table;
            var id = request.get('id');
            if (!id) {
                layer.msg('参数错误', { icon: 2, time: 1000 });
                return false;
            }
            layarea_lc = layui.layarea_lc;
            laydate.render({
                elem: '#ID-laydate-demo,#last_medication_time'
            });
            var has_account = false;
            // 页面顶部插入缓存的用户数据
            let local_userinfo = localStorage.getItem('local_userinfo');
            let user_name = "";
            if (local_userinfo) {
                local_userinfo = JSON.parse(local_userinfo);
                user_name = local_userinfo.Name + " ( " + local_userinfo.Roles + " )";
            } else {
                user_name = "未登录";
            }
            $('.user_name').text(user_name);
            render_menu($);
            $('.refresh_cache').click(function () {
                refresh_cache($);
            });

            // 渲染用户基础信息
            function renderUserInfo(userData) {
                // 构建用户基础信息的HTML
                let userInfoHtml = `
                    <!-- 第一行 -->
                    <div class="layui-row">
                        <div class="layui-col-md4">
                            <div class="layui-form-item">
                                <label class="layui-form-label">姓名</label>
                                <div class="layui-input-block">
                                    <div class="detail-text">${userData.Name || '-'}</div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md4">
                            <div class="layui-form-item">
                                <label class="layui-form-label">性别</label>
                                <div class="layui-input-block">
                                    <div class="detail-text">${userData.Sex === '1' ? '男' : (userData.Sex === '0' ? '女' : '-')}</div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md4">
                            <div class="layui-form-item">
                                <label class="layui-form-label">出生日期</label>
                                <div class="layui-input-block">
                                    <div class="detail-text">${userData.Born_date ? userData.Born_date.split('T')[0] : '-'}</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 第二行 -->
                    <div class="layui-row">
                        <div class="layui-col-md4">
                            <div class="layui-form-item">
                                <label class="layui-form-label">手机号</label>
                                <div class="layui-input-block">
                                    <div class="detail-text">${userData.Phone || '-'}</div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md4">
                            <div class="layui-form-item">
                                <label class="layui-form-label">身份证号</label>
                                <div class="layui-input-block">
                                    <div class="detail-text">${userData.Idcard || '-'}</div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md4">
                            <div class="layui-form-item">
                                <label class="layui-form-label">微信号</label>
                                <div class="layui-input-block">
                                    <div class="detail-text">${userData.Weixin || '-'}</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 第三行 -->
                    <div class="layui-row">
                        <div class="layui-col-md4">
                            <div class="layui-form-item">
                                <label class="layui-form-label">医保卡号</label>
                                <div class="layui-input-block">
                                    <div class="detail-text">${userData.Ins_card_num || '-'}</div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md4">
                            <div class="layui-form-item">
                                <label class="layui-form-label">参保类型</label>
                                <div class="layui-input-block">
                                    <div class="detail-text">${userData.Ins_type ? '类型' + userData.Ins_type : '请选择'}</div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md4">
                            <div class="layui-form-item">
                                <label class="layui-form-label">婚否</label>
                                <div class="layui-input-block">
                                    <div class="detail-text">${userData.Ismarried === 1 ? '已婚' : (userData.Ismarried === 0 ? '未婚' : '-')}</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 第四行 -->
                    <div class="layui-row">
                        <div class="layui-col-md4">
                            <div class="layui-form-item">
                                <label class="layui-form-label">身高</label>
                                <div class="layui-input-block">
                                    <div class="detail-text">${userData.Height || '0'}</div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md4">
                            <div class="layui-form-item">
                                <label class="layui-form-label">体重</label>
                                <div class="layui-input-block">
                                    <div class="detail-text">${userData.Weight || '0'}</div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md4">
                            <div class="layui-form-item">
                                <label class="layui-form-label">地址</label>
                                <div class="layui-input-block">
                                    <div class="detail-text">${removeAllPipe(userData.Address) || '-'}</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 第五行 -->
                    <div class="layui-row">
                        <div class="layui-col-md4">
                            <div class="layui-form-item">
                                <label class="layui-form-label">过敏史</label>
                                <div class="layui-input-block">
                                    <div class="detail-text">${userData.Allergies || '无'}</div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md4">
                            <div class="layui-form-item">
                                <label class="layui-form-label">既往史</label>
                                <div class="layui-input-block">
                                    <div class="detail-text">${userData.Medical_history || '无'}</div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md4">
                            <div class="layui-form-item">
                                <label class="layui-form-label">主诉</label>
                                <div class="layui-input-block">
                                    <div class="detail-text">${userData.Chief_complaint || '-'}</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 第六行 -->
                    <div class="layui-row">
                        <div class="layui-col-md4">
                            <div class="layui-form-item">
                                <label class="layui-form-label">客户来源</label>
                                <div class="layui-input-block">
                                    <div class="detail-text">${userData.Patient_from || '-'}</div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md4">
                            <div class="layui-form-item">
                                <label class="layui-form-label">用户级别</label>
                                <div class="layui-input-block">
                                    <div class="detail-text">${userData.Level || '-'}</div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md4">
                            <div class="layui-form-item">
                                <label class="layui-form-label">用户状态</label>
                                <div class="layui-input-block">
                                    <div class="detail-text">${userData.Status === 1 ? '正常' : (userData.Status === 0 ? '异常' : '-')}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                // 将HTML插入到页面中
                $('#user_info_sample').html(userInfoHtml);
            }
            // 获取病历详情（接口/admin/patient_records/detail），并回填表单
            layer.load(2);
            $.ajax({
                url: serverUrl + "/admin/patient_records/detail",
                type: "post",
                data: {
                    id: id,
                },
                success: function (res) {
                    layer.closeAll('loading');
                    if (res.code == 200) {
                        let data = res.data;
                        // 展示文本数据
                        $('#chief_complaint').text(data.Chief_complaint);
                        $('#re_chief_complaint').text(data.Re_chief_complaint);
                        $('#history_of_present_illness').text(data.History_of_present_illness);
                        $('#past_medical_history').text(data.Past_medical_history);
                        $('#past_medication_history').text(data.Past_medication_history);
                        $('#personal_history').text(data.Personal_history);
                        $('#family_history').text(data.Family_history);
                        $('#allergy_history').text(data.Allergy_history);
                        $('#diagnosis_information').text(data.Diagnosis_information);
                        $('#treatment_plan').text(data.Treatment_plan);
                        $('#tonguedesc').text(data.Tonguedesc);
                        $('#urination').text(data.Urination);
                        $('#last_medication_time').text(data.Last_medication_time.replace('T', ' ').replace('Z', ''));
                        $('#last_medical').text(data.Last_medical);
                        $('#now_needs').text(data.Now_needs);

                        // 处理三高显示
                        $('#triad').text(data.Triad);

                        // 处理意向剂型显示
                        $('#tx_day').text(data.Tx_day);
                        $('#tx_type').text(data.Tx_type);
                        // 右侧信息显示
                        $('#discharge_time').text(data.Discharge_time == '0001-01-01T00:00:00Z' ? "未核销" : "已核销");
                        $('#status').text(Record_Status[data.Status]);
                        $('#create_time').text(data.Create_time.replace('T', ' ').replace('Z', ''));
                        $('#pre_id').text(data.Pre_id == 0 ? "未开处方" : data.Pre_id);
                        // 根据科室ID求科室名
                        $.ajax({
                            url: serverUrl + "/normal/department_cache_get",
                            type: "post",
                            data: {
                                id: data.Department_id,
                            },
                            success: function (res) {
                                $('#department_id').text(res.data.Name);
                            }
                        });

                        // 获取医助和医生姓名
                        if (data.Asst_id > 0 || data.Doc_id > 0) {
                            $.ajax({
                                url: serverUrl + "/admin/user/list_low",
                                type: "post",
                                success: function (res) {
                                    if (res.code == 200) {
                                        let users = res.data;
                                        // 查找医助姓名
                                        if (data.Asst_id > 0) {
                                            let asst = users.find(user => user.ID == data.Asst_id);
                                            if (asst) {
                                                $('#asst_name').text(asst.Name);
                                            } else {
                                                $('#asst_name').text(`未知(ID:${data.Asst_id})`);
                                            }
                                        } else {
                                            $('#asst_name').text('未分配');
                                        }

                                        // 查找医生姓名
                                        if (data.Doc_id > 0) {
                                            let doc = users.find(user => user.ID == data.Doc_id);
                                            if (doc) {
                                                $('#doc_name').text(doc.Name);
                                            } else {
                                                $('#doc_name').text(`未知(ID:${data.Doc_id})`);
                                            }
                                        } else {
                                            $('#doc_name').text('未分配');
                                        }
                                    }
                                }
                            });
                        } else {
                            $('#asst_name').text('未分配');
                            $('#doc_name').text('未分配');
                        }

                        // 显示舌苔照图片
                        if (data.Photo_tongue) {
                            let tongue_photos = data.Photo_tongue.split('\n');
                            let container = $('#photo_tongue_container');
                            tongue_photos.forEach(photo => {
                                if (photo.trim()) {
                                    let pic = '/static/uploads/normal_pics/photo_tongue/' + photo.trim();
                                    container.append(`<img src="${pic}" alt="舌苔照" onclick="viewImage('${pic}')">`);
                                }
                            });
                        }

                        // 显示检查单图片
                        if (data.Photo_sheet) {
                            let sheet_photos = data.Photo_sheet.split('\n');
                            let container = $('#photo_sheet_container');
                            sheet_photos.forEach(photo => {
                                if (photo.trim()) {
                                    let pic = '/static/uploads/normal_pics/photo_sheet/' + photo.trim();
                                    container.append(`<img src="${pic}" alt="检查单" onclick="viewImage('${pic}')">`);
                                }
                            });
                        }

                        // 获取用户信息
                        if (data.Pat_pro_id) {
                            // 请求用户信息接口
                            $.ajax({
                                url: serverUrl + "/admin/patient_profile/detail",
                                type: "post",
                                data: {
                                    id: data.Pat_pro_id,
                                },
                                success: function (userRes) {
                                    if (userRes.code == 200) {
                                        let userData = userRes.data;
                                        // 渲染用户基础信息
                                        renderUserInfo(userData);
                                    } else {
                                        layer.msg('获取用户信息失败: ' + userRes.msg, { icon: 2 });
                                    }
                                },
                                error: function (err) {
                                    layer.msg('获取用户信息失败', { icon: 2 });
                                    console.error('获取用户信息失败:', err);
                                }
                            });
                        }

                        // 加载图文沟通记录
                        loadVisitLogs(id);

                        // 加载音视频沟通记录
                        loadVideoLogs(id);

                        // 加载电话沟通记录
                        loadPhoneLogs(id);

                        // 初始化飘浮导航
                        initFloatingNav();
                    }
                },
                error: function (err) {
                    layer.closeAll('loading');

                    // 创建模糊覆盖层和错误信息显示
                    let errorMsg = err.responseJSON ? err.responseJSON.msg : '获取病历详情失败，请稍后重试';
                    let blurOverlay = `
                        <div class="error-overlay">
                            <div class="error-card layui-card">
                                <div class="layui-card-body">
                                    <p style="font-size: 16px; padding: 20px 10px; color: #333;">${errorMsg}</p>
                                    <div style="padding: 10px 0 20px 0;">
                                        <button class="layui-btn layui-btn-normal return-btn" style="padding: 0 30px;">返回</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                    $('body').append(blurOverlay);

                    // 添加动画效果
                    setTimeout(function () {
                        $('.error-overlay').addClass('show');
                    }, 10);

                    // 添加返回按钮点击事件
                    $('.return-btn').click(function () {
                        $('.error-overlay').removeClass('show');
                        setTimeout(function () {
                            window.history.go(-1);
                            $('.error-overlay').remove();
                        }, 300);
                    });
                }
            });

            // 加载图文沟通记录
            function loadVisitLogs(recordId) {
                // 获取用户数据
                $.ajax({
                    url: serverUrl + "/admin/user/list_low",
                    type: "post",
                    success: function (res) {
                        if (res.code == 200) {
                            const users = res.data;

                            // 初始化表格
                            table.render({
                                elem: '#visit-logs-table',
                                url: serverUrl + '/admin/visit_logs/list',
                                method: 'post',
                                where: {
                                    record_id: recordId
                                },
                                response: {
                                    statusName: 'code',
                                    statusCode: 200,
                                    msgName: 'msg',
                                    countName: 'count',
                                    dataName: 'data'
                                },
                                page: true,
                                limit: 5,
                                cols: [[
                                    {
                                        field: 'Asst_id', title: '客服', width: 100, templet: function (d) {
                                            let asstName = '未知用户';
                                            if (d.Asst_id > 0) {
                                                const asst = users.find(user => user.ID == d.Asst_id);
                                                if (asst) {
                                                    asstName = asst.Name;
                                                }
                                            }
                                            return asstName;
                                        }
                                    },
                                    {
                                        field: 'Contents', title: '内容', minWidth: 300, templet: function (d) {
                                            return d.Contents || '';
                                        }
                                    },
                                    {
                                        field: 'Pics', title: '图片', width: 200, templet: function (d) {
                                            let imagesHtml = '';
                                            if (d.Pics) {
                                                const imageUrls = d.Pics.split(',');
                                                imageUrls.forEach(img => {
                                                    if (img.trim()) {
                                                        const imgUrl = '/static/uploads/normal_pics/visit_logs/' + img.trim();
                                                        imagesHtml += `<img src="${imgUrl}" alt="沟通图片" onclick="viewImage('${imgUrl}')" style="width:50px;height:50px;margin:2px;cursor:pointer;border-radius:4px;">`;
                                                    }
                                                });
                                            }
                                            return imagesHtml;
                                        }
                                    },
                                    {
                                        field: 'Create_time', title: '时间', width: 170, templet: function (d) {
                                            return d.Create_time.replace('T', ' ').replace('Z', '');
                                        }
                                    },
                                    {
                                        title: '操作', width: 100, align: 'center', templet: function (d) {
                                            // 检查当前用户是否为超级管理员(ID=1)
                                            let userinfo = JSON.parse(localStorage.getItem('local_userinfo') || '{}');
                                            if (userinfo.Id === 1) {
                                                return '<button class="layui-btn layui-btn-xs layui-btn-danger" lay-event="del">删除</button>';
                                            }
                                            return '';
                                        }
                                    }
                                ]],
                                done: function () {
                                    layer.closeAll('loading');
                                }
                            });

                            // 监听表格工具条事件
                            table.on('tool(visit-logs-table)', function (obj) {
                                const data = obj.data;
                                if (obj.event === 'del') {
                                    layer.confirm('确定要删除这条图文沟通记录吗？<br>删除后将无法恢复，且图片将从服务器中删除。', {
                                        icon: 3,
                                        title: '删除确认',
                                        btn: ['确定删除', '取消']
                                    }, function (index) {
                                        // 发送删除请求
                                        $.ajax({
                                            url: serverUrl + '/admin/visit_logs/del',
                                            type: 'POST',
                                            data: {
                                                id: data.ID
                                            },
                                            success: function (res) {
                                                if (res.code === 200) {
                                                    layer.msg('删除成功', { icon: 1, time: 1000 });
                                                    // 重新加载表格数据
                                                    table.reload('visit-logs-table');
                                                } else {
                                                    layer.msg(res.msg || '删除失败', { icon: 2 });
                                                }
                                            },
                                            error: function (err) {
                                                layer.msg(err.responseJSON ? err.responseJSON.msg : '删除请求失败', { icon: 2 });
                                            }
                                        });
                                        layer.close(index);
                                    });
                                }
                            });
                        } else {
                            layer.msg(res.msg || '获取用户数据失败', { icon: 2 });
                        }
                    },
                    error: function (err) {
                        layer.msg(err.responseJSON ? err.responseJSON.msg : '获取用户数据失败', { icon: 2 });
                    }
                });
            }

            // 新增图文沟通记录按钮点击事件
            $('#add-visit-log-btn').on('click', function () {
                layer.open({
                    type: 1,
                    title: '新增图文沟通记录',
                    area: ['700px', '700px'],
                    shadeClose: true,
                    content: laytpl($('#visit-log-modal').html()).render({
                        record_id: id
                    }),
                    success: function (layero, index) {
                        form.render();

                        // 初始化图片上传
                        initImageUpload();

                        // 监听表单提交
                        form.on('submit(visit-log-submit)', function (data) {
                            $.ajax({
                                url: serverUrl + '/admin/visit_logs/add',
                                type: 'POST',
                                data: data.field,
                                success: function (res) {
                                    if (res.code === 200) {
                                        layer.close(index);
                                        layer.msg('添加成功', { icon: 1, time: 1000 });
                                        // 重新加载图文沟通记录
                                        loadVisitLogs(id);
                                    } else {
                                        layer.msg(res.msg || '添加失败', { icon: 2 });
                                    }
                                },
                                error: function (err) {
                                    layer.msg(err.responseJSON ? err.responseJSON.msg : '请求失败', { icon: 2 });
                                }
                            });
                            return false;
                        });
                    }
                });
            });

            // 新增电话沟通记录按钮点击事件
            $('#add-phone-log-btn').on('click', function () {
                layer.open({
                    type: 1,
                    title: '新增电话沟通记录',
                    area: ['700px', '700px'],
                    shadeClose: true,
                    content: laytpl($('#phone-log-modal').html()).render({
                        record_id: id
                    }),
                    success: function (layero, index) {
                        form.render();

                        // 初始化录音文件上传
                        initAudioUpload();

                        // 监听表单提交
                        form.on('submit(phone-log-submit)', function (data) {
                            $.ajax({
                                url: serverUrl + '/admin/phone_logs/add',
                                type: 'POST',
                                data: data.field,
                                success: function (res) {
                                    if (res.code === 200) {
                                        layer.close(index);
                                        layer.msg('添加成功', { icon: 1, time: 1000 });
                                        // 重新加载电话沟通记录
                                        loadPhoneLogs(id);
                                    } else {
                                        layer.msg(res.msg || '添加失败', { icon: 2 });
                                    }
                                },
                                error: function (err) {
                                    layer.msg(err.responseJSON ? err.responseJSON.msg : '请求失败', { icon: 2 });
                                }
                            });
                            return false;
                        });
                    }
                });
            });

            // 加载音视频沟通记录
            function loadVideoLogs(recordId) {
                // 获取用户数据
                $.ajax({
                    url: serverUrl + "/admin/user/list_low",
                    type: "post",
                    success: function (res) {
                        if (res.code == 200) {
                            const users = res.data;

                            // 初始化表格
                            table.render({
                                elem: '#video-logs-table',
                                filter: 'video-logs-table',
                                url: serverUrl + '/admin/video_logs/list',
                                method: 'post',
                                where: {
                                    record_id: recordId
                                },
                                response: {
                                    statusName: 'code',
                                    statusCode: 200,
                                    msgName: 'msg',
                                    countName: 'count',
                                    dataName: 'data'
                                },
                                page: true,
                                limit: 5,
                                cols: [[
                                    { field: 'ID', title: 'ID', align: 'center' },
                                    {
                                        field: 'Asst_id', title: '医助', width: 100, templet: function (d) {
                                            let asstName = '未知用户';
                                            if (d.Asst_id > 0) {
                                                const asst = users.find(user => user.ID == d.Asst_id);
                                                if (asst) {
                                                    asstName = asst.Name;
                                                }
                                            }
                                            return asstName;
                                        }
                                    },
                                    {
                                        field: 'Create_time', title: '创建时间', templet: function (d) {
                                            return Utc2time(d.Create_time);
                                        }
                                    },
                                    {
                                        field: 'Scheduled_time', title: '预约时间', templet: function (d) {
                                            return d.Scheduled_time ? Utc2time(d.Scheduled_time) : '-';
                                        }
                                    },
                                    {
                                        field: 'Finish_time', title: '完成时间', templet: function (d) {
                                            // 检查是否为数字时间戳
                                            if (d.Finish_time && !isNaN(d.Finish_time) && d.Finish_time.length > 8) {
                                                return linuxTimestampToNormalTime(d.Finish_time);
                                            } else if (d.Finish_time) {
                                                return Utc2time(d.Finish_time);
                                            } else {
                                                return '-';
                                            }
                                        }
                                    },
                                    {
                                        title: '操作', align: 'center', templet: function (d) {
                                            let html = '<button class="layui-btn layui-btn-xs layui-btn-normal" lay-event="download">播放</button>';

                                            // 检查当前用户是否为超级管理员(ID=1)
                                            let userinfo = JSON.parse(localStorage.getItem('local_userinfo') || '{}');
                                            if (userinfo.Id === 1) {
                                                html += ' <button class="layui-btn layui-btn-xs layui-btn-danger" lay-event="del">删除</button>';
                                            }

                                            return html;
                                        }
                                    }
                                ]],
                                done: function () {
                                    layer.closeAll('loading');
                                }
                            });

                            // 监听表格工具条事件
                            table.on('tool(video-logs-table)', function (obj) {
                                const data = obj.data;
                                if (obj.event === 'del') {
                                    layer.confirm('确定要删除这条音视频沟通记录吗？<br>删除后将无法恢复，且视频文件将从服务器中删除。', {
                                        icon: 3,
                                        title: '删除确认',
                                        btn: ['确定删除', '取消']
                                    }, function (index) {
                                        // 发送删除请求
                                        $.ajax({
                                            url: serverUrl + '/admin/video_logs/del',
                                            type: 'POST',
                                            data: {
                                                id: data.ID
                                            },
                                            success: function (res) {
                                                if (res.code === 200) {
                                                    layer.msg('删除成功', { icon: 1, time: 1000 });
                                                    // 重新加载表格数据
                                                    table.reload('video-logs-table');
                                                } else {
                                                    layer.msg(res.msg || '删除失败', { icon: 2 });
                                                }
                                            },
                                            error: function (err) {
                                                layer.msg(err.responseJSON ? err.responseJSON.msg : '删除请求失败', { icon: 2 });
                                            }
                                        });
                                        layer.close(index);
                                    });
                                } else if (obj.event === 'download') {
                                    // 播放功能
                                    layer.open({
                                        type: 2,
                                        title: '音视频播放',
                                        area: ['900px', '600px'],
                                        shadeClose: true,
                                        content: 'rtc_videos_modal.html?pid=' + data.ID,
                                        maxmin: true
                                    });
                                }
                            });
                        } else {
                            layer.msg(res.msg || '获取用户数据失败', { icon: 2 });
                        }
                    },
                    error: function (err) {
                        layer.msg(err.responseJSON ? err.responseJSON.msg : '获取用户数据失败', { icon: 2 });
                    }
                });
            }

            // 加载电话沟通记录
            function loadPhoneLogs(recordId) {
                // 获取用户数据
                $.ajax({
                    url: serverUrl + "/admin/user/list_low",
                    type: "post",
                    success: function (res) {
                        if (res.code == 200) {
                            const users = res.data;

                            // 初始化表格
                            table.render({
                                elem: '#phone-logs-table',
                                url: serverUrl + '/admin/phone_logs/list',
                                method: 'post',
                                where: {
                                    record_id: recordId
                                },
                                response: {
                                    statusName: 'code',
                                    statusCode: 200,
                                    msgName: 'msg',
                                    countName: 'count',
                                    dataName: 'data'
                                },
                                page: true,
                                limit: 5,
                                cols: [[
                                    {
                                        field: 'Asst_id', title: '客服', width: 100, templet: function (d) {
                                            let asstName = '未知用户';
                                            if (d.Asst_id > 0) {
                                                const asst = users.find(user => user.ID == d.Asst_id);
                                                if (asst) {
                                                    asstName = asst.Name;
                                                }
                                            }
                                            return asstName;
                                        }
                                    },
                                    {
                                        field: 'Contents', title: '内容', minWidth: 200, templet: function (d) {
                                            return d.Contents || '';
                                        }
                                    },
                                    {
                                        field: 'Files', title: '录音', width: 300, templet: function (d) {
                                            let audioHtml = '';
                                            if (d.Files) {
                                                const fileUrls = d.Files.split(',');
                                                fileUrls.forEach(file => {
                                                    if (file.trim()) {
                                                        const fileUrl = '/static/uploads/normal_files/phone_recording/' + file.trim();
                                                        audioHtml += `<audio controls style="width:100%;height:30px;margin:2px;" src="${fileUrl}"></audio>`;
                                                    }
                                                });
                                            }
                                            return audioHtml;
                                        }
                                    },
                                    {
                                        field: 'Create_time', title: '时间', width: 170, templet: function (d) {
                                            return d.Create_time.replace('T', ' ').replace('Z', '');
                                        }
                                    },
                                    {
                                        title: '操作', width: 200, align: 'center', templet: function (d) {
                                            // 检查当前用户是否为超级管理员(ID=1)
                                            let userinfo = JSON.parse(localStorage.getItem('local_userinfo') || '{}');
                                            if (userinfo.Id === 1) {
                                                return '<button class="layui-btn layui-btn-xs layui-btn-danger" lay-event="del">删除</button>';
                                            }
                                            return '';
                                        }
                                    }
                                ]],
                                done: function () {
                                    layer.closeAll('loading');
                                }
                            });

                            // 监听表格工具条事件
                            table.on('tool(phone-logs-table)', function (obj) {
                                const data = obj.data;
                                if (obj.event === 'del') {
                                    layer.confirm('确定要删除这条电话沟通记录吗？<br>删除后将无法恢复，且录音文件将从服务器中删除。', {
                                        icon: 3,
                                        title: '删除确认',
                                        btn: ['确定删除', '取消']
                                    }, function (index) {
                                        // 发送删除请求
                                        $.ajax({
                                            url: serverUrl + '/admin/phone_logs/del',
                                            type: 'POST',
                                            data: {
                                                id: data.ID
                                            },
                                            success: function (res) {
                                                if (res.code === 200) {
                                                    layer.msg('删除成功', { icon: 1, time: 1000 });
                                                    // 重新加载表格数据
                                                    table.reload('phone-logs-table');
                                                } else {
                                                    layer.msg(res.msg || '删除失败', { icon: 2 });
                                                }
                                            },
                                            error: function (err) {
                                                layer.msg(err.responseJSON ? err.responseJSON.msg : '删除请求失败', { icon: 2 });
                                            }
                                        });
                                        layer.close(index);
                                    });
                                }
                            });
                        } else {
                            layer.msg(res.msg || '获取用户数据失败', { icon: 2 });
                        }
                    },
                    error: function (err) {
                        layer.msg(err.responseJSON ? err.responseJSON.msg : '获取用户数据失败', { icon: 2 });
                    }
                });
            }

            // 初始化录音文件上传
            function initAudioUpload() {
                // 录音文件上传
                upload.render({
                    elem: '#upload-audio-btn',
                    url: serverUrl + '/admin/upload_normal_file',
                    data: {
                        category: 'phone_recording'
                    },
                    accept: 'audio',
                    acceptMime: 'audio/mp3',
                    exts: 'mp3',
                    multiple: true,
                    size: 5120, // 限制文件大小，单位 KB (5MB)
                    before: function (obj) {
                        layer.load(2);
                    },
                    done: function (res) {
                        layer.closeAll('loading');
                        if (res.code === 200) {
                            // 获取当前已有的录音文件
                            let currentFiles = $('input[name="files"]').val();
                            let filesArray = currentFiles ? currentFiles.split(',') : [];

                            // 添加新上传的录音文件
                            filesArray.push(res.data[0].filename);

                            // 更新隐藏字段的值
                            $('input[name="files"]').val(filesArray.join(','));

                            // 添加录音文件预览
                            $('#audio-preview-container').append(`
                                <div class="audio-preview-item">
                                    <audio controls src="${serverUrl}/${res.data[0].filepath}"></audio>
                                    <div class="delete-btn" data-filename="${res.data[0].filename}">
                                        <i class="layui-icon layui-icon-close"></i>
                                    </div>
                                </div>
                            `);

                            // 绑定删除按钮事件
                            bindDeleteAudioEvent();

                            layer.msg('上传成功', { icon: 1 });
                        } else {
                            layer.msg(res.msg || '上传失败', { icon: 2 });
                        }
                    },
                    error: function () {
                        layer.closeAll('loading');
                        layer.msg('上传失败，请重试', { icon: 2 });
                    }
                });

                // 绑定删除录音文件事件
                function bindDeleteAudioEvent() {
                    $('.audio-preview-item .delete-btn').off('click').on('click', function () {
                        let filename = $(this).data('filename');
                        let $item = $(this).closest('.audio-preview-item');

                        layer.confirm('确定删除该录音文件吗？', { icon: 3, title: '提示' }, function (index) {
                            $.ajax({
                                url: serverUrl + '/admin/normal_file_del',
                                type: 'POST',
                                data: {
                                    filename: filename,
                                    category: 'phone_recording'
                                },
                                success: function (res) {
                                    if (res.code === 200) {
                                        // 从隐藏字段中移除该录音文件
                                        let currentFiles = $('input[name="files"]').val();
                                        let filesArray = currentFiles ? currentFiles.split(',') : [];
                                        let newFilesArray = filesArray.filter(file => file !== filename);
                                        $('input[name="files"]').val(newFilesArray.join(','));

                                        // 移除预览元素
                                        $item.remove();
                                        layer.msg('删除成功', { icon: 1, time: 1000 });
                                    } else {
                                        layer.msg(res.msg || '删除失败', { icon: 2 });
                                    }
                                },
                                error: function (data) {
                                    layer.msg('删除失败: ' + (data.responseJSON ? data.responseJSON.msg : '未知错误'), { icon: 2 });
                                }
                            });
                            layer.close(index);
                        });
                    });
                }
            }

            // 初始化图片上传
            function initImageUpload() {
                // 图片预览函数
                function appendImagePreview(containerId, filepath, filename) {
                    const container = $(`#${containerId}`);

                    const imageItem = $(`
                        <div class="image-preview-item" data-filename="${filename}" data-filepath="${filepath}">
                            <img src="${filepath}">
                            <div class="delete-btn"><i class="layui-icon layui-icon-close"></i></div>
                        </div>
                    `);

                    // 点击图片查看大图
                    imageItem.find('img').on('click', function () {
                        layer.photos({
                            photos: {
                                title: '查看图片',
                                data: [{ src: filepath }]
                            },
                            footer: false
                        });
                    });

                    // 删除图片
                    imageItem.find('.delete-btn').on('click', function () {
                        layer.confirm('确定删除该图片吗？', { icon: 3, title: '提示' }, function (index) {
                            $.ajax({
                                url: '/admin/normal_pic_del',
                                type: 'POST',
                                data: {
                                    filename: filename,
                                    category: 'visit_logs'
                                },
                                success: function (res) {
                                    if (res.code === 200) {
                                        // 从隐藏字段中移除该图片
                                        let currentPics = $('input[name="pics"]').val();
                                        let picsArray = currentPics ? currentPics.split(',') : [];
                                        let newPicsArray = picsArray.filter(pic => pic !== filename);
                                        $('input[name="pics"]').val(newPicsArray.join(','));

                                        // 移除预览
                                        imageItem.remove();
                                        layer.msg('删除成功', { icon: 1, time: 1000 });
                                    }
                                },
                                error: function (data) {
                                    layer.msg('删除失败: ' + (data.responseJSON ? data.responseJSON.msg : '未知错误'), { icon: 2 });
                                }
                            });
                            layer.close(index);
                        });
                    });

                    container.append(imageItem);
                }

                // 初始化文件上传
                upload.render({
                    elem: '#upload-images-btn',
                    url: '/admin/upload_normal_pic',
                    multiple: true,
                    data: {
                        category: 'visit_logs',
                    },
                    drag: true,
                    before: function (obj) {
                        obj.preview(function (index, file, result) {
                            // 在预览回调中进行文件上传
                            autoCompressAndUpload(file, {
                                data: { category: 'visit_logs' },
                                success: function (res) {
                                    if (res.code === 200) {
                                        const fileInfo = res.data[0];

                                        // 显示图片预览
                                        appendImagePreview('image-preview-container', fileInfo.filepath, fileInfo.filename);

                                        // 将图片名称添加到隐藏字段中
                                        let currentPics = $('input[name="pics"]').val();
                                        let picsArray = currentPics ? currentPics.split(',') : [];
                                        picsArray.push(fileInfo.filename);
                                        $('input[name="pics"]').val(picsArray.join(','));

                                        layer.msg('上传成功', { icon: 1, time: 1000 });
                                    } else {
                                        layer.msg(res.msg || '上传失败', { icon: 2 });
                                    }
                                },
                                error: function (error) {
                                    layer.msg(error.message || '上传出错', { icon: 2 });
                                }
                            });
                        });
                        return false;
                    }
                });

                // 添加粘贴上传功能
                document.addEventListener('paste', function (event) {
                    const items = event.clipboardData && event.clipboardData.items;
                    let file = null;

                    if (items && items.length) {
                        // 遍历剪切板内容
                        for (let i = 0; i < items.length; i++) {
                            if (items[i].type.indexOf('image') !== -1) {
                                file = items[i].getAsFile();
                                break;
                            }
                        }
                    }

                    if (!file) {
                        return;
                    }

                    // 阻止默认粘贴行为
                    event.preventDefault();

                    // 使用通用的压缩上传函数
                    autoCompressAndUpload(file, {
                        data: { category: 'visit_logs' },
                        success: function (res) {
                            if (res.code === 200) {
                                const fileInfo = res.data[0];

                                // 显示图片预览
                                appendImagePreview('image-preview-container', fileInfo.filepath, fileInfo.filename);

                                // 将图片名称添加到隐藏字段中
                                let currentPics = $('input[name="pics"]').val();
                                let picsArray = currentPics ? currentPics.split(',') : [];
                                picsArray.push(fileInfo.filename);
                                $('input[name="pics"]').val(picsArray.join(','));

                                layer.msg('上传成功', { icon: 1, time: 1000 });
                            } else {
                                layer.msg(res.msg || '上传失败', { icon: 2 });
                            }
                        },
                        error: function (error) {
                            layer.msg(error.message || '上传出错', { icon: 2 });
                        }
                    });
                });
            }

            // 初始化飘浮导航函数
            function initFloatingNav() {
                // 切换按钮组显示/隐藏
                $('.floating-nav-toggle').on('click', function () {
                    $('.floating-nav').toggleClass('active');
                });

                // 初始状态设为展开
                setTimeout(function () {
                    $('.floating-nav').addClass('active');
                }, 1000);

                // 点击导航按钮
                $('.floating-nav-btn').on('click', function (e) {
                    // 阻止默认锚点跳转行为，改用手动滚动
                    e.preventDefault();

                    // 获取目标元素
                    var targetId = $(this).attr('data-target');
                    var $target = $('#' + targetId);

                    if ($target.length) {
                        // 获取目标位置
                        var targetPosition = $target.offset().top - 70; // 减去头部高度

                        // 立即更新活动按钮状态，提供即时反馈
                        $('.floating-nav-btn').removeClass('active');
                        $(this).addClass('active');

                        // 使用setTimeout延迟执行滚动操作
                        setTimeout(function () {
                            // 直接使用锚点跳转
                            window.location.hash = targetId;
                            console.log('使用锚点跳转: #' + targetId);
                        }, 10);
                    } else {
                        console.log('未找到目标元素: ' + targetId);
                    }
                });

                // 监听滚动，更新活动按钮状态
                $(window).on('scroll', function () {
                    var scrollPosition = $(window).scrollTop();

                    // 检查每个部分的位置
                    $('.layui-panel[id^="section-"]').each(function () {
                        var $section = $(this);
                        var sectionTop = $section.offset().top - 100;
                        var sectionBottom = sectionTop + $section.outerHeight();

                        if (scrollPosition >= sectionTop && scrollPosition < sectionBottom) {
                            var sectionId = $section.attr('id');
                            $('.floating-nav-btn').removeClass('active');
                            $('.floating-nav-btn[data-target="' + sectionId + '"]').addClass('active');
                        }
                    });
                });
            }
        });
        // 添加图片查看函数（在script标签内，但在layui.use外）
        function viewImage(src) {
            layer.photos({
                photos: {
                    title: "查看图片",
                    start: 0,
                    data: [{ src: src }]
                },
                anim: 5,
                shadeClose: true,
                footer: false
            });
        }


    </script>

    <!-- 飘浮导航按钮组 -->
    <div class="floating-nav">
        <div class="floating-nav-toggle">
            <i class="layui-icon layui-icon-spread-left"></i>
        </div>
        <div class="floating-nav-buttons">
            <a href="#section-details" class="floating-nav-btn active" data-target="section-details">
                <i class="layui-icon layui-icon-form"></i>
                <span>病历详情</span>
            </a>
            <a href="#section-image-text" class="floating-nav-btn" data-target="section-image-text">
                <i class="layui-icon layui-icon-picture"></i>
                <span>图文沟通</span>
            </a>
            <a href="#section-video" class="floating-nav-btn" data-target="section-video">
                <i class="layui-icon layui-icon-video"></i>
                <span>音视频沟通</span>
            </a>
            <a href="#section-phone" class="floating-nav-btn" data-target="section-phone">
                <i class="layui-icon layui-icon-cellphone"></i>
                <span>电话沟通</span>
            </a>
        </div>
    </div>
</body>

</html>