package admin

import (
	"fmt"
	"mstproject/pkg/common"
	"mstproject/pkg/config"
	"mstproject/pkg/database"
	"net/http"
	"time"
)

// TRTC配置结构体
type TRTCConfig struct {
	SDKAppID  int    `json:"sdkAppId"`
	UserID    string `json:"userId"`
	RoleID    int    `json:"roleId"` // 添加角色ID
	UserSig   string `json:"userSig"`
	RoomID    int    `json:"roomId"`
	RoomIDStr string `json:"roomIdStr"` // 添加带前缀的房间ID
}

// 获取TRTC配置
func Rtc_room_get_trtc_config(w http.ResponseWriter, r *http.Request) {
	api_id := config.NormalPerm
	session, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}

	// 获取房间ID
	roomID, err := common.CheckInt(r.FormValue("room_id"))
	if err != nil || roomID < 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "房间ID不能为空",
		})
		return
	}

	// 获取当前用户ID和角色ID
	userID := session.Values["id"]
	roleID := 4 // 默认为医生角色

	//判断当前诊室状态是否允许问诊
	type RoomDatas struct {
		Status         int    `json:"status"`
		Doc_id         int    `json:"doc_id"`
		Scheduled_time string `json:"scheduled_time"`
	}

	sql := "select status,doc_id,scheduled_time from rtc_room where id = ?"
	var roomDatas RoomDatas
	err = database.GetRow(sql, &roomDatas, roomID)

	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "获取诊室状态失败",
			"sql":  common.DebugSql(sql, roomID),
		})
		return
	}

	// 1. 检查用户权限
	if userID != 1 && userID != roomDatas.Doc_id {
		common.JSONResponse(w, http.StatusForbidden, map[string]interface{}{
			"code": 500,
			"msg":  "当前医生非隶属于该诊室",
		})
		return
	}

	// 2. 检查诊室状态
	if roomDatas.Status != 0 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "当前诊室状态不允许视频问诊",
		})
		return
	}

	// 3. 检查是否是当天预约
	currentTime := time.Now()
	scheduledTime, err := time.Parse(time.RFC3339, roomDatas.Scheduled_time)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "时间格式转换失败",
			"err":  err.Error(),
		})
		return
	}

	// 检查是否是同一天
	if currentTime.Format("2006-01-02") != scheduledTime.Format("2006-01-02") {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "当前诊室预约时间还未到问诊时间",
		})
		return
	}

	// 4. 检查是否超过预约时间30分钟
	// if currentTime.Sub(scheduledTime) >= 30*time.Minute {
	// 	common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
	// 		"code": 500,
	// 		"msg":  "当前诊室的预约时间已过期",
	// 	})
	// 	return
	// }

	// 使用官方 UserSig 生成方法
	userIDStr := fmt.Sprintf("%d_%d", roleID, userID)
	userSig, err := common.GenUserSig(config.TRTC_SDK_APPID, config.TRTC_SECRET_KEY, userIDStr, 86400*180) // 180天有效期

	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "生成UserSig失败",
			"err":  err.Error(),
		})
		return
	}

	// 返回TRTC配置
	config := TRTCConfig{
		SDKAppID:  config.TRTC_SDK_APPID,
		UserID:    userIDStr,
		RoleID:    roleID,
		UserSig:   userSig,
		RoomID:    roomID,
		RoomIDStr: fmt.Sprintf("%s%d", config.TRTC_APP_NAME, roomID), // 添加带前缀的房间ID
	}

	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "获取TRTC配置成功",
		"data": config,
	})
}
