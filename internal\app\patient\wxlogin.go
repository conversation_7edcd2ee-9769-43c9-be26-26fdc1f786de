package patient

import (
	"encoding/json"
	"fmt"
	"mstproject/pkg/common"
	"mstproject/pkg/config"
	"mstproject/pkg/database"
	"net/http"
	"time"
)

type AuthResponse struct {
	OpenID     string `json:"openid"`
	SessionKey string `json:"session_key"`
	ErrMsg     string `json:"errmsg"`
}

var httpClient = &http.Client{Timeout: 10 * time.Second} // 设置超时

func getSession(code, appid, appsecret string) (AuthResponse, error) {
	var authResponse AuthResponse
	apiUrl := fmt.Sprintf("https://api.weixin.qq.com/sns/jscode2session?appid=%s&secret=%s&js_code=%s&grant_type=authorization_code", appid, appsecret, code)
	resp, err := httpClient.Get(apiUrl)
	if err != nil {
		return authResponse, err
	}
	defer resp.Body.Close()

	if err := json.NewDecoder(resp.Body).Decode(&authResponse); err != nil {
		return authResponse, err
	}

	return authResponse, nil
}

// func Get_wechat_info(w http.ResponseWriter, r *http.Request) {
// 	appid := config.WX_appid
// 	appsecret := config.WX_appsecret

// 	code, err := common.CheckStr(r.FormValue("code"))
// 	if code == "" || err != nil {
// 		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
// 			"code": 500,
// 			"err":  "code is empty",
// 		})
// 		return
// 	}

// 	authResponse, err := getSession(code, appid, appsecret)
// 	if err != nil {
// 		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
// 			"code": 500,
// 			"err":  err.Error(),
// 		})
// 		return
// 	}

// 	if authResponse.OpenID == "" {
// 		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
// 			"code": 500,
// 			"err":  authResponse.ErrMsg,
// 		})
// 		return
// 	}

// 	type Patient struct {
// 		User_id         int    `json:"user_id"`         // 帐号ID
// 		Profile_id      int    `json:"profile_id"`      // 个人资料ID
// 		Phone           string `json:"phone"`           // 电话号码
// 		Relation        int    `json:"relation"`        // 关系
// 		User_status     int    `json:"user_status"`     // 用户状态
// 		Asst_id         int    `json:"asst_id"`         // 助手ID
// 		Doc_id          int    `json:"doc_id"`          // 医生ID
// 		Support_id      int    `json:"support_id"`      // 支持者ID
// 		Name            string `json:"name"`            // 姓名
// 		Asst_phone      string `json:"asst_phone"`      // 助手电话
// 		Asst_name       string `json:"asst_name"`       // 助手姓名
// 		Doc_phone       string `json:"doc_phone"`       // 医生电话
// 		Doc_name        string `json:"doc_name"`        // 医生姓名
// 		Support_phone   string `json:"support_phone"`   // 支持者电话
// 		Support_name    string `json:"support_name"`    // 支持者姓名
// 		Sex             int    `json:"sex"`             // 性别
// 		Born_date       string `json:"born_date"`       // 出生日期
// 		Idcard          string `json:"idcard"`          // 身份证号
// 		Weixin          string `json:"weixin"`          // 微信号
// 		Ins_card_num    string `json:"ins_card_num"`    // 保险卡号
// 		Ins_type        string `json:"ins_type"`        // 保险类型
// 		Height          int    `json:"height"`          // 身高
// 		Weight          int    `json:"weight"`          // 体重
// 		Allergies       string `json:"allergies"`       // 过敏史
// 		Medical_history string `json:"medical_history"` // 病史
// 		Patient_type    int    `json:"patient_type"`    // 患者类型
// 		Patient_from    int    `json:"patient_from"`    // 患者来源
// 		Address         string `json:"address"`         // 地址
// 		Ismarried       int    `json:"ismarried"`       // 婚姻状态
// 		Patient_status  int    `json:"patient_status"`  // 患者状态
// 		Customer_notes  string `json:"customer_notes"`  // 客户备注
// 		Create_time     string `json:"create_time"`     // 创建时间
// 	}

//		var patient []Patient
//		sql := `
//			SELECT
//			a.id user_id,
//			a.phone,
//			a.status as user_status,
//			b.relation,
//			b.asst_id,
//			b.doc_id,
//			b.support_id,
//			b.name,
//			c.phone asst_phone,
//			c.name asst_name,
//			ifnull(d.phone,'无') doc_phone,
//			ifnull(d.name,'未绑定') as doc_name,
//			ifnull(e.phone,'无') support_phone,
//			ifnull(e.name,'未绑定') as support_name,
//			b.sex,
//			b.born_date,
//			b.id as profile_id,
//			b.idcard,
//			b.weixin,
//			b.ins_card_num,
//			b.ins_type,
//			b.height,
//			b.weight,
//			b.allergies,
//			b.medical_history,
//			b.patient_type,
//			b.patient_from,
//			b.address,
//			b.ismarried,
//			b.status as patient_status,
//			b.customer_notes,
//			b.create_time
//			FROM
//			patient_account AS a
//			LEFT JOIN patient_profile AS b ON b.pid = a.id
//			LEFT JOIN rbac_user AS c ON c.id = b.asst_id
//			LEFT JOIN rbac_user AS d ON d.id = b.doc_id
//			LEFT JOIN rbac_user AS e ON e.id = b.support_id
//			WHERE
//			a.openid = ?
//		`
//		// AND b.relation = 0
//		err = database.GetAll(sql, &patient, authResponse.OpenID)
//		if err != nil {
//			common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
//				"code":   500,
//				"err":    err.Error(),
//				"msg":    "您还未绑定帐号",
//				"openid": authResponse.OpenID,
//				"sql":    common.DebugSql(sql, authResponse.OpenID),
//			})
//			return
//		}
//		// 生成JWT TOKEN
//		token, err := common.GenerateJWTToken(patient[0].User_id)
//		if err != nil {
//			common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
//				"code": 500,
//				"err":  err.Error(),
//				"msg":  "生成token失败",
//			})
//			return
//		}
//		common.JSONResponse(w, http.StatusOK, map[string]interface{}{
//			"code": 200,
//			"data": map[string]interface{}{
//				"token":       token,
//				"patient":     patient,
//				"openid":      authResponse.OpenID,
//				"session_key": authResponse.SessionKey,
//			},
//			"msg": "登录成功",
//			"sql": common.DebugSql(sql, authResponse.OpenID),
//		})
//	}
//
// 按照get_wechat_info函数写一个专门获取OPENID的函数
func Get_wechat_openid(w http.ResponseWriter, r *http.Request) {
	appid := config.WX_appid
	appsecret := config.WX_appsecret

	code, err := common.CheckStr(r.FormValue("code"))
	if code == "" || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"err":  "code is empty",
		})
		return
	}

	authResponse, err := getSession(code, appid, appsecret)
	if err != nil || authResponse.OpenID == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"err":  err.Error(),
			"msg":  "获取微信信息失败",
		})
		return
	}

	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"data": map[string]interface{}{
			"openid":      authResponse.OpenID,
			"session_key": authResponse.SessionKey,
		},
		"msg": "获取openid成功",
	})
}
func Get_user_role(w http.ResponseWriter, r *http.Request) {
	// 根据传递来的OPENID查询这个用户所对应的角色
	openid := r.FormValue("openid")
	if openid == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"err":  "openid is empty",
		})
		return
	}
	type Role struct {
		User_id int `json:"user_id"`
		Role_id int `json:"role_id"`
	}
	// 如果1个用户即绑了医助又绑了仓库，则小程序后台优先级为：医助 -> 仓库
	sql := `
	
		select user_id,role_id from rbac_user_roles where user_id = (select id from rbac_user where openid = ? limit 1)
		ORDER BY 
			CASE WHEN role_id = 3 THEN 1 ELSE 0 END DESC,
			CASE WHEN role_id = 11 THEN 1 ELSE 0 END DESC
		LIMIT 1;
	
	`
	var role Role
	err := database.GetRow(sql, &role, openid)
	if err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "普通用户，未绑定职能科室角色",
			// "err":  err.Error(),
			// "sql":  common.DebugSql(sql, openid),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"data": role,
		"msg":  "获取用户角色成功",
		"sql":  common.DebugSql(sql, openid),
	})

}

func Get_wechat_info(w http.ResponseWriter, r *http.Request) {
	appid := config.WX_appid
	appsecret := config.WX_appsecret

	code, err := common.CheckStr(r.FormValue("code"))
	if code == "" || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"err":  "code is empty",
		})
		return
	}
	useragent := r.FormValue("useragent")
	authResponse, err := getSession(code, appid, appsecret)
	if err != nil || authResponse.OpenID == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code":   500,
			"err":    err.Error(),
			"msg":    "获取微信信息失败",
			"openid": authResponse.OpenID,
			"sql":    common.DebugSql(fmt.Sprintf("https://api.weixin.qq.com/sns/jscode2session?appid=%s&secret=%s&js_code=%s&grant_type=authorization_code", appid, appsecret, code)),
		})
		return
	}

	type Account struct {
		User_ID     int    `json:"user_id"`     // 帐号ID
		Phone       string `json:"phone"`       // 电话号码
		User_Status int    `json:"user_status"` // 用户状态
	}

	var account Account
	accountSQL := `
    SELECT
        id AS user_id,
        phone,
        status AS user_status
    FROM
        patient_account
    WHERE
        openid = ?`

	err = database.GetOne(accountSQL, &account, authResponse.OpenID)
	if err != nil {
		common.JSONResponse(w, http.StatusOK, map[string]interface{}{
			"code":   500,
			"err":    err.Error(),
			"msg":    "您还未绑定帐号",
			"openid": authResponse.OpenID,
			// "sql":    common.DebugSql(accountSQL, authResponse.OpenID),
		})
		common.Add_log_wxapp(fmt.Sprintf("未绑定帐号的微信：%s", authResponse.OpenID), 0, r, useragent)
		return
	}

	type Patient struct {
		Pat_pro_id      int    `json:"pat_pro_id"`      // 个人资料ID
		Name            string `json:"name"`            // 姓名
		Relation        int    `json:"relation"`        // 关系
		Asst_ID         int    `json:"asst_id"`         // 助手ID
		Doc_ID          int    `json:"doc_id"`          // 医生ID
		Support_ID      int    `json:"support_id"`      // 支持者ID
		Asst_Phone      string `json:"asst_phone"`      // 助手电话
		Asst_Name       string `json:"asst_name"`       // 助手姓名
		Doc_Phone       string `json:"doc_phone"`       // 医生电话
		Doc_Name        string `json:"doc_name"`        // 医生姓名
		Support_Phone   string `json:"support_phone"`   // 支持者电话
		Support_Name    string `json:"support_name"`    // 支持者姓名
		Sex             int    `json:"sex"`             // 性别
		Born_Date       string `json:"born_date"`       // 出生日期
		IDCard          string `json:"idcard"`          // 身份证号
		Weixin          string `json:"weixin"`          // 微信号
		Ins_Card_Num    string `json:"ins_card_num"`    // 保险卡号
		Ins_Type        string `json:"ins_type"`        // 保险类型
		Height          int    `json:"height"`          // 身高
		Weight          int    `json:"weight"`          // 体重
		Allergies       string `json:"allergies"`       // 过敏史
		Medical_History string `json:"medical_history"` // 病史
		Patient_Type    int    `json:"patient_type"`    // 患者类型
		Patient_From    int    `json:"patient_from"`    // 患者来源
		Address         string `json:"address"`         // 地址
		IsMarried       int    `json:"ismarried"`       // 婚姻状态
		Patient_Status  int    `json:"patient_status"`  // 患者状态
		Customer_Notes  string `json:"customer_notes"`  // 客户备注
		Create_Time     string `json:"create_time"`     // 创建时间
		Phone           string `json:"phone"`           // 电话号码
	}

	var patients []Patient
	patientSQL := `
    SELECT
        b.id AS pat_pro_id,
        b.name,
        b.relation,
        b.asst_id,
        b.doc_id,
        b.support_id,
        c.phone AS asst_phone,
        c.name AS asst_name,
        IFNULL(d.phone, '无') AS doc_phone,
        IFNULL(d.name, '未绑定') AS doc_name,
        IFNULL(e.phone, '无') AS support_phone,
        IFNULL(e.name, '未绑定') AS support_name,
        b.sex,
        b.born_date,
        b.idcard,
        b.weixin,
        b.ins_card_num,
        b.ins_type,
        b.height,
        b.weight,
        b.allergies,
        b.medical_history,
        b.patient_type,
        b.patient_from,
        b.address,
        b.ismarried,
        b.status AS patient_status,
        b.customer_notes,
        b.create_time,
		b.phone
    FROM
        patient_profile AS b
    LEFT JOIN 
        patient_account AS a ON b.pid = a.id
    LEFT JOIN rbac_user AS c ON c.id = b.asst_id
    LEFT JOIN rbac_user AS d ON d.id = b.doc_id
    LEFT JOIN rbac_user AS e ON e.id = b.support_id
    WHERE 
        a.openid = ?`

	err = database.GetAll(patientSQL, &patients, authResponse.OpenID)
	if err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code":   500,
			"err":    err.Error(),
			"msg":    "获取患者信息失败",
			"openid": authResponse.OpenID,
			"sql":    common.DebugSql(patientSQL, authResponse.OpenID),
		})
		return
	}

	// 生成JWT TOKEN
	token, err := common.GenerateJWTToken(account.User_ID)
	if err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"err":  err.Error(),
			"msg":  "生成token失败",
		})
		return
	}

	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"data": map[string]interface{}{
			"token":          token,
			"account":        account,
			"patients":       patients,
			"openid":         authResponse.OpenID,
			"session_key":    authResponse.SessionKey,
			"familyRelation": config.FamilyRelation,
		},
		"msg": "登录成功",
		"sql": common.DebugSql(accountSQL, authResponse.OpenID),
	})
	common.Add_log_wxapp(fmt.Sprintf("用户登录成功，OPENID：%s", authResponse.OpenID), account.User_ID, r, useragent)
}
