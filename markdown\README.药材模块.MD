# 药材模块（drug）开发

## 1. 技术栈
- 前端：HTML + LayUI
- 后端：Golang
- 数据库：MySQL
- 开发模式：前后端分离

## 2. 目录结构
```
├── interface/
│   ├── admin/
│   │   ├── drug_list.html    # 列表页，文件不存在，需要新增文件，参考页面：supplier_drug_list.html
│   │   ├── drug_add.html     # 添加页，文件不存在，需要新增文件，参考页面：supplier_drug_add.html
│   │   ├── drug_edit.html    # 编辑页，文件不存在，需要新增文件，参考页面：supplier_drug_edit.html
│   │   └── drug_detail.html  # 详情页，文件不存在，需要新增文件，参考页面：supplier_drug_detail.html
├── internal/
│   ├── app/
│   │   └── admin/
│   │       └── admin_extra.go  # 业务逻辑，文件存在，需要修改文件，追加代码
│   └── routes/
│       └── admin.go        # 路由配置，文件存在，需要修改文件，追加代码
```

## 3. 开发步骤

### 3.1 数据库详情
   -  表名：drug
   -  字段
   `
   id int   ID
   name  varchar  药名
   batch  varchar  批次
   code  varchar  批号
   unit  varchar  单位
   property varchar  性味 (如：寒、热、温、凉)
   origin   varchar  产地
   harvestTime date  采收时间
   validity_days  smallint 默认效期（天）
   storage  varchar  储存条件
   dosage   varchar  常用剂量
   directions  varchar  用法
   effect   varchar  药效
   indication  varchar  适应症
   contraindication  varchar  禁忌症
   sideeffect  varchar  副作用
   interaction varchar  药物相互作用
   price decimal  参考价格（仓库里还有个价格，为时仓库设置的价格）
   create_time timestamp   建档时间
   `

### 3.2 后端需要给`admin_extra.go`文件增加函数的细节
   - `Drug_list`: 列表查询（参考当前页面的函数：Supplier_drug_list()）
   - `Drug_detail`: 获取详情（参考当前页面的函数：Supplier_drug_detail()）
   - `Drug_add`: 添加记录（参考当前页面的函数：Supplier_drug_add()）
   - `Drug_edit`: 编辑记录（参考当前页面的函数：Supplier_drug_edit()）
   - `Drug_del`: 删除记录（参考当前页面的函数：Supplier_drug_del()）

### 3.3 需要给 `admin.go` 文件新增的路由注册
   ```
   mux.HandleFunc("/admin/drug/list", handlers.Drug_list)
   mux.HandleFunc("/admin/drug/add", handlers.Drug_add)
   mux.HandleFunc("/admin/drug/edit", handlers.Drug_edit)
   mux.HandleFunc("/admin/drug/detail", handlers.Drug_detail)
   mux.HandleFunc("/admin/drug/del", handlers.Drug_del)
   ```