<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>赠品管理/修改</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <script src="/dist/layui/layui.js"></script>
    <style>
        .layui-table-cell {
            height: auto;
            line-height: 28px;
        }
    </style>
</head>

<body style="padding: 10px;">
    <table id="giftTable" lay-filter="giftTable"></table>

    <div
        style="position: fixed; bottom: 0; left: 0; right: 0; background-color: #fff; padding: 10px; text-align: center; box-shadow: 0 -2px 10px rgba(0,0,0,0.1);">
        <button type="button" class="layui-btn" id="submitBtn">确认保存该订单赠品</button>
    </div>

    <script>
        layui.use(['table', 'layer', 'form'], function () {
            var table = layui.table;
            var layer = layui.layer;
            var form = layui.form;
            var $ = layui.$;

            // 从父窗口获取数据
            var parentData = window.parent.giftModalData || {};
            var orderId = parentData.orderId;
            var existingGifts = parentData.existingGifts || [];

            // 存储所有输入的数量值
            var quantityInputs = {};
            
            // 将已选赠品数据转换为 map
            var existingGiftsMap = {};
            existingGifts.forEach(function (gift) {
                existingGiftsMap[gift.Wh_gift_id] = gift.quantity;
                // 初始化 quantityInputs
                quantityInputs[gift.Wh_gift_id] = gift.quantity;
            });

            // 渲染表格
            table.render({
                elem: '#giftTable',
                url: '/admin/warehouse_gifts/list',
                method: 'post',
                height: 'full-80',
                page: true,
                limit: 10,
                cols: [[
                    { field: 'ID', title: '库存ID', sort: true },
                    { field: 'Gift_id', title: '商品ID', sort: true },
                    { field: 'Name', title: '商品名称', sort: true },
                    { field: 'Price', title: '单价', sort: true },
                    { field: 'Quantity', title: '库存量', sort: true },
                    { field: 'Shelf', title: '存放位置', sort: true },
                    {
                        title: '选择数量', width: 120, templet: function (d) {
                            var value = quantityInputs[d.ID] || '';
                            return '<input type="number" class="layui-input gift-quantity" ' +
                                   'data-id="' + d.ID + '" value="' + value + '" min="0">';
                        }
                    }
                ]],
                response: {
                    statusName: 'code',
                    statusCode: 200,
                    msgName: 'msg',
                    countName: 'count',
                    dataName: 'data'
                },
                done: function (res) {
                    // 为新渲染的输入框绑定事件
                    $('.gift-quantity').each(function () {
                        var input = $(this);
                        var id = parseInt(input.data('id'));

                        // 设置已存储的值
                        if (quantityInputs[id]) {
                            input.val(quantityInputs[id]);
                        }

                        // 绑定输入事件
                        input.on('input', function () {
                            var max = parseInt($(this).closest('tr').find('td[data-field="Quantity"]').text());
                            var val = parseInt($(this).val()) || 0;
                            if (val > max) {
                                layer.msg('不能超过库存量', { icon: 2 });
                                $(this).val(max);
                                val = max;
                            }
                            // 存储输入值
                            quantityInputs[id] = val;
                        });
                    });
                }
            });

            // 提交按钮点击事件
            $('#submitBtn').on('click', function () {
                var selectedData = [];

                // 遍历所有存储的数量值
                for (var id in quantityInputs) {
                    var quantity = parseInt(quantityInputs[id]);
                    if (quantity && quantity > 0) {
                        selectedData.push({
                            wh_gift_id: parseInt(id),
                            quantity: quantity
                        });
                    }
                }

                if (selectedData.length === 0) {
                    layer.msg('请至少选择一个赠品并输入数量', { icon: 2 });
                    return;
                }

                // 按 wh_gift_id 倒序排序
                selectedData.sort(function(a, b) {
                    return b.wh_gift_id - a.wh_gift_id;
                });

                // 直接保存到数据库
                $.ajax({
                    url: '/admin/order/edit_gift_save',
                    type: 'POST',
                    data: {
                        ord_id: orderId,
                        gifts: JSON.stringify(selectedData)
                    },
                    success: function(res) {
                        if (res.code === 200) {
                            layer.msg('保存成功', { icon: 1 });
                            window.parent.loadGiftData();
                            var index = parent.layer.getFrameIndex(window.name);
                            parent.layer.close(index);
                            parent.layer.msg('赠品数据保存成功', { icon: 1 });
                        } else {
                            console.error('保存失败：', res);
                            layer.msg(res.msg, { icon: 2 });
                        }
                    },
                    error: function(xhr) {
                        console.error('请求失败：', xhr.responseText);
                        try {
                            var response = JSON.parse(xhr.responseText);
                            layer.msg('保存失败：' + response.msg, { icon: 2 });
                        } catch (e) {
                            layer.msg('保存失败：' + xhr.responseText, { icon: 2 });
                        }
                    }
                });
            });
        });
    </script>
</body>

</html>