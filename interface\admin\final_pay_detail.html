<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 尾款明细</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
    <style>
        /* 高级质感 */
        .premium-section {
            margin-bottom: 15px;
            padding: 15px;
            border-radius: 8px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        }

        .final-pay-pic {
            max-width: 100%;
            display: block;
            margin: 0 auto;
            cursor: pointer;
        }

        .final-pay-pic img {
            border-radius: 10px;
        }

        .image-preview-container-normal img {
            margin: 0 !important;
        }

        /* 玻璃拟态效果 */
        .glassmorphism {
            background: rgba(255, 255, 255, 0.7);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.18);
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.15);
            padding: 15px;
            margin-bottom: 15px;
        }

        /* 动画效果 */
        .animated-value {
            transition: all 0.3s ease;
        }

        /* 金额显示区域 */
        .amount-display {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 15px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .amount-item {
            padding: 8px;
            text-align: center;
        }

        .amount-item:not(:last-child) {
            border-right: 1px dashed rgba(0, 0, 0, 0.1);
        }

        .amount-label {
            color: #666;
            font-weight: 500;
            font-size: 13px;
            margin-bottom: 3px;
        }

        .amount-value {
            font-weight: 600;
            color: #333;
            font-size: 16px;
        }

        .final-amount {
            color: #FF5722;
            font-size: 18px;
        }

        /* 图片预览区域 */
        .image-preview-container-normal {
            min-height: 120px;
            max-height: 180px;
            border: 1px dashed #e6e6e6;
            border-radius: 4px;
            padding: 8px;
            background-color: #fafafa;
            overflow: auto;
        }

        /* 紧凑布局 */
        .compact-card .layui-card-body {
            padding: 10px 15px;
        }

        /* 图片容器高度限制 */
        .image-preview-item img {
            max-height: 180px;
        }

        /* 标题样式优化 */
        .section-title {
            margin-bottom: 10px;
            font-size: 14px;
            font-weight: 500;
        }

        /* 尾款结算方式标签样式 */
        .payment-type-tag {
            position: absolute;
            top: -20px;
            right: -10px;
            z-index: 10;
            padding: 1px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 500;
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
            transform: scale(0.9);
            transition: all 0.3s ease;
            white-space: nowrap;
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
        }

        .payment-type-tag:hover {
            transform: scale(1);
        }

        .payment-type-tag.full {
            background: linear-gradient(135deg, rgba(46, 125, 50, 0.9) 0%, rgba(76, 175, 80, 0.9) 100%);
            color: white;
            border: 1px solid rgba(76, 175, 80, 0.5);
        }

        .payment-type-tag.partial {
            background: linear-gradient(135deg, rgba(211, 47, 47, 0.9) 0%, rgba(229, 115, 115, 0.9) 100%);
            color: white;
            border: 1px solid rgba(229, 115, 115, 0.5);
        }

        .payment-type-tag i {
            margin-right: 3px;
            font-size: 11px;
        }

        /* 日志项样式 */
        .log-item {
            padding: 8px 10px;
            margin-bottom: 8px;
            border-radius: 6px;
            background-color: #f5f5f5;
            border-left: 3px solid #1E9FFF;
            font-size: 13px;
        }

        .log-item:last-child {
            margin-bottom: 0;
        }

        .log-item .log-content {
            margin-bottom: 4px;
        }

        .log-item .log-meta {
            display: flex;
            justify-content: space-between;
            color: #999;
            font-size: 12px;
        }

        .log-item .log-operator {
            margin-right: 10px;
        }

        .image-preview-container-normal img {
            width: auto;
            height: 130px;
        }
    </style>
</head>

<body>
    <div class="layui-padding-2">
        <div class="layui-form" lay-filter="myform">
            <!-- 尾款明细卡片 -->
            <div class="layui-card compact-card">
                <div class="layui-card-body">
                    <!-- 金额信息显示 - 水平布局 -->
                    <div class="amount-display glassmorphism">
                        <div class="layui-row">
                            <div class="layui-col-xs3 amount-item">
                                <div class="amount-label">订单总额</div>
                                <div class="amount-value">¥<span id="display-total">0.00</span></div>
                            </div>
                            <div class="layui-col-xs3 amount-item">
                                <div class="amount-label">已预付款</div>
                                <div class="amount-value">¥<span id="display-prepaid">0.00</span></div>
                            </div>
                            <div class="layui-col-xs3 amount-item">
                                <div class="amount-label">应付尾款</div>
                                <div class="amount-value">¥<span id="display-final" class="animated-value">0.00</span>
                                </div>
                            </div>
                            <div class="layui-col-xs3 amount-item" style="position: relative;">
                                <div class="amount-label">实付尾款</div>
                                <div class="amount-value final-amount">¥<span id="display-actual-final"
                                        class="animated-value">0.00</span></div>
                                <!-- 尾款结算方式标签 -->
                                <div class="payment-type-tag" id="payment-type-tag">
                                    <i class="layui-icon"></i> <span id="payment-type-text">加载中...</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 收款截图预览 - 独立模块 -->
                    <div class="premium-section">
                        <div class="section-title">
                            <i class="layui-icon layui-icon-picture"></i> 收款截图
                        </div>
                        <div class="image-preview-container-normal" id="image-container"></div>
                    </div>

                    <!-- 尾款日志 - 独立模块 -->
                    <div class="premium-section" id="final-pay-remark-container" style="display: none;">
                        <div class="section-title">
                            <i class="layui-icon layui-icon-note"></i> 尾款日志
                        </div>
                        <div id="final-pay-remark-content" style="color: #333; line-height: 1.6;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        layui.use(['layer'], function () {
            var layer = layui.layer;
            var $ = layui.$;

            // 获取URL中的订单ID参数
            var orderId = request.get('id');

            // 检查订单ID是否有效
            if (!orderId) {
                layer.msg('订单ID无效', { icon: 2 });
                return;
            }

            // 格式化金额显示，保留两位小数
            function formatMoney(amount) {
                return parseFloat(amount || 0).toFixed(2);
            }

            // 从后端获取尾款明细数据
            layer.load(2);
            $.ajax({
                url: '/admin/order/pay_detail',
                type: 'POST',
                data: { id: orderId },
                success: function (res) {
                    layer.closeAll('loading');
                    if (res.code === 200) {
                        var orderData = res.data;

                        // 计算应付尾款
                        var finalPayAmount = orderData.Total_money - orderData.Pre_pay;

                        // 初始化金额显示
                        $('#display-total').text(formatMoney(orderData.Total_money));
                        $('#display-prepaid').text(formatMoney(orderData.Pre_pay));
                        $('#display-final').text(formatMoney(finalPayAmount));
                        $('#display-actual-final').text(formatMoney(orderData.Final_pay || 0));

                        // 设置尾款结算方式标签
                        var paymentTypeTag = $('#payment-type-tag');
                        var paymentTypeText = $('#payment-type-text');

                        if (orderData.Final_is_full === 1) {
                            paymentTypeTag.addClass('full').removeClass('partial');
                            paymentTypeText.text('全额');
                            paymentTypeTag.find('i').addClass('layui-icon-ok-circle');
                        } else {
                            paymentTypeTag.addClass('partial').removeClass('full');
                            paymentTypeText.text('非全额');
                            paymentTypeTag.find('i').addClass('layui-icon-rmb');
                        }

                        // 处理尾款日志
                        if (orderData.Final_pay_remark && orderData.Final_pay_remark.trim() !== '') {
                            // 清空原有内容
                            $('#final-pay-remark-content').empty();

                            // 按 ### 分割日志
                            var logs = orderData.Final_pay_remark.split('###').filter(function (log) {
                                return log.trim() !== '';
                            });

                            // 遍历日志并添加到容器
                            if (logs.length > 0) {
                                logs.forEach(function (log) {
                                    // 按 | 分割每条日志的内容、操作者ID和时间
                                    var logParts = log.split('|');
                                    var logContent = logParts[0] || '';
                                    var operatorId = logParts[1] || '';
                                    var operateTime = logParts[2] || '';
                                    var final_money = logParts[3] || '';//当时实付尾款

                                    // 创建日志项元素
                                    var logItem = $('<div class="log-item"></div>');
                                    logItem.append('<div class="log-content">' + logContent + '</div>');

                                    // 添加操作者和时间信息
                                    var logMeta = $('<div class="log-meta"></div>');
                                    if (operatorId) {
                                        logMeta.append('<span class="log-operator">操作者ID: ' + operatorId + '</span>');
                                    }
                                    if (final_money) {
                                        logMeta.append('<span class="log-operator">当时实付: ￥' + final_money + '</span>');
                                    }
                                    if (operateTime) {
                                        logMeta.append('<span class="log-time">' + operateTime + '</span>');
                                    }

                                    logItem.append(logMeta);
                                    $('#final-pay-remark-content').append(logItem);
                                });

                                $('#final-pay-remark-container').show();
                            }
                        }

                        // 显示收款截图
                        var payPic = orderData.Final_pay_pic || '';
                        if (payPic && payPic !== 'undefined') {
                            // 添加图片路径前缀
                            var fullImagePath = "/static/uploads/normal_pics/order/" + payPic;
                            appendImagePreview('image-container', fullImagePath);
                        } else {
                            $('#image-container').html('<div class="layui-text" style="text-align: center; padding: 15px 0; color: #999;"><i class="layui-icon layui-icon-picture" style="font-size: 24px;"></i><p style="margin-top: 8px;">暂无收款截图</p></div>');
                        }
                    } else {
                        layer.msg(res.msg || '获取尾款明细失败', { icon: 2 });
                    }
                },
                error: function (xhr) {
                    layer.closeAll('loading');
                    layer.msg(xhr.responseJSON ? xhr.responseJSON.msg : '网络错误', { icon: 2 });
                }
            });

            // 辅助函数：添加图片预览
            function appendImagePreview(containerId, filepath) {
                var container = $('#' + containerId);
                container.empty();

                // 创建图片预览元素，添加可点击链接
                var imageItem = $(`
                    <div class="image-preview-item">
                        <a href="${filepath}" target="_blank" title="点击查看大图" class="final-pay-pic">
                            <img src="${filepath}" alt="收款截图">
                        </a>
                    </div>
                `);

                container.append(imageItem);
            }
        });
    </script>
</body>

</html>