<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 病历创建</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/layui/layarea_lc.js"></script>
    <script src="/dist/js/main.js"></script>
    <style>
        .has_account {
            display: none;
        }

        .layui-input-block input,
        .layui-form-select {
            max-width: 100% !important;
        }

        .chooseUser {
            box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
        }

        /* 图片预览容器样式 */
        .image-preview-container {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-top: 15px;
        }

        .image-preview-item {
            position: relative;
            width: 150px;
            height: 150px;
            background-color: #f5f5f5;
            border-radius: 4px;
            overflow: hidden;
        }

        .image-preview-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 4px;
            cursor: pointer;
        }

        .image-preview-item .delete-btn {
            position: absolute;
            bottom: 5px;
            left: 50%;
            transform: translateX(-50%);
            padding: 2px 10px;
            background-color: rgba(255, 255, 255, 0.9);
            border: 1px solid #ddd;
            border-radius: 3px;
            cursor: pointer;
        }

        .delete-btn:hover {
            background-color: #f56c6c;
            color: white;
        }
    </style>
</head>

<body>
    <div class="layui-layout layui-layout-admin">
        <!-- 头部 -->
        <div class="layui-header">
            <div class="layui-logo">幸年堂</div>
            <ul class="layui-nav layui-layout-right">
                <li class="layui-nav-item" style="background-color:transparent!important;padding-right:10px">
                    <a href="javascript:;" style="display:flex;align-items:center;color:#fff"><img
                            src="/dist/images/user.png" class="layui-nav-img"><span class="user_name"></span></a>
                    <dl class="layui-nav-child pop_menu">
                        <dd><i class="iconfont">&#xe62f;</i><a href="change_password.html#7">用户资料</a></dd>
                        <dd><i class="iconfont">&#xe601;</i><a href="javascript:void(0)" class="refresh_cache">刷新缓存</a>
                        </dd>
                        <dd><i class="iconfont">&#xe634;</i><a href="login_out.html">退出登录</a></dd>
                    </dl>
                </li>
            </ul>
        </div>
        <!-- 导航 -->
        <ul class="layui-nav layui-nav-tree layui-nav-side" id="menuContainer" lay-bar="disabled" lay-accordion>
            <div style="margin: 50px 0;color: #bbb;text-align: center;">菜单加载中...</div>
        </ul>
        <!-- 主体内容 -->
        <div class="layui-body">
            <div class="body_child">
                <div class="layui-panel">
                    <div class="layui-card-header">
                        <div class="layui-row" style="padding-top:10px;">
                            <div class="layui-col-md8">病历创建</div>
                            <!-- <div class="layui-col-md4" style="text-align: right;">
                                <button type="submit" class="layui-btn layui-btn-fluid create_btn" lay-submit=""
                                    style="max-width: 200px;">
                                    已有帐号，新建家庭成员？
                                </button>
                            </div> -->

                        </div>
                    </div>
                    <div style="min-height: 800px;padding: 10px 32px 32px 32px;">

                        <form class="layui-form" lay-filter="form_show_patient_infos" onsubmit="return false">

                            <div
                                style="font-size: 15px;font-weight: bold;margin:10px 0 20px 0;border-bottom: 1px solid #efefef;padding: 10px;">
                                基本信息
                            </div>

                            <div class="layui-row">
                                <div class="layui-col-md6" id="patient_info_before_choose">
                                    <div class="layui-input-wrap">
                                        <div class="layui-input-prefix">
                                            <i class="layui-icon layui-icon-username"></i>
                                        </div>
                                        <input type="text" name="wd" id="dropdown_input" autocomplete="off"
                                            lay-affix="clear" placeholder="模糊搜索患者，电话号码或姓名均可"
                                            class="layui-input chooseUser">
                                    </div>
                                </div>






                                <div id="patient_info_after_choose">
                                    <div class="layui-form-item">
                                        <button
                                            class="layui-btn layui-btn-primary layui-border-blue choose_patient">重新选择患者</button>
                                        <button
                                            class="layui-btn layui-btn-primary layui-border-blue edit_patient_info">修改已选患者信息</button>
                                    </div>
                                    <!-- 第一行 -->
                                    <div class="layui-row">
                                        <div class="layui-col-md4">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">姓名</label>
                                                <div class="layui-input-block">
                                                    <input type="text" name="Name" placeholder="请输入姓名"
                                                        class="layui-input" disabled>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="layui-col-md4">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">性别</label>
                                                <div class="layui-input-block">
                                                    <select name="Sex" disabled>
                                                        <option value="1">男</option>
                                                        <option value="0">女</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="layui-col-md4">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">出生日期</label>
                                                <div class="layui-input-block">
                                                    <input type="text" name="Born_date" disabled placeholder="请输入内容"
                                                        autocomplete="off" class="layui-input" id="ID-laydate-demo">
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 第二行 -->
                                    <div class="layui-row">
                                        <div class="layui-col-md4">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">手机号</label>
                                                <div class="layui-input-block">
                                                    <input type="text" name="Phone" placeholder="请输入手机号"
                                                        class="layui-input" disabled>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="layui-col-md4">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">身份证号</label>
                                                <div class="layui-input-block">
                                                    <input type="text" name="Idcard" placeholder="请输入身份证号"
                                                        class="layui-input" disabled>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="layui-col-md4">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">微信号</label>
                                                <div class="layui-input-block">
                                                    <input type="text" name="Weixin" placeholder="请输入微信号"
                                                        class="layui-input" disabled>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 第三行 -->
                                    <div class="layui-row">
                                        <div class="layui-col-md4">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">医保卡号</label>
                                                <div class="layui-input-block">
                                                    <input type="text" name="Ins_card_num" placeholder="请输入医保卡号"
                                                        class="layui-input" disabled>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="layui-col-md4">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">参保类型</label>
                                                <div class="layui-input-block">
                                                    <select name="Ins_type" disabled>
                                                        <option value="1">类型1</option>
                                                        <option value="2">类型2</option>
                                                        <option value="3">类型3</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="layui-col-md4">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">婚否</label>
                                                <div class="layui-input-block">
                                                    <select name="IsMarried" disabled>
                                                        <option value="1">已婚</option>
                                                        <option value="0">未婚</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 第四行 -->
                                    <div class="layui-row">
                                        <div class="layui-col-md4">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">身高</label>
                                                <div class="layui-input-block">
                                                    <input type="number" name="Height" placeholder="请输入身高(cm)"
                                                        class="layui-input" disabled>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="layui-col-md4">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">体重</label>
                                                <div class="layui-input-block">
                                                    <input type="number" name="Weight" placeholder="请输入体重(kg)"
                                                        class="layui-input" disabled>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="layui-col-md4">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">地址</label>
                                                <div class="layui-input-block">
                                                    <input type="text" name="Address" placeholder="请输入地址"
                                                        class="layui-input" disabled>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 第五行 -->
                                    <div class="layui-row">
                                        <div class="layui-col-md4">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">过敏史</label>
                                                <div class="layui-input-block">
                                                    <textarea name="Allergies" placeholder="请输入过敏史"
                                                        class="layui-textarea" disabled></textarea>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="layui-col-md4">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">既往史</label>
                                                <div class="layui-input-block">
                                                    <textarea name="Medical_history" placeholder="请输入既往史"
                                                        class="layui-textarea" disabled></textarea>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="layui-col-md4">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">主诉</label>
                                                <div class="layui-input-block">
                                                    <textarea name="Chief_complaint" placeholder="请输入主诉"
                                                        class="layui-textarea" disabled></textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 第六行 -->
                                    <div class="layui-row">
                                        <div class="layui-col-md4">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">客户来源</label>
                                                <div class="layui-input-block">
                                                    <select name="Patient_from" id="Patient_from" disabled>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="layui-col-md4">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">用户级别</label>
                                                <div class="layui-input-block">
                                                    <select name="Level" disabled>
                                                        <option value="A">A级</option>
                                                        <option value="B">B级</option>
                                                        <option value="C">C级</option>
                                                        <option value="D">D级</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="layui-col-md4">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">用户状态</label>
                                                <div class="layui-input-block">
                                                    <select name="Status" disabled>
                                                        <option value="1">正常</option>
                                                        <option value="0">异常</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>




















                                </div>
                            </div>
                        </form>

                        <form class="layui-form" lay-filter="form_add" onsubmit="return false">
                            <div
                                style="font-size: 15px;font-weight: bold;margin:10px 0 20px 0;border-bottom: 1px solid #efefef;padding: 10px;">
                                <span class="red_star">*</span>病历信息
                            </div>

                            <!-- 布局容器：医生和医助两列布局 -->
                            <div class="layui-row" id="doctor_assistant_row">
                                <!-- 医生与科室 - 占据左侧50% -->
                                <div class="layui-col-md6">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">医生与科室</label>
                                        <div class="layui-input-block">
                                            <div class="layui-col-md5" style="margin-right: 10px;">
                                                <select name="department_id" id="keshi" lay-filter="keshi"
                                                    lay-verify="required"></select>
                                            </div>
                                            <div class="layui-col-md5"><select name="doc_id" id="doctor"
                                                    lay-verify="required"></select></div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 医助信息 - 占据右侧50% -->
                                <div class="layui-col-md6" id="assistant_display_area">
                                    <!-- 医助角色自动填充区域 -->
                                    <div class="layui-form-item" id="assistant_role" style="display:none;">
                                        <label class="layui-form-label">医助</label>
                                        <div class="layui-input-block">
                                            <div class="layui-col-md5" style="margin-right: 10px;">
                                                <input type="text" id="assistant_name_display" readonly
                                                    class="layui-input" style="background-color: #eee;">
                                                <input type="hidden" name="asst_id" id="assistant_id_hidden">
                                            </div>
                                            <div class="layui-col-md6">
                                                <div class="layui-form-mid layui-word-aux">* 您已登录医助帐号，默认为您的ID</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 非医助角色科室选择区域 - 独立行 -->
                            <div class="layui-form-item" id="non_assistant_role">
                                <label class="layui-form-label">医助与科室</label>
                                <div class="layui-input-block">
                                    <div class="layui-col-md3" style="margin-right: 10px;">
                                        <select name="asst_dep_id" id="asst_dep_name"
                                            lay-filter="asst_dep_name"></select>
                                    </div>
                                    <div class="layui-col-md3"><select name="asst_id" id="asst_name"></select></div>
                                    <div class="layui-col-md3">
                                        <div class="layui-form-mid layui-word-aux" style="margin-left: 10px;">*
                                            超级管理员时才有此选项</div>
                                    </div>
                                </div>
                            </div>



                            <div
                                style="font-size: 15px;font-weight: bold;margin:10px 0 20px 0;border-bottom: 1px solid #efefef;padding: 10px;">
                                病历选填
                            </div>

                            <div class="layui-row">
                                <div class="layui-col-md6">


                                    <div class="layui-form-item" id="non_assistant_role">

                                        <label class="layui-form-label">意向剂型</label>
                                        <div style="display: flex;">
                                            <div style="display: flex;">
                                                <input type="text" name="tx_day" class="layui-input"
                                                    oninput="if(value>100)value=100">
                                                <div style="width:28px;padding: 9px 10px">天</div>
                                            </div>
                                            <div style="display: flex;">
                                                <div style="width:38px;padding: 9px 10px">剂型</div>
                                                <select name="tx_type">
                                                    <option value=""></option>
                                                    <option value="膏滋">膏滋</option>
                                                    <option value="丸剂">丸剂</option>
                                                    <option value="汤剂">汤剂</option>
                                                    <option value="散剂">散剂</option>
                                                    <option value="无糖膏">无糖膏</option>
                                                    <option value="水丸">水丸</option>
                                                    <option value="内服粉剂">内服粉剂</option>
                                                    <option value="外用粉剂">外用粉剂</option>
                                                    <option value="中药材">中药材</option>
                                                    <option value="清膏">清膏</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>


                                    <div class="layui-form-item">
                                        <label class="layui-form-label">有无三高</label>
                                        <div class="layui-input-block">
                                            <input type="checkbox" name="triad_input" value="高血压" title="高血压"
                                                lay-skin="primary">
                                            <input type="checkbox" name="triad_input" value="高血糖" title="高血糖"
                                                lay-skin="primary">
                                            <input type="checkbox" name="triad_input" value="高血脂" title="高血脂"
                                                lay-skin="primary">
                                            <input type="checkbox" name="triad_input" value="无" title="无"
                                                lay-skin="primary">
                                            <input type="hidden" value="">
                                        </div>
                                    </div>



                                    <div class="layui-form-item">
                                        <label class="layui-form-label">患者主诉</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="chief_complaint" placeholder="请输入患者主诉症状"
                                                autocomplete="off" class="layui-input">
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">复诊主诉</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="re_chief_complaint" placeholder="请输入复 诊主诉信息"
                                                autocomplete="off" class="layui-input">
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">大小便情况</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="urination" placeholder="请输入大小便情况，如正常、便秘、腹泻等"
                                                autocomplete="off" class="layui-input">
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">既往病史</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="past_medical_history"
                                                placeholder="请输入既往病史，如高血压、糖尿病等" autocomplete="off" class="layui-input">
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">用药史</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="past_medication_history"
                                                placeholder="请输入用药史，如长期服用降压药等" autocomplete="off" class="layui-input">
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">舌象描述</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="tonguedesc"
                                                placeholder="请输入舌象描述" autocomplete="off" class="layui-input">
                                        </div>
                                    </div>
                                    <div class="layui-form-item">

                                        <label class="layui-form-label smalltext">上次用药时间</label>
                                        <div class="layui-input-block">
                                            <div class="layui-input-wrap">
                                                <div class="layui-input-prefix">
                                                    <i class="layui-icon layui-icon-date"></i>
                                                </div>
                                                <input type="text" name="last_medication_time" id="last_medication_time"
                                                    placeholder="请选择上次用药时间" autocomplete="off" class="layui-input"
                                                    readonly>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                                <div class="layui-col-md6">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label smalltext">上次用药情况</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="last_medical" placeholder="请输入目前需要的治疗方法"
                                                autocomplete="off" class="layui-input">
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">现需治疗</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="now_needs" placeholder="请输入目前需要的治疗方法"
                                                autocomplete="off" class="layui-input">
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">现病史</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="history_of_present_illness"
                                                placeholder="暂不删，以防后续需要，已设置非必填" autocomplete="off" class="layui-input">
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">个人史</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="personal_history" placeholder="暂不删，以防后续需要，已设置非必填"
                                                autocomplete="off" class="layui-input">
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">家族史</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="family_history" placeholder="暂不删，以防后续需要，已设置非必填"
                                                autocomplete="off" class="layui-input">
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">过敏史</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="allergy_history" placeholder="暂不删，以防后续需要，已设置非必填"
                                                autocomplete="off" class="layui-input">
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">诊断信息</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="diagnosis_information"
                                                placeholder="暂不删，以防后续需要，已设置非必填" autocomplete="off" class="layui-input">
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">治疗方案</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="treatment_plan" placeholder="暂不删，以防后续需要，已设置非必填"
                                                autocomplete="off" class="layui-input">
                                        </div>
                                    </div>
                                </div>
                            </div>









                            <div class="layui-row">
                                <div class="layui-col-md6">
                                    <!-- 舌苔照 -->
                                    <div class="layui-upload layui-padding-4">
                                        <button type="button" class="layui-btn upload_big_btn"
                                            id="ID-upload-demo-btn-1">
                                            <div class="btn_big_font"><i
                                                    class="layui-icon layui-icon-upload btn_big_font"></i> 舌苔照上传
                                            </div>
                                            <div>jpg、png、jpeg、bmp、gif格式，2M以内</div>
                                            <div>可点选上传和拖拽上传</div>
                                        </button>
                                        <div class="image-preview-container" id="tongue-photo-container"></div>
                                    </div>
                                </div>


                                <div class="layui-col-md6">
                                    <!-- 检查单 -->
                                    <div class="layui-upload layui-padding-4">
                                        <button type="button" class="layui-btn upload_big_btn"
                                            id="ID-upload-demo-btn-2">
                                            <div class="btn_big_font"><i
                                                    class="layui-icon layui-icon-upload btn_big_font"></i> 检查单上传
                                            </div>
                                            <div>jpg、png、jpeg、bmp、gif格式，2M以内</div>
                                            <div>可点选上传和拖拽上传</div>
                                        </button>
                                        <div class="image-preview-container" id="sheet-photo-container"></div>
                                    </div>
                                </div>
                            </div>








                            <div style="display: flex; justify-content: center;margin-top: 50px;">
                                <button class="layui-btn" lay-submit lay-filter="formSubmitBtn" id="formSubmitBtn"
                                    style="margin-right: 50px;">确认新建</button>
                                <button type="reset" class="layui-btn layui-btn-primary"
                                    onclick="history.go(-1)">取消</button>
                            </div>

                        </form>


                    </div>
                </div>
                <br>
                <br>
            </div>
        </div>
    </div>


    <script>
        layui.config({
            base: './mods/'
            , version: '1.0'
        });
        layui.use(['element', 'layer', 'util'], function () {
            var element = layui.element;
            var layer = layui.layer;
            var form = layui.form;
            var util = layui.util;
            var treeTable = layui.treeTable;
            var laydate = layui.laydate;
            var dropdown = layui.dropdown;
            var $ = layui.$;
            var upload = layui.upload;
            var pat_pro_id = 0;
            layarea_lc = layui.layarea_lc;
            laydate.render({
                elem: '#ID-laydate-demo,#last_medication_time'
            });
            var has_account = false;
            // 页面顶部插入缓存的用户数据
            let local_userinfo = localStorage.getItem('local_userinfo');
            let user_name = "";
            if (local_userinfo) {
                local_userinfo = JSON.parse(local_userinfo);
                user_name = local_userinfo.Name + " ( " + local_userinfo.Roles + " )";

                // 判断用户角色，如果是医助角色则显示对应界面
                if (local_userinfo.Role_ids === "3" || local_userinfo.Role_ids === "9") {
                    // 显示医助角色界面
                    $('#assistant_role').show();
                    $('#non_assistant_role').hide();

                    // 设置医助姓名和ID到表单元素
                    $('#assistant_name_display').val(local_userinfo.Name);
                    $('#assistant_id_hidden').val(local_userinfo.Id);
                } else {
                    // 显示正常的科室选择界面
                    $('#assistant_role').hide();
                    $('#non_assistant_role').show();
                }
            } else {
                user_name = "未登录";
            }
            $('.user_name').text(user_name);
            render_menu($);
            $('.refresh_cache').click(function () {
                refresh_cache($);
            });
            // 顶部 - 渲染科室下拉表
            layer.load(2);
            $.ajax({
                url: '/admin/department/list',
                data: {
                    name: '医疗部门',
                    store_id: global_default_store_id,
                },
                type: 'post',
                success: function (res) {
                    layer.closeAll('loading');
                    if (res.code === 200) {
                        let data = res.data;
                        // 将科室固定为"中医内科"(ID为3)
                        let html = '';
                        for (let i = 0; i < data.length; i++) {
                            if (data[i].id === 3 || data[i].name === "中医内科") {
                                html = '<option value="' + data[i].id + '" selected>' + data[i].name + '</option>';
                                break;
                            }
                        }
                        // 如果没有找到中医内科，使用固定值
                        if (html === '') {
                            html = '<option value="3" selected>中医内科</option>';
                        }
                        $('#keshi').html(html);
                        // 设置下拉菜单为禁用状态
                        $('#keshi').prop('disabled', true);
                        form.render('select');

                        // 自动触发获取中医内科医生的请求
                        layer.load(2);
                        $.ajax({
                            url: '/admin/user/list_low',
                            data: {
                                role_id: 4,
                                department_id: 3,  // 中医内科的ID
                            },
                            type: 'post',
                            success: function (res) {
                                layer.closeAll('loading');
                                if (res.code === 200) {
                                    let data = res.data;
                                    let html = '<option value="">选择医生</option>';
                                    if (data) {
                                        for (let i = 0; i < data.length; i++) {
                                            html += '<option value="' + data[i].ID + '">' + data[i].Name + '</option>';
                                        }
                                    } else {
                                        layer.msg('该科室暂无医生', { icon: 2, time: 1000 });
                                        html = '<option value="">该科室暂无医生</option>';
                                    }
                                    $('#doctor').html(html);
                                    form.render('select');
                                } else {
                                    layer.msg(res.msg, { icon: 2, time: 1000 });
                                }
                            }, error: function (res) {
                                layer.closeAll('loading');
                                layer.msg(res.responseJSON.msg, { icon: 2, time: 1000 });
                            }
                        });

                        // 保留原有的 form.on 事件，但实际上由于下拉框被禁用了，这个事件不会被触发
                        form.on('select(keshi)', function (data) {
                            let keshi_id = data.value;
                            if (keshi_id) {
                                layer.load(2);
                                $.ajax({
                                    url: '/admin/user/list_low',
                                    data: {
                                        role_id: 4,
                                        department_id: keshi_id,
                                    },
                                    type: 'post',
                                    success: function (res) {
                                        layer.closeAll('loading');
                                        if (res.code === 200) {
                                            let data = res.data;
                                            let html = '<option value="">选择医生</option>';
                                            if (data) {
                                                for (let i = 0; i < data.length; i++) {
                                                    html += '<option value="' + data[i].ID + '">' + data[i].Name + '</option>';
                                                }
                                            } else {
                                                layer.msg('该科室暂无医生', { icon: 2, time: 1000 });
                                                html = '<option value="">该科室暂无医生</option>';
                                            }
                                            $('#doctor').html(html);
                                            form.render('select');
                                        } else {
                                            layer.msg(res.msg, { icon: 2, time: 1000 });
                                        }
                                    }, error: function (res) {
                                        layer.closeAll('loading');
                                        layer.msg(res.responseJSON.msg, { icon: 2, time: 1000 });
                                    }
                                });
                            }
                        });
                    } else {
                        layer.msg(res.msg, { icon: 2, time: 1000 });
                    }
                }, error: function (res) {
                    layer.closeAll('loading');
                    layer.msg(res.responseJSON.msg, { icon: 2, time: 1000 });
                }
            });


            // 渲染医助下拉 - 只有超级管理员时才需要，因为超级管理员需要选择哪个医助来创建病历
            layer.load(2);
            $.ajax({
                url: '/admin/department/list',
                data: {
                    name: '售前部门',
                    store_id: global_default_store_id,
                },
                type: 'post',
                success: function (res) {
                    layer.closeAll('loading');
                    if (res.code === 200) {
                        let data = res.data;
                        let html = '<option value="">选择科室</option>';
                        for (let i = 0; i < data.length; i++) {
                            html += '<option value="' + data[i].id + '">' + data[i].name + '</option>';
                        }
                        $('#asst_dep_name').html(html);
                        form.render('select');
                        form.on('select(asst_dep_name)', function (data) {
                            let asst_dep_id = data.value;
                            if (asst_dep_id) {
                                layer.load(2);
                                $.ajax({
                                    url: '/admin/user/list_low',
                                    data: {
                                        role_id: 3,
                                        department_id: asst_dep_id,
                                    },
                                    type: 'post',
                                    success: function (res) {
                                        layer.closeAll('loading');
                                        if (res.code === 200) {
                                            let data = res.data;
                                            let html = '<option value="">选择医助</option>';
                                            if (data) {
                                                for (let i = 0; i < data.length; i++) {
                                                    html += '<option value="' + data[i].ID + '">' + data[i].Name + '</option>';
                                                }
                                            } else {
                                                layer.msg('该科室暂无医生', { icon: 2, time: 1000 });
                                                html = '<option value="">该科室暂无医助</option>';
                                            }
                                            $('#asst_name').html(html);
                                            form.render('select');
                                        } else {
                                            layer.msg(res.msg, { icon: 2, time: 1000 });
                                        }
                                    }, error: function (res) {
                                        layer.closeAll('loading');
                                        layer.msg(res.responseJSON.msg, { icon: 2, time: 1000 });
                                    }
                                });
                            } else {
                                $('#asst_name').html('<option value="">选择医助</option>');
                                form.render('select');
                            }
                        });
                    } else {
                        layer.msg(res.msg, { icon: 2, time: 1000 });
                    }
                }, error: function (res) {
                    layer.closeAll('loading');
                    layer.msg(res.responseJSON.msg, { icon: 2, time: 1000 });
                }
            });

            //渲染客户来源下拉框
            $.each(Patient_From, function (index, value) {
                // console.log(index, value);
                $('#Patient_from').append($('<option>').val(index).text(value));
            });
            //监听提交
            form.on('submit(formSubmitBtn)', function (data) {
                let formData = new FormData();
                for (let key in data.field) {
                    formData.append(key, data.field[key]);
                }
                // 处理"有无三高"复选框
                // if ($("input:checkbox[name='triad_input']:checked").length == 0) {
                //     layer.msg('请选择是否有三高', { icon: 2, time: 1000 });
                //     return;
                // }
                let triad_arr = [];
                $("input:checkbox[name='triad_input']:checked").each(function (i) {
                    triad_arr[i] = $(this).val();
                });
                let triad = triad_arr.join(",");
                formData.append('triad', triad);
                // 舌苔照
                let tongue_photos = [];
                $('#tongue-photo-container .image-preview-item').each(function () {
                    let tongue_photo = $(this).data('filename');
                    tongue_photo = tongue_photo.replace(/[\r\n]/g, '');
                    tongue_photos.push(tongue_photo);
                });
                let tongue_photos_str = tongue_photos.join('\n');
                formData.append('tongue_photos', tongue_photos_str);
                // 检查单
                let sheet_photos = [];
                $('#sheet-photo-container .image-preview-item').each(function () {
                    let sheet_photo = $(this).data('filename');
                    sheet_photo = sheet_photo.replace(/[\r\n]/g, '');
                    sheet_photos.push(sheet_photo);
                });
                let sheet_photos_str = sheet_photos.join('\n');
                formData.append('sheet_photos', sheet_photos_str);
                // 添加患者ID
                formData.append('pat_pro_id', pat_pro_id);
                layer.load(2);
                $.ajax({
                    url: serverUrl + "/admin/patient_records/add",
                    type: "post",
                    data: formData,
                    processData: false, // 不处理数据
                    contentType: false, // 不设置内容类型
                    success: function (res) {
                        layer.closeAll('loading');
                        if (res.code == 200) {
                            layer.msg(res.msg, {
                                time: 2000,
                                icon: 1
                            }, function () {
                                layer.confirm('是否继续添加？', {
                                    btn: ['继续添加', '返回列表', '取消']
                                }, function () {
                                    location.reload();
                                }, function () {
                                    window.location.href = "patient_records_list.html";
                                });
                            }
                            );
                        } else {
                            layer.msg(res.msg, {
                                time: 2000,
                                icon: 2
                            });
                        }
                    },
                    error: function (res) {
                        console.log(res);
                        layer.closeAll('loading');
                        layer.msg(res.responseJSON.msg, {
                            time: 5000,
                            icon: 2
                        });
                    }
                });
                return false;
            });
            //模糊搜索用户的下拉框组件
            var inst = dropdown.render({
                elem: '#dropdown_input',
                trigger: 'keyup click',
                data: [],
                className: 'my-dropdown',
                style: '',
                click: function (data) {
                    if (!isEmpty(data)) {
                        this.elem.val(data.id + ' | ' + data.name + ' | ' + data.phone + ' | ' + (data.sex == 1 ? '男' : '女') + ' | ' + date2age(data.born_date) + '岁');
                        pat_pro_id = data.id;
                    }
                    // ajax加载选择的用户数据
                    pushPatientProfileData(data.id);
                }
            });
            $(inst.config.elem).on('input propertychange', function () {
                var elem = $(this);
                var value = elem.val().trim();
                if (value.length > 1) {
                    // 如果value是纯数字
                    let go_post = false
                    if (/^\d+$/.test(value)) {
                        if (value.length == 7 || value.length == 11 || value.length == 13) {
                            go_post = true
                        }
                    } else {
                        go_post = true
                    }
                    if (go_post) {
                        $.ajax({
                            url: "/admin/patient_profile/patient_profile_phone2id",
                            type: 'post',
                            dataType: 'json',
                            data: {
                                "wd": value,
                            },
                            success: function (response) {
                                let data = response.data;
                                if (data && data.length > 0) {
                                    dropdown.reloadData(inst.config.id, {
                                        data: data,
                                        templet: function (d) {
                                            var exp = new RegExp(value, 'gi');
                                            return ('<div>' + d.id + '</div><div>' + d.name + '</div><div>' + d.phone + '</div><div>' + (d.sex == 1 ? '男' : '女') + '</div><div>' + date2age(d.born_date) + '岁</div>').replace(exp, function (str) {
                                                return '<span style="color: red;">' + str + '</span>';
                                            });
                                        }
                                    });
                                } else {
                                    dropdown.reloadData(inst.config.id, {
                                        data: [],
                                    });
                                }
                            },
                            error: function (err) {
                                dropdown.reloadData(inst.config.id, {
                                    data: [],
                                });
                            }
                        });
                    }
                } else {
                    dropdown.reloadData(inst.config.id, {
                        data: [],
                    });
                }
            });

            // ajax加载选择的用户数据
            var pushPatientProfileData = function (id) {
                if (id) {
                    layer.load(2);
                    $.ajax({
                        url: serverUrl + "/admin/patient_profile/detail",
                        type: "post",
                        data: {
                            id: id,
                        },
                        success: function (res) {
                            layer.closeAll('loading');
                            // console.log(res);
                            if (res.code == 200) {
                                let data = res.data;
                                data.Address = data.Address.replace('|', '');
                                data.Born_date = data.Born_date.split('T')[0];
                                form.val('form_show_patient_infos', data);
                                $('#patient_info_before_choose').hide(300);
                                $('#patient_info_after_choose').show(600);
                                let Doc_id = res.data.Doc_id;
                                if (Doc_id) {
                                    $('#doctor').val(Doc_id);
                                    form.render('select');
                                }
                            };
                        }, error: function (res) {
                            layer.closeAll('loading');
                            layer.msg(res.responseJSON.msg, {
                                time: 5000,
                                icon: 2
                            });
                        }
                    });
                }
            };
            $('.choose_patient').click(function () {
                $('#patient_info_before_choose').show(300);
                $('#patient_info_after_choose').hide(600);
            });
            $('.edit_patient_info').click(function () {
                window.location.href = "patient_profile_edit.html?id=" + pat_pro_id + '#/admin/patient_records_add.html';
            });




            // 图片上传部分 - 开始
            // 图片预览函数
            function appendImagePreview(containerId, filepath, filename) {
                const container = $(`#${containerId}`);
                const imageItem = $(`
                    <div class="image-preview-item" data-filename="${filename}" data-filepath="${filepath}">
                        <img src="${filepath}">
                        <div class="delete-btn">删除</div>
                    </div>
                `);

                // 点击图片查看大图
                imageItem.find('img').on('click', function () {
                    layer.photos({
                        photos: {
                            title: '查看图片',
                            data: [{ src: filepath }]
                        },
                        footer: false
                    });
                });

                // 点击删除按钮删除图片
                imageItem.find('.delete-btn').on('click', function () {
                    const category = filepath.split('/')[4]; // 从路径中提取类别
                    layer.confirm('确定删除该图片吗？', { icon: 3, title: '提示' }, function (index) {
                        $.ajax({
                            url: '/admin/normal_pic_del',
                            type: 'POST',
                            data: {
                                filename: filename,
                                category: category
                            },
                            success: function (res) {
                                if (res.code === 200) {
                                    imageItem.remove();
                                    layer.msg('删除成功', { icon: 1, time: 1000 });
                                }
                            },
                            error: function (data) {
                                layer.confirm('删除失败: ' + data.responseJSON.msg + '，是否移除该图片？',
                                    { icon: 3, title: '提示' },
                                    function (index) {
                                        imageItem.remove();
                                        layer.msg('移除成功', { icon: 1, time: 1000 });
                                    }
                                );
                            }
                        });
                        layer.close(index);
                    });
                });

                container.append(imageItem);
            }

            // 初始化舌苔照上传
            upload.render({
                elem: '#ID-upload-demo-btn-1',
                url: '/admin/upload_normal_pic',
                multiple: true,
                data: {
                    category: 'photo_tongue',
                },
                drag: true,
                before: function(obj) {
                    // 预览回调
                },
                done: function(res) {
                    if (res.code === 200) {
                        const fileInfo = res.data[0];
                        appendImagePreview('tongue-photo-container', fileInfo.filepath, fileInfo.filename);
                        layer.msg('上传成功', {icon: 1, time: 1000});
                    } else {
                        layer.msg('上传失败: ' + res.msg, {icon: 2, time: 1000});
                    }
                },
                error: function() {
                    layer.msg('上传失败', {icon: 2, time: 1000});
                }
            });

            // 初始化检查单上传
            upload.render({
                elem: '#ID-upload-demo-btn-2',
                url: '/admin/upload_normal_pic',
                multiple: true,
                data: {
                    category: 'photo_sheet',
                },
                drag: true,
                before: function(obj) {
                    // 预览回调
                },
                done: function(res) {
                    if (res.code === 200) {
                        const fileInfo = res.data[0];
                        appendImagePreview('sheet-photo-container', fileInfo.filepath, fileInfo.filename);
                        layer.msg('上传成功', {icon: 1, time: 1000});
                    } else {
                        layer.msg('上传失败: ' + res.msg, {icon: 2, time: 1000});
                    }
                },
                error: function() {
                    layer.msg('上传失败', {icon: 2, time: 1000});
                }
            });
            // 图片上传部分 - 结束

            // 添加自定义验证规则
            form.verify({
                triad_required: function (value, item) {
                    if ($("input:checkbox[name='triad_input']:checked").length == 0) {
                        return '请选择是否有三高';
                    }
                }
            });

        });
    </script>
</body>

</html>