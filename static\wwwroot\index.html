<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>API</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <script type="text/javascript" src="/static/static/jquery-3.7.1.min.js"></script>
    <script type="text/javascript">
        function getCurrentTime() {
          const now = new Date();
          const hours = String(now.getHours()).padStart(2, '0');
          const minutes = String(now.getMinutes()).padStart(2, '0');
          const seconds = String(now.getSeconds()).padStart(2, '0');
          return `${hours}:${minutes}:${seconds}`;
        }
    </script>
    <style type="text/css">
        .header{
            display: flex;
            justify-content: space-between;
            margin: 3px 0;
        }
        pre{
            line-height: 20px;
        }
        h3{
            padding: 0;
            margin: 0;
        }
        .part{
            padding: 10px;
            line-height: 35px;
        }
        .right_space{
            display: flex;
        }
        .right_space span{
            font-size: 14px;
            margin: 0 10px;
            cursor: pointer;
        }
        .right_space input{
            width: 100px;
            margin: 0 5px;
        }
        .btn{
            background-color: #3ec315;
            color: white;
            border: 0;
            padding: 0 30px;
            cursor: pointer;
        }
        .api{
            background-color: #fffcad;
            padding: 0 10px;
        }
        .result{
            background-color: #d3ffc9;
            padding: 0 10px;
            margin: 3px 0;
            max-height: 250px;
            overflow-y: auto;
            position: relative;
            display: none;
        }
        .time{
            background-color: #3ec315;
            color: white;
            font-size: 12px;
            position: absolute;
            right: 10px;
            top: 8px;
            line-height: 15px;
            padding: 2px 5px;
            border-radius: 5px;
        }
    </style>
</head>

<body>

<div class="part">
    <div class="header">
        <h3>角色列表</h3>
        <div class="right_space">
            <span>关闭</span>
            <button class="btn">测试</button>
        </div>
    </div>
    <div class="api">/admin/roles/list</div>
    <div class="result"></div>
</div>

<div class="part">
    <div class="header">
        <h3>用户列表</h3>
        <div class="right_space">
            <span>关闭</span>
            <button class="btn">测试</button>
        </div>
    </div>
    <div class="api">/admin/user/list</div>
    <div class="result"></div>
</div>

<div class="part">
    <div class="header">
        <h3>权限列表</h3>
        <div class="right_space">
            <span>关闭</span>
            <button class="btn">测试</button>
        </div>
    </div>
    <div class="api">/admin/perm/list</div>
    <div class="result"></div>
</div>

<div class="part">
    <div class="header">
        <h3>用户登陆</h3>
        <div class="right_space">
            <span>关闭</span>
            <input type="text" name="user" value="13888888888">
            <input type="password" name="pwd" value="123456">
            <button class="btn">测试</button>
        </div>
    </div>
    <div class="api">/admin/perm/list</div>
    <div class="result"></div>
</div>

<script type="text/javascript">
$(function(){
    $('.right_space span').click(function(){
        $(this).parent().parent().next().next().hide(300);
    });
    $('.btn').click(function(){
        $(this).parent().parent().next().next().show(300);
    });
    $('.btn:eq(0)').click(function(){
        let index = $('.btn').index($(this));
        let time = getCurrentTime();
        time = "<div class='time'>"+ time +"</div>";
        $.ajax({
            url: "/admin/roles/list",
            type: "POST",
            data: {},
            success: function(response) {
                let data = JSON.stringify(response.data,null,2);
                $('.result:eq('+ index +')').html(time + '<pre>' + data + '</pre>');
            },
            error: function(xhr, status, error) {
                const errorResponse = JSON.parse(xhr.responseText);
                $('.result:eq('+ index +')').html(time + errorResponse.msg);
            }
        });
    });

    $('.btn:eq(1)').click(function(){
        let index = $('.btn').index($(this));
        let time = getCurrentTime();
        time = "<div class='time'>"+ time +"</div>";
        $.ajax({
            url: "/admin/user/list",
            type: "POST",
            data: {},
            success: function(response) {
                let data = JSON.stringify(response.data,null,2);
                $('.result:eq('+ index +')').html(time + '<pre>' + data + '</pre>');
            },
            error: function(xhr, status, error) {
                const errorResponse = JSON.parse(xhr.responseText);
                $('.result:eq('+ index +')').html(time + errorResponse.msg);
            }
        });
    });

    $('.btn:eq(2)').click(function(){
        let index = $('.btn').index($(this));
        let time = getCurrentTime();
        time = "<div class='time'>"+ time +"</div>";
        $.ajax({
            url: "/admin/perm/list",
            type: "POST",
            data: {},
            success: function(response) {
                let data = JSON.stringify(response.data,null,2);
                $('.result:eq('+ index +')').html(time + '<pre>' + data + '</pre>');
            },
            error: function(xhr, status, error) {
                const errorResponse = JSON.parse(xhr.responseText);
                $('.result:eq('+ index +')').html(time + errorResponse.msg);
            }
        });
    });

    $('.btn:eq(3)').click(function(){
        let index = $('.btn').index($(this));
        let time = getCurrentTime();
        time = "<div class='time'>"+ time +"</div>";
        const credentials = btoa($('input[name=user]').val() + ':' + $('input[name=pwd]').val());
        $.ajax({
            url: "/admin/user/login",
            type: "POST",
            headers: {
                'Authorization': 'Basic ' + credentials
            },
            data: {},
            success: function(response) {
                let data = JSON.stringify(response.data,null,2);
                $('.result:eq('+ index +')').html(time + '<pre>' + data + '</pre>');
            },
            error: function(xhr, status, error) {
                const errorResponse = JSON.parse(xhr.responseText);
                $('.result:eq('+ index +')').html(time + errorResponse.msg);
            }
        });
    });


});
</script>

    



</body>
</html>