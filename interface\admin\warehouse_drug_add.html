<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>药品采购入库</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <script src="/dist/layui/layui.js"></script>
    <style>
        .layui-table-cell {
            height: auto;
            line-height: 28px;
        }

        .layui-form-select {
            margin-bottom: 10px;
        }

        .layui-input-inline {
            width: 300px !important;
        }

        .unit-label {
            line-height: 38px;
            margin-left: 5px;
        }

        .action-cell,
        .status-cell {
            text-align: center;
        }

        .msg_box_style {
            margin-bottom: 15px;
            display: flex;
            padding: 20px;
            justify-content: space-around;
            align-items: center;
            font-size: 15px;
        }
    </style>
</head>

<body style="padding: 10px;">
    <div class="layui-form">
        <!-- 厂商选择下拉框 -->
        <div class="layui-form-item">
            <label class="layui-form-label">选择厂商</label>
            <div class="layui-input-inline">
                <select name="supplier" lay-filter="supplier" lay-search>
                    <option value="">请选择厂商</option>
                </select>
            </div>
        </div>
    </div>

    <div style="display: flex; justify-content: center; margin-top: 20px;">
        <div
            style="width:300px;overflow-y: auto;background-color: #f8f8f8;padding: 10px;border-right: 1px solid #e6e6e6;">
            <table class="layui-table" lay-size="sm">
                <thead>
                    <tr>
                        <th style="width: 50%;">药材名称</th>
                        <th style="width: 25%;text-align: center;">类型</th>
                        <th style="width: 25%;text-align: center;">状态</th>
                    </tr>
                </thead>
                <tbody id="selectedDrugs">
                    <!-- 动态插入已选择的药材 -->
                </tbody>
            </table>
        </div>
        <div style="flex: 1;">
            <table id="medsTable" lay-filter="medsTable"></table>
        </div>
    </div>

    <div
        style="position: fixed; bottom: 0; left: 0; right: 0; background-color: #fff; padding: 10px; text-align: center; box-shadow: 0 -2px 10px rgba(0,0,0,0.1);">
        <button type="button" class="layui-btn" id="submitBtn">采购入库</button>
        <button type="button" class="layui-btn layui-btn-primary" id="cancelBtn">取消</button>
    </div>

    <!-- 在body最后添加进度条容器 -->
    <div id="uploadProgress"
        style="display: none; position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 20px; border-radius: 4px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); width: 400px; z-index: 999;">
        <div style="margin-bottom: 10px;">正在处理药材入库...</div>
        <div class="layui-progress layui-progress-big" lay-filter="upload-progress" lay-showPercent="true">
            <div class="layui-progress-bar" lay-percent="0%"></div>
        </div>
    </div>

    <!-- 添加结果统计对话框的模板 -->
    <script type="text/html" id="resultDialogTpl">
        <div style="padding: 20px;">
            <div class="msg_box_style">
                <p>总计处理：{{d.total}}条</p>
                <p>成功入库：<span style="color: #5FB878;">{{d.success}}条</span></p>
                <p>入库失败：<span style="color: #FF5722;">{{d.fail}}条</span></p>
            </div>
            <div style="text-align: center;">
                <button type="button" class="layui-btn" onclick="continueUpload()">继续入库</button>
                <button type="button" class="layui-btn layui-btn-primary" onclick="exitUpload()">退出采购</button>
            </div>
        </div>
    </script>

    <script>
        layui.use(['table', 'layer', 'form', 'element'], function () {
            var table = layui.table;
            var layer = layui.layer;
            var form = layui.form;
            var element = layui.element;
            var $ = layui.$;

            // 存储用户输入数据的对象（跨分页保存）
            var userInputData = {};
            // 存储默认批次号
            var defaultBatch = generateBatch();

            // 生成批次号（Linux时间戳）
            function generateBatch() {
                return Math.floor(Date.now() / 1000).toString();
            }

            // 防抖函数
            function debounce(func, wait) {
                var timeout;
                return function () {
                    var context = this,
                        args = arguments;
                    clearTimeout(timeout);
                    timeout = setTimeout(function () {
                        func.apply(context, args);
                    }, wait);
                };
            }

            // 更新左侧已选药材列表
            function updateSelectedDrugs(drugId, drugName, status) {
                var $selectedDrug = $('#selectedDrugs').find('[data-id="' + drugId + '"]');
                if (status) {
                    if ($selectedDrug.length === 0) {
                        var statusIcon = '<i class="layui-icon layui-icon-time"></i>';
                        // 检查药材名称
                        $.ajax({
                            url: '/admin/warehouse_drug/check_by_id',
                            async: false,
                            type: 'POST',
                            data: { id: drugId },
                            success: function (res) {
                                var action = res.code === 200 ? '补货' : '新增';
                                var newRow = '<tr data-id="' + drugId + '">' +
                                    '<td>' + drugName + '</td>' +
                                    '<td class="action-cell">' + action + '</td>' +
                                    '<td class="status-cell">' + statusIcon + '</td>' +
                                    '</tr>';
                                $('#selectedDrugs').append(newRow);
                            },
                            error: function () {
                                layer.msg('检查药材名称失败', { icon: 2 });
                            }
                        });
                        // return;
                        // $('#selectedDrugs').append(newRow);
                    }
                } else {
                    $selectedDrug.remove();
                }
            }

            // 防抖处理函数
            var debouncedUpdate = debounce(function ($input) {
                var id = $input.data('id');
                var $row = $input.closest('tr');
                var drugName = $row.find('td[data-field="Name"]').text();

                if (!userInputData[id]) {
                    userInputData[id] = {
                        validity_days: parseInt(table.cache.medsTable.find(item => item.ID === parseInt(id))?.Validity_Days || 0),
                        unit: table.cache.medsTable.find(item => item.ID === parseInt(id))?.Unit || ''
                    };
                }

                if ($input.hasClass('price-input')) {
                    userInputData[id].price = $input.val();
                } else if ($input.hasClass('quantity-input')) {
                    userInputData[id].quantity = $input.val();
                } else if ($input.hasClass('batch-input')) {
                    userInputData[id].batch = $input.val();
                }

                // 检查是否所有必填字段都已填写
                // 由于批次号有默认值，只需要检查价格和数量
                var isComplete = userInputData[id].price &&
                    userInputData[id].quantity;

                updateSelectedDrugs(id, drugName, isComplete);
            }, 100);

            // 加载厂商数据
            $.ajax({
                url: '/admin/supplier_drug/list',
                type: 'POST',
                success: function (res) {
                    if (res.code === 200) {
                        var select = $('select[name="supplier"]');
                        res.data.forEach(function (item) {
                            select.append(new Option(item.Name, item.ID));
                        });
                        form.render('select');
                    }
                }
            });

            // 渲染表格
            table.render({
                elem: '#medsTable',
                url: '/admin/drug/list',
                where: {
                    pid: 0
                },
                limit: 10,
                method: 'post',
                page: true,
                cols: [[
                    {
                        field: 'Name',
                        title: '药材名',
                        align: 'center'
                    },
                    {
                        field: 'batch',
                        title: '批次',
                        templet: function (d) {
                            return '<div class="batch-text" data-id="' + d.ID + '">' + defaultBatch + '</div>';
                        }
                    },
                    {
                        field: 'Validity_Days',
                        title: '效期',
                        width: 70,
                        align: 'center'
                    },
                    {
                        field: 'price',
                        title: '核算价格',
                        templet: function (d) {
                            var value = userInputData[d.ID]?.price || '';
                            return '<input type="number" class="layui-input price-input" data-id="' + d.ID +
                                '" value="' + value + '" step="0.01" min="0">';
                        }
                    },
                    {
                        field: 'quantity',
                        title: '数量',
                        templet: function (d) {
                            var value = userInputData[d.ID]?.quantity || '';
                            return '<div style="display: flex; align-items: center;">' +
                                '<input type="number" class="layui-input quantity-input" data-id="' + d.ID +
                                '" value="' + value + '" step="0.01" min="0">' +
                                '<span class="unit-label">' + d.Unit + '</span>' +
                                '</div>';
                        }
                    }
                ]],
                response: {
                    statusName: 'code',
                    statusCode: 200,
                    msgName: 'msg',
                    countName: 'count',
                    dataName: 'data'
                },
                done: function () {
                    // 绑定输入事件
                    $('.price-input, .quantity-input').on('input', function () {
                        debouncedUpdate($(this));
                    });
                }
            });

            // 确认采购入库
            $('#submitBtn').on('click', function () {
                layer.confirm('是否确认将选定的药材入库？', function (index) {
                    layer.close(index);
                    var selectedSupplier = $('select[name="supplier"]').val();
                    var selectedSupplierName = $('select[name="supplier"] option:selected').text();
                    if (!selectedSupplier) {
                        layer.msg('请选择厂商', { icon: 2 });
                        return;
                    }

                    var $selectedDrugs = $('#selectedDrugs tr');
                    if ($selectedDrugs.length === 0) {
                        layer.msg('请至少选择一个药材并填写完整信息', { icon: 2 });
                        return;
                    }

                    // 显示进度条
                    $('#uploadProgress').show();
                    element.progress('upload-progress', '0%');

                    // 初始化计数器
                    var successCount = 0;
                    var failCount = 0;
                    var totalCount = $selectedDrugs.length;
                    var currentIndex = 0;

                    // 串行处理每个药材
                    function processNextDrug() {
                        if (currentIndex >= totalCount) {
                            // 所有药材处理完成，显示结果对话框
                            $('#uploadProgress').hide();
                            showResultDialog(successCount, failCount, totalCount);
                            return;
                        }

                        var $currentDrug = $($selectedDrugs[currentIndex]);
                        var id = $currentDrug.data('id');

                        // 更新进度条
                        var progress = Math.round((currentIndex + 1) / totalCount * 100);
                        element.progress('upload-progress', progress + '%');

                        // 更新状态为处理中
                        $currentDrug.find('.status-cell').html('<i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i>');

                        $.ajax({
                            url: '/admin/warehouse_drug/in',
                            type: 'POST',
                            data: {
                                drug_id: parseInt(id),
                                supplier_id: parseInt(selectedSupplier),
                                supplier_name: selectedSupplierName,
                                batch: defaultBatch,
                                price: parseFloat(userInputData[id].price),
                                quantity: parseFloat(userInputData[id].quantity),
                                validity_days: userInputData[id].validity_days,
                                unit: userInputData[id].unit
                            },
                            success: function (res) {
                                var $drugRow = $('#selectedDrugs').find('[data-id="' + id + '"]');
                                if (res.code === 200) {
                                    $drugRow.find('.status-cell').html('<i class="layui-icon layui-icon-ok"></i>');
                                    successCount++;
                                } else {
                                    $drugRow.find('.status-cell').html('<i class="layui-icon layui-icon-close"></i>');
                                    failCount++;
                                    layer.msg(res.msg || '药材ID:' + id + ' 入库失败', { icon: 2 });
                                }
                                currentIndex++;
                                processNextDrug();
                            },
                            error: function (res) {
                                var $drugRow = $('#selectedDrugs').find('[data-id="' + id + '"]');
                                $drugRow.find('.status-cell').html('<i class="layui-icon layui-icon-close"></i>');
                                failCount++;
                                layer.msg('药材ID:' + id + ' 网络错误', { icon: 2 });
                                currentIndex++;
                                processNextDrug();
                            }
                        });
                    }

                    // 开始处理第一个药材
                    processNextDrug();
                });
            });

            // 显示结果统计对话框
            function showResultDialog(successCount, failCount, totalCount) {
                var content = layui.laytpl($('#resultDialogTpl').html()).render({
                    total: totalCount,
                    success: successCount,
                    fail: failCount
                });

                layer.open({
                    type: 1,
                    title: '入库完成',
                    content: content,
                    area: ['500px', 'auto'],
                    closeBtn: 0,
                    shadeClose: false
                });
            }

            // 继续上传按钮回调
            window.continueUpload = function () {
                layer.closeAll();
                // 清空已选药材列表和用户输入数据
                $('#selectedDrugs').empty();
                userInputData = {};
                // 重新生成批次号
                defaultBatch = generateBatch();
                // 刷新表格
                table.reload('medsTable');
            };

            // 退出采购按钮回调
            window.exitUpload = function () {
                var index = parent.layer.getFrameIndex(window.name);
                parent.layer.close(index);
                parent.layui.table.reload('mytable');
            };

            // 取消按钮
            $('#cancelBtn').on('click', function () {
                var index = parent.layer.getFrameIndex(window.name);
                parent.layer.close(index);
            });
        });
    </script>
</body>

</html>