<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 在线诊室</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/layui/layarea_lc.js"></script>
    <script src="/dist/js/main.js"></script>
    <script src="/dist/js/xm-select.js"></script>
    <script src="https://web.sdk.qcloud.com/trtc/webrtc/demo/latest/dist/trtc.js"></script>
    <style>
        #control_buttons .iconfont {
            font-size: 18px;
        }

        #control_buttons .layui-btn {
            height: 32px;
            line-height: 32px;
        }

        #bottom_btn {
            display: flex;
            justify-content: center;
            margin-top: 50px;
        }

        .user_info {
            display: flex;
            align-items: center;
        }

        .user_info div {
            font-weight: bold;
            margin-right: 20px;
            font-size: 17px;
        }

        /* 视频容器包装器 */
        .video-container-wrapper {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 0 auto;
            max-width: 100%;
            padding: 20px;
        }

        /* 响应式调整 */
        @media screen and (max-width: 1600px) {
            .video-container-wrapper {
                margin-bottom: 30px;
            }
        }

        @media screen and (max-width: 768px) {
            #control_buttons {
                flex-wrap: wrap;
            }
        }

        #control_buttons {
            display: flex;
            justify-content: center;
            margin-top: 15px;
        }

        .recording {
            background-color: #FF4D4F !important;
        }

        /* 表单容器 */
        .layui-form-item-container {
            padding: 20px;
        }

        /* 图片预览容器样式 */
        .image-preview-container {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-top: 15px;
        }

        .image-preview-item {
            position: relative;
            width: 150px;
            height: 150px;
            background-color: #f5f5f5;
            border-radius: 4px;
            overflow: hidden;
        }

        .image-preview-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 4px;
            cursor: pointer;
        }

        .image-preview-item .delete-btn {
            position: absolute;
            bottom: 5px;
            left: 50%;
            transform: translateX(-50%);
            padding: 2px 10px;
            background-color: rgba(255, 255, 255, 0.9);
            border: 1px solid #ddd;
            border-radius: 3px;
            cursor: pointer;
        }

        .delete-btn:hover {
            background-color: #f56c6c;
            color: white;
        }

        /* 新增视频布局样式 */
        #video_container {
            position: relative;
            width: 100%;
            max-width: 900px;
            height: 712px;
            background-color: #000;
            overflow: hidden;
            box-sizing: border-box;
        }

        /* 视频包装器（通用样式） */
        .video-wrapper {
            position: relative;
            /* 重要：设为相对定位，作为标签的定位参考 */
            overflow: hidden;
            box-sizing: border-box;
        }

        /* 视频元素样式 */
        .video-player {
            width: 100%;
            height: 100%;
            background-color: #333;
            object-fit: cover;
            /* 确保视频填满容器而不变形 */
        }

        /* 全屏模式下的视频样式 */
        .video-player.fullscreen-mode {
            object-fit: contain !important;
            /* 确保显示完整视频，可能会有黑边 */
        }

        /* TRTC在video元素外会包一层div，我们需要确保全屏模式下这个元素内的video也是contain模式 */
        .video-player.fullscreen-mode video,
        .video-player.fullscreen-mode div {
            object-fit: contain !important;
            width: 100% !important;
            height: 100% !important;
            background-color: #000;
        }

        /* 视频角色标签 */
        .video-label {
            position: absolute;
            /* 相对于包装器绝对定位 */
            padding: 4px 8px;
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            font-size: 12px;
            z-index: 10;
            pointer-events: none;
            /* 防止标签拦截点击事件 */
            top: 5px;
            right: 5px;
            /* 统一放置在右上角 */
        }

        /* 背景视频包装器 */
        .video-wrapper.background {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
            border: none;
            overflow: hidden;
            /* 防止内容溢出 */
            background-color: #000;
            /* 确保黑色背景 */
        }

        /* 背景视频的标签 */
        .video-wrapper.background .video-label {
            top: 5px;
            right: 5px;
            bottom: auto;
            left: auto;
            font-size: 14px;
            padding: 5px 10px;
            border-radius: 4px;
            z-index: 11;
            /* 确保标签在小视频之上 */
        }

        /* 小视频包装器通用样式 */
        .video-wrapper.small {
            position: absolute;
            width: 200px;
            height: 190px;
            z-index: 2;
            border: 2px solid #fff;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        /* 小视频的标签 */
        .video-wrapper.small .video-label {
            top: 0;
            right: 0;
            bottom: auto;
            left: auto;
            font-size: 10px;
            padding: 2px 4px;
            border-radius: 0 0 0 4px;
        }

        /* 第一个小视频放在左下角 */
        .video-wrapper.small-1 {
            bottom: 20px;
            left: 20px;
        }

        /* 第二个小视频放在右下角 */
        .video-wrapper.small-2 {
            bottom: 20px;
            right: 20px;
        }

        /* 如果有第三个小视频，放在右上角 */
        .video-wrapper.small-3 {
            top: 20px;
            right: 20px;
        }

        /* 悬停效果 */
        .video-wrapper.small:hover {
            transform: scale(1.05);
            border-color: #1E9FFF;
        }
    </style>
</head>

<body>
    <div class="layui-layout layui-layout-admin">
        <!-- 头部 -->
        <div class="layui-header">
            <div class="layui-logo">幸年堂</div>
            <ul class="layui-nav layui-layout-right">
                <li class="layui-nav-item" style="background-color:transparent!important;padding-right:10px">
                    <a href="javascript:;" style="display:flex;align-items:center;color:#fff"><img
                            src="/dist/images/user.png" class="layui-nav-img"><span class="user_name"></span></a>
                    <dl class="layui-nav-child pop_menu">
                        <dd><i class="iconfont">&#xe62f;</i><a href="change_password.html#7">用户资料</a></dd>
                        <dd><i class="iconfont">&#xe601;</i><a href="javascript:void(0)" class="refresh_cache">刷新缓存</a>
                        </dd>
                        <dd><i class="iconfont">&#xe634;</i><a href="login_out.html">退出登录</a></dd>
                    </dl>
                </li>
            </ul>
        </div>
        <!-- 导航 -->
        <ul class="layui-nav layui-nav-tree layui-nav-side" id="menuContainer" lay-bar="disabled" lay-accordion>
            <div style="margin: 50px 0;color: #bbb;text-align: center;">菜单加载中...</div>
        </ul>
        <!-- 主体内容 -->
        <div class="layui-body">
            <div class="body_child">
                <div class="layui-panel">
                    <!-- <div class="layui-card-header">
                        <div class="layui-row" style="padding-top:10px;">
                            <div class="layui-col-md8">在线诊室</div>
                        </div>
                    </div> -->
                    <div class="layui-padding-4" style="min-height: 800px;">
                        <form class="layui-form" lay-filter="form_edit" onsubmit="return false">
                            <div class="layui-row">
                                <!-- 视频区域 -->
                                <div class="layui-col-xl6">
                                    <div class="video-container-wrapper">
                                        <div id="video_container"></div>
                                        <div id="control_buttons">
                                            <button type="button" class="layui-btn" id="startConsultBtn">
                                                <i class="layui-icon layui-icon-play"></i> 开始
                                            </button>
                                            <button type="button" class="layui-btn layui-btn-danger layui-btn-disabled"
                                                id="endConsultBtn">
                                                <i class="layui-icon layui-icon-close"></i> 结束
                                            </button>
                                            <button type="button" class="layui-btn layui-btn-disabled"
                                                id="startRecordBtn">
                                                <i class="layui-icon layui-icon-video"></i> 录制
                                            </button>
                                            <button type="button" class="layui-btn layui-btn-danger layui-btn-disabled"
                                                id="stopRecordBtn">
                                                <i class="layui-icon layui-icon-close"></i> 结束录制
                                            </button>

                                            <button type="button"
                                                class="layui-btn layui-btn-primary layui-border-blue layui-btn-disabled"
                                                id="patient_fullscreen">
                                                <i class="layui-icon layui-icon-screen-full"></i> 患者全屏
                                            </button>

                                            <button type="button"
                                                class="layui-btn layui-btn-primary layui-border-blue layui-btn-disabled"
                                                id="toggle_camera">
                                                <i class="iconfont">&#xeca6;</i> 摄像头
                                            </button>

                                            <button type="button"
                                                class="layui-btn layui-btn-primary layui-border-blue layui-btn-disabled"
                                                id="toggle_mic">
                                                <i class="iconfont">&#xeca7;</i> 麦克风
                                            </button>

                                        </div>

                                        <div class="layui-badge layui-bg-gray"
                                            style="margin:15px 0 0 0;padding: 10px 20px;">
                                            <i class="layui-icon layui-icon-tips"
                                                style="font-size: 12px; margin-right: 5px;"></i>
                                            提示：1<span class="perm_item_rows">ALT+H</span>全屏患者视频 ；<a
                                                href="/admin/sys_config_detail.html#/admin/rtc_room_list.html"
                                                class="perm_item_rows">NAS配置</a>成功后则上传至NAS，否则下载本地。
                                        </div>


                                    </div>
                                </div>

                                <!-- 表单区域 -->
                                <div class="layui-col-xl6">
                                    <div class="layui-form-item-container">
                                        <div class="layui-row">
                                            <div class="layui-col-md6">
                                                <div class="layui-form-item">
                                                    <label class="layui-form-label">患者姓名</label>
                                                    <div class="layui-input-block user_info">
                                                        <!-- 帐号ID：
                                                        <div class="user_info_0"></div>
                                                        用户ID：
                                                        <div class="user_info_1"></div>
                                                        用户姓名：
                                                        -->
                                                        <div class="user_info_2"></div>
                                                    </div>
                                                </div>

                                                <div class="layui-form-item">
                                                    <label class="layui-form-label">病历ID</label>
                                                    <div class="layui-input-block">
                                                        <div class="record_id layui-form-mid layui-word-aux"></div>
                                                    </div>
                                                </div>

                                                <div class="layui-form-item">
                                                    <label class="layui-form-label">有无三高</label>
                                                    <div class="layui-input-block">
                                                        <div id="triad_select"></div>
                                                        <!-- 隐藏的输入字段用于保存实际值 -->
                                                        <input type="hidden" name="triad" id="triad_hidden">
                                                    </div>
                                                </div>

                                                <div class="layui-form-item">
                                                    <label class="layui-form-label">意向剂型</label>
                                                    <div class="layui-input-block">
                                                        <div class="layui-col-md6"
                                                            style="width: 130px;margin-right: 10px;">
                                                            <select name="tx_type">
                                                                <option value="">请选择</option>
                                                                <option value="膏滋">膏滋</option>
                                                                <option value="丸剂">丸剂</option>
                                                                <option value="汤剂">汤剂</option>
                                                                <option value="散剂">散剂</option>
                                                                <option value="无糖膏">无糖膏</option>
                                                                <option value="水丸">水丸</option>
                                                                <option value="内服粉剂">内服粉剂</option>
                                                                <option value="外用粉剂">外用粉剂</option>
                                                                <option value="中药材">中药材</option>
                                                                <option value="清膏">清膏</option>
                                                            </select>
                                                        </div>
                                                        <div class="layui-col-md6" style="width: 130px;">
                                                            <select name="tx_day">
                                                                <option value="">请选择</option>
                                                                <option value="1">1天</option>
                                                                <option value="2">2天</option>
                                                                <option value="3">3天</option>
                                                                <option value="4">4天</option>
                                                                <option value="5">5天</option>
                                                                <option value="6">6天</option>
                                                                <option value="7">7天</option>
                                                                <option value="8">8天</option>
                                                                <option value="9">9天</option>
                                                                <option value="10">10天</option>
                                                                <option value="11">11天</option>
                                                                <option value="12">12天</option>
                                                                <option value="13">13天</option>
                                                                <option value="14">14天</option>
                                                                <option value="15">15天</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="layui-form-item">
                                                    <label class="layui-form-label">舌象描述</label>
                                                    <div class="layui-input-block">
                                                        <input type="text" name="tonguedesc" placeholder="请输入患者舌象描述"
                                                            autocomplete="off" class="layui-input">
                                                    </div>
                                                </div>
                                                <div class="layui-form-item">
                                                    <label class="layui-form-label">患者主诉</label>
                                                    <div class="layui-input-block">
                                                        <input type="text" name="chief_complaint"
                                                            placeholder="请输入患者主诉症状" autocomplete="off"
                                                            class="layui-input">
                                                    </div>
                                                </div>
                                                <div class="layui-form-item">
                                                    <label class="layui-form-label">复诊主诉</label>
                                                    <div class="layui-input-block">
                                                        <input type="text" name="re_chief_complaint"
                                                            placeholder="请输入复 诊主诉信息" autocomplete="off"
                                                            class="layui-input">
                                                    </div>
                                                </div>
                                                <div class="layui-form-item">
                                                    <label class="layui-form-label">大小便情况</label>
                                                    <div class="layui-input-block">
                                                        <input type="text" name="urination"
                                                            placeholder="请输入大小便情况，如正常、便秘、腹泻等" autocomplete="off"
                                                            class="layui-input">
                                                    </div>
                                                </div>
                                                <div class="layui-form-item">
                                                    <label class="layui-form-label">既往病史</label>
                                                    <div class="layui-input-block">
                                                        <input type="text" name="past_medical_history"
                                                            placeholder="请输入既往病史，如高血压、糖尿病等" autocomplete="off"
                                                            class="layui-input">
                                                    </div>
                                                </div>
                                                <div class="layui-form-item">
                                                    <label class="layui-form-label">用药史</label>
                                                    <div class="layui-input-block">
                                                        <input type="text" name="past_medication_history"
                                                            placeholder="请输入用药史，如长期服用降压药等" autocomplete="off"
                                                            class="layui-input">
                                                    </div>
                                                </div>

                                            </div>
                                            <div class="layui-col-md6">
                                                <div class="layui-form-item">
                                                    <label class="layui-form-label smalltext">上次用药时间</label>
                                                    <div class="layui-input-block">
                                                        <div class="layui-input-wrap">
                                                            <div class="layui-input-prefix">
                                                                <i class="layui-icon layui-icon-date"></i>
                                                            </div>
                                                            <input type="text" name="last_medication_time"
                                                                id="last_medication_time" placeholder="请选择上次用药时间"
                                                                autocomplete="off" class="layui-input" readonly>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="layui-form-item">
                                                    <label class="layui-form-label smalltext">上次用药情况</label>
                                                    <div class="layui-input-block">
                                                        <input type="text" name="last_medical"
                                                            placeholder="请输入目前需要的治疗方法" autocomplete="off"
                                                            class="layui-input">
                                                    </div>
                                                </div>
                                                <div class="layui-form-item">
                                                    <label class="layui-form-label">现需治疗</label>
                                                    <div class="layui-input-block">
                                                        <input type="text" name="now_needs" placeholder="请输入目前需要的治疗方法"
                                                            autocomplete="off" class="layui-input">
                                                    </div>
                                                </div>
                                                <div class="layui-form-item">
                                                    <label class="layui-form-label">现病史</label>
                                                    <div class="layui-input-block">
                                                        <input type="text" name="history_of_present_illness"
                                                            placeholder="原型图不见这个字段了，要留吗？" autocomplete="off"
                                                            class="layui-input">
                                                    </div>
                                                </div>
                                                <div class="layui-form-item">
                                                    <label class="layui-form-label">个人史</label>
                                                    <div class="layui-input-block">
                                                        <input type="text" name="personal_history"
                                                            placeholder="原型图不见这个字段了，要留吗？" autocomplete="off"
                                                            class="layui-input">
                                                    </div>
                                                </div>
                                                <div class="layui-form-item">
                                                    <label class="layui-form-label">家族史</label>
                                                    <div class="layui-input-block">
                                                        <input type="text" name="family_history"
                                                            placeholder="原型图不见这个字段了，要留吗？" autocomplete="off"
                                                            class="layui-input">
                                                    </div>
                                                </div>
                                                <div class="layui-form-item">
                                                    <label class="layui-form-label">过敏史</label>
                                                    <div class="layui-input-block">
                                                        <input type="text" name="allergy_history"
                                                            placeholder="原型图不见这个字段了，要留吗？" autocomplete="off"
                                                            class="layui-input">
                                                    </div>
                                                </div>
                                                <div class="layui-form-item">
                                                    <label class="layui-form-label">诊断信息</label>
                                                    <div class="layui-input-block">
                                                        <input type="text" name="diagnosis_information"
                                                            placeholder="原型图不见这个字段了，要留吗？" autocomplete="off"
                                                            class="layui-input">
                                                    </div>
                                                </div>
                                                <div class="layui-form-item">
                                                    <label class="layui-form-label">治疗方案</label>
                                                    <div class="layui-input-block">
                                                        <input type="text" name="treatment_plan"
                                                            placeholder="原型图不见这个字段了，要留吗？" autocomplete="off"
                                                            class="layui-input">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                </div>



                                <div class="layui-form-item">
                                    <div class="layui-col-md6">
                                        <!-- 舌苔照 -->
                                        <div class="layui-upload layui-padding-4">
                                            <button type="button" class="layui-btn upload_big_btn"
                                                id="ID-upload-demo-btn-1">
                                                <div class="btn_big_font">
                                                    <i class="layui-icon layui-icon-upload btn_big_font"></i> 舌苔照上传
                                                </div>
                                                <div>jpg、png、jpeg、bmp、gif格式，2M以内</div>
                                                <div>可点选上传和拖拽上传</div>
                                            </button>
                                            <div class="image-preview-container" id="tongue-photo-container"></div>
                                        </div>
                                    </div>
                                    <div class="layui-col-md6">
                                        <!-- 检查单 -->
                                        <div class="layui-upload layui-padding-4">
                                            <button type="button" class="layui-btn upload_big_btn"
                                                id="ID-upload-demo-btn-2">
                                                <div class="btn_big_font">
                                                    <i class="layui-icon layui-icon-upload btn_big_font"></i> 检查单上传
                                                </div>
                                                <div>jpg、png、jpeg、bmp、gif格式，2M以内</div>
                                                <div>可点选上传和拖拽上传</div>
                                            </button>
                                            <div class="image-preview-container" id="sheet-photo-container"></div>
                                        </div>
                                    </div>
                                </div>

                                <div id="bottom_btn">
                                    <button class="layui-btn layui-btn-disabled" lay-submit lay-filter="formSubmitBtn"
                                        id="formSubmitBtn" style="margin-right: 50px;" disabled>提交数据并完成问诊</button>
                                    <button type="reset" class="layui-btn layui-btn-primary"
                                        onclick="history.go(-1)">取消</button>
                                </div>



                            </div>
                        </form>
                    </div>
                </div>
                <br>
                <br>
            </div>
        </div>
    </div>

    <script>
        layui.config({
            base: './mods/'
            , version: '1.0'
        });
        layui.use(['element', 'layer', 'util', 'form', 'treeTable', 'laydate', 'dropdown', 'upload'], function () {
            var element = layui.element;
            var layer = layui.layer;
            var form = layui.form;
            var util = layui.util;
            var treeTable = layui.treeTable;
            var laydate = layui.laydate;
            var dropdown = layui.dropdown;
            var $ = layui.$;
            var upload = layui.upload;
            var record_id = 0;
            var doctor_id = 0;
            var patientName = ''; // 添加患者姓名变量
            var doctorName = ''; // 添加医生姓名变量
            var rtc_auto_record = 0;//自动录制默认状态为不录制
            var id = request.get('id');
            if (!id) {
                layer.msg('参数错误', { icon: 2, time: 1000 });
                return false;
            }
            layarea_lc = layui.layarea_lc;
            laydate.render({
                elem: '#ID-laydate-demo,#last_medication_time'
            });
            // 页面顶部插入缓存的用户数据
            let local_userinfo = localStorage.getItem('local_userinfo');
            let user_name = "";
            if (local_userinfo) {
                local_userinfo = JSON.parse(local_userinfo);
                user_name = local_userinfo.Name + " ( " + local_userinfo.Roles + " )";
                doctor_id = local_userinfo.Id;
                // 获取医生姓名
                $.ajax({
                    url: serverUrl + "/admin/user/list_low",
                    type: "post",
                    data: {
                        id: doctor_id
                    },
                    success: function (res) {
                        if (res.code == 200 && res.data.length > 0) {
                            doctorName = res.data[0].Name;
                            console.log('获取到医生姓名:', doctorName);
                        }
                    }
                });
            } else {
                user_name = "未登录";
            }
            $('.user_name').text(user_name);
            render_menu($);
            $('.refresh_cache').click(function () {
                refresh_cache($);
            });
            // 获取自动录制开关
            $.ajax({
                url: serverUrl + "/admin/sys_config/rtc_auto_record_detail",
                type: "post",
                async: false,
                success: function (res) {
                    if (res.code == 200) {
                        rtc_auto_record = res.data;
                    }
                },
            });
            //通过诊室ID获取病历ID
            layer.load(2);
            $.ajax({
                url: serverUrl + "/admin/rtc_room/detail",
                type: "post",
                async: false,
                data: {
                    id: id,
                },
                success: function (res) {
                    if (res.code == 200) {
                        record_id = res.data.Record_id;
                        $('.record_id').html('<a href="patient_records_show.html?id=' + record_id + '#/admin/rtc_room_list.html" target=_blank class="btn_arg_pm">B' + record_id + '</a>');
                        if (res.data.Status === 0) {
                            // 待诊诊室，医生可以开始问诊
                            $('#startConsultBtn').removeClass('layui-btn-disabled');
                            $('#endConsultBtn').addClass('layui-btn-disabled');
                            $('#startRecordBtn').addClass('layui-btn-disabled');
                            $('#stopRecordBtn').addClass('layui-btn-disabled');
                        } else if (res.data.Status === 1) {
                            $('#bottom_btn').hide();
                            $('#startConsultBtn').addClass('layui-btn-disabled');
                            layer.msg('该诊室已结束', { icon: 2, time: 1000 });
                            return false;
                        }
                    } else {
                        layer.msg(res.msg, { icon: 2, time: 1000 });
                    }
                },
                error: function (res) {
                    layer.msg('获取诊室详情失败', { icon: 2, time: 1000 });
                }
            });
            // 获取病历详情（接口/admin/patient_records/detail），并回填表单
            $.ajax({
                url: serverUrl + "/admin/patient_records/detail",
                type: "post",
                data: {
                    id: record_id,
                },
                success: function (res) {
                    layer.closeAll('loading');
                    if (res.code == 200) {
                        let data = res.data;
                        console.log(data);
                        form.val('form_edit', {
                            'chief_complaint': data.Chief_complaint, // 患者主诉
                            'history_of_present_illness': data.History_of_present_illness, // 现病史
                            'past_medical_history': data.Past_medical_history, // 既往病史
                            'personal_history': data.Personal_history, // 个人史
                            'family_history': data.Family_history, // 家族史
                            'allergy_history': data.Allergy_history, // 过敏史
                            'diagnosis_information': data.Diagnosis_information, // 诊断信息
                            'treatment_plan': data.Treatment_plan, // 治疗方案
                            'discharge_time': data.Discharge_time, // 出院时间
                            // 'status': data.Status, // 诊室状态
                            'create_time': data.Create_time, // 创建时间
                            'pre_id': data.Pre_id, // 预先ID
                            'department_id': data.Department_id, // 科室ID
                            // 'triad': data.Triad, // 三高情况
                            'tx_day': data.Tx_day, // 意向剂型天数
                            'tx_type': data.Tx_type, // 意向剂型
                            're_chief_complaint': data.Re_chief_complaint, // 复诊主诉
                            'urination': data.Urination, // 大小便情况
                            'now_needs': data.Now_needs, // 现需治疗
                            'last_medical': data.Last_medical, // 上次用药情况
                            'last_medication_time': data.Last_medication_time.split('T')[0], // 上次用药时间
                            'past_medication_history': data.Past_medication_history,//用药史
                            'tonguedesc': data.Tonguedesc == '0' ? '' : data.Tonguedesc,//舌象描述
                        });
                        //用户基础信息，帐号ID，用户ID、姓名
                        $('.user_info_0').text(data.Pat_id);
                        $('.user_info_1').text(data.Pat_pro_id);
                        $('.user_info_2').text(data.Name);
                        patientName = data.Name; // 保存患者姓名
                        // 回填"三高"复选框
                        // const triadValues = data.Triad.split(','); // 获取复选框的值
                        // triadValues.forEach(value => {
                        //     const checkbox = document.querySelector(`input[name="triad_input"][title="${value}"]`);
                        //     if (checkbox) {
                        //         checkbox.checked = true; // 选中复选框
                        //     }
                        // });
                        // form.render('checkbox');
                        // 回填"三高"数据
                        const triadValues = data.Triad.split(','); // 获取三高值
                        $('#triad_hidden').val(data.Triad); // 设置隐藏字段值
                        triadSelect.setValue(triadValues); // 使用xm-select的setValue方法设置选中值
                        // 回填舌苔照图片
                        let pic = '';
                        if (data.Photo_tongue) {
                            let tongue_photos = data.Photo_tongue.split('\n');
                            for (let i = 0; i < tongue_photos.length; i++) {
                                tongue_photos[i] = tongue_photos[i].replace(/[\r\n]/g, '');
                                pic = '/static/uploads/normal_pics/photo_tongue/' + tongue_photos[i];
                                appendImagePreview('tongue-photo-container', pic, tongue_photos[i]);
                            }
                        }
                        // 回填检查单图片
                        if (data.Photo_sheet) {
                            let sheet_photos = data.Photo_sheet.split('\n');
                            for (let i = 0; i < sheet_photos.length; i++) {
                                sheet_photos[i] = sheet_photos[i].replace(/[\r\n]/g, '');
                                pic = '/static/uploads/normal_pics/photo_sheet/' + sheet_photos[i];
                                appendImagePreview('sheet-photo-container', pic, sheet_photos[i]);
                            }
                        }
                    } else {
                        layer.msg(res.msg, { icon: 2, time: 1000 });
                    }
                },
                error: function (res) {
                    layer.closeAll('loading');
                    layer.msg('获取病历详情失败', { icon: 2, time: 1000 });
                }
            });
            //监听提交
            form.on('submit(formSubmitBtn)', function (data) {
                if (!confirm('确定要提交数据并完成问诊吗？')) {
                    return false;
                }
                // 结束问诊
                endConsultation();
                // 处理提交数据
                let formData = new FormData();
                formData.append('id', record_id);
                formData.append('rtc_room_id', id);
                for (let key in data.field) {
                    formData.append(key, data.field[key]);
                }
                let triad_arr = [];
                $("input:checkbox[name='triad_input']:checked").each(function (i) {
                    triad_arr[i] = $(this).val();
                });
                let triad = triad_arr.join(",");
                formData.append('triad', triad);
                // 舌苔照
                let tongue_photos = [];
                $('#tongue-photo-container .image-preview-item').each(function () {
                    let tongue_photo = $(this).data('filename');
                    tongue_photo = tongue_photo.replace(/[\r\n]/g, '');
                    tongue_photos.push(tongue_photo);
                });
                let tongue_photos_str = tongue_photos.join('\n');
                formData.append('tongue_photos', tongue_photos_str);
                // 检查单
                let sheet_photos = [];
                $('#sheet-photo-container .image-preview-item').each(function () {
                    let sheet_photo = $(this).data('filename');
                    sheet_photo = sheet_photo.replace(/[\r\n]/g, '');
                    sheet_photos.push(sheet_photo);
                });
                let sheet_photos_str = sheet_photos.join('\n');
                formData.append('sheet_photos', sheet_photos_str);
                layer.load(2);
                $.ajax({
                    url: serverUrl + "/admin/patient_records/edit",
                    type: "post",
                    data: formData,
                    processData: false, // 不处理数据
                    contentType: false, // 不设置内容类型
                    success: function (res) {
                        layer.closeAll('loading');
                        if (res.code == 200) {
                            layer.msg(res.msg, {
                                time: 2000,
                                icon: 1
                            }, function () {
                                layer.confirm('是否继续修改？', {
                                    btn: ['继续修改', '返回列表', '取消']
                                }, function () {
                                    location.reload();
                                }, function () {
                                    window.location.href = "rtc_room_list.html";
                                });
                            }
                            );
                        } else {
                            layer.msg(res.msg, {
                                time: 2000,
                                icon: 2
                            });
                        }
                    },
                    error: function (res) {
                        layer.closeAll('loading');
                        layer.msg(res.responseJSON.msg, {
                            time: 5000,
                            icon: 2
                        });
                    }
                });
                return false;
            });

            // 解析用户角色
            function parseUserRole(userId) {
                const [roleId] = userId.split('_');
                const parsedRoleId = parseInt(roleId);

                // 根据roleId确定角色名称
                let roleName = '未知';
                if (parsedRoleId === 0) {
                    roleName = '患者';
                } else if (parsedRoleId === 4) {
                    roleName = '医生';
                } else if (parsedRoleId === 3 || parsedRoleId === 9) {
                    roleName = '医助';
                }

                return {
                    roleId: parsedRoleId,
                    roleName: roleName
                };
            }

            // 处理远端用户加入
            function handleUserJoined(event) {
                const { userId } = event;

                // 解析用户角色信息
                const { roleId, roleName } = parseUserRole(userId);

                // 存储用户信息
                remoteUsers.set(userId, { roleId, roleName });

                // 更新录制按钮状态
                updateRecordButtonState();

                // 如果是患者加入
                if (roleId === 0) {
                    // 移除占位符
                    $('#wrapper-placeholder').remove();
                    // 激活患者全屏按钮
                    setButtonState($('#patient_fullscreen'), true);

                    // 修改：使用轮询检测替代固定延时录制
                    if (rtc_auto_record == 1) {
                        console.log('患者加入，准备初始化自动录制检测');
                        initAutoRecording();
                    }
                }
            }

            // 图片上传部分 - 开始
            // 图片预览函数
            function appendImagePreview(containerId, filepath, filename) {
                const container = $(`#${containerId}`);
                const imageItem = $(`
                    <div class="image-preview-item" data-filename="${filename}" data-filepath="${filepath}">
                        <img src="${filepath}">
                        <div class="delete-btn">删除</div>
                    </div>
                `);

                // 点击图片查看大图
                imageItem.find('img').on('click', function () {
                    layer.photos({
                        photos: {
                            title: '查看图片',
                            data: [{ src: filepath }]
                        },
                        footer: false
                    });
                });

                // 点击删除按钮删除图片
                imageItem.find('.delete-btn').on('click', function () {
                    const category = filepath.split('/')[4]; // 从路径中提取类别
                    layer.confirm('确定删除该图片吗？', { icon: 3, title: '提示' }, function (index) {
                        $.ajax({
                            url: '/admin/normal_pic_del',
                            type: 'POST',
                            data: {
                                filename: filename,
                                category: category
                            },
                            success: function (res) {
                                if (res.code === 200) {
                                    imageItem.remove();
                                    layer.msg('删除成功', { icon: 1, time: 1000 });
                                }
                            },
                            error: function (data) {
                                layer.confirm('删除失败: ' + data.responseJSON.msg + '，是否移除该图片？',
                                    { icon: 3, title: '提示' },
                                    function (index) {
                                        imageItem.remove();
                                        layer.msg('移除成功', { icon: 1, time: 1000 });
                                    }
                                );
                            }
                        });
                        layer.close(index);
                    });
                });

                container.append(imageItem);
            }

            // 初始化舌苔照上传
            upload.render({
                elem: '#ID-upload-demo-btn-1',
                url: '/admin/upload_normal_pic',
                multiple: true,
                data: {
                    category: 'photo_tongue',
                },
                drag: true,
                before: function (obj) {
                    // 预览回调
                },
                done: function (res) {
                    if (res.code === 200) {
                        const fileInfo = res.data[0];
                        appendImagePreview('tongue-photo-container', fileInfo.filepath, fileInfo.filename);
                        layer.msg('上传成功', { icon: 1, time: 1000 });
                    } else {
                        layer.msg('上传失败: ' + res.msg, { icon: 2, time: 1000 });
                    }
                },
                error: function () {
                    layer.msg('上传失败', { icon: 2, time: 1000 });
                }
            });

            // 初始化检查单上传
            upload.render({
                elem: '#ID-upload-demo-btn-2',
                url: '/admin/upload_normal_pic',
                multiple: true,
                data: {
                    category: 'photo_sheet',
                },
                drag: true,
                before: function (obj) {
                    // 预览回调
                },
                done: function (res) {
                    if (res.code === 200) {
                        const fileInfo = res.data[0];
                        appendImagePreview('sheet-photo-container', fileInfo.filepath, fileInfo.filename);
                        layer.msg('上传成功', { icon: 1, time: 1000 });
                    } else {
                        layer.msg('上传失败: ' + res.msg, { icon: 2, time: 1000 });
                    }
                },
                error: function () {
                    layer.msg('上传失败', { icon: 2, time: 1000 });
                }
            });
            // 图片上传部分 - 结束

            // 绑定查看文件事件
            $(document).on('click', '.file-view-link', function () {
                const filepath = $(this).data('filepath');
                layer.photos({
                    photos: {
                        title: "查看图片",
                        start: 0,
                        data: [{ src: filepath }]
                    },
                    footer: false
                });
            });

            // 绑定删除文件事件
            $(document).on('click', '.file-delete-btn', function () {
                const button = $(this); // 存储当前按钮
                const filename = button.data('filename');
                const category = $(this).data('filepath').split('/')[4];
                layer.confirm('确定删除该文件吗？', { icon: 3, title: '提示' }, function (index) {
                    $.ajax({
                        url: '/admin/normal_pic_del',
                        type: 'POST',
                        data: { filename: filename, category: category },
                        success: function (res) {
                            if (res.code === 200) {
                                button.closest('tr').remove();
                                layer.msg('删除成功', { icon: 1, time: 1000 });
                            }
                        },
                        error: function (data) {
                            layer.confirm('删除失败: ' + data.responseJSON.msg + '，是否移除该条数据？', { icon: 3, title: '提示' }, function (index) {
                                button.closest('tr').remove();
                                layer.msg('移除成功', { icon: 1, time: 1000 });
                            });
                        }
                    });

                    layer.close(index);
                });
                return false;
            });
            // 图片上传部分 - 结束

            let client = null;
            let localStream = null;
            let recorder = null;
            let isRecording = false;
            let remoteUsers = new Map(); // 存储远程用户信息
            let remoteStreamsMap = new Map(); // 存储远程流的Map
            // 添加麦克风和摄像头状态变量
            let isMicEnabled = true;
            let isCameraEnabled = true;

            // 新增：检查流就绪状态函数
            function checkStreamsReady() {
                // 检查患者流
                const hasPatient = Array.from(remoteUsers.values()).some(user => user.roleId === 0);
                if (!hasPatient) return false;

                // 获取患者流并详细检查
                let patientStream = null;
                for (const [userId, user] of remoteUsers.entries()) {
                    if (user.roleId === 0) {
                        patientStream = remoteStreamsMap.get(userId);
                        // 详细检查患者流状态
                        if (!patientStream ||
                            !patientStream.getMediaStream() ||
                            !patientStream.getMediaStream().getVideoTracks().length ||
                            !patientStream.getMediaStream().getAudioTracks().length) {
                            return false;
                        }

                        // 检查视频轨道的状态
                        const videoTrack = patientStream.getMediaStream().getVideoTracks()[0];
                        if (!videoTrack || videoTrack.readyState !== 'live') {
                            return false;
                        }

                        break;
                    }
                }

                // 检查医生流
                if (!localStream ||
                    !localStream.getMediaStream() ||
                    !localStream.getMediaStream().getVideoTracks().length ||
                    !localStream.getMediaStream().getAudioTracks().length) {
                    return false;
                }

                // 检查医生视频轨道状态
                const localVideoTrack = localStream.getMediaStream().getVideoTracks()[0];
                if (!localVideoTrack || localVideoTrack.readyState !== 'live') {
                    return false;
                }

                return true;
            }

            // 新增：自动录制初始化函数，使用轮询替代固定延时
            function initAutoRecording() {
                if (rtc_auto_record != 1) return;

                // 创建一个状态指示器元素
                const streamCheckIndicator = $('<div>')
                    .attr('id', 'stream-check-indicator')
                    .css({
                        'position': 'fixed',
                        'bottom': '20px',
                        'right': '20px',
                        'background-color': 'rgba(0, 0, 0, 0.7)',
                        'color': 'white',
                        'padding': '10px 15px',
                        'border-radius': '8px',
                        'font-size': '14px',
                        'box-shadow': '0 2px 10px rgba(0,0,0,0.2)',
                        'z-index': 9999,
                        'display': 'none',
                        'transition': 'opacity 0.3s ease-in-out',
                        'opacity': '0',
                        'max-width': '250px'
                    })
                    .html(`
                        <div style="display:flex; align-items:center;">
                            <div class="loading-spinner" style="margin-right:10px; width:20px; height:20px; border:2px solid #fff; border-top-color:transparent; border-radius:50%; animation:spin 1s linear infinite;"></div>
                            <div>
                                <div style="font-weight:bold; margin-bottom:3px;">准备录制中</div>
                                <div style="font-size:12px;" id="stream-check-message">等待视频流就绪...</div>
                            </div>
                        </div>
                        <style>
                            @keyframes spin {
                                to { transform: rotate(360deg); }
                            }
                        </style>
                    `)
                    .appendTo('body');

                // 添加进入动画
                streamCheckIndicator.show().css('opacity', '1');

                let checkAttempts = 0;
                const maxAttempts = 15; // 最多尝试15次，每次1秒，共15秒

                // 开始轮询检查
                const checkInterval = setInterval(() => {
                    checkAttempts++;

                    // 更新UI提示
                    $('#stream-check-message').text(`等待视频流就绪...尝试(${checkAttempts}/${maxAttempts})`);

                    // 调用流检查函数
                    if (checkStreamsReady()) {
                        // 流已就绪，停止轮询
                        clearInterval(checkInterval);

                        // 更新UI提示
                        $('#stream-check-message').text('视频流已就绪，开始录制...');

                        // 淡出并移除指示器
                        setTimeout(() => {
                            streamCheckIndicator.css('opacity', '0');
                            setTimeout(() => {
                                streamCheckIndicator.remove();
                            }, 300);
                        }, 1000);

                        // 开始录制
                        startRecording();
                    } else if (checkAttempts >= maxAttempts) {
                        // 超过最大尝试次数，放弃自动录制
                        clearInterval(checkInterval);

                        // 更新UI提示
                        $('#stream-check-message').text('无法获取完整视频流，请手动录制');
                        streamCheckIndicator.css('background-color', 'rgba(220, 53, 69, 0.8)');

                        // 3秒后淡出并移除指示器
                        setTimeout(() => {
                            streamCheckIndicator.css('opacity', '0');
                            setTimeout(() => {
                                streamCheckIndicator.remove();
                            }, 300);
                        }, 3000);

                        // 通知用户
                        layer.msg('自动录制失败：无法获取完整视频流，请稍后手动录制', { icon: 0 });
                    }
                }, 1000);
            }

            // 新增：录制状态指示器添加函数
            function addRecordingIndicator() {
                // 创建录制状态指示器
                const recordingIndicator = $('<div>')
                    .attr('id', 'recording-indicator')
                    .css({
                        'position': 'absolute',
                        'top': '10px',
                        'left': '10px',
                        'background-color': 'rgba(220, 53, 69, 0.8)',
                        'color': 'white',
                        'padding': '5px 10px',
                        'border-radius': '4px',
                        'font-size': '12px',
                        'z-index': 100,
                        'display': 'flex',
                        'align-items': 'center',
                        'box-shadow': '0 2px 5px rgba(0,0,0,0.2)',
                        'opacity': '0',
                        'transition': 'opacity 0.3s ease-in-out'
                    })
                    .html(`
                        <div class="recording-dot" style="width:8px; height:8px; background-color:#fff; border-radius:50%; margin-right:5px; animation:pulse 1.5s infinite;"></div>
                        <div>录制中</div>
                        <style>
                            @keyframes pulse {
                                0% { opacity: 1; }
                                50% { opacity: 0.3; }
                                100% { opacity: 1; }
                            }
                        </style>
                    `)
                    .appendTo('#video_container');

                // 添加显示动画
                setTimeout(() => {
                    recordingIndicator.css('opacity', '1');
                }, 10);

                // 返回指示器元素，用于后续移除
                return recordingIndicator;
            }

            // 新增：移除录制状态指示器
            function removeRecordingIndicator() {
                const indicator = $('#recording-indicator');
                if (indicator.length) {
                    // 添加淡出动画
                    indicator.css('opacity', '0');
                    // 动画完成后移除元素
                    setTimeout(() => {
                        indicator.remove();
                    }, 300);
                }
            }

            // 初始化按钮状态
            function initButtonStates() {
                // 移除调试代码
                // console.log('----------------------->',remoteUsers.size, client);

                // 清晰的逻辑：页面加载初始状态或问诊结束后的状态
                // 只激活开始问诊按钮，其他全部禁用
                setButtonState($('#startConsultBtn'), true);
                setButtonState($('#endConsultBtn'), false);
                setButtonState($('#startRecordBtn'), false);
                setButtonState($('#stopRecordBtn'), false);
                setButtonState($('#patient_fullscreen'), false);
                setButtonState($('#toggle_camera'), false);
                setButtonState($('#toggle_mic'), false);

                // 移除可能存在的alert调试代码
                // alert(1)
            }

            // 按钮状态控制函数
            function setButtonState($btn, enabled) {
                if (enabled) {
                    $btn.removeClass('layui-btn-disabled').removeAttr('disabled');
                } else {
                    $btn.addClass('layui-btn-disabled').attr('disabled', 'disabled');
                }
            }

            // 开始问诊
            $('#startConsultBtn').click(function () {
                if ($(this).hasClass('layui-btn-disabled')) return;

                layer.confirm('确定要开始问诊吗？', { icon: 3, title: '提示' }, function (index) {
                    setButtonState($('#startConsultBtn'), false);
                    setButtonState($('#endConsultBtn'), true);
                    // 开始问诊时禁用录制按钮，等待患者加入后再激活
                    setButtonState($('#startRecordBtn'), false);
                    setButtonState($('#stopRecordBtn'), false);
                    initTRTC();
                    layer.msg('开始问诊...', { icon: 1 });
                    layer.close(index);
                });
            });

            // 结束问诊
            $('#endConsultBtn').click(function () {
                if ($(this).hasClass('layui-btn-disabled')) return;

                // 使用更明显的确认框样式
                layer.confirm('确定要结束问诊吗？', {
                    icon: 3,
                    title: '提示',
                    closeBtn: 0,  // 不显示关闭按钮，必须点击按钮关闭
                    shade: 0.6,    // 加深遮罩，使确认框更加明显
                    btn: ['确定结束', '取消']  // 更明确的按钮文字
                }, function (index) {
                    layer.close(index);
                    // 短暂延迟后再执行结束操作，避免与其他消息冲突
                    setTimeout(function() {
                        endConsultation();
                    }, 100);
                });
            });

            // 停止录制
            $('#stopRecordBtn').click(function () {
                if ($(this).hasClass('layui-btn-disabled')) return;

                if (recorder && isRecording) {
                    recorder.stop();
                    isRecording = false;
                    setButtonState($(this), false);
                    // 如果患者还在，则激活开始录制按钮
                    const hasPatient = Array.from(remoteUsers.values()).some(user => user.roleId === 0);
                    setButtonState($('#startRecordBtn'), hasPatient);

                    // 使用更短的显示时间，避免与后续消息重叠
                    layer.msg('录制已停止', {
                        icon: 1,
                        time: 1000,  // 显示1秒
                        offset: 't'  // 显示在顶部，避免与其他消息重叠
                    });
                }
            });

            // 初始化TRTC
            async function initTRTC() {
                try {
                    // 从后端获取TRTC配置
                    const response = await fetch(`/admin/rtc_room/get_trtc_config`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: new URLSearchParams({ room_id: id }).toString(),
                    });
                    const result = await response.json();
                    if (result.code !== 200) {
                        initButtonStates(); // 恢复初始按钮状态
                        layer.msg(result.msg, { icon: 2 });
                        return;
                    }
                    const config = result.data;

                    // 创建客户端
                    client = TRTC.createClient({
                        mode: 'rtc',
                        useStringRoomId: true,
                        sdkAppId: config.sdkAppId,
                        userId: config.userId,
                        userSig: config.userSig
                    });

                    // 监听事件 - 确保在加入房间前绑定所有事件
                    client.on('peer-join', handleUserJoined);
                    client.on('peer-leave', handleUserLeft);
                    client.on('stream-added', handleStreamAdded);
                    client.on('stream-removed', handleStreamRemoved);
                    client.on('error', handleError);

                    // 加入房间
                    await client.join({ roomId: config.roomIdStr }); // 使用带前缀的房间ID

                    // 创建本地流
                    localStream = TRTC.createStream({
                        userId: config.userId,
                        audio: true,
                        video: true
                    });

                    // 初始化本地流
                    await localStream.initialize();

                    // 创建本地视频元素
                    const playerId = createLocalVideoElement(config);

                    // 播放本地流
                    localStream.play(playerId);

                    // 发布本地流
                    await client.publish(localStream);

                    // 初始化视频交互事件
                    initVideoInteraction();

                    // 正确设置按钮状态
                    setButtonState($('#startConsultBtn'), false);
                    setButtonState($('#endConsultBtn'), true);
                    setButtonState($('#toggle_camera'), true);
                    setButtonState($('#toggle_mic'), true);

                    // 患者按钮和录制按钮状态由updateRecordButtonState管理
                    updateRecordButtonState();

                    // 修改：检查是否已有患者在房间，使用轮询检测替代固定延时录制
                    if (rtc_auto_record == 1) {
                        // 检查是否有患者在线
                        const hasPatient = Array.from(remoteUsers.values()).some(user => user.roleId === 0);
                        if (hasPatient) {
                            console.log('医生加入后检测到已有患者在线，准备初始化自动录制检测');
                            initAutoRecording();
                        }
                    }

                } catch (error) {
                    console.error('Error initializing TRTC:', error);
                    layer.msg('初始化视频失败：' + error.message, { icon: 2 });
                    initButtonStates(); // 发生错误时恢复初始按钮状态
                }
            }

            // 错误处理函数
            function handleError(error) {
                console.error('TRTC错误:', error);
                layer.msg('视频连接发生错误: ' + error.message, { icon: 2 });
            }

            // 清理TRTC资源
            async function cleanupTRTC() {
                try {
                    if (localStream) {
                        localStream.stop();
                        localStream.close();
                        localStream = null;

                        // 本地流被清理，禁用麦克风和摄像头按钮
                        setButtonState($('#toggle_camera'), false);
                        setButtonState($('#toggle_mic'), false);
                    }

                    if (client) {
                        // 移除所有事件监听器
                        client.off('peer-join', handleUserJoined);
                        client.off('peer-leave', handleUserLeft);
                        client.off('stream-added', handleStreamAdded);
                        client.off('stream-removed', handleStreamRemoved);
                        client.off('error', handleError);

                        // 离开房间
                        await client.leave();
                        client = null;
                    }

                    // 清理视频容器
                    $('#video_container').empty();

                    // 重置麦克风和摄像头状态
                    isMicEnabled = true;
                    isCameraEnabled = true;
                } catch (error) {
                    console.error('清理TRTC资源失败:', error);
                    layer.msg('清理视频资源失败：' + error.message);
                }
            }

            // 初始化视频交互事件
            function initVideoInteraction() {
                // 先移除旧的事件处理器以避免重复
                $(document).off('click', '.video-wrapper.small');

                // 绑定视频点击事件处理
                $(document).on('click', '.video-wrapper.small', function () {
                    const $clickedWrapper = $(this);
                    const clickedId = $clickedWrapper.attr('id');
                    const clickedUserId = $clickedWrapper.data('user-id');
                    const clickedRole = $clickedWrapper.data('role');

                    // 查找当前的背景视频
                    const $currentBackground = $('.video-wrapper.background');
                    const currentBackgroundId = $currentBackground.attr('id');
                    const currentBackgroundUserId = $currentBackground.data('user-id');
                    const currentBackgroundRole = $currentBackground.data('role');

                    // 交换类名
                    $clickedWrapper.removeClass('small small-1 small-2 small-3').addClass('background');
                    $currentBackground.removeClass('background').addClass('small');

                    // 使用防抖处理，减少重排次数
                    clearTimeout(window.rearrangeTimeout);
                    window.rearrangeTimeout = setTimeout(rearrangeSmallVideos, 50);

                    // 更新按钮状态 - 确保录制按钮状态正确
                    updateRecordButtonState();
                });

                // 初始化时调用一次，确保小视频位置正确
                rearrangeSmallVideos();
            }

            // 更新录制按钮状态
            function updateRecordButtonState() {
                // 检查是否有患者在线
                const hasPatient = Array.from(remoteUsers.values()).some(user => user.roleId === 0);

                // 只有在患者在线且医生在线时，才允许录制
                if (hasPatient && localStream) {
                    setButtonState($('#startRecordBtn'), !isRecording);
                    setButtonState($('#stopRecordBtn'), isRecording);
                    // 同时激活患者全屏按钮
                    setButtonState($('#patient_fullscreen'), true);
                } else {
                    setButtonState($('#startRecordBtn'), false);
                    setButtonState($('#stopRecordBtn'), false);
                    // 禁用患者全屏按钮
                    setButtonState($('#patient_fullscreen'), false);
                }
            }

            // 创建本地视频元素并添加到容器中
            function createLocalVideoElement(config) {
                // 创建本地视频包装器
                const localVideoWrapper = $('<div>')
                    .addClass('video-wrapper small small-1')  // 修改为默认小视频
                    .attr('id', `wrapper-${config.userId}`)
                    .attr('data-role', '医生')
                    .attr('data-user-id', config.userId);

                // 创建视频播放容器
                const localVideoEl = $('<div>')
                    .addClass('video-player')
                    .attr('id', `player-${config.userId}`);

                // 添加视频角色标签
                const localLabel = $('<div>')
                    .addClass('video-label')
                    .text('医生');

                // 组装元素
                localVideoWrapper.append(localVideoEl);
                localVideoWrapper.append(localLabel);

                // 检查当前视频容器中是否已有视频元素
                const existingVideos = $('#video_container').children();
                const hasExistingVideos = existingVideos.length > 0;
                const hasPatientVideo = $('.video-wrapper[data-role="患者"]').length > 0;

                // 如果已有患者视频，不创建占位符，只添加本地视频
                if (hasPatientVideo) {
                    // 添加医生视频，不清空容器
                    $('#video_container').append(localVideoWrapper);

                    // 确保患者视频是背景，医助和医生是小视频
                    $('.video-wrapper[data-role="患者"]').removeClass('small small-1 small-2 small-3').addClass('background');
                    $('.video-wrapper[data-role="医助"]').removeClass('background').addClass('small');
                    $('.video-wrapper[data-role="医生"]').removeClass('background').addClass('small');

                    // 移除任何占位符
                    $('#wrapper-placeholder').remove();
                }
                // 如果有医助但无患者
                else if (hasExistingVideos && $('.video-wrapper[data-role="医助"]').length > 0) {
                    // 先移除任何可能的占位符
                    $('#wrapper-placeholder').remove();

                    // 添加医生视频，不清空容器
                    $('#video_container').append(localVideoWrapper);

                    // 创建占位符
                    createPlaceholderVideo();

                    // 确保医助和医生显示为小视频
                    $('.video-wrapper[data-role="医助"]').removeClass('background').addClass('small');
                    $('.video-wrapper[data-role="医生"]').removeClass('background').addClass('small');
                }
                // 如果没有任何视频元素，创建医生小视频和占位符
                else {
                    // 清空视频容器（这里是安全的，因为没有现有视频）
                    $('#video_container').empty();

                    // 添加医生视频
                    $('#video_container').append(localVideoWrapper);

                    // 创建一个占位的背景视频容器
                    createPlaceholderVideo();
                }

                // 重新排列小视频位置
                setTimeout(rearrangeSmallVideos, 100);

                return localVideoEl.attr('id');
            }

            // 创建占位视频
            function createPlaceholderVideo() {
                // 如果已有占位符，不重复创建
                if ($('#wrapper-placeholder').length > 0) return;

                // 创建一个占位的背景视频容器
                const placeholderWrapper = $('<div>')
                    .addClass('video-wrapper background')
                    .attr('id', 'wrapper-placeholder')
                    .attr('data-role', '等待患者加入')
                    .css({
                        'display': 'flex',
                        'align-items': 'center',
                        'justify-content': 'center',
                        'background-color': '#2c2c2c',
                        'color': '#ffffff',
                        'font-size': '18px'
                    });

                // 创建占位标签
                const placeholderLabel = $('<div>')
                    .addClass('video-label')
                    .text('等待患者加入');

                // 创建占位内容
                const placeholderContent = $('<div>')
                    .html('<i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop" style="font-size: 32px; margin-right: 10px;"></i> 等待患者加入...');

                // 组装占位元素
                placeholderWrapper.append(placeholderContent);
                placeholderWrapper.append(placeholderLabel);

                // 将占位符添加到容器
                $('#video_container').append(placeholderWrapper);
            }

            // 重新排列小视频位置
            function rearrangeSmallVideos() {
                const smallVideos = $('.video-wrapper.small');
                if (!smallVideos.length) return; // 没有小视频时不处理

                // 批量处理DOM操作，减少重排和重绘
                requestAnimationFrame(() => {
                    // 移除所有旧的位置类
                    smallVideos.removeClass('small-1 small-2 small-3');

                    // 根据小视频的数量重新排列它们的位置
                    smallVideos.each(function (index) {
                        const positionClass = `small-${index + 1}`;
                        $(this).addClass(positionClass);
                    });
                });
            }

            // 处理远端用户离开
            function handleUserLeft(event) {
                const { userId } = event;
                const leavingUser = remoteUsers.get(userId);

                if (leavingUser) {
                    // 如果离开的是患者
                    if (leavingUser.roleId === 0) {
                        // layer.confirm('患者已退出诊室，是否结束问诊？', {
                        //     btn: ['确定'],
                        //     closeBtn: 0
                        // }, function (index) {
                        //     endConsultation('患者已离开，问诊自动结束');
                        //     layer.close(index);
                        // });
                        endConsultation('患者已离开，问诊自动结束');
                        setButtonState($('#patient_fullscreen'), false);
                    }

                    // 检查离开的是否是背景视频
                    const $wrapper = $(`#wrapper-${userId}`);
                    const isLeavingBackground = $wrapper.hasClass('background');

                    // 清理用户信息和视频元素
                    remoteUsers.delete(userId);
                    remoteStreamsMap.delete(userId); // 从Map中移除流
                    $wrapper.remove();

                    // 如果离开的用户是背景视频，需要将其他视频设为背景
                    if (isLeavingBackground) {
                        // 首先检查是否有患者视频可以设为背景
                        let $patientWrapper = null;
                        remoteUsers.forEach((user, id) => {
                            if (user.roleId === 0) {
                                $patientWrapper = $(`#wrapper-${id}`);
                            }
                        });

                        if ($patientWrapper && $patientWrapper.length) {
                            $patientWrapper.removeClass('small small-1 small-2 small-3').addClass('background');
                        } else {
                            // 否则使用医生视频或第一个可用视频
                            const doctorId = Array.from(client._users)[0]; // 获取医生ID
                            const $doctorWrapper = $(`#wrapper-${doctorId}`);

                            if ($doctorWrapper && $doctorWrapper.length) {
                                $doctorWrapper.removeClass('small small-1 small-2 small-3').addClass('background');
                            } else {
                                // 最后尝试任何可用视频
                                const $availableWrapper = $('.video-wrapper').first();
                                if ($availableWrapper.length) {
                                    $availableWrapper.removeClass('small small-1 small-2 small-3').addClass('background');
                                }
                            }
                        }
                    }

                    // 重新排列所有小视频
                    clearTimeout(window.rearrangeTimeout);
                    window.rearrangeTimeout = setTimeout(rearrangeSmallVideos, 100);

                    // 仅更新录制和患者全屏按钮状态，不影响麦克风和摄像头按钮
                    updateRecordButtonState();
                }
            }

            // 处理远端流移除
            function handleStreamRemoved(event) {
                const remoteStream = event.stream;
                const userId = remoteStream.getUserId();

                // 检查是否是背景视频
                const $wrapper = $(`#wrapper-${userId}`);
                const isBackground = $wrapper.hasClass('background');

                remoteStreamsMap.delete(userId); // 从Map中移除流

                // 移除视频元素和包装器
                $wrapper.remove();

                // 如果移除的是背景视频，需要将其他视频设为背景
                if (isBackground) {
                    // 首先尝试将患者视频作为背景
                    let $patientWrapper = null;
                    remoteUsers.forEach((user, id) => {
                        if (user.roleId === 0) {
                            $patientWrapper = $(`#wrapper-${id}`);
                        }
                    });

                    if ($patientWrapper && $patientWrapper.length) {
                        $patientWrapper.removeClass('small').addClass('background');
                    } else {
                        // 否则选择第一个可用视频
                        const $availableWrapper = $('.video-wrapper').first();
                        if ($availableWrapper.length) {
                            $availableWrapper.removeClass('small').addClass('background');
                        }
                    }
                }

                // 重新排列小视频
                setTimeout(rearrangeSmallVideos, 100);
            }

            // 处理远端流添加
            async function handleStreamAdded(event) {
                const remoteStream = event.stream;
                const userId = remoteStream.getUserId();

                try {
                    await client.subscribe(remoteStream);

                    // 保存远程流到Map中
                    remoteStreamsMap.set(userId, remoteStream);

                    // 解析用户角色
                    const { roleId, roleName } = parseUserRole(userId);

                    // 先查找是否已存在该用户的视频元素
                    const existingWrapper = $(`#wrapper-${userId}`);

                    // 如果已存在该用户的视频元素
                    if (existingWrapper.length > 0) {
                        // 直接使用已存在的元素播放视频流
                        remoteStream.play(`player-${userId}`);

                        // 如果是患者，确保显示为背景
                        if (roleId === 0 && !existingWrapper.hasClass('background')) {
                            // 移除占位符
                            $('#wrapper-placeholder').remove();
                            // 将现有背景视频变为小视频
                            $('.video-wrapper.background').not(existingWrapper).removeClass('background').addClass('small');
                            // 将患者视频设为背景
                            existingWrapper.removeClass('small small-1 small-2 small-3').addClass('background');
                        }
                    } else {
                        // 创建新的视频元素
                        let videoClass = 'small';

                        // 如果是患者
                        if (roleId === 0) {
                            videoClass = 'background';
                            // 移除占位符
                            $('#wrapper-placeholder').remove();
                            // 如果已有背景视频，将其变为小视频
                            $('.video-wrapper.background').removeClass('background').addClass('small');
                        }

                        // 创建视频包装器
                        const remoteVideoWrapper = $('<div>')
                            .addClass(`video-wrapper ${videoClass}`)
                            .attr('id', `wrapper-${userId}`)
                            .attr('data-role', roleName)
                            .attr('data-user-id', userId);

                        // 创建视频播放容器
                        const remoteVideoEl = $('<div>')
                            .addClass('video-player')
                            .attr('id', `player-${userId}`);

                        // 创建标签
                        const remoteLabel = $('<div>')
                            .addClass('video-label')
                            .text(roleName);

                        // 组装元素
                        remoteVideoWrapper.append(remoteVideoEl);
                        remoteVideoWrapper.append(remoteLabel);

                        // 添加到容器
                        $('#video_container').append(remoteVideoWrapper);

                        // 播放远程流
                        remoteStream.play(remoteVideoEl.attr('id'));
                    }

                    // 使用防抖处理，延迟调整小视频的位置
                    clearTimeout(window.rearrangeTimeout);
                    window.rearrangeTimeout = setTimeout(rearrangeSmallVideos, 100);

                    // 如果是新用户，确保在remoteUsers中记录
                    if (!remoteUsers.has(userId)) {
                        remoteUsers.set(userId, { roleId, roleName });
                    }

                    // 更新录制按钮状态
                    updateRecordButtonState();
                } catch (error) {
                    console.error('订阅远程流失败:', error);
                    layer.msg('订阅远程流失败: ' + error.message, { icon: 2 });
                }
            }

            // 结束问诊的统一处理函数
            async function endConsultation(reason = '') {
                // 如果正在录制，先停止录制
                if (isRecording && recorder) {
                    recorder.stop();
                    isRecording = false;
                }

                try {
                    // 清理TRTC资源
                    await cleanupTRTC();

                    // 恢复初始按钮状态
                    initButtonStates();

                    // 重置麦克风和摄像头按钮状态
                    $('#toggle_mic').html('<i class="iconfont">&#xeca7;</i> 麦克风');
                    $('#toggle_mic').removeClass('layui-border-orange').addClass('layui-border-blue');
                    $('#toggle_camera').html('<i class="iconfont">&#xeca6;</i> 摄像头');
                    $('#toggle_camera').removeClass('layui-border-orange').addClass('layui-border-blue');
                    isMicEnabled = true;
                    isCameraEnabled = true;

                    // 显示结束原因（如果有）
                    if (reason) {
                        // 使用更持久的通知，并设置更长的显示时间
                        layer.msg(reason, {
                            time: 2000,  // 显示2秒
                            offset: 'b'  // 显示在底部，避免与其他消息重叠
                        });
                    } else {
                        // 使用更持久的通知，并设置更长的显示时间
                        layer.msg('问诊已结束', {
                            time: 2000,  // 显示2秒
                            offset: 'b'  // 显示在底部，避免与其他消息重叠
                        });
                    }
                } catch (error) {
                    console.error('结束问诊时发生错误:', error);
                    layer.msg('结束问诊失败: ' + error.message, { icon: 2 });
                    // 即使发生错误，也尝试恢复按钮状态
                    initButtonStates();
                }
            }

            // 添加页面关闭/刷新事件处理
            $(window).on('beforeunload', function () {
                if (client) {
                    endConsultation('医生已离开，问诊自动结束');
                }
            });

            // 开始录制
            $('#startRecordBtn').click(function () {
                if ($(this).hasClass('layui-btn-disabled')) return;
                startRecording();
            });

            // 新增：将录制功能抽取为独立函数
            async function startRecording() {
                // 添加更严格的流检查
                console.log('开始录制前检查流...');

                // 使用流检查函数代替简单检查
                if (!checkStreamsReady()) {
                    layer.msg('录制失败：视频流未就绪，请稍后再试', { icon: 2 });
                    return;
                }

                // 如果已经在录制，不要重复开始
                if (isRecording) {
                    return;
                }

                try {
                    // 获取患者流
                    let patientStream = null;
                    for (const [userId, user] of remoteUsers.entries()) {
                        if (user.roleId === 0) { // 患者角色
                            patientStream = remoteStreamsMap.get(userId);
                            break;
                        }
                    }

                    // 准备视频合成所需数据
                    const streams = [localStream.getMediaStream(), patientStream.getMediaStream()];

                    // 创建画布用于合成视频
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');

                    // 设置画布大小（调整分辨率以减小文件大小）
                    const videoWidth = 400;
                    const videoHeight = 383;
                    canvas.width = videoWidth;
                    canvas.height = videoHeight * 2; // 两倍高度以容纳两个视频

                    // 创建用于绘制的video元素
                    const videos = streams.map(stream => {
                        const video = document.createElement('video');
                        video.srcObject = stream;
                        video.autoplay = true;
                        video.muted = true;
                        return video;
                    });

                    // 等待视频元素加载完成，添加更严格的检查
                    await Promise.all(videos.map(video =>
                        new Promise((resolve, reject) => {
                            const timeout = setTimeout(() => {
                                reject(new Error('视频加载超时'));
                            }, 5000);

                            video.onloadedmetadata = () => {
                                // 额外检查视频尺寸是否有效
                                if (video.videoWidth > 0 && video.videoHeight > 0) {
                                    clearTimeout(timeout);
                                    // 开始播放以确保数据流动
                                    video.play().then(() => {
                                        // 确认视频已经开始播放
                                        setTimeout(() => {
                                            resolve(video);
                                        }, 500);
                                    }).catch(err => {
                                        clearTimeout(timeout);
                                        reject(err);
                                    });
                                } else {
                                    // 视频尺寸无效，可能是流还没有完全准备好
                                    reject(new Error('视频尺寸无效'));
                                }
                            };

                            video.onerror = (err) => {
                                clearTimeout(timeout);
                                reject(err);
                            };
                        })
                    ));

                    console.log('视频元素已加载，准备开始录制');

                    // 创建合成视频流（降低帧率）
                    const compositeStream = canvas.captureStream(16); // 24fps，电影级帧率，对于医疗问诊已足够流畅 - 压缩002

                    // 添加音频轨道
                    const audioCtx = new AudioContext();
                    const dest = audioCtx.createMediaStreamDestination();

                    streams.forEach(stream => {
                        stream.getAudioTracks().forEach(track => {
                            const source = audioCtx.createMediaStreamSource(new MediaStream([track]));
                            source.connect(dest);
                        });
                    });

                    // 将音频轨道添加到合成流中
                    dest.stream.getAudioTracks().forEach(track => {
                        compositeStream.addTrack(track);
                    });

                    // 在录制视频之前，修改 drawVideos 函数，加载水印图片
                    // 加载水印图片
                    const watermarkImg = new Image();
                    watermarkImg.src = '/static/wechat_res/logo.png';
                    watermarkImg.onload = function () {
                        console.log('水印图片加载成功');
                    };
                    watermarkImg.onerror = function () {
                        console.error('水印图片加载失败');
                    };

                    // 开始定时绘制
                    function drawVideos() {
                        // 清空画布
                        ctx.fillStyle = 'black';
                        ctx.fillRect(0, 0, canvas.width, canvas.height);

                        // 绘制患者视频（上方）
                        ctx.drawImage(videos[1], 0, 0, videoWidth, videoHeight);

                        // 绘制医生视频（下方）
                        ctx.drawImage(videos[0], 0, videoHeight, videoWidth, videoHeight);

                        // 绘制水印（添加在医生视频的右上角）
                        if (watermarkImg.complete && watermarkImg.naturalWidth !== 0) {
                            // 计算水印大小（根据原始图片按比例缩放）
                            const watermarkWidth = Math.min(150, watermarkImg.naturalWidth);
                            const watermarkHeight = (watermarkImg.naturalHeight / watermarkImg.naturalWidth) * watermarkWidth;

                            // 在医生视频的右上角绘制水印
                            const watermarkX = videoWidth - watermarkWidth - 20; // 右边距离20px
                            const watermarkY = videoHeight + 10; // 上边距离

                            ctx.globalAlpha = 0.7; // 水印透明度
                            ctx.drawImage(watermarkImg, watermarkX, watermarkY, watermarkWidth, watermarkHeight);
                            ctx.globalAlpha = 1.0; // 恢复完全不透明
                        }

                        // 添加标签
                        ctx.fillStyle = 'white';
                        ctx.font = '15px Microsoft YaHei, SimHei, Arial'; // 使用更好的字体和更大的字号

                        // 添加文字阴影效果
                        ctx.shadowColor = 'rgba(0, 0, 0, 0.8)';
                        ctx.shadowBlur = 3;
                        ctx.shadowOffsetX = 2;
                        ctx.shadowOffsetY = 2;

                        // 患者和医生信息
                        ctx.fillText(`患者：${patientName}`, 10, 30);
                        ctx.fillText(`医生：${doctorName}`, 10, videoHeight + 30);

                        // 添加时间戳（只在患者视频区域显示）
                        const now = new Date();
                        const timeString = now.toLocaleString('zh-CN', {
                            year: 'numeric',
                            month: '2-digit',
                            day: '2-digit',
                            hour: '2-digit',
                            minute: '2-digit',
                            second: '2-digit',
                            hour12: false
                        });
                        ctx.font = '13px Microsoft YaHei, SimHei, Arial';
                        ctx.fillText(timeString, videoWidth - 150, 30); // 上方视频右上角

                        // 重置阴影效果，避免影响其他绘制
                        ctx.shadowColor = 'transparent';
                        ctx.shadowBlur = 0;
                        ctx.shadowOffsetX = 0;
                        ctx.shadowOffsetY = 0;

                        requestAnimationFrame(drawVideos);
                    }
                    drawVideos();

                    // 创建 MediaRecorder - 调整参数以减小视频大小
                    recorder = new MediaRecorder(compositeStream, {
                        mimeType: 'video/webm;codecs=vp8,opus',
                        videoBitsPerSecond: 1000000, // 2.5Mbps，相比原来的5Mbps减小一半 - 压缩-001
                        audioBitsPerSecond: 128000   // 128kbps音频质量，够用的音频质量
                    });

                    const chunks = [];
                    recorder.ondataavailable = e => chunks.push(e.data);
                    recorder.onstop = async () => {
                        // 移除录制状态指示器
                        removeRecordingIndicator();

                        // 创建视频文件并下载
                        const blob = new Blob(chunks, { type: 'video/webm' });
                        const url = window.URL.createObjectURL(blob);
                        const a = $('<a>')
                            .css('display', 'none')
                            .attr('href', url)
                            .attr('download', `诊室_${id}_${new Date().getTime()}.webm`);
                        $('body').append(a);
                        a[0].click();
                        window.URL.revokeObjectURL(url);
                        a.remove();

                        // 清理资源
                        videos.forEach(video => {
                            video.srcObject = null;
                            video.remove();
                        });
                        canvas.remove();

                        // 使用一个通知，并在其回调中显示确认框，避免被其他消息覆盖
                        const notifyIndex = layer.msg('录制文件已保存，请查看下载', {
                            icon: 1,
                            time: 1500  // 显示1.5秒后自动关闭
                        }, function() {
                            // 通知关闭后再显示确认框
                            setTimeout(function() {
                                // 询问是否上传至腾讯云COS
                                layer.confirm('是否将视频上传至腾讯云COS对象存储？', {
                                    btn: ['上传', '取消'],
                                    title: '上传至云存储',
                                    closeBtn: 0,  // 不显示关闭按钮，必须点击按钮关闭
                                    shade: 0.6    // 加深遮罩，使确认框更加明显
                                }, function (index) {
                                    layer.close(index);
                                    uploadToCOS(blob);
                                });
                            }, 100);  // 短暂延迟，确保通知完全关闭
                        });
                    };

                    // 添加录制状态指示器
                    addRecordingIndicator();

                    recorder.start();
                    isRecording = true;
                    setButtonState($('#startRecordBtn'), false);
                    setButtonState($('#stopRecordBtn'), true);

                    // 根据是否是自动录制显示不同消息
                    if (rtc_auto_record == 1) {
                        layer.msg('自动录制已开始', { icon: 1 });
                    } else {
                        layer.msg('开始录制');
                    }

                    $('#formSubmitBtn').removeClass('layui-btn-disabled').prop('disabled', false);
                } catch (error) {
                    console.error('录制准备失败:', error);
                    layer.msg('录制准备失败：' + error.message, { icon: 2 });
                    return;
                }
            }

            // 上传视频到腾讯云COS
            async function uploadToCOS(blob) {
                try {
                    // 创建上传进度提示框
                    const uploadProgressIndex = layer.open({
                        type: 1,
                        title: '上传至腾讯云COS',
                        closeBtn: 0,
                        shade: 0.3,
                        anim: 2,
                        shadeClose: false,
                        area: ['350px', '200px'],
                        offset: 'rb', // 右下角
                        content: `
                            <div style="padding: 20px; background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(10px); border-radius: 8px; box-shadow: 0 4px 20px rgba(0,0,0,0.1);">
                                <div style="margin-bottom: 15px; font-size: 16px; display: flex; align-items: center;">
                                    <i class="layui-icon layui-icon-upload" style="font-size: 22px; margin-right: 10px; color: #1E9FFF;"></i>
                                    <span>正在上传视频文件...</span>
                                </div>
                                <div class="layui-progress layui-progress-big" lay-showpercent="true" lay-filter="uploadProgress" style="margin-top: 15px;">
                                    <div class="layui-progress-bar layui-bg-blue" lay-percent="0%"></div>
                                </div>
                                <div style="margin-top: 15px; color: #666; font-size: 13px;" id="upload-status">准备上传...</div>
                            </div>
                        `
                    });

                    // 初始化进度条
                    element.render('progress');
                    element.progress('uploadProgress', '0%');

                    // 获取签名
                    $('#upload-status').text('获取上传授权...');
                    const signResponse = await fetch(serverUrl + '/admin/cos/get_upload_sign', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded'
                        },
                        body: new URLSearchParams({
                            ext: 'webm'
                        })
                    });

                    if (!signResponse.ok) {
                        throw new Error('获取签名失败');
                    }

                    const signData = await signResponse.json();
                    if (signData.code !== 200) {
                        throw new Error(signData.msg || '获取签名失败');
                    }

                    // 准备上传
                    $('#upload-status').text('准备上传文件...');
                    const { presignedURL, cosKey, cosHost, url } = signData.data;

                    console.log('获取到预签名URL:', presignedURL);
                    console.log('文件Key:', cosKey);
                    console.log('COS主机:', cosHost);

                    // 创建上传请求
                    const xhr = new XMLHttpRequest();
                    xhr.open('PUT', presignedURL, true);

                    // 设置请求头
                    xhr.setRequestHeader('Content-Type', 'video/webm');

                    // 监听上传进度
                    xhr.upload.onprogress = (e) => {
                        if (e.lengthComputable) {
                            const percent = Math.round((e.loaded / e.total) * 100);
                            element.progress('uploadProgress', percent + '%');
                            $('#upload-status').text(`已上传 ${formatFileSize(e.loaded)}/${formatFileSize(e.total)}...`);
                        }
                    };

                    // 上传完成处理
                    xhr.onload = function () {
                        if (xhr.status >= 200 && xhr.status < 300) {
                            // 上传成功
                            $('#upload-status').text('上传成功!');
                            element.progress('uploadProgress', '100%');

                            // 2秒后关闭进度条
                            setTimeout(() => {
                                layer.close(uploadProgressIndex);
                                layer.msg('视频已成功上传至腾讯云COS', {
                                    icon: 1,
                                    time: 3000
                                });
                            }, 2000);
                        } else {
                            // 上传失败
                            $('#upload-status').text(`上传失败: ${xhr.status} ${xhr.statusText}`);
                            setTimeout(() => {
                                layer.close(uploadProgressIndex);
                                layer.msg('上传失败，请稍后重试', {
                                    icon: 2,
                                    time: 3000
                                });
                            }, 2000);
                        }
                    };

                    // 错误处理
                    xhr.onerror = function () {
                        console.error('上传错误:', xhr.status, xhr.statusText);
                        $('#upload-status').text('网络错误，上传失败');
                        setTimeout(() => {
                            layer.close(uploadProgressIndex);
                            layer.msg('网络错误，上传失败', {
                                icon: 2,
                                time: 3000
                            });
                        }, 2000);
                    };

                    // 添加超时处理
                    xhr.timeout = 3600000; // 1小时超时
                    xhr.ontimeout = function () {
                        $('#upload-status').text('上传超时，请检查网络连接');
                        setTimeout(() => {
                            layer.close(uploadProgressIndex);
                            layer.msg('上传超时，请检查网络连接', {
                                icon: 2,
                                time: 3000
                            });
                        }, 2000);
                    };

                    // 开始上传
                    try {
                        console.log('开始上传文件，大小:', formatFileSize(blob.size));
                        xhr.send(blob);
                    } catch (error) {
                        console.error('发送请求时出错:', error);
                        $('#upload-status').text('发送请求时出错: ' + error.message);
                    }

                } catch (error) {
                    console.error('上传失败:', error);
                    layer.msg('上传失败: ' + error.message, {
                        icon: 2,
                        time: 3000
                    });
                }
            }

            // 格式化文件大小
            function formatFileSize(bytes) {
                if (bytes === 0) return '0 B';
                const k = 1024;
                const sizes = ['B', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }

            // 初始化
            $(document).ready(function () {
                // 页面加载时初始化按钮状态
                initButtonStates();

                // 添加窗口大小变化事件，优化视频布局
                $(window).on('resize', function () {
                    // 防抖处理
                    clearTimeout(window.resizeTimeout);
                    window.resizeTimeout = setTimeout(function () {
                        // 如果已经有视频流，重新调整视频布局
                        if ($('.video-wrapper').length > 0) {
                            rearrangeSmallVideos();
                        }
                    }, 200);
                });

                // 提取全屏功能为单独函数
                function togglePatientFullscreen() {
                    // 查找患者视频元素
                    const $patientVideo = $('.video-wrapper[data-role="患者"]');
                    if ($patientVideo.length) {
                        // 如果找到患者视频，获取其中的视频播放元素
                        const $playerElement = $patientVideo.find('.video-player');

                        if ($playerElement.length) {
                            // 检查当前是否处于全屏状态
                            const isFullscreen = document.fullscreenElement ||
                                document.webkitFullscreenElement ||
                                document.mozFullScreenElement ||
                                document.msFullscreenElement;

                            if (isFullscreen) {
                                // 如果已经在全屏状态，则退出全屏
                                if (document.exitFullscreen) {
                                    document.exitFullscreen();
                                } else if (document.webkitExitFullscreen) { /* Safari */
                                    document.webkitExitFullscreen();
                                } else if (document.mozCancelFullScreen) { /* Firefox */
                                    document.mozCancelFullScreen();
                                } else if (document.msExitFullscreen) { /* IE/Edge */
                                    document.msExitFullscreen();
                                }
                            } else {
                                // 如果不在全屏状态，则进入全屏
                                // 先添加全屏模式类，确保视频显示完整内容
                                $playerElement.addClass('fullscreen-mode');

                                // 获取DOM元素
                                const playerElement = $playerElement[0];

                                // 创建全屏事件处理器
                                const handleFullscreenChange = function () {
                                    // 检查是否已退出全屏
                                    if (!document.fullscreenElement &&
                                        !document.webkitFullscreenElement &&
                                        !document.mozFullScreenElement &&
                                        !document.msFullscreenElement) {
                                        // 移除全屏样式类
                                        $playerElement.removeClass('fullscreen-mode');

                                        // 移除事件监听器
                                        document.removeEventListener('fullscreenchange', handleFullscreenChange);
                                        document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
                                        document.removeEventListener('mozfullscreenchange', handleFullscreenChange);
                                        document.removeEventListener('MSFullscreenChange', handleFullscreenChange);
                                    }
                                };

                                // 添加全屏变化事件监听
                                document.addEventListener('fullscreenchange', handleFullscreenChange);
                                document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
                                document.addEventListener('mozfullscreenchange', handleFullscreenChange);
                                document.addEventListener('MSFullscreenChange', handleFullscreenChange);

                                // 请求全屏
                                if (playerElement.requestFullscreen) {
                                    playerElement.requestFullscreen();
                                } else if (playerElement.webkitRequestFullscreen) { /* Safari */
                                    playerElement.webkitRequestFullscreen();
                                } else if (playerElement.mozRequestFullScreen) { /* Firefox */
                                    playerElement.mozRequestFullScreen();
                                } else if (playerElement.msRequestFullscreen) { /* IE/Edge */
                                    playerElement.msRequestFullscreen();
                                }

                                layer.msg('患者视频已全屏，按ESC或ALT+H键退出', { time: 2000 });
                            }
                        }
                    } else {
                        layer.msg('当前患者未在线，无法进入患者全屏', { icon: 2 });
                    }
                }

                // 绑定患者全屏按钮点击事件
                $('#patient_fullscreen').on('click', function () {
                    if (!$(this).hasClass('layui-btn-disabled')) {
                        togglePatientFullscreen();
                    }
                });

                // 添加键盘快捷键事件监听
                $(document).on('keydown', function (e) {
                    // Alt+H 快捷键：患者视频全屏显示/退出全屏
                    if (e.altKey && e.keyCode === 72) { // 72是H键的keyCode
                        e.preventDefault(); // 阻止默认行为
                        togglePatientFullscreen();
                    }
                });

                // 需要添加提示的文本域名称数组
                const fieldsToShowTips = [
                    'chief_complaint', 're_chief_complaint', 'urination',
                    'past_medical_history', 'past_medication_history', 'last_medical',
                    'now_needs', 'history_of_present_illness', 'personal_history',
                    'family_history', 'allergy_history', 'diagnosis_information',
                    'treatment_plan'
                ];

                // 为每个文本域添加鼠标悬停事件
                $.each(fieldsToShowTips, function (index, fieldName) {
                    const $input = $(`input[name="${fieldName}"]`);
                    if ($input.length) {
                        let tipsIndex; // 用于存储tips的索引，便于关闭

                        // 鼠标移入显示提示
                        $input.on('mouseover', function () {
                            // 获取文本域的当前值
                            const currentValue = $(this).val();

                            // 只有当文本域有内容时才显示提示
                            if (currentValue && currentValue.trim() !== '') {
                                // 使用layer.tips显示文本域的当前内容
                                tipsIndex = layer.tips(currentValue, this, {
                                    tips: [1, '#3595CC'], // 提示方向和颜色
                                    time: 0, // 不自动关闭
                                    anim: 5 // 动画效果
                                });
                            }
                        });

                        // 鼠标移出关闭提示
                        $input.on('mouseout', function () {
                            // 如果提示层已经创建，则关闭它
                            if (tipsIndex) {
                                layer.close(tipsIndex);
                            }
                        });
                    }
                });

                // 添加麦克风和摄像头控制功能
                // 麦克风控制
                $('#toggle_mic').on('click', function () {
                    if ($(this).hasClass('layui-btn-disabled')) return;

                    if (localStream) {
                        if (isMicEnabled) {
                            // 关闭麦克风
                            localStream.muteAudio();
                            // 更新按钮状态和图标
                            $(this).html('<i class="iconfont">&#xeca9;</i> 麦克风');
                            $(this).removeClass('layui-border-blue').addClass('layui-border-orange');
                            isMicEnabled = false;
                            layer.msg('麦克风已关闭');
                        } else {
                            // 开启麦克风
                            localStream.unmuteAudio();
                            // 更新按钮状态和图标
                            $(this).html('<i class="iconfont">&#xeca7;</i> 麦克风');
                            $(this).removeClass('layui-border-orange').addClass('layui-border-blue');
                            isMicEnabled = true;
                            layer.msg('麦克风已开启');
                        }
                    } else {
                        layer.msg('本地视频流未初始化', { icon: 2 });
                    }
                });

                // 摄像头控制
                $('#toggle_camera').on('click', function () {
                    if ($(this).hasClass('layui-btn-disabled')) return;

                    if (localStream) {
                        if (isCameraEnabled) {
                            // 关闭摄像头
                            localStream.muteVideo();
                            // 更新按钮状态和图标
                            $(this).html('<i class="iconfont">&#xeca5;</i> 摄像头');
                            $(this).removeClass('layui-border-blue').addClass('layui-border-orange');
                            isCameraEnabled = false;
                            layer.msg('摄像头已关闭');
                        } else {
                            // 开启摄像头
                            localStream.unmuteVideo();
                            // 更新按钮状态和图标
                            $(this).html('<i class="iconfont">&#xeca6;</i> 摄像头');
                            $(this).removeClass('layui-border-orange').addClass('layui-border-blue');
                            isCameraEnabled = true;
                            layer.msg('摄像头已开启');
                        }
                    } else {
                        layer.msg('本地视频流未初始化', { icon: 2 });
                    }
                });

                // 需要添加提示的文本域名称数组
            });
            // 初始化xm-select组件
            var triadSelect = xmSelect.render({
                el: '#triad_select',
                language: 'zn',
                data: [
                    { name: '高血压', value: '高血压' },
                    { name: '高血糖', value: '高血糖' },
                    { name: '高血脂', value: '高血脂' },
                    { name: '无', value: '无' }
                ],
                theme: {
                    color: '#4096ff',
                },
                model: {
                    label: {
                        type: 'text'
                    }
                },
                radio: false,
                clickClose: false,
                height: 'auto',
                on: function (data) {
                    // 当选择"无"时，取消其他选项
                    let arr = data.arr;
                    let change = data.change;
                    if (change.length > 0) {
                        let isNone = false;
                        // 检查是否选择了"无"
                        change.forEach(item => {
                            if (item.value === '无') {
                                isNone = true;
                            }
                        });

                        if (isNone) {
                            // 如果选择了"无"，则取消其他选项
                            let newArr = [];
                            arr.forEach(item => {
                                if (item.value === '无') {
                                    newArr.push(item);
                                }
                            });
                            triadSelect.update({
                                data: [
                                    { name: '高血压', value: '高血压', selected: false },
                                    { name: '高血糖', value: '高血糖', selected: false },
                                    { name: '高血脂', value: '高血脂', selected: false },
                                    { name: '无', value: '无', selected: true }
                                ]
                            });
                        } else {
                            // 如果选择了其他选项，则取消"无"
                            let hasNone = false;
                            arr.forEach(item => {
                                if (item.value === '无') {
                                    hasNone = true;
                                }
                            });


                            if (hasNone && arr.length > 1) {
                                let newArr = [];
                                arr.forEach(item => {
                                    if (item.value !== '无') {
                                        newArr.push(item);
                                    }
                                });
                                triadSelect.setValue(newArr.map(item => item.value));
                            }
                        }
                    }

                    // 更新隐藏字段的值
                    let selectedValues = data.arr.map(item => item.value);
                    $('#triad_hidden').val(selectedValues.join(','));
                }
            });
        });
    </script>
</body>

</html>