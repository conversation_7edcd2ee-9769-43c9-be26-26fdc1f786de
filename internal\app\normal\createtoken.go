package normal

import (
	"mstproject/pkg/common"
	"net/http"
	"time"

	"github.com/livekit/protocol/auth"
)

func CreateToken(w http.ResponseWriter, r *http.Request) {
	apiKey := r.URL.Query().Get("apiKey")
	apiSecret := r.URL.Query().Get("apiSecret")
	room := r.URL.Query().Get("room")
	identity := r.URL.Query().Get("identity")

	if apiKey == "" || apiSecret == "" || room == "" || identity == "" {
		data := map[string]interface{}{
			"code": 500,
			"msg":  "参数错误",
			"data": nil,
		}
		common.JSONResponse(w, http.StatusOK, data)
		return
	}

	canPublish := true
	canSubscribe := true

	at := auth.NewAccessToken(apiKey, apiSecret)
	grant := &auth.VideoGrant{
		RoomJoin:     true,
		Room:         room,
		CanPublish:   &canPublish,
		CanSubscribe: &canSubscribe,
	}

	// at.AddGrant(grant).SetIdentity(identity).SetValidFor(time.Hour)
	// at.AddGrant已不支持，下方代码为其平替
	at.SetVideoGrant(grant).SetIdentity(identity).SetValidFor(time.Hour)

	token, _ := at.ToJWT()

	data := map[string]interface{}{
		"code": 200,
		"msg":  "密钥生成成功",
		"data": token,
	}
	common.JSONResponse(w, http.StatusOK, data)

}

// 示例URL：
// https://domain:8086/rtc/createtoken?apiKey=1&apiSecret=ajf90jfo23msadf&room=room18&identity=8
// https://domain:8086/rtc/createtoken?apiKey=APIbxDWetqcjHaa&apiSecret=RlZfytYLmdMOgV2u6fSFAbMhrYQok9B4aVWq48eIE1aa&room=room18&identity=8
