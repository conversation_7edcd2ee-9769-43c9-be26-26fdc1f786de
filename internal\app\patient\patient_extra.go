package patient

import (
	"mstproject/pkg/common"
	"mstproject/pkg/database"
	"net/http"
	"strconv"
)

// 库管 - 下面4个图标，赠品库存 - 当前库存列表
func Warehouse_gifts_list(w http.ResponseWriter, r *http.Request) {
	// TODO: 测试阶段该环节暂未JWT鉴权 - 获取库管员ID
	page := r.FormValue("page")
	if page == "" {
		page = "1"
	}
	limit, err := strconv.Atoi(r.FormValue("limit"))
	if err != nil {
		limit = 10
	}
	offset, err := strconv.Atoi(page)
	if err != nil {
		offset = 1
	}
	offset = (offset - 1) * limit
	type Gifts struct {
		ID          int     `db:"id"`
		Name        string  `db:"name"`
		Gift_id     int     `db:"gift_id"`
		Price       float64 `db:"price"`
		Quantity    int     `db:"quantity"`
		Min_stock   int     `db:"min_stock"`
		Create_time string  `db:"create_time"`
	}
	var gifts []Gifts
	sql := `
		SELECT
		a.id,
		b.name,
		a.gift_id,
		a.price,
		a.quantity,
		a.min_stock,
		a.create_time
		FROM
		warehouse_gifts AS a
		LEFT JOIN gifts AS b ON a.gift_id = b.id
		ORDER BY
		a.id DESC
		LIMIT ? OFFSET ?
	`
	err = database.GetAll(sql, &gifts, limit, offset)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "查询数据失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, limit, offset),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "查询成功",
		"data": gifts,
	})
}

// 赠品出入库日志
func Warehouse_gifts_log_list(w http.ResponseWriter, r *http.Request) {
	// TODO: 测试阶段该环节暂未JWT鉴权 - 获取库管员ID
	page := r.FormValue("page")
	if page == "" {
		page = "1"
	}
	limit, err := strconv.Atoi(r.FormValue("limit"))
	if err != nil {
		limit = 10
	}
	offset, err := strconv.Atoi(page)
	if err != nil {
		offset = 1
	}
	offset = (offset - 1) * limit
	// kind 0出库 1入库
	kind, err := strconv.Atoi(r.FormValue("kind"))
	if err != nil {
		kind = 0
	}
	type GiftsLog struct {
		ID          int    `db:"id"`
		Change_data string `db:"change_data"`
		Old_data    string `db:"old_data"`
		New_data    string `db:"new_data"`
		Notes       string `db:"notes"`
		Create_time string `db:"create_time"`
		Name        string `db:"name"`
		User_name   string `db:"user_name"`
	}
	sql := `
		SELECT
		a.id,
		a.change_data,
		a.old_data,
		a.new_data,
		a.notes,
		a.create_time,
		b.name,
		c.name as user_name
		FROM
		warehouse_gifts_log AS a
		LEFT JOIN gifts AS b ON a.pid = b.id
		LEFT JOIN rbac_user AS c ON c.id = a.user_id
		WHERE
		a.kind = ?
		ORDER BY
		a.id DESC
		LIMIT ? OFFSET ?
	`
	var gifts []GiftsLog
	err = database.GetAll(sql, &gifts, kind, limit, offset)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "查询数据失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, kind, limit, offset),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "查询成功",
		"data": gifts,
	})
}

// 药材 - 库管 - 小程序
// 库管 - 下面4个图标，药材库存 - 当前库存列表
func Warehouse_drug_list(w http.ResponseWriter, r *http.Request) {
	// TODO: 测试阶段该环节暂未JWT鉴权 - 获取库管员ID
	page := r.FormValue("page")
	if page == "" {
		page = "1"
	}
	limit, err := strconv.Atoi(r.FormValue("limit"))
	if err != nil {
		limit = 10
	}
	offset, err := strconv.Atoi(page)
	if err != nil {
		offset = 1
	}
	offset = (offset - 1) * limit
	type Drug struct {
		ID          int     `db:"id"`
		Name        string  `db:"name"`
		Drug_id     int     `db:"drug_id"`
		Price       float64 `db:"price"`
		Quantity    int     `db:"quantity"`
		Min_stock   int     `db:"min_stock"`
		Create_time string  `db:"create_time"`
	}
	var drug []Drug
	sql := `
        SELECT
        a.id,
        b.name,
        a.drug_id,
        a.price,
        a.quantity,
        a.min_stock,
        a.create_time
        FROM
        warehouse_drug AS a
        LEFT JOIN drug AS b ON a.drug_id = b.id
        ORDER BY
        a.id DESC
        LIMIT ? OFFSET ?
    `
	err = database.GetAll(sql, &drug, limit, offset)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "查询数据失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, limit, offset),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "查询成功",
		"data": drug,
	})
}

// 赠品出入库日志
func Warehouse_drug_log_list(w http.ResponseWriter, r *http.Request) {
	// TODO: 测试阶段该环节暂未JWT鉴权 - 获取库管员ID
	page := r.FormValue("page")
	if page == "" {
		page = "1"
	}
	limit, err := strconv.Atoi(r.FormValue("limit"))
	if err != nil {
		limit = 10
	}
	offset, err := strconv.Atoi(page)
	if err != nil {
		offset = 1
	}
	offset = (offset - 1) * limit
	// kind 0出库 1入库
	kind, err := strconv.Atoi(r.FormValue("kind"))
	if err != nil {
		kind = 0
	}
	type DrugLog struct {
		ID          int    `db:"id"`
		Change_data string `db:"change_data"`
		Old_data    string `db:"old_data"`
		New_data    string `db:"new_data"`
		Notes       string `db:"notes"`
		Create_time string `db:"create_time"`
		Name        string `db:"name"`
		User_name   string `db:"user_name"`
	}
	sql := `
        SELECT
        a.id,
        a.change_data,
        a.old_data,
        a.new_data,
        a.notes,
        a.create_time,
        b.name,
        c.name as user_name
        FROM
        warehouse_drug_log AS a
        LEFT JOIN drug AS b ON a.pid = b.id
        LEFT JOIN rbac_user AS c ON c.id = a.user_id
        WHERE
        a.kind = ?
        ORDER BY
        a.id DESC
        LIMIT ? OFFSET ?
    `
	var drug []DrugLog
	err = database.GetAll(sql, &drug, kind, limit, offset)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "查询数据失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, kind, limit, offset),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "查询成功",
		"data": drug,
	})
}

// 库管 - 下面4个图标，成品库存 - 当前库存列表
func Warehouse_finisheddrug_list(w http.ResponseWriter, r *http.Request) {
	// TODO: 测试阶段该环节暂未JWT鉴权 - 获取库管员ID
	page := r.FormValue("page")
	if page == "" {
		page = "1"
	}
	limit, err := strconv.Atoi(r.FormValue("limit"))
	if err != nil {
		limit = 10
	}
	offset, err := strconv.Atoi(page)
	if err != nil {
		offset = 1
	}
	offset = (offset - 1) * limit
	type Finisheddrug struct {
		ID          int     `db:"id"`
		Kind        int     `db:"kind"`
		Pre_id      int     `db:"pre_id"`
		Quantity    float64 `db:"quantity"`
		Status      int     `db:"status"`
		Unit        string  `db:"unit"`
		Notes       string  `db:"notes"`
		Create_time string  `db:"create_time"`
		Update_time string  `db:"update_time"`
	}
	var finisheddrug []Finisheddrug
	sql := `
        select id,kind,pre_id,quantity,status,unit,name as notes,create_time,update_time from warehouse_finisheddrug order by id desc
        LIMIT ? OFFSET ?
    `
	err = database.GetAll(sql, &finisheddrug, limit, offset)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "查询数据失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, limit, offset),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "查询成功",
		"data": finisheddrug,
	})
}

// 赠品出入库日志
func Warehouse_finisheddrug_log_list(w http.ResponseWriter, r *http.Request) {
	// TODO: 测试阶段该环节暂未JWT鉴权 - 获取库管员ID
	page := r.FormValue("page")
	if page == "" {
		page = "1"
	}
	limit, err := strconv.Atoi(r.FormValue("limit"))
	if err != nil {
		limit = 10
	}
	offset, err := strconv.Atoi(page)
	if err != nil {
		offset = 1
	}
	offset = (offset - 1) * limit
	// kind 0出库 1入库
	kind, err := strconv.Atoi(r.FormValue("kind"))
	if err != nil {
		kind = 0
	}
	type FinisheddrugLog struct {
		ID          int     `db:"id"`
		Pre_id      int     `db:"pre_id"`
		Kind        int     `db:"kind"`
		Change_data float64 `db:"change_data"`
		Old_data    float64 `db:"old_data"`
		New_data    float64 `db:"new_data"`
		Notes       string  `db:"notes"`
		Create_time string  `db:"create_time"`
		User_name   string  `db:"user_name"`
	}
	sql := `
        select
        a.id,
        a.pre_id,
        a.kind,
        a.change_data,
        a.old_data,
        a.new_data,
        a.notes,
        a.create_time,
        b.name as user_name
        FROM
          warehouse_finisheddrug_log AS a
          LEFT JOIN rbac_user AS b ON a.user_id = b.id
        WHERE
        a.kind = ?
        ORDER BY
        a.id DESC
        LIMIT ? OFFSET ?
    `
	var finisheddrug []FinisheddrugLog
	err = database.GetAll(sql, &finisheddrug, kind, limit, offset)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "查询数据失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, kind, limit, offset),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "查询成功",
		"data": finisheddrug,
	})
}

// 订单日志
func Warehouse_order_list(w http.ResponseWriter, r *http.Request) {
	// TODO: 测试阶段该环节暂未JWT鉴权 - 获取库管员ID
	page := r.FormValue("page")
	if page == "" {
		page = "1"
	}
	limit, err := strconv.Atoi(r.FormValue("limit"))
	if err != nil {
		limit = 10
	}
	offset, err := strconv.Atoi(page)
	if err != nil {
		offset = 1
	}
	offset = (offset - 1) * limit
	type Orders struct {
		ID           int    `db:"id"`
		Status       int    `db:"status"`
		Express      string `db:"express"`
		Tracking_num string `db:"tracking_num"`
		Address      string `db:"address"`
		Name         string `db:"name"`
		Phone        string `db:"phone"`
		Create_time  string `db:"create_time"`
	}
	var orders []Orders
	sql := `

		SELECT
		a.id,
		a.status,
		a.express,
		a.tracking_num,
		a.address,
		b.name,
		b.phone,
		a.create_time
		FROM
		orders AS a
		LEFT JOIN patient_profile AS b ON a.pat_pro_id = b.id
		WHERE
		a.STATUS > 0
		ORDER BY
		a.id DESC

	
        LIMIT ? OFFSET ?
    `
	err = database.GetAll(sql, &orders, limit, offset)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "查询数据失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, limit, offset),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "查询成功",
		"data": orders,
	})
}
