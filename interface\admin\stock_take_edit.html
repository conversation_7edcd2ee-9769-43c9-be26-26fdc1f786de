<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 编辑盘点单</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
</head>

<body>
    <div class="layui-padding-3">
        <form class="layui-form" lay-filter="form">
            <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">盘点结果</label>
                <div class="layui-input-block">
                    <textarea name="check_result" placeholder="请输入盘点结果" class="layui-textarea"></textarea>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">盘点状态</label>
                <div class="layui-input-block">
                    <input type="radio" name="check_status" value="0" title="未盘点">
                    <input type="radio" name="check_status" value="1" title="已盘点">
                </div>
            </div>

            <!-- 底部按钮 -->
            <div class="layui-form-item" style="text-align: center;">
                <button class="layui-btn" lay-submit lay-filter="save">保存</button>
                <button type="button" class="layui-btn layui-btn-primary" onclick="closeModalWindow()">关闭</button>
            </div>
        </form>
    </div>

    <script>
        layui.use(['form', 'layer'], function () {
            var form = layui.form;
            var layer = layui.layer;
            var $ = layui.$;

            // 获取URL参数
            var id = request.get('id');
            if (!id) {
                layer.msg('缺少必要参数', { icon: 2 });
                return;
            }

            // 加载数据
            layer.load(2);
            $.ajax({
                url: serverUrl + '/admin/stock_take/detail',
                type: 'POST',
                data: { id: id },
                success: function (res) {
                    layer.closeAll('loading');
                    if (res.code === 200) {
                        // 渲染数据到表单
                        form.val('form', {
                            check_result: res.data.Check_result,
                            check_status: res.data.Check_status.toString()
                        });
                        
                        // 如果已盘点，禁用所有输入和按钮
                        if (res.data.Check_status === 1) {
                            $('textarea[name="check_result"]').attr('disabled', true);
                            $('input[name="check_status"]').attr('disabled', true);
                            $('button[lay-filter="save"]').addClass('layui-btn-disabled').attr('disabled', true);
                            form.render();
                        }
                    } else {
                        layer.msg(res.msg || '加载数据失败', { icon: 2 });
                    }
                },
                error: function (res) {
                    layer.closeAll('loading');
                    layer.msg(res.responseJSON.msg, { icon: 2, time: 1000 });
                }
            });

            //监听提交
            form.on('submit(save)', function (data) {
                data.field.id = id;
                layer.load(2);
                $.ajax({
                    url: serverUrl + '/admin/stock_take/edit',
                    type: 'POST',
                    data: data.field,
                    success: function (res) {
                        layer.closeAll('loading');
                        if (res.code === 200) {
                            layer.msg('保存成功', { icon: 1, time: 1000 }, function () {
                                // 刷新父页面表格
                                parent.layui.table.reload('mytable');
                                // 关闭当前弹窗
                                var index = parent.layer.getFrameIndex(window.name);
                                parent.layer.close(index);
                            });
                        } else {
                            layer.msg(res.msg || '保存失败', { icon: 2 });
                        }
                    },
                    error: function (res) {
                        layer.closeAll('loading');
                        layer.msg(res.responseJSON.msg, { icon: 2, time: 1000 });
                    }
                });
                return false;
            });
        });

        function closeModalWindow() {
            var index = parent.layer.getFrameIndex(window.name);
            parent.layer.close(index);
        }
    </script>
</body>

</html>