package main

import (
	"log"
	"mstproject/internal/app/patient"
	"mstproject/internal/routes"
	"mstproject/pkg/config"
	"mstproject/pkg/database"
	"net/http"
	"strconv"
)

var enableSSL string

// CORS 中间件
func cors(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		origin := r.Header.Get("Origin")
		if origin != "" {
			w.<PERSON>er().Set("Access-Control-Allow-Origin", origin)
			w.Header().Set("Access-Control-Allow-Credentials", "true")
		} else {
			w.Header().Set("Access-Control-Allow-Origin", "*")
			w.Header().Set("Access-Control-Allow-Credentials", "false")
		}
		w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		w.<PERSON><PERSON>().Set("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With")
		if r.Method == http.MethodOptions {
			w.WriteHeader(http.StatusNoContent)
			return
		}
		next.ServeHTTP(w, r)
	})
}

func main() {
	database.DB = database.InitDB()
	defer database.Close()
	mux := http.NewServeMux()
	routes.PatientRoutes(mux)
	routes.AdminRoutes(mux)
	routes.NormalRoutes(mux)
	handler := cors(mux)
	mux.Handle("/static/", http.StripPrefix("/static/", http.FileServer(http.Dir(config.Dist_catagory))))
	mux.Handle("/", http.StripPrefix("/", http.FileServer(http.Dir(config.Web_catagory))))

	// 启动WebSocket监控服务，用于RTC房间实时通知
	go patient.StartRtcRoomMonitor()
	// 初始化时通知所有医助有关未处理的RTC房间
	go patient.NotifyAllPendingRtcRooms()

	port := config.BasePort
	useSSL, err := strconv.ParseBool(enableSSL)
	if err != nil {
		useSSL = true
	}
	if useSSL {
		log.Println("Starting server with HTTPS...")
		err := http.ListenAndServeTLS(":"+port, "../cert/fullchain.pem", "../cert/privkey.key", handler)
		if err != nil {
			log.Fatal("Error starting server with HTTPS: ", err)
		}
	} else {
		log.Println("Starting server with HTTP...")
		err := http.ListenAndServe(":"+port, handler)
		if err != nil {
			log.Fatal("Error starting server with HTTP: ", err)
		}
	}
}
