<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 资源添加</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
    <style>
        /* 确保 input 使用 iconfont 字体 */
        input[name="icon"] {
            font-family: 'iconfont';
        }
    </style>
</head>

<body class="layui-padding-5">
    <form class="layui-form" lay-filter="res_edit" action="" onsubmit="return false">
        <div class="layui-form-item">
            <label class="layui-form-label">资源名</label>
            <div class="layui-input-block">
                <input type="text" name="name" required lay-verify="required" placeholder="请填写资源名" autocomplete="off"
                    class="layui-input">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">父级资源</label>
            <div class="layui-input-block">
                <select name="pid"></select>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">资源类型</label>
            <div class="layui-input-block">
                <input type="radio" name="type" value="0" title="菜单" checked>
                <input type="radio" name="type" value="1" title="按钮">
                <input type="radio" name="type" value="2" title="接口">
            </div>
        </div>
        
        <div class="layui-form-item">
            <label class="layui-form-label">图标代码</label>
            <div class="layui-input-inline">
                <input type="text" name="icon" required placeholder="请输入输入框内容" autocomplete="off"
                    class="layui-input">
            </div>
            <!-- 图标演示 -->
            <div class="layui-input-inline" style="width: 50px;text-align: center;padding-top: 10px;">
                <i class='iconfont'></i>
            </div>
            <div class="layui-input-inline">
                <button type="button" class="layui-btn layui-btn-primary chooseicon">选择图标</button>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">链接</label>
            <div class="layui-input-block">
                <input type="text" name="url" required placeholder="请输入链接地址" autocomplete="off"
                    class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">API</label>
            <div class="layui-input-block">
                <input type="text" name="api_path" required placeholder="请输入API资源地址"
                    autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">状态</label>
            <div class="layui-input-block">
                <input type="radio" name="status" value="1" title="可用" checked>
                <input type="radio" name="status" value="0" title="不可用">
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-input-block">
                <button class="layui-btn" lay-submit lay-filter="formDemo">立即提交</button>
                <button id="reset" class="layui-btn layui-btn-primary">重置</button>
            </div>
        </div>
    </form>

    <script>
        var receiveIconData;
        layui.use('form', function () {
            var $ = layui.$;
            var form = layui.form;
            let index = parent.layer.getFrameIndex(window.name);
            let id = window.location.search.split('=')[1];


            $.ajax({
                url: '/admin/perm/get_par_perms',
                type: 'POST',
                data: { pid: 0 },
                dataType: 'json',
                success: function (res) {
                    layer.closeAll('loading');
                    let data = res.data;
                    let options = '<option value="0">顶级目录</option>';
                    data.forEach(item => {
                        options += '<option value="' + item.ID + '">' + item.Name + '</option>';
                    });
                    $('select[name="pid"]').html(options);
                    form.render(); // 刷新表单
                },
                error: function (xhr, status, error) {
                    layer.closeAll('loading');
                    layer.msg('请求失败', { icon: 2, time: 1000 });
                }
            });


            // 选择图标
            $('.chooseicon').click(function () {
                layer.open({
                    type: 2,
                    title: '选择图标',
                    shadeClose: true,
                    area: ['90%', '90%'],
                    content: 'icons.html'
                });
            });
            form.on('submit(formDemo)', function (data) {
                let params = data.field;
                layer.closeAll('loading');
                $.ajax({
                    url: '/admin/perm/add',
                    type: 'POST',
                    data: params,
                    dataType: 'json',
                    success: function (res) {
                        layer.closeAll('loading');
                        if (res.code === 200) {
                            layer.msg(res.msg, { icon: 1, time: 1000 }, function () {
                                parent.window.location.reload();
                            });
                        } else {
                            layer.msg(res.msg, { icon: 2, time: 1000 });
                        }
                    },
                    error: function (xhr, status, error) {
                        layer.closeAll('loading');
                        layer.msg('请求失败', { icon: 2, time: 1000 });
                    }
                });
                return false;
            });
            $('#reset').click(function () {
                window.location.reload();
            });
            receiveIconData = function (iconText, icon_data) {
                $('.iconfont').html(iconText);
                $('input[name="icon"]').val("&#" + icon_data + ";");
            }
        });
    </script>
</body>

</html>