<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 编辑供应商</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
    <style>
        .image-preview-container {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-top: 15px;
        }

        .image-preview-item {
            position: relative;
            width: 150px;
            height: 150px;
        }

        .image-preview-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 4px;
            cursor: pointer;
        }

        .image-preview-item .delete-btn {
            position: absolute;
            bottom: 5px;
            left: 50%;
            transform: translateX(-50%);
            padding: 2px 10px;
            background-color: rgba(255, 255, 255, 0.9);
            border: 1px solid #ddd;
            border-radius: 3px;
            cursor: pointer;
        }

        .delete-btn:hover {
            background-color: #f56c6c;
            color: white;
        }
    </style>
</head>

<body>
    <div class="layui-padding-3">
        <form class="layui-form" lay-filter="form">
            <input type="hidden" name="id">
            <div class="layui-row">
                <!-- 第一列 -->
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">供应商名称</label>
                        <div class="layui-input-block">
                            <input type="text" name="name" lay-verify="required" placeholder="请输入供应商名称"
                                autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">社会代码</label>
                        <div class="layui-input-block">
                            <input type="text" name="code" placeholder="请输入社会统一代码" autocomplete="off"
                                class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">供应商地址</label>
                        <div class="layui-input-block">
                            <input type="text" name="address" placeholder="请输入供应商地址" autocomplete="off"
                                class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">开户银行</label>
                        <div class="layui-input-block">
                            <input type="text" name="bank_name" placeholder="请输入开户银行" autocomplete="off"
                                class="layui-input">
                        </div>
                    </div>
                </div>

                <!-- 第二列 -->
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">联系人姓名</label>
                        <div class="layui-input-block">
                            <input type="text" name="contact_name" placeholder="请输入联系人姓名" autocomplete="off"
                                class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">联系人手机</label>
                        <div class="layui-input-block">
                            <input type="text" name="contact_mobile" placeholder="请输入联系人手机" autocomplete="off"
                                class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">联系人座机</label>
                        <div class="layui-input-block">
                            <input type="text" name="contact_phone" placeholder="请输入联系人座机" autocomplete="off"
                                class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">银行账号</label>
                        <div class="layui-input-block">
                            <input type="text" name="bank_account" placeholder="请输入银行账号" autocomplete="off"
                                class="layui-input">
                        </div>
                    </div>
                </div>
            </div>

            <!-- 经营范围单独占一行 -->
            <div class="layui-row">
                <div class="layui-col-md12">
                    <div class="layui-form-item">
                        <label class="layui-form-label">经营范围</label>
                        <div class="layui-input-block">
                            <textarea name="business_scope" placeholder="请输入经营范围" class="layui-textarea"></textarea>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 供应商资质图片上传 -->
            <div class="layui-row">
                <div class="layui-col-md12">
                    <div class="layui-form-item">
                        <label class="layui-form-label">供应商资质</label>
                        <div class="layui-input-block">
                            <div class="layui-upload layui-padding-4" style="border: 1px solid #eee;">
                                <button type="button" class="layui-btn upload_big_btn" id="upload-qualification-btn">
                                    <div class="btn_big_font">
                                        <i class="layui-icon layui-icon-upload btn_big_font"></i> 资质证明上传
                                    </div>
                                    <div>jpg、png、jpeg、bmp、gif格式，2M以内</div>
                                    <div>支持点击、拖拽和 Ctrl+V 上传</div>
                                </button>
                                <div class="image-preview-container" id="qualification-container"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 状态独占一行 -->
            <div class="layui-row">
                <div class="layui-col-md12">
                    <div class="layui-form-item">
                        <label class="layui-form-label">状态</label>
                        <div class="layui-input-block">
                            <input type="radio" name="status" value="1" title="启用" checked>
                            <input type="radio" name="status" value="0" title="禁用">
                        </div>
                    </div>
                </div>
            </div>

            <!-- 底部按钮 -->
            <div class="layui-form-item" style="text-align: center;">
                <button class="layui-btn" lay-submit lay-filter="save">保存</button>
                <button type="button" class="layui-btn layui-btn-primary" onclick="closeModalWindow()">关闭</button>
            </div>
        </form>
    </div>

    <script>
        layui.use(['form', 'layer', 'upload'], function () {
            var form = layui.form;
            var layer = layui.layer;
            var upload = layui.upload;
            var $ = layui.$;

            // 获取URL参数
            var id = request.get('id');
            if (!id) {
                layer.msg('缺少必要参数', { icon: 2 });
                return;
            }

            // 图片预览函数
            function appendImagePreview(containerId, filepath, filename) {
                const container = $(`#${containerId}`);
                const imageItem = $(`
                    <div class="image-preview-item" data-filename="${filename}" data-filepath="${filepath}">
                        <img src="${filepath}">
                        <div class="delete-btn">删除</div>
                    </div>
                `);

                imageItem.find('img').on('click', function () {
                    layer.photos({
                        photos: {
                            title: '查看图片',
                            data: [{ src: filepath }]
                        },
                        footer: false
                    });
                });

                imageItem.find('.delete-btn').on('click', function () {
                    layer.confirm('确定删除该图片吗？', { icon: 3, title: '提示' }, function (index) {
                        $.ajax({
                            url: '/admin/normal_pic_del',
                            type: 'POST',
                            data: {
                                filename: filename,
                                category: 'qualification'
                            },
                            success: function (res) {
                                if (res.code === 200) {
                                    imageItem.remove();
                                    layer.msg('删除成功', { icon: 1, time: 1000 });
                                }
                            },
                            error: function (data) {
                                layer.confirm('删除失败: ' + data.responseJSON.msg + '，是否移除该图片？',
                                    { icon: 3, title: '提示' },
                                    function (index) {
                                        imageItem.remove();
                                        layer.msg('移除成功', { icon: 1, time: 1000 });
                                    }
                                );
                            }
                        });
                        layer.close(index);
                    });
                });

                container.append(imageItem);
            }

            // 初始化文件上传
            upload.render({
                elem: '#upload-qualification-btn',
                url: '/admin/upload_normal_pic',
                multiple: true,
                data: {
                    category: 'qualification',
                },
                drag: true,
                before: function(obj) {
                    obj.preview(function(index, file, result) {
                        // 在预览回调中进行文件上传
                        autoCompressAndUpload(file, {
                            data: { category: 'qualification' },
                            success: function(res) {
                                if (res.code === 200) {
                                    const fileInfo = res.data[0];
                                    appendImagePreview('qualification-container', fileInfo.filepath, fileInfo.filename);
                                    layer.msg('上传成功', {icon: 1, time: 1000});
                                } else {
                                    layer.msg(res.msg || '上传失败', {icon: 2});
                                }
                            },
                            error: function(error) {
                                layer.msg(error.message || '上传出错', {icon: 2});
                            }
                        });
                    });
                    return false;
                }
            });

            // 添加粘贴上传功能
            document.addEventListener('paste', function(event) {
                const items = event.clipboardData && event.clipboardData.items;
                let files = [];
                
                if (items && items.length) {
                    // 遍历剪切板内容
                    for (let i = 0; i < items.length; i++) {
                        if (items[i].type.indexOf('image') !== -1) {
                            const file = items[i].getAsFile();
                            if (file) {
                                files.push(file);
                            }
                        }
                    }
                }
                
                if (files.length === 0) {
                    return;
                }
                
                // 阻止默认粘贴行为
                event.preventDefault();

                // 使用Promise.all处理多个文件上传
                const uploadPromises = files.map(file => {
                    return new Promise((resolve, reject) => {
                        autoCompressAndUpload(file, {
                            data: { category: 'qualification' },
                            success: function(res) {
                                if (res.code === 200) {
                                    resolve(res);
                                } else {
                                    reject(new Error(res.msg || '上传失败'));
                                }
                            },
                            error: function(error) {
                                reject(error);
                            }
                        });
                    });
                });

                // 处理所有上传
                Promise.all(uploadPromises)
                    .then(results => {
                        results.forEach(res => {
                            const fileInfo = res.data[0];
                            appendImagePreview('qualification-container', fileInfo.filepath, fileInfo.filename);
                        });
                        layer.msg('上传成功', {icon: 1, time: 1000});
                    })
                    .catch(error => {
                        layer.msg(error.message || '上传出错', {icon: 2});
                    });
            });

            // 加载数据
            layer.load(2);
            $.ajax({
                url: serverUrl + '/admin/supplier_drug/detail',
                type: 'POST',
                data: { id: id },
                success: function (res) {
                    layer.closeAll('loading');
                    if (res.code === 200) {
                        // 将接口返回的数据转换为表单字段格式
                        var formData = {
                            id: res.data.ID,
                            name: res.data.Name,
                            code: res.data.Code,
                            address: res.data.Address,
                            bank_name: res.data.Bank_name,
                            bank_account: res.data.Bank_account,
                            contact_name: res.data.Contact_name,
                            contact_mobile: res.data.Contact_mobile,
                            contact_phone: res.data.Contact_phone,
                            business_scope: res.data.Business_scope,
                            status: res.data.Status.toString()
                        };
                        // 为表单赋值
                        form.val('form', formData);

                        // 渲染资质图片
                        if (res.data.Qualification) {
                            res.data.Qualification.split('\n').forEach(function (filename) {
                                if (filename) {
                                    appendImagePreview(
                                        'qualification-container',
                                        `/static/uploads/normal_pics/qualification/${filename}`,
                                        filename
                                    );
                                }
                            });
                        }
                    } else {
                        layer.msg(res.msg || '加载失败', { icon: 2 });
                    }
                },
                error: function (res) {
                    layer.closeAll('loading');
                    layer.msg(res.responseJSON.msg, { icon: 2, time: 1000 });
                }
            });

            //监听提交
            form.on('submit(save)', function (data) {
                // 收集图片数据
                let qualification_pics = [];
                $('#qualification-container .image-preview-item').each(function () {
                    qualification_pics.push($(this).data('filename'));
                });
                data.field.qualification = qualification_pics.join('\n');

                layer.load(2);
                $.ajax({
                    url: serverUrl + '/admin/supplier_drug/edit',
                    type: 'POST',
                    data: data.field,
                    success: function (res) {
                        layer.closeAll('loading');
                        if (res.code === 200) {
                            layer.msg('保存成功', { icon: 1, time: 1000 }, function () {
                                // 刷新父页面表格
                                parent.layui.table.reload('mytable');
                                // 关闭当前弹窗
                                var index = parent.layer.getFrameIndex(window.name);
                                parent.layer.close(index);
                            });
                        } else {
                            layer.msg(res.msg || '保存失败', { icon: 2 });
                        }
                    },
                    error: function (res) {
                        layer.closeAll('loading');
                        layer.msg(res.responseJSON.msg, { icon: 2, time: 1000 });
                    }
                });
                return false;
            });
        });
    </script>
</body>

</html>