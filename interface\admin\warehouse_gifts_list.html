<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 赠品库存管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
</head>

<body>
    <div class="layui-layout layui-layout-admin">
        <!-- 头部 -->
        <div class="layui-header">
            <div class="layui-logo">幸年堂</div>
            <ul class="layui-nav layui-layout-right">
                <li class="layui-nav-item" style="background-color:transparent!important;padding-right:10px">
                    <a href="javascript:;" style="display:flex;align-items:center;color:#fff"><img
                            src="/dist/images/user.png" class="layui-nav-img"><span class="user_name"></span></a>
                    <dl class="layui-nav-child pop_menu">
                        <dd><i class="iconfont">&#xe62f;</i><a href="change_password.html#7">用户资料</a></dd>
                        <dd><i class="iconfont">&#xe601;</i><a href="javascript:void(0)" class="refresh_cache">刷新缓存</a>
                        </dd>
                        <dd><i class="iconfont">&#xe634;</i><a href="login_out.html">退出登录</a></dd>
                    </dl>
                </li>
            </ul>
        </div>
        <!-- 导航 -->
        <ul class="layui-nav layui-nav-tree layui-nav-side" id="menuContainer" lay-bar="disabled" lay-accordion>
            <div style="margin: 50px 0;color: #bbb;text-align: center;">菜单加载中...</div>
        </ul>
        <!-- 主体内容 -->
        <div class="layui-body">
            <div class="body_child">
                <div class="layui-panel">
                    <div class="layui-card-header">
                        <div class="layui-row" style="padding-top:10px;">
                            <div class="layui-col-md11 layui-col-sm10" style="display: flex;">
                                <div>赠品库存管理</div>
                            </div>
                            <div class="layui-col-md1 layui-col-sm2 perm_check_btn" style="text-align: right;"
                                res_id="118">
                                <button class="layui-btn layui-btn-primary layui-border-red" lay-event="add"
                                    onclick="add()">
                                    <i class="layui-icon">&#xe654;</i> 采购入库
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="layui-padding-3">
                        <div id="data_search">
                            <div class="layui-form">
                                <div class="layui-row">
                                    <div class="layui-col-md3" style="min-width:380px;">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label">搜索</label>
                                            <div class="layui-input-block">
                                                <input type="text" name="key" placeholder="请输入赠品名称" autocomplete="off"
                                                    class="layui-input" lay-affix="clear">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-md3">
                                        <div class="layui-form-item">
                                            <div class="layui-input-inline" style="margin-left: 10px;">
                                                <button class="layui-btn" lay-submit lay-filter="search"
                                                    style="width: 120px;">筛选</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <table id="mytable" lay-filter="mytable" class="layui-table" lay-skin="row"></table>
                    </div>
                </div>
                <br>
                <br>
            </div>
        </div>
    </div>

    <script type="text/html" id="TPL-bar">
        <button class="layui-btn layui-btn-xs perm_check_btn" lay-event="detail">详情</button>
    </script>

    <script>
        layui.use(['element', 'layer', 'util'], function () {
            var element = layui.element;
            var form = layui.form;
            var layer = layui.layer;
            var util = layui.util;
            var table = layui.table;
            var $ = layui.$;

            // 页面顶部插入缓存的用户数据
            let local_userinfo = localStorage.getItem('local_userinfo');
            let user_name = "";
            if (local_userinfo) {
                local_userinfo = JSON.parse(local_userinfo);
                user_name = local_userinfo.Name + " ( " + local_userinfo.Roles + " )";
            } else {
                user_name = "未登录";
            }
            $('.user_name').text(user_name);
            render_menu($);
            $('.refresh_cache').click(function () {
                refresh_cache($);
            });

            //引入TABLE模块
            layer.load(2);
            table.render({
                elem: '#mytable'
                , even: true
                , url: serverUrl + "/admin/warehouse_gifts/list"
                , method: 'post'
                , response: {
                    statusName: 'code'
                    , statusCode: 200
                    , msgName: 'msg'
                    , countName: 'count'
                    , dataName: 'data'
                }
                , cols: [[
                    { field: 'ID', title: 'ID', align: 'center', width: 80 }
                    , { field: 'Gift_id', title: '赠品ID', align: 'center' }
                    , {
                        field: 'Gift_name',
                        title: '赠品名称',
                        minWidth: 150,
                        templet: function (d) {
                            return '<a href="javascript:;" class="btn_arg_pm" lay-event="gift_detail">' + d.Gift_name + '</a>';
                        }
                    }
                    , { field: 'Price', title: '进货价', align: 'center' }
                    , { field: 'Quantity', title: '数量', align: 'center' }
                    , {
                        field: 'Create_time', title: '创建时间', width: 180, align: 'center',
                        templet: function (d) {
                            return d.Create_time.replace('T', ' ').replace('Z', '');
                        }
                    }
                    , { title: '操作', align: 'center', toolbar: '#TPL-bar', width: 200, fixed: 'right' }
                ]]
                , page: true
                , limit: 12
                , done: function () {
                    layer.closeAll('loading');
                }
            });
            //检查菜单权限
            render_button($);
            //监听工具条
            table.on('tool(mytable)', function (obj) {
                var data = obj.data;
                if (obj.event === 'detail') {
                    layer.open({
                        type: 2,
                        title: '赠品库存详情',
                        area: ['600px', '500px'],
                        shadeClose: true,
                        content: 'warehouse_gifts_detail.html?id=' + data.ID
                    });
                } else if (obj.event === 'gift_detail') {
                    layer.open({
                        type: 2,
                        title: '赠品详情',
                        area: ['500px', '400px'],
                        shadeClose: true,
                        content: '/admin/gift_detail.html?id=' + data.Gift_id
                    });
                }
            });

            //监听搜索
            form.on('submit(search)', function (data) {
                layer.load(2);
                table.reload('mytable', {
                    where: data.field
                    , page: {
                        curr: 1
                    }
                });
                return false;
            });

            // 添加赠品库存函数定义
            window.add = function () {
                layer.open({
                    type: 2,
                    title: '赠品入库',
                    shadeClose: true,
                    area: ['1000px', '730px'],
                    content: 'warehouse_gifts_add.html'
                });
            };
        });
    </script>
</body>

</html>