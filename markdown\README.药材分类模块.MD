# 药材分类模块（drug_category）开发

## 1. 技术栈
- 前端：HTML + LayUI
- 后端：Golang
- 数据库：MySQL
- 开发模式：前后端分离

## 2. 目录结构
```
└─ interface/
    └── admin/
        ├── drug_category_list.html    # 列表页，文件不存在，需要新增文件，参考页面：supplier_drug_list.html
        ├── drug_category_add.html     # 添加页，文件不存在，需要新增文件，参考页面：supplier_drug_add.html
        ├── drug_category_edit.html    # 编辑页，文件不存在，需要新增文件，参考页面：supplier_drug_edit.html
        └── drug_category_detail.html  # 详情页，文件不存在，需要新增文件，参考页面：supplier_drug_detail.html
```

## 3. 对应的后端接口
   ```
   /admin/drug_category/list
   /admin/drug_category/add
   /admin/drug_category/edit
   /admin/drug_category/detail
   /admin/drug_category/del
   ```