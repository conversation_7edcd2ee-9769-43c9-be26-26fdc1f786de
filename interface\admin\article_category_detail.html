<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 文章分类详情</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
</head>

<body>
    <div class="layui-padding-3" style="margin:30px 50px 20px 0;">
        <div class="layui-row layui-col-space15">
            <!-- 第一列 -->
            <div class="layui-col-md6">
                <div class="layui-form-item">
                    <label class="layui-form-label">名称</label>
                    <div class="layui-input-block">
                        <div class="detail-text" id="title"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">描述</label>
                    <div class="layui-input-block">
                        <div class="detail-text" id="description"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">排序</label>
                    <div class="layui-input-block">
                        <div class="detail-text" id="sort"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">创建时间</label>
                    <div class="layui-input-block">
                        <div class="detail-text" id="created_time"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-form-item">
            <div class="layui-input-block">
                <button type="button" class="layui-btn layui-btn-primary" onclick="closeModalWindow()">关闭</button>
            </div>
        </div>
    </div>

    <script>
        layui.use(['layer'], function () {
            var layer = layui.layer;
            var $ = layui.$;

            // 获取URL中的ID参数
            var id = getUrlParam('id');

            // 如果有ID，则加载数据
            if (id) {
                layer.load(2);
                $.ajax({
                    url: serverUrl + '/admin/article_category/detail',
                    type: 'POST',
                    data: { id: id },
                    success: function (res) {
                        layer.closeAll('loading');
                        if (res.code === 200) {
                            var data = res.data;
                            $('#title').text(data.Title || '--');
                            $('#description').text(data.Description || '--');
                            $('#sort').text(data.Sort || '--');
                            $('#created_time').text(data.Create_time || '--');
                        } else {
                            layer.msg(res.msg || '数据加载失败', { icon: 2 });
                        }
                    },
                    error: function (res) {
                        layer.closeAll('loading');
                        layer.msg(res.responseJSON.msg, { icon: 2, time: 1000 });
                    }
                });
            }
        });

        function closeModalWindow() {
            var index = parent.layer.getFrameIndex(window.name);
            parent.layer.close(index);
        }

        function getUrlParam(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return decodeURI(r[2]);
            return null;
        }
    </script>
</body>

</html> 