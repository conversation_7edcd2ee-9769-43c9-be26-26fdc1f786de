package main

import (
    "fmt"
    "io/ioutil"
    "net/http"
    "net/url"
    "strings"
)

func Get_KD100_Cookies(com, nu string) ([]*http.Cookie, error) {
    // Create request URL with variables
    requestURL := fmt.Sprintf("https://m.kuaidi100.com/result.jsp?com=%s&nu=%s", com, nu)
    
    // Create request
    req, err := http.NewRequest("GET", requestURL, nil)
    if err != nil {
        return nil, fmt.Errorf("error creating request: %w", err)
    }
    
    // Set headers
    req.Header.Add("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7")
    req.Header.Add("Accept-Language", "zh-CN,zh;q=0.9")
    req.Header.Add("Connection", "keep-alive")
    req.Header.Add("Sec-Fetch-Dest", "document")
    req.Header.Add("Sec-Fetch-Mode", "navigate")
    req.Header.Add("Sec-Fetch-Site", "none")
    req.Header.Add("Sec-Fetch-User", "?1")
    req.Header.Add("Upgrade-Insecure-Requests", "1")
    req.Header.Add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
    req.Header.Add("sec-ch-ua", `"Chromium";v="134", "Not:A-Brand";v="24", "Google Chrome";v="134"`)
    req.Header.Add("sec-ch-ua-mobile", "?0")
    req.Header.Add("sec-ch-ua-platform", `"Windows"`)
    
    // Create client with cookie jar
    client := &http.Client{}
    
    // Make the request
    resp, err := client.Do(req)
    if err != nil {
        return nil, fmt.Errorf("error making request: %w", err)
    }
    defer resp.Body.Close()
    
    // Return cookies
    return resp.Cookies(), nil
}

func getTrackingInfo(com, nu , phone string) (string, error) {
    // First get cookies
    cookies, err := Get_KD100_Cookies(com, nu)
    if err != nil {
        return "", fmt.Errorf("error getting cookies: %w", err)
    }
    
    // Create request URL
    requestURL := "https://m.kuaidi100.com/query"
    
    // Create form data
    formData := url.Values{}
    formData.Set("postid", nu)
    formData.Set("id", "1")
    formData.Set("valicode", "")
    formData.Set("temp", "0.43716214544888965") // This might need to be randomized
    formData.Set("type", com)
    formData.Set("phone", phone)
    formData.Set("token", "")
    formData.Set("platform", "MWWW")
    
    // Create request
    req, err := http.NewRequest("POST", requestURL, strings.NewReader(formData.Encode()))
    if err != nil {
        return "", fmt.Errorf("error creating request: %w", err)
    }
    
    // Set headers
    req.Header.Add("Accept", "application/json, text/javascript, */*; q=0.01")
    req.Header.Add("Accept-Language", "zh-CN,zh;q=0.9")
    req.Header.Add("Connection", "keep-alive")
    req.Header.Add("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8")
    req.Header.Add("Origin", "https://m.kuaidi100.com")
    req.Header.Add("Referer", fmt.Sprintf("https://m.kuaidi100.com/result.jsp?com=%s&nu=%s", com, nu))
    req.Header.Add("Sec-Fetch-Dest", "empty")
    req.Header.Add("Sec-Fetch-Mode", "cors")
    req.Header.Add("Sec-Fetch-Site", "same-origin")
    req.Header.Add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
    req.Header.Add("X-Requested-With", "XMLHttpRequest")
    req.Header.Add("sec-ch-ua", `"Chromium";v="134", "Not:A-Brand";v="24", "Google Chrome";v="134"`)
    req.Header.Add("sec-ch-ua-mobile", "?0")
    req.Header.Add("sec-ch-ua-platform", `"Windows"`)
    
    // Add cookies
    for _, cookie := range cookies {
        req.AddCookie(cookie)
    }
    
    // Create client
    client := &http.Client{}
    
    // Make the request
    resp, err := client.Do(req)
    if err != nil {
        return "", fmt.Errorf("error making request: %w", err)
    }
    defer resp.Body.Close()
    
    // Read response
    body, err := ioutil.ReadAll(resp.Body)
    if err != nil {
        return "", fmt.Errorf("error reading response: %w", err)
    }
    
    return string(body), nil
}

func main() {
    // Example usage
    com := "sut56"
    nu := "8000041340957"
    phone := "1212"
    
    result, err := getTrackingInfo(com, nu ,phone)
    if err != nil {
        fmt.Printf("Error: %v\n", err)
        return
    }
    
    fmt.Println(result)
}