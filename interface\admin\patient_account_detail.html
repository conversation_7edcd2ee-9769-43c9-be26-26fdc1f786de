<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 帐号信息修改</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
    <style>
        #avatar_img {
            border-radius: 50%;
            border: 1px solid #ccc;
            width: 80px;
            height: 80px;
            position: absolute;
            right: 81px;
            top: 9px;
        }

        #Department i {
            margin-right: 5px;
        }
    </style>
</head>

<body class="layui-padding-2">

    <div class="layui-card">
        <div class="layui-card-body data-body">
        </div>
    </div>

    <script>
        layui.use(['form', 'upload', 'jquery', 'layer'], function () {
            var form = layui.form;
            var upload = layui.upload;
            var $ = layui.jquery;
            var layer = layui.layer;
            var dropdown = layui.dropdown;
            var index = parent.layer.getFrameIndex(window.name);
            var pid = 0;
            var id = Number(request.get("id"));

            if (!id) {
                layer.msg("参数错误，请从列表页面进入编辑页面", { icon: 2, time: 1000 });
                return false;
            }

            layer.load(2);
            $.ajax({
                url: '/admin/patient_account/detail',
                type: 'post',
                data: { id: id },
                dataType: 'json',
                success: function (res) {
                    layer.closeAll('loading');
                    var data = res.data;
                    var html = '';
                    html += '<form class="layui-form" action="">';

                    html += '<div class="layui-form-item">';
                    html += '<label class="layui-form-label">手机号</label>';
                    html += '<div class="layui-input-block"><input type="text" name="phone" value="' + data.Phone + '" required lay-verify="required" autocomplete="off" class="layui-input"></div>';
                    html += '</div>';

                    html += '<div class="layui-form-item">';
                    html += '<label class="layui-form-label">密码</label>';
                    html += '<div class="layui-input-block"><input type="password" name="pwd" autocomplete="off" class="layui-input" placeholder="不修改密码请留空"></div>';
                    html += '</div>';


                    html += '<div class="layui-form-item">';
                    html += '<label class="layui-form-label">绑定微信</label>';
                    html += '<div class="layui-form-mid">' + (data.Is_bind == 1 ? '是' : '否') + '</div>';
                    html += '</div>';

                    html += '<div class="layui-form-item">';
                    html += '<label class="layui-form-label">医助</label>';
                    html += '<div class="layui-form-mid" id="asst_name_container">' + (data.Asst_id == 0 ? '未绑定' : data.Asst_id) + '</div>';
                    html += '</div>';

                    // 如果医助ID不为0，获取医助名称
                    if (data.Asst_id && data.Asst_id != 0) {
                        $.ajax({
                            url: '/admin/user/list_low',
                            type: 'post',
                            data: { id: data.Asst_id },
                            dataType: 'json',
                            success: function(res) {
                                if (res.code == 200 && res.data && res.data.length > 0) {
                                    $('#asst_name_container').html(res.data[0].Name + ' <span class="layui-badge layui-bg-gray">ID: ' + data.Asst_id + '</span>');
                                }
                            }
                        });
                    }

                    html += '<div class="layui-form-item">';
                    html += '<label class="layui-form-label">售后流程？</label>';
                    html += '<div class="layui-form-mid">' + (data.Is_transfer == 1 ? '是' : '否') + '</div>';
                    html += '</div>';

                    html += '<div class="layui-form-item">';
                    html += '<label class="layui-form-label">状态</label>';
                    html += '<div class="layui-input-block">';
                    // 添加 lay-filter="status"
                    html += '<input type="checkbox" name="status" lay-skin="switch" lay-text="启用|禁用" lay-filter="status" ' + (data.Status == 1 ? 'checked' : '') + '>';
                    html += '</div>';
                    html += '</div>';

                    html += '<div class="layui-form-item">';
                    html += '<label class="layui-form-label">最后登录IP</label>';
                    html += '<div class="layui-form-mid">' + (data.Last_login_ip == '0' ? '未登录' : data.Last_login_ip) + '</div>';
                    html += '</div>';

                    html += '<div class="layui-form-item">';
                    html += '<label class="layui-form-label">最后登录</label>';
                    html += '<div class="layui-form-mid">' + data.Last_login_time.replace("T", " ").replace("Z", "") + '</div>';
                    html += '</div>';

                    html += '<div class="layui-form-item">';
                    html += '<label class="layui-form-label">创建时间</label>';
                    html += '<div class="layui-form-mid">' + data.Create_time.replace("T", " ").replace("Z", "") + '</div>';
                    html += '</div>';

                    html += '<div class="layui-form-item">';
                    html += '<div class="layui-input-block">';
                    html += '<button class="layui-btn" lay-submit lay-filter="formDemo">保存设置</button>';
                    html += '<button type="button" class="layui-btn layui-btn-primary layui-border-green" id="qrCodeBtn">小程序码</button>';
                    html += '</div>';
                    html += '</div>';

                    html += '</form>';

                    $('.data-body').html(html);
                    form.render();
                    form.on('switch(status)', function (data) {
                        console.log('状态切换:', data.elem.checked);
                        var checked = data.elem.checked;
                        if (!checked) {
                            layer.confirm('您确定要停用该帐号吗？', {
                                icon: 3,
                                title: '提示'
                            }, function (index) {
                                layer.close(index);
                            }, function (index) {
                                layer.close(index);
                                data.elem.checked = true;
                                form.render();
                            });
                        }
                    });

                    // 小程序码按钮点击事件
                    $('#qrCodeBtn').on('click', function() {
                        layer.load(2);
                        $.ajax({
                            url: '/admin/get_wx_bind_user_acode',
                            type: 'post',
                            data: { user_id: id },
                            dataType: 'json',
                            success: function(res) {
                                layer.closeAll('loading');
                                if (res.code == 200) {
                                    // 弹出图片层
                                    layer.open({
                                        type: 1,
                                        title: '患者微信小程序码',
                                        area: ['350px', '400px'],
                                        shadeClose: true,
                                        content: '<div style="text-align:center;padding:20px;"><img src="' + res.data.base64Img + '" style="max-width:100%;"></div>'
                                    });
                                } else {
                                    layer.msg(res.msg || '获取小程序码失败', {icon: 2, time: 2000});
                                }
                            },
                            error: function(xhr) {
                                layer.closeAll('loading');
                                layer.msg('请求失败，请稍后再试', {icon: 2, time: 2000});
                            }
                        });
                    });

                    form.on('submit(formDemo)', function (data) {
                        event.preventDefault();
                        var formData = data.field;
                        formData.id = id;
                        formData.status = formData.status == 'on' ? 1 : 0;
                        $.ajax({
                            url: '/admin/patient_account/edit',
                            type: 'post',
                            data: formData,
                            dataType: 'json',
                            success: function (res) {
                                if (res.code == 200) {
                                    parent.layer.msg(res.msg, { time: 1000 });
                                    parent.layer.close(index);
                                    // 重新载入父页面的表格mytable
                                    parent.layui.table.reload('mytable');
                                }
                            },
                            error: function (xhr, status, error) {
                                layer.msg('请求失败，请稍后再试', { time: 1000 });
                            }
                        });
                        return false;
                    });
                }
            });
        });
    </script>
</body>

</html>