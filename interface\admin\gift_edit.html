<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 编辑赠品</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
</head>

<body>
    <div class="layui-padding-3">
        <form class="layui-form" lay-filter="form">
            <input type="hidden" name="id">
            
            <div class="layui-row layui-col-space15">
                <!-- 第一列 -->
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">赠品名称</label>
                        <div class="layui-input-block">
                            <input type="text" name="name" lay-verify="required" placeholder="请输入赠品名称" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">出厂日期</label>
                        <div class="layui-input-block">
                            <input type="text" name="factory_date" id="factory_date" placeholder="请选择出厂日期" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">参考价格</label>
                        <div class="layui-input-block">
                            <input type="number" name="price" placeholder="请输入参考价格" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button type="submit" class="layui-btn" lay-submit lay-filter="form">保存</button>
                    <button type="button" class="layui-btn layui-btn-primary" onclick="closeModalWindow()">关闭</button>
                </div>
            </div>
        </form>
    </div>

    <script>
        layui.use(['form', 'laydate'], function () {
            var form = layui.form;
            var laydate = layui.laydate;
            var $ = layui.$;

            // 初始化日期选择器
            laydate.render({
                elem: '#factory_date',
                type: 'date'
            });

            // 获取URL中的ID参数
            var id = request.get('id');
            if (!id) {
                layer.msg('ID不能为空', { icon: 2 });
                return;
            }

            // 加载赠品详情数据
            $.ajax({
                url: serverUrl + '/admin/gift/detail',
                type: 'POST',
                data: { id: id },
                success: function (res) {
                    if (res.code === 200) {
                        var data = res.data;
                        var formData = {
                            id: data.ID,
                            name: data.Name,
                            factory_date: data.Factory_date ? data.Factory_date.split('T')[0] : '',
                            price: data.Price
                        };
                        // 为表单赋值
                        form.val('form', formData);
                    } else {
                        layer.msg(res.msg || '加载失败', { icon: 2 });
                    }
                },
                error: function (res) {
                    layer.msg(res.responseJSON.msg, { icon: 2, time: 1000 });
                }
            });

            // 监听表单提交
            form.on('submit(form)', function (data) {
                var formData = data.field;
                
                $.ajax({
                    url: serverUrl + '/admin/gift/edit',
                    type: 'POST',
                    data: formData,
                    success: function (res) {
                        if (res.code === 200) {
                            layer.msg('保存成功', { icon: 1, time: 1000 }, function () {
                                closeModalWindow();
                            });
                        } else {
                            layer.msg(res.msg || '保存失败', { icon: 2 });
                        }
                    },
                    error: function (res) {
                        layer.msg(res.responseJSON.msg, { icon: 2, time: 1000 });
                    }
                });
                return false;
            });
        });

        function closeModalWindow() {
            var index = parent.layer.getFrameIndex(window.name);
            parent.layer.close(index);
        }
    </script>
</body>

</html> 