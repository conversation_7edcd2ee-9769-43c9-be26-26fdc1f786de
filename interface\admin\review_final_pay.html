<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 审核尾款</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
    <style>
        /* 高级质感 */
        .premium-section {
            margin-bottom: 15px;
            padding: 15px;
            border-radius: 8px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        }

        .final-pay-pic {
            max-width: 100%;
            display: block;
            margin: 0 auto;
            cursor: pointer;
        }

        .final-pay-pic img {
            border-radius: 10px;
        }

        .image-preview-container-normal img {
            margin: 0 !important;
        }

        /* 玻璃拟态效果 */
        .glassmorphism {
            background: rgba(255, 255, 255, 0.7);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.18);
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.15);
            padding: 15px;
            margin-bottom: 15px;
        }

        .currency-symbol {
            font-size: 16px;
            color: #666;
            margin-right: 5px;
        }

        .payment-value.highlight {
            color: #FF5722;
            font-size: 18px;
            font-weight: bold;
        }

        /* 美化审核选项 - 更现代的设计 */
        .review-options {
            display: flex;
            gap: 15px;
            margin-top: 5px;
        }

        .review-option {
            flex: 1;
            padding: 12px 10px;
            border-radius: 8px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
            background: linear-gradient(135deg, #f5f5f5 0%, #e0e0e0 100%);
            border: 1px solid #d5d5d5;
            color: #666;
        }

        .review-option.selected {
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        }

        .review-option.approve.selected {
            background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
            border: 1px solid #a5d6a7;
            color: #2E7D32;
        }

        .review-option.reject.selected {
            background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
            border: 1px solid #ef9a9a;
            color: #C62828;
        }

        .review-option i {
            font-size: 18px;
            margin-right: 8px;
            display: none;
            /* 默认隐藏图标 */
        }

        .review-option.selected i {
            display: inline-block;
            /* 选中时显示图标 */
        }

        .review-option.approve.selected i {
            color: #2E7D32;
        }

        .review-option.reject.selected i {
            color: #C62828;
        }

        .review-option h3 {
            font-size: 15px;
            margin: 0;
            font-weight: 500;
        }

        /* 动画效果 */
        .animated-value {
            transition: all 0.3s ease;
        }

        .animated-value.changed {
            animation: highlight 1s ease;
        }

        @keyframes highlight {
            0% {
                background-color: rgba(255, 87, 34, 0.1);
            }

            100% {
                background-color: transparent;
            }
        }

        /* 金额显示区域 */
        .amount-display {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 15px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .amount-item {
            padding: 8px;
            text-align: center;
        }

        .amount-item:not(:last-child) {
            border-right: 1px dashed rgba(0, 0, 0, 0.1);
        }

        .amount-label {
            color: #666;
            font-weight: 500;
            font-size: 13px;
            margin-bottom: 3px;
        }

        .amount-value {
            font-weight: 600;
            color: #333;
            font-size: 16px;
        }

        .final-amount {
            color: #FF5722;
            font-size: 18px;
        }

        /* 小尺寸文本域 */
        .small-textarea {
            min-height: 50px !important;
            height: 50px !important;
        }

        /* 图片预览区域 */
        .image-preview-container-normal {
            min-height: 120px;
            max-height: 180px;
            border: 1px dashed #e6e6e6;
            border-radius: 4px;
            padding: 8px;
            background-color: #fafafa;
            overflow: auto;
        }

        /* 审核结果标记 */
        .review-result {
            position: absolute;
            top: 8px;
            right: 8px;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 12px;
        }

        .review-result.approve {
            background-color: #4CAF50;
        }

        .review-result.reject {
            background-color: #F44336;
        }

        /* 按钮美化 */
        .premium-btn {
            height: 38px;
            line-height: 38px;
            padding: 0 20px;
            font-size: 14px;
            border-radius: 4px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .premium-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }

        .premium-btn-primary {
            background: linear-gradient(135deg, #1E9FFF 0%, #0C8FE9 100%);
        }

        .premium-btn-default {
            background: linear-gradient(135deg, #F2F2F2 0%, #E6E6E6 100%);
            color: #666;
        }

        /* 紧凑布局 */
        .compact-card .layui-card-body {
            padding: 10px 15px;
        }

        /* 备注文本域容器 */
        .remark-container {
            margin-top: 10px;
            margin-bottom: 10px;
        }

        /* 图片容器高度限制 */
        .image-preview-item img {
            max-height: 180px;
        }

        /* 标题样式优化 */
        .section-title {
            margin-bottom: 10px;
            font-size: 14px;
            font-weight: 500;
        }

        /* 尾款结算方式标签样式 */
        .payment-type-tag {
            position: absolute;
            top: -20px;
            right: -10px;
            z-index: 10;
            padding: 1px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 500;
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
            transform: scale(0.9);
            transition: all 0.3s ease;
            white-space: nowrap;
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
        }

        .payment-type-tag:hover {
            transform: scale(1);
        }

        .payment-type-tag.full {
            background: linear-gradient(135deg, rgba(46, 125, 50, 0.9) 0%, rgba(76, 175, 80, 0.9) 100%);
            color: white;
            border: 1px solid rgba(76, 175, 80, 0.5);
        }

        .payment-type-tag.partial {
            background: linear-gradient(135deg, rgba(211, 47, 47, 0.9) 0%, rgba(229, 115, 115, 0.9) 100%);
            color: white;
            border: 1px solid rgba(229, 115, 115, 0.5);
        }

        .payment-type-tag i {
            margin-right: 3px;
            font-size: 11px;
        }

        /* 日志项样式 */
        .log-item {
            padding: 8px 10px;
            margin-bottom: 8px;
            border-radius: 6px;
            background-color: #f5f5f5;
            border-left: 3px solid #1E9FFF;
            font-size: 13px;
        }

        .log-item:last-child {
            margin-bottom: 0;
        }

        .log-item .log-content {
            margin-bottom: 4px;
        }

        .log-item .log-meta {
            display: flex;
            justify-content: space-between;
            color: #999;
            font-size: 12px;
        }

        .log-item .log-operator {
            margin-right: 10px;
        }

        .image-preview-container-normal img {
            width: auto;
            height: 130px;
        }
    </style>
</head>

<body>
    <div class="layui-padding-2">
        <form class="layui-form" lay-filter="myform">
            <!-- 尾款审核卡片 -->
            <div class="layui-card compact-card">
                <div class="layui-card-body">
                    <!-- 金额信息显示 - 水平布局 -->
                    <div class="amount-display glassmorphism">
                        <div class="layui-row">
                            <div class="layui-col-xs3 amount-item">
                                <div class="amount-label">订单总额</div>
                                <div class="amount-value">¥<span id="display-total">0.00</span></div>
                            </div>
                            <div class="layui-col-xs3 amount-item">
                                <div class="amount-label">已预付款</div>
                                <div class="amount-value">¥<span id="display-prepaid">0.00</span></div>
                            </div>
                            <div class="layui-col-xs3 amount-item">
                                <div class="amount-label">应付尾款</div>
                                <div class="amount-value">¥<span id="display-final" class="animated-value">0.00</span>
                                </div>
                            </div>
                            <div class="layui-col-xs3 amount-item" style="position: relative;">
                                <div class="amount-label">实付尾款</div>
                                <div class="amount-value final-amount">¥<span id="display-actual-final"
                                        class="animated-value">0.00</span></div>
                                <!-- 尾款结算方式标签 -->
                                <div class="payment-type-tag" id="payment-type-tag">
                                    <i class="layui-icon"></i> <span id="payment-type-text">加载中...</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 收款截图预览 -->
                    <div class="premium-section">
                        <div class="section-title">
                            <i class="layui-icon layui-icon-picture"></i> 收款截图
                        </div>
                        <div class="image-preview-container-normal" id="image-container"></div>
                        <!-- 尾款备注显示区域 -->
                        <div id="final-pay-remark-container" class="layui-text"
                            style="margin-top: 10px; padding: 8px; border-radius: 4px; background-color: #f9f9f9; display: none;">
                            <div style="font-weight: 500; margin-bottom: 5px; color: #666;">
                                <i class="layui-icon layui-icon-note"></i> 尾款日志：
                            </div>
                            <div id="final-pay-remark-content" style="color: #333; line-height: 1.6;"></div>
                        </div>
                    </div>

                    <!-- 审核选项 -->
                    <div class="glassmorphism">
                        <div class="section-title">
                            <i class="layui-icon layui-icon-form"></i> 审核结果
                        </div>
                        <div class="review-options">
                            <div class="review-option approve" data-value="4">
                                <i class="layui-icon layui-icon-ok-circle"></i>
                                <h3>审核通过</h3>
                            </div>
                            <div class="review-option reject" data-value="2">
                                <i class="layui-icon layui-icon-close-fill"></i>
                                <h3>审核不通过</h3>
                            </div>
                        </div>
                        <input type="hidden" name="pay_review_status" id="pay_review_status" value="">
                    </div>

                    <!-- 审核备注 - 移除标签，只保留文本域 -->
                    <div class="remark-container">
                        <textarea name="review_remark" placeholder="请输入审核备注信息（审核不通过时必填）"
                            class="layui-textarea small-textarea"></textarea>
                    </div>

                    <div class="layui-form-item" style="margin-top: 15px; text-align: center;">
                        <button class="layui-btn premium-btn premium-btn-primary" lay-submit
                            lay-filter="formDemo">提交审核</button>
                        <button type="reset"
                            class="layui-btn layui-btn-primary premium-btn premium-btn-default">重置</button>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <script>
        layui.use(['form', 'layer', 'upload'], function () {
            var form = layui.form;
            var layer = layui.layer;
            var $ = layui.$;

            // 获取URL中的订单ID参数
            var orderId = request.get('id');

            // 检查订单ID是否有效
            if (!orderId) {
                layer.msg('订单ID无效', { icon: 2 });
                return;
            }

            // 格式化金额显示，保留两位小数
            function formatMoney(amount) {
                return parseFloat(amount || 0).toFixed(2);
            }

            // 从后端获取尾款明细数据
            layer.load(2);
            $.ajax({
                url: '/admin/order/pay_detail',
                type: 'POST',
                data: { id: orderId },
                success: function (res) {
                    layer.closeAll('loading');
                    if (res.code === 200) {
                        var orderData = res.data;
                        console.log('订单数据:', orderData); // 调试用，查看返回的字段名

                        // 计算应付尾款
                        var finalPayAmount = orderData.Total_money - orderData.Pre_pay;

                        // 初始化金额显示
                        $('#display-total').text(formatMoney(orderData.Total_money));
                        $('#display-prepaid').text(formatMoney(orderData.Pre_pay));
                        $('#display-final').text(formatMoney(finalPayAmount));
                        $('#display-actual-final').text(formatMoney(orderData.Final_pay || 0));

                        // 设置尾款结算方式标签
                        var paymentTypeTag = $('#payment-type-tag');
                        var paymentTypeText = $('#payment-type-text');

                        if (orderData.Final_is_full === 1) {
                            paymentTypeTag.addClass('full').removeClass('partial');
                            paymentTypeText.text('全额');
                        } else {
                            paymentTypeTag.addClass('partial').removeClass('full');
                            paymentTypeText.text('非全额');
                        }

                        // 处理尾款日志
                        if (orderData.Final_pay_remark && orderData.Final_pay_remark.trim() !== '') {
                            // 清空原有内容
                            $('#final-pay-remark-content').empty();

                            // 按 ### 分割日志
                            var logs = orderData.Final_pay_remark.split('###').filter(function (log) {
                                return log.trim() !== '';
                            });

                            // 遍历日志并添加到容器
                            if (logs.length > 0) {
                                logs.forEach(function (log) {
                                    // 按 | 分割每条日志的内容、操作者ID和时间
                                    var logParts = log.split('|');
                                    var logContent = logParts[0] || '';
                                    var operatorId = logParts[1] || '';
                                    var operateTime = logParts[2] || '';
                                    var final_money = logParts[3] || '';//当时实付尾款

                                    // 创建日志项元素
                                    var logItem = $('<div class="log-item"></div>');
                                    logItem.append('<div class="log-content">' + logContent + '</div>');

                                    // 添加操作者和时间信息
                                    var logMeta = $('<div class="log-meta"></div>');
                                    if (operatorId) {
                                        logMeta.append('<span class="log-operator">操作者ID: ' + operatorId + '</span>');
                                    }
                                    if (final_money) {
                                        logMeta.append('<span class="log-operator">当时实付: ￥' + final_money + '</span>');
                                    }
                                    if (operateTime) {
                                        logMeta.append('<span class="log-time">' + operateTime + '</span>');
                                    }

                                    logItem.append(logMeta);
                                    $('#final-pay-remark-content').append(logItem);
                                });

                                $('#final-pay-remark-container').show();
                            }
                        }

                        // 根据支付审核状态设置按钮状态
                        // 0=订金待审, 1=已付订金, 2=尾款待付, 3=尾款待审, 4=已付尾款
                        if (orderData.Pay_review_status !== 3) {
                            // 如果不是尾款待审状态，禁用审核按钮
                            $('.review-option').addClass('layui-disabled').css('cursor', 'not-allowed');
                            $('.layui-btn[lay-submit]').addClass('layui-disabled').attr('disabled', true);
                            layer.msg('当前订单状态不是尾款待审，无法进行审核操作', { icon: 0, time: 3000 });
                        }

                        // 显示收款截图
                        var payPic = orderData.Final_pay_pic || '';
                        if (payPic && payPic !== 'undefined') {
                            // 添加图片路径前缀
                            var fullImagePath = "/static/uploads/normal_pics/order/" + payPic;
                            appendImagePreview('image-container', fullImagePath, '');
                        } else {
                            $('#image-container').html('<div class="layui-text" style="text-align: center; padding: 15px 0; color: #999;"><i class="layui-icon layui-icon-picture" style="font-size: 24px;"></i><p style="margin-top: 8px;">暂无收款截图</p></div>');
                        }
                    } else {
                        layer.msg(res.msg || '获取尾款明细失败', { icon: 2 });
                    }
                },
                error: function (xhr) {
                    layer.closeAll('loading');
                    layer.msg(xhr.responseJSON ? xhr.responseJSON.msg : '网络错误', { icon: 2 });
                }
            });

            // 审核选项点击事件
            $('.review-option').on('click', function () {
                var value = $(this).data('value');

                // 移除所有选中状态
                $('.review-option').removeClass('selected');

                // 添加当前选中状态
                $(this).addClass('selected');

                // 设置隐藏字段值
                $('#pay_review_status').val(value);
            });

            // 阻止表单默认提交行为
            $('form.layui-form').on('submit', function (e) {
                e.preventDefault();
                return false;
            });

            // 监听提交按钮点击
            form.on('submit(formDemo)', function (data) {
                // 检查是否选择了审核结果
                if (!data.field.pay_review_status) {
                    layer.msg('请选择审核结果', { icon: 2 });
                    return false;
                }

                // 如果选择了不通过，检查备注是否填写
                if (data.field.pay_review_status === '2' && (!data.field.review_remark || data.field.review_remark.trim() === '')) {
                    layer.msg('审核不通过时，备注信息为必填项', { icon: 2 });
                    return false;
                }

                // 定义提交表单的函数
                var submitForm = function () {
                    layer.load(2);

                    // 发送请求到后端接口
                    $.ajax({
                        url: '/admin/order/pay_review_final',
                        type: 'POST',
                        data: {
                            id: orderId,                           // 订单ID
                            pay_review_status: data.field.pay_review_status,  // 审核结果：4=审核通过 2=审核未过
                            review_remark: data.field.review_remark || ''  // 审核备注
                        },
                        success: function (res) {
                            layer.closeAll('loading');
                            if (res.code === 200) {
                                layer.msg('尾款审核成功', { icon: 1, time: 1000 }, function () {
                                    // 关闭当前iframe层
                                    var index = parent.layer.getFrameIndex(window.name);
                                    parent.layer.close(index);
                                    // 刷新父页面表格
                                    parent.layui.table.reload('mytable');
                                });
                            } else {
                                layer.msg(res.msg || '审核失败', { icon: 2 });
                            }
                        },
                        error: function (res) {
                            layer.closeAll('loading');
                            layer.msg(res.responseJSON ? res.responseJSON.msg : '网络错误', { icon: 2 });
                        }
                    });
                };

                // 如果选择了"审核不通过"，显示确认框
                if (data.field.pay_review_status === '2') {
                    layer.confirm('将重置该订单款项信息至审核前状态，您确认吗？', {
                        icon: 3,
                        title: '提示',
                        btn: ['确认', '取消']
                    }, function (index) {
                        layer.close(index);
                        submitForm(); // 用户点击确认后提交表单
                    });
                } else {
                    // 如果是审核通过，直接提交
                    submitForm();
                }

                return false; // 阻止表单跳转
            });

            // 辅助函数：添加图片预览
            function appendImagePreview(containerId, filepath, filename) {
                var container = $('#' + containerId);
                container.empty();

                // 创建图片预览元素，添加可点击链接
                var imageItem = $(`
                    <div class="image-preview-item">
                        <a href="${filepath}" target="_blank" title="点击查看大图" class="final-pay-pic">
                            <img src="${filepath}" alt="收款截图">
                        </a>
                    </div>
                `);

                container.append(imageItem);
            }
        });
    </script>
</body>

</html>