<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 角色列表</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
</head>

<body class="layui-padding-5">
    <form class="layui-form" lay-filter="form_edit" action="" onsubmit="return false">


        <div class="layui-col-sm12 layui-col-md12 layui-form-item">
            <span class="red_star">*</span> 科室名
        </div>

        <div class="layui-col-sm12 layui-col-md12 layui-form-item">
            <input type="text" name="name" required lay-verify="required" placeholder="请输入用户的姓名" autocomplete="off"
                class="layui-input">
        </div>


        <div class="layui-col-sm12 layui-col-md12 layui-form-item">
            <span class="red_star">*</span> 科室简介
        </div>

        <div class="layui-col-sm12 layui-col-md12 layui-form-item">
            <textarea name="details" placeholder="请输入内容" class="layui-textarea"></textarea>
        </div>

        <div class="layui-col-sm12 layui-col-md12 layui-form-item"
            style="color: #999;font-size: 11px;margin-top: 50px;">
            <span class="red_star">*</span> 注意：新建完后请继续完善该科室信息，如设置隶属关系，上传图标等
        </div>

        <div class="layui-input-block" style="text-align: right;">
            <button class="layui-btn" lay-submit lay-filter="formSubmitBtn">下一步</button>
            <button type="reset" class="layui-btn layui-btn-primary">重置</button>
        </div>
    </form>

    <script>
        layui.use('form', function () {
            var form = layui.form;
            var $ = layui.jquery;
            var index = parent.layer.getFrameIndex(window.name);
            form.on('submit(formSubmitBtn)', function (data) {
                layer.load(2);
                $.ajax({
                    url: serverUrl + '/admin/department/add',
                    type: 'post',
                    data: data.field,
                    success: function (res) {
                        layer.closeAll('loading');
                        if (res.code == 200) {
                            let new_id = res.new_id || 0;
                            $.post(serverUrl + '/normal/department_cache_set', function (res) {
                                if (res.code == 200) {
                                    parent.layer.msg('下一步继续完善', { time: 1000 }, function () {
                                        if (new_id) {
                                            window.location.href = '/admin/department_edit.html?id=' + new_id + '&frompage=add';
                                        }
                                    });
                                } else {
                                    layer.msg('刷新缓存失败', { icon: 2, time: 1000 });
                                }
                            });
                        } else {
                            layer.msg(res.msg, { icon: 2, time: 1000 });
                        }
                    },
                    error: function (err) {
                        layer.closeAll('loading');
                        layer.msg(err.responseJSON.msg);
                    }
                });
                return false;
            });
        });
    </script>




</body>

</html>