package config

// 全局常量
const (
	BasePort = "8088"
	// \internal\app\normal\normal.go，第99行，有关对端口判断
	DBHost                            = "************"
	DBPort                            = "3306"
	DBName                            = "mstdb_dev"
	DBPatient                         = "mstdb_dev"
	DBPassword                        = "win2024"
	SessionKey                        = "XzPqFLr38hvswU5lUIzc0AnEDFDdHPLXK9nX/cgJPCc="
	Md5Salt                           = "aLfj@V93i"
	Dist_catagory                     = "../static"
	Web_catagory                      = "../interface"
	REDIS_HOST                        = "127.0.0.1"
	REDIS_PORT                        = "6379"
	REDIS_PASSWORD                    = ""
	REDIS_DBNUM                       = 5  // Redis数据库编号，用于隔离不同版本的缓存
	CacheTipsCount                    = 20 //缓存redis中tips数量
	NormalPerm                        = 0  //普通用户
	SystemPerm                        = -1 //超级管理员
	WX_appid                          = "wx0dee4226bdf90a01"
	WX_appsecret                      = "ed261ed7ac8ae04c5a8a3bf2227bc232"
	WX_token                          = "1234567890abcdef"
	WX_encodingkey                    = "1234567890abcdef"
	WX_mchid                          = "1234567890abcdef"
	WX_mchkey                         = "1234567890abcdef"
	WX_notifyurl                      = "http://www.example.com/wx/notify"
	JWT_SECRET_KEY                    = "I1fh@N7Ma"
	Jwt_Expire_Time                   = 3600 * 24 * 3 // 3天过期
	Session_MaxAge                    = 86400 * 7     // 7天过期
	TRTC_SDK_APPID                    = 1600071713
	TRTC_APP_NAME                     = "mst_" // 应用标识
	TRTC_SECRET_KEY                   = "ef5d611303c922e4179eab0702f6ef7a9a8301989e40b9f16ffa91e36f889bfd"
	Global_sale_department_pre_sale   = 20 //售前部门ID
	Global_sale_department_after_sale = 21 //售后部门ID
)

// 药品单位
var Drug_unit = [13]string{
	"付",
	"袋",
	"瓶",
	"克",
}

// 药品用法
var Drug_Usage = [16]string{
	"口服",
	"冲服",
	"含漱",
	"涂抹",
	"敷",
	"贴",
	"水煎服",
}

// 病历状态
var Record_Status = [7]string{
	"待建诊室",
	"待问诊",
	"待下单",
	"无效病历（已废弃）",
	"待开方",
	"已开方",
	"病历完结",
}

// 问诊状态
var Room_Status = [4]string{
	"待诊",
	"已诊",
	"已取消",
	"已过期",
}

// 订单状态
var Order_Status = [9]string{
	"待发",
	"可发",
	"已发",
	"已收",
	"已退",
	"异常",
	"待转售后",
	"待分配",
	"已核销",
}

// 处方状态
var Pre_status = [8]string{
	"状态占位符",
	"已开方，待审核",
	"已驳回",
	"签章过半待调剂",
	"已调剂",
	"已入成品库",
	"已出库发药",
	"已异常废弃",
}

// 审核处方四角色
var Four_role_verify_name = [4]string{
	"医生审核",
	"药师审核",
	"调配审核",
	"发药审核",
}

// 四角色通用审核状态
var Foru_role_verify_status = [3]string{
	"未审核",
	"已审核",
	"审未过",
}

// 医生级别
var Doctor_Level = [2]string{
	"普通",
	"专家",
}

// 支付审核状态
var Pay_review_status = [5]string{
	"订金待审",
	"已付订金",
	"尾款待付",
	"尾款待审",
	"已付尾款",
}

// 家庭关系
var FamilyRelation = [21]string{
	"本人",
	"父亲",
	"母亲",
	"配偶",
	"爷爷",
	"奶奶",
	"外祖父",
	"外祖母",
	"儿子",
	"女儿",
	"兄弟",
	"姐妹",
	"孙子",
	"孙女",
	"外孙",
	"外孙女",
	"叔叔",
	"阿姨",
	"侄子",
	"侄女",
	"表亲",
}

//患者类型
var Patient_Type = [2]string{
	"初诊",
	"复诊",
}

//参保类型
var Ins_Type = [8]string{
	"城镇职工基本养老保险",
	"城乡居民基本养老保险",
	"失业保险",
	"工伤保险",
	"生育保险",
	"城镇职工基本医疗保险",
	"城乡居民基本医疗保险",
	"新型农村合作医疗",
}

//患者来源
var Patient_From = [4]string{
	"转介绍",
	"员工介绍",
	"互联网",
	"线下",
}

// 快递信息
var KD100_Express_Info = [11][2]string{
	{"shunfeng", "顺丰速运"},
	{"YTO", "圆通速递"},
	{"ZTO", "中通快递"},
	{"STO", "申通快递"},
	{"Yunda", "韵达快递"},
	{"Best Express", "百世快递"},
	{"Deppon", "德邦物流"},
	{"JD", "京东物流"},
	{"TTKD", "天天快递"},
	{"EMS", "邮政"},
	{"sut56", "速通物流"},
}

// 腾讯云密钥
var Qcloud_Secret_Id = "AKIDIdsytdAVildJm270mKn7ING0YxEKF0Zu"
var Qcloud_Secret_Key = "HMEtRt9GF4lIdptmS0PdicTBJLwQkt0w"

// 腾讯云COS配置
var COS_Region = "ap-nanjing"
var COS_Bucket = "mst-**********"
var COS_BasePath = "rtc_videos/"
