<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 前端WEB上传</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
    <style>
        .upload-area {
            display: grid;
            place-items: center;
            height: 70vh;
        }

        .image-preview-container {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-top: 15px;
        }

        .image-preview-item {
            position: relative;
            width: 150px;
            height: 150px;
        }

        .image-preview-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 4px;
            cursor: pointer;
        }

        .image-preview-item .delete-btn {
            position: absolute;
            bottom: 5px;
            left: 50%;
            transform: translateX(-50%);
            padding: 2px 10px;
            background-color: rgba(255, 255, 255, 0.9);
            border: 1px solid #ddd;
            border-radius: 3px;
            cursor: pointer;
        }

        .delete-btn:hover {
            background-color: #f56c6c;
            color: white;
        }
    </style>
</head>

<body>
    <div class="layui-layout layui-layout-admin">
        <!-- 头部 -->
        <div class="layui-header">
            <div class="layui-logo">幸年堂</div>
            <ul class="layui-nav layui-layout-right">
                <li class="layui-nav-item" style="background-color:transparent!important;padding-right:10px">
                    <a href="javascript:;" style="display:flex;align-items:center;color:#fff"><img
                            src="/dist/images/user.png" class="layui-nav-img"><span class="user_name"></span></a>
                    <dl class="layui-nav-child pop_menu">
                        <dd><i class="iconfont">&#xe62f;</i><a href="change_password.html#7">用户资料</a></dd>
                        <dd><i class="iconfont">&#xe601;</i><a href="javascript:void(0)" class="refresh_cache">刷新缓存</a>
                        </dd>
                        <dd><i class="iconfont">&#xe634;</i><a href="login_out.html">退出登录</a></dd>
                    </dl>
                </li>
            </ul>
        </div>
        <!-- 导航 -->
        <ul class="layui-nav layui-nav-tree layui-nav-side" id="menuContainer" lay-bar="disabled" lay-accordion>
            <div style="margin: 50px 0;color: #bbb;text-align: center;">菜单加载中...</div>
        </ul>
        <!-- 主体内容 -->
        <div class="layui-body">
            <div class="body_child">
                <div class="layui-panel">
                    <div class="layui-card-header">
                        <div class="layui-row" style="padding-top:10px;">
                            <div class="layui-col-md11 layui-col-sm10">前端WEB上传</div>
                        </div>
                    </div>
                    <!-- 内容开始 -->
                    <div class="upload-area">
                        <!-- ZIP文件上传区域 -->
                        <div class="layui-upload">
                            <button type="button" class="layui-btn upload_big_btn" id="upload-zip-btn">
                                <div class="btn_big_font">
                                    <i class="layui-icon layui-icon-upload btn_big_font"></i> WEB前端上传
                                </div>
                                <div>支持.ZIP 格式，点击或拖拽上传</div>
                            </button>
                        </div>
                    </div>
                    <!-- 内容结束 -->
                </div>
                <br>
                <br>
            </div>
        </div>
    </div>


    <script>
        layui.use(['element', 'layer', 'util', 'upload'], function () {
            var element = layui.element;
            var layer = layui.layer;
            var util = layui.util;
            var upload = layui.upload;
            var treeTable = layui.treeTable;
            var dropdown = layui.dropdown;
            var $ = layui.$;

            // 初始化文件上传
            upload.render({
                elem: '#upload-zip-btn',
                url: '/normal/uploadWebInterface',
                accept: 'file',
                exts: 'zip',
                drag: true,
                before: function (obj) {
                    layer.load(2);
                },
                done: function (res) {
                    layer.closeAll('loading');
                    layer.msg(res.msg);
                    if (res.code === 200) {
                        layer.confirm('恭喜，前端页面已同步成功，是否刷新缓存？', {
                            btn: ['是', '否']
                        }, function () {
                            refresh_cache($);
                        });
                    }
                },
                error: function () {
                    layer.closeAll('loading');
                    layer.msg('上传出错，请重试', { icon: 2 });
                }
            });

            // 页面顶部插入缓存的用户数据
            let local_userinfo = localStorage.getItem('local_userinfo');
            let user_name = "";
            if (local_userinfo) {
                local_userinfo = JSON.parse(local_userinfo);
                user_name = local_userinfo.Name + " ( " + local_userinfo.Roles + " )";
            } else {
                user_name = "未登录";
            }
            $('.user_name').text(user_name);
            render_menu($);
            
            $('.refresh_cache').click(function () {
                refresh_cache($);
            });
        });
    </script>
</body>

</html>