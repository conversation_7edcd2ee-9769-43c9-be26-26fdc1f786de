<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 科室修改</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
    <script src="/dist/js/xm-select.js"></script>
    <style>
        #avatar_img {
            border-radius: 50%;
            border: 1px solid #ccc;
            width: 80px;
            height: 80px;
            position: absolute;
            right: 81px;
            top: 9px;
        }

        #Department i {
            margin-right: 5px;
        }

        /* 美化xm-select树形结构 */
        .xm-tree-icon.xm-tree-icon-parent {
            border-width: 5px;
        }

        .xm-tree-icon.xm-tree-icon-parent.expand {
            border-color: transparent transparent transparent #4096ff;
        }

        .xm-tree-icon.xm-tree-icon-leaf {
            border: 0;
            margin-right: 3px;
        }

        /* 选中态样式增强 */
        .xm-tree-show .xm-option.selected {
            background-color: #e6f4ff !important;
        }

        /* 调整tree节点间距 */
        .xm-tree-show {
            padding: 5px 0 !important;
        }

        .xm-tree-show .xm-option {
            padding-left: 30px !important;
        }

        .xm-tree-show .xm-tree-item .xm-option {
            padding-left: 35px !important;
        }

        .xm-tree-show .xm-tree-item .xm-tree-item .xm-option {
            padding-left: 60px !important;
        }
    </style>
</head>

<body class="layui-padding-5">
    <form class="layui-form" lay-filter="form_edit" action="" onsubmit="return false">
        <div class="layui-form-item">
            <label class="layui-form-label">名称</label>
            <div class="layui-input-block">
                <input type="text" name="name" required lay-verify="required" placeholder="请输入输入框内容" autocomplete="off"
                    class="layui-input">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">简介</label>
            <div class="layui-input-block">
                <textarea name="details" placeholder="请输入内容" class="layui-textarea"></textarea>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">排序</label>
            <div class="layui-input-block">
                <input type="number" name="sort" required lay-verify="required" placeholder="请输入输入框内容"
                    autocomplete="off" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item" style="position: relative;">
            <label class="layui-form-label">科室ICON图</label>
            <div class="layui-input-block">
                <button type="button" class="layui-btn layui-btn-normal" id="iconfile">选择文件</button>
            </div>
            <img id="avatar_img">
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">父级部门</label>
            <div class="layui-input-block">
                <div id="Department" style="min-width: 100px;max-width:250px;"></div>
                <input type="hidden" id="department_id" name="department_id">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">顶级分类？</label>
            <div class="layui-input-block">
                <input type="radio" name="is_top" value="0" title="是" lay-filter="is_top">
                <input type="radio" name="is_top" value="1" title="否" checked lay-filter="is_top">
            </div>
            <div class="layui-form-mid layui-word-aux">顶级分类，即门店，其选中后，上面的父级科室选项则无效</div>
        </div>

        <div class="layui-form-item" style="margin-top: 50px;">
            <div class="layui-input-block form_bottom_button">
                <button class="layui-btn" lay-submit lay-filter="formSubmitBtn">立即提交</button>
                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
            </div>
        </div>
    </form>
    <script>
        layui.use(['form', 'upload', 'jquery', 'layer'], function () {
            var form = layui.form;
            var upload = layui.upload;
            var $ = layui.jquery;
            var layer = layui.layer;
            var dropdown = layui.dropdown;
            var index = parent.layer.getFrameIndex(window.name);
            var pid = 0;
            var id = Number(request.get("id"));
            if (!id) {
                layer.msg("参数错误，请从列表页面进入编辑页面", { icon: 2, time: 1000 });
                return false;
            }
            var frompage = request.get("frompage");
            if (frompage === "add") {
                $('.layui-form-item:eq(0),.layui-form-item:eq(1)').hide();
            }
            var avatarFile = null; // 用于存储头像文件
            // 上传组件
            upload.render({
                elem: '#iconfile', // 上传按钮
                auto: false,          // 禁用自动上传
                accept: 'file',       // 文件类型
                choose: function (obj) {
                    obj.preview(function (index, file) {
                        avatarFile = file; // 保存文件到变量
                        console.log("选择的文件：", file);
                        $('#avatar_img').attr('src', URL.createObjectURL(file)); // 显示图片
                    });
                },
            });

            // 请求科室/部门数据
            function fetchDepartmentData() {
                $.ajax({
                    url: '/normal/department_cache_get',
                    type: 'post',
                    success: function (res) {
                        layer.closeAll('loading');
                        if (res.code === 200) {
                            let data = res.data;

                            // 打印原始数据以便调试
                            console.log('原始数据总数:', data.length);

                            try {
                                // 从data中移除id=id的元素，避免自己选择自己作为父级
                                data = data.filter(item => item.Id !== id);
                                console.log('过滤后数据总数:', data.length);

                                // 首先构建完整的节点路径映射
                                const pathMap = buildNodePathMap(data);

                                // 将扁平数组转为树形结构数据
                                let treeData = convertToTreeData(data);

                                // 获取所有顶级节点的ID，用于默认展开顶级节点
                                const topLevelKeys = data.filter(item => item.Pid === 0).map(item => item.Id);
                                console.log('顶级节点:', topLevelKeys);

                                // 获取当前选中节点的路径
                                let expandKeys = [];
                                if (pid > 0) {
                                    expandKeys = getNodePath(pid, pathMap);
                                    // 确保所有父节点都被展开
                                    expandKeys = [...new Set([...topLevelKeys, ...expandKeys])];
                                    console.log('展开路径:', expandKeys);
                                } else {
                                    expandKeys = topLevelKeys;
                                }

                                // 初始化xm-select组件
                                let departmentSelect = xmSelect.render({
                                    el: '#Department',
                                    model: { label: { type: 'text' } },
                                    radio: true,
                                    clickClose: true,
                                    empty: '没有可选择的部门',
                                    searchTips: '输入关键词搜索',
                                    toolbar: {
                                        show: true,
                                        list: ['CLEAR']
                                    },
                                    filterable: true, // 启用搜索过滤功能
                                    paging: true, // 超过10个选项显示分页
                                    pageSize: 10,
                                    tree: {
                                        show: true,
                                        strict: false,
                                        expandedKeys: expandKeys, // 使用计算好的展开节点路径
                                        autoExpandParent: true // 自动展开父节点
                                    },
                                    theme: {
                                        color: '#4096ff', // 主题颜色
                                    },
                                    height: 'auto',
                                    data: treeData,
                                    on: function(data) {
                                        if(data.isAdd && data.change.length > 0) {
                                            // 设置选中的部门ID
                                            const selectedDept = data.change[0];
                                            pid = selectedDept.value;
                                            $('#department_id').val(pid);
                                            console.log("新的科室ID：", pid);

                                            // 显示选中提示
                                            layer.msg(`已选择: ${selectedDept.name}`, {icon: 1, time: 1000});

                                            return data.change.slice(0, 1); // 确保只能选一个
                                        }
                                    },
                                    done: function() {
                                        // 组件初始化完成后的回调
                                        console.log('xm-select初始化完成');
                                    }
                                });

                                // 如果有pid，设置默认选中值
                                if (pid > 0) {
                                    // 设置选中值（添加延时确保渲染完成）
                                    setTimeout(() => {
                                        departmentSelect.setValue([pid]);

                                        // 强制刷新一次，确保正确显示
                                        setTimeout(() => {
                                            departmentSelect.update({
                                                tree: {
                                                    expandedKeys: expandKeys
                                                }
                                            });
                                        }, 200);
                                    }, 100);
                                }

                                // 检查是否为顶级分类，如果是则禁用父级科室选择器
                                if ($('input[name="is_top"]:checked').val() === '0') {
                                    departmentSelect.update({
                                        disabled: true
                                    });
                                }
                            } catch (error) {
                                console.error('处理数据时出错:', error);
                                layer.msg('处理数据时出错，请检查控制台日志', { icon: 2, time: 1000 });
                            }
                        } else {
                            layer.msg(res.msg, { icon: 2, time: 1000 });
                        }
                    },
                    error: function (err) {
                        layer.closeAll('loading');
                        layer.msg(err.responseJSON ? err.responseJSON.msg : '请求失败', { icon: 2, time: 1000 });
                    },
                });
            }

            // 将扁平数组转换为树形结构
            function convertToTreeData(data) {
                // 创建ID到对象的映射
                const idMapping = {};
                data.forEach(item => {
                    idMapping[item.Id] = {
                        name: item.Name,
                        value: item.Id,
                        pid: item.Pid,
                        children: []
                    };
                });

                // 构建树形结构
                const treeData = [];

                // 使用更高效的方法构建树
                data.forEach(item => {
                    // 当前节点
                    const node = idMapping[item.Id];

                    // 如果是顶级节点（Pid为0），直接添加到树中
                    if (item.Pid === 0) {
                        treeData.push(node);
                    }
                    // 否则添加到父节点的children数组
                    else if (idMapping[item.Pid]) {
                        idMapping[item.Pid].children.push(node);
                    }
                    // 特殊情况：父节点不存在，作为顶级节点处理
                    else {
                        treeData.push(node);
                    }
                });

                // 对各级节点按Sort字段排序
                function sortTree(nodes) {
                    if (!nodes || !nodes.length) return nodes;

                    // 查找对应的原始数据以获取Sort值
                    nodes.sort((a, b) => {
                        const aSort = data.find(item => item.Id === a.value)?.Sort || 0;
                        const bSort = data.find(item => item.Id === b.value)?.Sort || 0;
                        return bSort - aSort; // 修改为降序排列（从大到小）
                    });

                    // 递归排序子节点
                    nodes.forEach(node => {
                        if (node.children && node.children.length) {
                            sortTree(node.children);
                        }
                    });

                    return nodes;
                }

                // 对树进行排序
                sortTree(treeData);

                console.log('构建的树形结构:', JSON.stringify(treeData, null, 2));
                return treeData;
            }

            // 构建节点路径映射，用于快速查找任意节点的完整路径
            function buildNodePathMap(data) {
                // 创建ID到项的映射
                const idToItem = {};
                data.forEach(item => {
                    idToItem[item.Id] = item;
                });

                // 为每个节点构建路径映射
                const pathMap = {};

                data.forEach(item => {
                    const path = [];
                    let currentId = item.Id;
                    let loopProtection = 0; // 防止循环引用

                    // 向上查找所有父节点，构建完整路径
                    while (currentId && loopProtection < 100) {
                        const currentItem = idToItem[currentId];
                        if (!currentItem) break;

                        path.unshift(currentItem.Id); // 将当前节点ID添加到路径开头
                        currentId = currentItem.Pid; // 移动到父节点
                        loopProtection++;
                    }

                    pathMap[item.Id] = path;
                });

                return pathMap;
            }

            // 获取节点的完整路径（包括自身和所有父节点）
            function getNodePath(nodeId, pathMap) {
                return pathMap[nodeId] || [];
            }

            // 请求用户数据并填充表单
            function fetchUserData() {
                layer.load(2);
                $.ajax({
                    url: '/admin/department/detail',
                    type: 'post',
                    data: { id: id },
                    success: function (res) {
                        layer.closeAll('loading');
                        if (res.code === 200) {
                            var data = res.data;
                            form.val('form_edit', {
                                name: data.name,
                                sort: data.sort,
                                details: data.details == '0' ? '' : data.details,
                                is_top:data.pid === 0 && frompage !== "add" ? 0 : 1,
                            });
                            pid = data.pid;
                            fetchDepartmentData();
                            //判断用户头像是否存在
                            let avatarUrl = "/static/uploads/icons/department_" + id + ".svg";
                            console.log("头像URL：", avatarUrl);
                            let img = new Image();
                            img.src = avatarUrl;
                            img.onload = function () {
                                $('#avatar_img').attr('src', avatarUrl + "?" + Math.random());
                            }
                        } else {
                            layer.msg(res.msg, { icon: 2, time: 1000 });
                        }
                    },
                    error: function (err) {
                        layer.closeAll('loading');
                        layer.msg(err.responseJSON ? err.responseJSON.msg : '请求失败', { icon: 2, time: 1000 });
                    },
                });
            }

            // 初始化科室数据和用户数据
            fetchUserData();

            // 监听顶级分类单选按钮切换事件
            form.on('radio(is_top)', function(data) {
                if (data.value === '0') {
                    // 如果选择"是"，则禁用父级科室选择
                    xmSelect.get('#Department', true).update({
                        disabled: true
                    });
                    // 清空父级ID
                    pid = 0;
                    $('#department_id').val(0);
                } else {
                    // 如果选择"否"，则启用父级科室选择
                    xmSelect.get('#Department', true).update({
                        disabled: false
                    });
                }
            });

            // 表单提交
            form.on('submit(formSubmitBtn)', function (data) {
                layer.load(2);

                // 使用 FormData 提交文件和表单字段
                var formData = new FormData();

                // 添加文件（头像）
                if (avatarFile) {
                    formData.append('iconfile', avatarFile); // 注意字段名需要与后端一致
                }

                // 添加其他表单字段
                for (var key in data.field) {
                    if (data.field.hasOwnProperty(key)) {
                        formData.append(key, data.field[key]);
                    }
                }
                // 添加用户ID
                formData.append('id', id);

                // 如果是顶级分类，则pid设为0
                if ($('input[name="is_top"]:checked').val() === '0') {
                    pid = 0;
                }
                formData.append('pid', pid);
                // 发送数据到后端
                $.ajax({
                    url: '/admin/department/edit',
                    type: 'post',
                    data: formData,
                    processData: false, // 不处理数据
                    contentType: false, // 不设置 contentType
                    success: function (res) {
                        layer.closeAll('loading');
                        if (res.code === 200) {


                            $.post(serverUrl + '/normal/department_cache_set', function (res) {
                                if (res.code == 200) {
                                    layer.msg(res.msg, { icon: 1, time: 1000 }, function () {
                                        parent.location.reload();
                                    });
                                } else {
                                    layer.msg('刷新缓存失败', { icon: 2, time: 1000 });
                                }
                            });



                        } else {
                            layer.msg(res.msg, { icon: 2, time: 1000 });
                        }
                    },
                    error: function (err) {
                        layer.closeAll('loading');
                        console.error(err);
                        layer.msg(err.responseJSON ? err.responseJSON.msg : '请求失败', { icon: 2, time: 1000 });
                    },
                });

                return false; // 防止表单默认提交
            });
        });

    </script>

</body>

</html>