package patient

import (
	"fmt"
	"mstproject/pkg/common"
	"mstproject/pkg/config"
	"mstproject/pkg/database"
	"net/http"
	"time"
)

// TRTC配置结构体
type TRTCConfig struct {
	SDKAppID  int    `json:"sdkAppId"`
	UserID    string `json:"userId"`
	RoleID    int    `json:"roleId"` // 添加角色ID
	UserSig   string `json:"userSig"`
	RoomID    int    `json:"roomId"`
	RoomIDStr string `json:"roomIdStr"` // 添加带前缀的房间ID
	//新增患者姓名、医助姓名、医生姓名
	PatName  string `json:"patName"`
	AsstName string `json:"asstName"`
	DocName  string `json:"docName"`
}

// 获取TRTC配置
func Rtc_room_get_trtc_config(w http.ResponseWriter, r *http.Request) {
	// 获取房间ID
	roomID, err := common.CheckInt(r.FormValue("room_id"))
	if err != nil || roomID < 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "房间ID不能为空",
		})
		return
	}

	// 获取当前用户ID和角色ID
	userID, err := common.CheckInt(r.FormValue("user_id"))
	if err != nil || userID < 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]any{
			"code": 500,
			"msg":  "用户ID不能为空",
		})
		return
	}

	// 获取角色ID
	RoleID, err := common.CheckInt(r.FormValue("role_id"))
	if err != nil || RoleID < 0 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]any{
			"code": 500,
			"msg":  "角色ID不能为空",
		})
		return
	}

	//判断当前诊室状态是否允许问诊
	type RoomDatas struct {
		Status         int    `json:"status"`
		Asst_id        int    `json:"asst_id"`
		Scheduled_time string `json:"scheduled_time"`
		Pat_name       string `json:"pat_name"`
		Doc_name       string `json:"doc_name"`
		Asst_name      string `json:"asst_name"`
	}
	//sql := "select status,asst_id,scheduled_time from rtc_room where id = ?" - 诊室未加姓名前
	sql := "select a.status,a.asst_id,a.scheduled_time,b.name as pat_name,c.name as doc_name,d.name as asst_name from rtc_room as a left join patient_profile as b on a.pat_pro_id = b.id left join rbac_user as c on c.id = a.doc_id left join rbac_user as d on d.id = a.asst_id where a.id = ?"
	var roomDatas RoomDatas
	err = database.GetRow(sql, &roomDatas, roomID)

	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "获取诊室状态失败",
			"sql":  common.DebugSql(sql, roomID),
		})
		return
	}

	// 1. 检查用户权限
	if (RoleID == 3 || RoleID == 9) && userID != roomDatas.Asst_id {
		common.JSONResponse(w, http.StatusForbidden, map[string]interface{}{
			"code": 500,
			"msg":  "当前医助非隶属于该诊室",
		})
		return
	}

	// 2. 检查诊室状态
	if roomDatas.Status != 0 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "当前诊室状态不允许视频问诊",
		})
		return
	}

	// 3. 检查是否是当天预约
	currentTime := time.Now()
	scheduledTime, err := time.Parse(time.RFC3339, roomDatas.Scheduled_time)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "时间格式转换失败",
			"err":  err.Error(),
		})
		return
	}

	// 检查是否是同一天
	if currentTime.Format("2006-01-02") != scheduledTime.Format("2006-01-02") {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "当前诊室预约时间(" + scheduledTime.Format("2006-01-02") + ")非今日视频问诊时间",
		})
		return
	}

	// 4. 检查是否超过预约时间30分钟
	// if currentTime.Sub(scheduledTime) >= 30*time.Minute {
	// 	common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
	// 		"code": 500,
	// 		"msg":  "当前诊室的预约时间已过期",
	// 	})
	// 	return
	// }

	// 使用官方 UserSig 生成方法
	userIDStr := fmt.Sprintf("%d_%d", RoleID, userID)
	userSig, err := common.GenUserSig(config.TRTC_SDK_APPID, config.TRTC_SECRET_KEY, userIDStr, 86400*180) // 180天有效期

	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "生成UserSig失败",
			"err":  err.Error(),
		})
		return
	}

	// 返回TRTC配置
	config := TRTCConfig{
		SDKAppID:  config.TRTC_SDK_APPID,
		UserID:    userIDStr,
		RoleID:    RoleID,
		UserSig:   userSig,
		RoomID:    roomID,
		RoomIDStr: fmt.Sprintf("%s%d", config.TRTC_APP_NAME, roomID), // 添加带前缀的房间ID
		//新增患者姓名、医助姓名、医生姓名
		PatName:  roomDatas.Pat_name,
		AsstName: roomDatas.Asst_name,
		DocName:  roomDatas.Doc_name,
	}

	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "获取TRTC配置成功",
		"data": config,
	})
}
