<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>幸年堂 - 系统日志详情</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
    <script src="/dist/js/ua-parser.min.js"></script>
</head>

<body>
    <div class="layui-padding-3">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md12">
                <div class="layui-form-item">
                    <label class="layui-form-label">ID</label>
                    <div class="layui-input-block">
                        <div class="detail-text" id="id"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">日志类型</label>
                    <div class="layui-input-block">
                        <div class="detail-text" id="type"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">操作人</label>
                    <div class="layui-input-block">
                        <div class="detail-text" id="user_id"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">日志内容</label>
                    <div class="layui-input-block">
                        <div class="detail-text" id="contents"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">IP地址</label>
                    <div class="layui-input-block">
                        <div class="detail-text" id="ip"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">客户端信息</label>
                    <div class="layui-input-block">
                        <div class="detail-text" id="useragent"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">发生时间</label>
                    <div class="layui-input-block">
                        <div class="detail-text" id="create_time"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部按钮 -->
        <div class="layui-form-item" style="text-align: center;">
            <button type="button" class="layui-btn layui-btn-primary" onclick="closeModalWindow()">关闭</button>
        </div>
    </div>

    <script>
        layui.use(['layer'], function () {
            var layer = layui.layer;
            var $ = layui.$;

            // 获取URL参数
            var id = request.get('id');
            if (!id) {
                layer.msg('缺少必要参数', { icon: 2 });
                return;
            }

            // 加载日志详情
            layer.load(2);
            $.ajax({
                url: '/admin/system_logs/detail',
                type: 'POST',
                data: { id: id },
                success: function (res) {
                    layer.closeAll('loading');
                    if (res.code === 200) {
                        var data = res.data;
                        $('#id').text(data.ID || '--');
                        
                        // 处理日志类型
                        let typeText = '未知类型';
                        switch(data.Type) {
                            case 0: typeText = '员工日志'; break;
                            case 1: typeText = '患者日志'; break;
                            case 2: typeText = '系统日志'; break;
                        }
                        $('#type').text(typeText);

                        // 处理操作人
                        if (data.Type === 2) {
                            $('#user_id').html("<div class='green_font'>系统自动化</div>");
                        } else if (data.Type === 1) {
                            if (data.User_id === 0) {
                                $('#user_id').text("未登录患者");
                            } else {
                                $.ajax({
                                    url: '/admin/patient_profile/detail',
                                    type: 'post',
                                    async: false,
                                    data: {
                                        id: data.User_id
                                    },
                                    success: function (res) {
                                        if (res.code === 200 && res.data) {
                                            $('#user_id').text(res.data.Name || "未知患者");
                                        } else {
                                            $('#user_id').text("未知患者");
                                        }
                                    },
                                    error: function () {
                                        $('#user_id').text("未知患者");
                                    }
                                });
                            }
                        } else if (data.Type === 0) {
                            $.ajax({
                                url: '/admin/user/list_low',
                                type: 'post',
                                async: false,
                                data: {
                                    id: data.User_id
                                },
                                success: function (res) {
                                    if (res.code === 200 && res.data.length > 0) {
                                        $('#user_id').text(res.data[0].Name);
                                    } else {
                                        $('#user_id').text("未知用户");
                                    }
                                },
                                error: function () {
                                    $('#user_id').text("未知用户");
                                }
                            });
                        }

                        $('#contents').text(data.Contents || '--');
                        $('#ip').text(data.Ip || '--');

                        // 处理 useragent
                        let uaText = '--';
                        if (data.Useragent === "") {
                            uaText = "空值";
                        } else if (data.Useragent === "curl/7.29.0") {
                            uaText = "服务器自动化";
                        } else if (data.Useragent && data.Useragent.split('|').length === 6) {
                            const [baseLib, debug, env, lang, theme, wxVersion] = data.Useragent.split('|');
                            uaText = `微信小程序客户端信息：<br>
                                     · 基础库版本：${baseLib}<br>
                                     · 调试模式：${debug === 'true' ? '是' : '否'}<br>
                                     · 运行环境：${env}<br>
                                     · 微信语言：${lang}<br>
                                     · 系统主题：${theme}<br>
                                     · 微信版本：${wxVersion}`;
                            $('#useragent').html(uaText);
                        } else {
                            uaText = data.Useragent;
                            console.log(uaText);
                            if(uaText.indexOf('Mozilla') > -1){
                                const parser = new UAParser();
                                parser.setUA(uaText);
                                const browser = parser.getBrowser();
                                const os = parser.getOS();
                                const device = parser.getDevice();
                                uaText = `浏览器信息：<br>
                                         · 名称：${browser.name}<br>
                                         · 版本：${browser.version}<br>
                                         · 引擎：${browser.engine}<br>
                                         · 内核：${browser.engineVersion}<br>
                                         · 操作系统：<br>
                                             &nbsp;&nbsp;&nbsp;&nbsp;· 名称：${os.name}<br>
                                             &nbsp;&nbsp;&nbsp;&nbsp;· 版本：${os.version}<br>
                                         · 设备：<br>
                                             &nbsp;&nbsp;· 名称：${device.vendor}<br>
                                             &nbsp;&nbsp;· 型号：${device.model}<br>
                                             &nbsp;&nbsp;· 屏幕分辨率：${device.resolution}<br>
                                             &nbsp;&nbsp;· 屏幕大小：${device.width}x${device.height}<br>
                                             &nbsp;&nbsp;· 像素比：${device.pixelRatio}`;
                            } else {
                                uaText = uaText.replace(/;/g, '<br>');
                            }
                        }
                        if (uaText !== '--') {
                            $('#useragent').html(uaText);
                        }

                        $('#create_time').text(data.Create_time ? data.Create_time.replace('T', ' ').replace('Z', '') : '--');
                    } else {
                        layer.msg(res.msg || '加载失败', { icon: 2 });
                    }
                },
                error: function (res) {
                    layer.closeAll('loading');
                    layer.msg(res.responseJSON.msg, { icon: 2, time: 1000 });
                }
            });
        });

        function closeModalWindow() {
            var index = parent.layer.getFrameIndex(window.name);
            parent.layer.close(index);
        }
    </script>
</body>
</html> 