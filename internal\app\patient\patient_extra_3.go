package patient

import (
	"mstproject/pkg/common"
	"mstproject/pkg/database"
	"net/http"
	"strconv"
)

// 医助 - 患者病历接口
func Asst_records_list(w http.ResponseWriter, r *http.Request) {
	// TODO: 测试阶段该环节暂未JWT鉴权
	asst_id, err := common.CheckInt(r.FormValue("asst_id"))
	if err != nil || asst_id < 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "参数错误",
			"err":  "asst_id不能为空",
		})
		return
	}
	pageStr := r.FormValue("page")
	limitStr := r.<PERSON>alue("limit")
	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit < 1 {
		limit = 10
	}
	type Patient_records struct {
		ID                   int    `db:"id"`
		Name                 string `db:"name"`
		Sex                  string `db:"sex"`
		Born_date            string `db:"born_date"`
		Status               int    `db:"status"`
		Pat_id               int    `db:"pat_id"`
		Pat_pro_id           int    `db:"pat_pro_id"`
		Doc_id               int    `db:"doc_id"`
		Department_id        int    `db:"department_id"`
		Create_time          string `db:"create_time"`
		Confirm_consultation string `db:"confirm_consultation"`
	}
	sql := "select a.id,a.status,a.pat_id,a.pat_pro_id,a.doc_id,a.department_id,a.confirm_consultation,a.create_time,b.name,b.sex,b.born_date from patient_records as a left join patient_profile as b on a.pat_pro_id = b.id where a.asst_id = ?"
	sql += " ORDER BY a.id DESC LIMIT ? OFFSET ?"
	offset := (page - 1) * limit
	params := []any{asst_id}
	params = append(params, limit, offset)

	var patient_records []Patient_records
	err = database.GetAll(sql, &patient_records, params...)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败",
			"err":  err.Error(),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]any{
		"code": 200,
		"msg":  "ok",
		"data": patient_records,
		// "sql":  common.DebugSql(sql, params...),
	})
}

// 医助 - 患者病历详情
func Asst_records_detail(w http.ResponseWriter, r *http.Request) {
	// TODO: 测试阶段该环节暂未JWT鉴权
	asst_id, err := common.CheckInt(r.FormValue("asst_id"))
	if err != nil || asst_id < 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "参数错误",
			"err":  "asst_id不能为空",
		})
		return
	}
	record_id, err := common.CheckInt(r.FormValue("record_id"))
	if err != nil || record_id < 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "参数错误",
			"err":  "record_id不能为空",
		})
		return
	}
	type Patient_records struct {
		ID                         int    `db:"id"`
		Pat_pro_id                 int    `db:"pat_pro_id"`
		Chief_complaint            string `db:"chief_complaint"`
		Past_medical_history       string `db:"past_medical_history"`
		Personal_history           string `db:"personal_history"`
		Family_history             string `db:"family_history"`
		Allergy_history            string `db:"allergy_history"`
		Past_medication_history    string `db:"past_medication_history"`
		Re_chief_complaint         string `db:"re_chief_complaint"`
		History_of_present_illness string `db:"history_of_present_illness"`
		Urination                  string `db:"urination"`
		Triad                      string `db:"triad"`
		Tonguedesc                 string `db:"tonguedesc"`
		Photo_tongue               string `db:"photo_tongue"`
		Photo_sheet                string `db:"photo_sheet"`
		Confirm_consultation       string `db:"confirm_consultation"`
		Phone                      string `db:"phone"`
		Name                       string `db:"name"`
	}
	sql := `
		SELECT
		a.id,
		a.pat_pro_id,
		a.chief_complaint,
		a.past_medical_history,
		a.personal_history,
		a.family_history,
		a.allergy_history,
		a.past_medication_history,
		a.re_chief_complaint,
		a.history_of_present_illness,
		a.urination,
		a.triad,
		a.tonguedesc,
		a.photo_tongue,
		a.photo_sheet,
		a.confirm_consultation,
		b.name,
		b.phone
		FROM
		patient_records AS a
		LEFT JOIN patient_profile AS b ON a.pat_pro_id = b.id
		WHERE
		a.asst_id = ?
		AND a.id = ?
	`
	params := []any{asst_id, record_id}
	var patient_records Patient_records
	err = database.GetRow(sql, &patient_records, params...)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败",
			"err":  err.Error(),
		})
		return
	}
	phone := patient_records.Phone
	if len(phone) == 11 || len(phone) == 13 {
		patient_records.Phone = phone[:5] + "****" + phone[len(phone)-2:]
	} else {
		patient_records.Phone = "手机号码格式错误"
	}
	common.JSONResponse(w, http.StatusOK, map[string]any{
		"code": 200,
		"msg":  "ok",
		"data": patient_records,
		// "sql":  common.DebugSql(sql, params...),
	})
}

// 医助 - 确认问诊咨询单
func Confirm_consultation(w http.ResponseWriter, r *http.Request) {
	asst_id, err := common.CheckInt(r.FormValue("asst_id"))
	if err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "参数错误",
			"err":  err.Error(),
		})
		return
	}
	record_id, err := common.CheckInt(r.FormValue("record_id"))
	if err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "参数错误",
			"err":  err.Error(),
		})
		return
	}
	sql := "update patient_records set confirm_consultation = now() where id = ? and asst_id = ?"
	_, err = database.Query(sql, record_id, asst_id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "更新失败",
			"err":  err.Error(),
			// "sql":  common.DebugSql(sql, record_id, patientid),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "更新成功",
		// "sql":  common.DebugSql(sql, record_id, patientid),
	})
}
