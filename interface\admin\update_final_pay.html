<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 更新尾款</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
    <style>
        /* 高级质感 */
        .premium-section {
            margin-bottom: 20px;
            padding: 20px;
            border-radius: 8px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }

        .currency-symbol {
            font-size: 16px;
            color: #666;
            margin-right: 5px;
        }

        .payment-value.highlight {
            color: #FF5722;
            font-size: 18px;
            font-weight: bold;
        }

        .layui-input[readonly] {
            background-color: #f5f5f5;
            color: #666;
            cursor: not-allowed;
        }

        /* 美化尾款输入框 */
        .enhanced-input {
            font-size: 18px !important;
            height: 45px !important;
            background-color: #fff !important;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
            border: 1px solid #e6e6e6 !important;
            transition: all 0.3s ease !important;
            padding: 0 15px !important;
            color: #FF5722 !important;
            font-weight: 500 !important;
        }

        .enhanced-input:focus {
            box-shadow: 0 4px 12px rgba(30, 159, 255, 0.2) !important;
            border-color: #1E9FFF !important;
        }

        /* 动画效果 */
        .animated-value {
            transition: all 0.3s ease;
        }

        .animated-value.changed {
            animation: highlight 1s ease;
        }

        @keyframes highlight {
            0% {
                background-color: transparent;
            }

            50% {
                background-color: rgba(255, 87, 34, 0.2);
            }

            100% {
                background-color: transparent;
            }
        }

        /* 警告提示样式 */
        .warning-alert {
            background-color: #FFF3F0;
            border-left: 3px solid #FF5722;
            padding: 10px 15px;
            margin: 10px 0;
            border-radius: 3px;
            color: #FF5722;
            font-size: 14px;
            display: flex;
            align-items: center;
        }

        .warning-alert .layui-icon {
            margin-right: 10px;
            font-size: 18px;
        }

        /* 水平布局的金额信息样式 */
        .amount-display {
            background: linear-gradient(145deg, #f5f7fa, #e0e5ec);
            border-radius: 8px;
            padding: 12px 15px;
            margin-bottom: 15px;
            border: 1px solid white;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .amount-item {
            text-align: center;
            padding: 0 10px;
        }

        .amount-item:not(:last-child) {
            border-right: 1px dashed rgba(0, 0, 0, 0.1);
        }

        .amount-label {
            color: #666;
            font-weight: 500;
            font-size: 13px;
            margin-bottom: 5px;
        }

        .amount-value {
            font-weight: 600;
            color: #333;
            font-size: 16px;
        }

        .final-amount {
            color: #FF5722;
            font-size: 18px;
        }

        /* 小尺寸文本域 */
        .small-textarea {
            min-height: 60px !important;
            height: 60px !important;
        }

        /* 日志项样式 */
        .log-item {
            padding: 8px 10px;
            margin-bottom: 8px;
            border-radius: 6px;
            background-color: #f5f5f5;
            border-left: 3px solid #1E9FFF;
            font-size: 13px;
            transition: all 0.2s ease;
        }

        .log-item:hover {
            background-color: #f0f0f0;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        }

        .log-item:last-child {
            margin-bottom: 0;
        }

        .log-item .log-content {
            margin-bottom: 4px;
        }

        .log-item .log-meta {
            display: flex;
            justify-content: space-between;
            color: #999;
            font-size: 12px;
        }

        .log-item .log-operator {
            margin-right: 10px;
        }

        .upload_big_btn {
            width: 260px;
        }

        .upload-container {
            padding: 10px;
        }

        .image-preview-container-normal {
            min-height: auto;
        }

        .image-preview-container-normal img {
            width:auto;
            height: 130px;
        }
    </style>
</head>

<body>
    <div class="layui-padding-3">
        <form class="layui-form" lay-filter="myform">
            <!-- 尾款设置卡片 -->
            <div class="layui-card">
                <!-- <div class="layui-card-header">
                    <i class="layui-icon layui-icon-rmb"></i> 订单尾款结算
                </div> -->
                <div class="layui-card-body" style="padding-top:0">
                    <!-- 金额信息显示 - 水平布局 -->
                    <div class="amount-display">
                        <div class="layui-row">
                            <div class="layui-col-xs3 amount-item">
                                <div class="amount-label">订单总额</div>
                                <div class="amount-value">¥<span id="display-total">0.00</span></div>
                            </div>
                            <div class="layui-col-xs3 amount-item">
                                <div class="amount-label">已预付款</div>
                                <div class="amount-value">¥<span id="display-prepaid">0.00</span></div>
                            </div>
                            <div class="layui-col-xs3 amount-item">
                                <div class="amount-label">应付尾款</div>
                                <div class="amount-value final-amount">¥<span id="display-final"
                                        class="animated-value">0.00</span></div>
                            </div>
                            <div class="layui-col-xs3 amount-item" id="actual-payment-container" style="display: none;">
                                <div class="amount-label">实付尾款</div>
                                <div class="amount-value">¥<span id="display-actual-final"
                                        class="animated-value">0.00</span></div>
                            </div>
                        </div>
                    </div>

                    <!-- 尾款日志显示区域 -->
                    <div id="final-pay-remark-container" class="layui-text"
                        style="margin-top: 10px; margin-bottom: 15px; padding: 10px; border-radius: 6px; background-color: #f9f9f9; display: none;">
                        <div style="font-weight: 500; margin-bottom: 5px; color: #666;">
                            <i class="layui-icon layui-icon-note"></i> 尾款日志：
                        </div>
                        <div id="final-pay-remark-content" style="color: #333; line-height: 1.6;"></div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">尾款类型</label>
                        <div class="layui-input-block">
                            <input type="radio" name="pay_type" value="full" title="全额尾款" checked lay-filter="payType">
                            <input type="radio" name="pay_type" value="partial" title="非全额尾款" lay-filter="payType">
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">尾款金额</label>
                        <div class="layui-input-block">
                            <input type="number" name="final_money" id="final_money" readonly lay-verify="required"
                                lay-reqtext="请输入尾款金额" placeholder="请输入尾款金额" autocomplete="off" class="layui-input">
                            <!-- 添加警告提示，默认隐藏 -->
                            <div class="warning-alert" id="amount-warning" style="display: none;">
                                <i class="layui-icon layui-icon-tips"></i>
                                <span>尾款金额不得超过原始应付尾款！</span>
                            </div>
                            <!-- 隐藏字段 -->
                            <input type="hidden" name="total_amount" id="total_amount">
                            <input type="hidden" name="prepaid_amount" id="prepaid_amount">
                        </div>
                    </div>

                    <!-- 添加备注文本域 - 减小高度 -->
                    <div class="layui-form-item" id="remark_container" style="display: none;">
                        <label class="layui-form-label">备注说明</label>
                        <div class="layui-input-block">
                            <textarea name="remark" placeholder="请输入非全额尾款的原因说明，及返回商品物流信息"
                                class="layui-textarea small-textarea"></textarea>
                        </div>
                    </div>

                    <!-- 收款截图上传 -->
                    <div class="layui-row layui-form-item">
                        <!-- 第1行：收款截图上传 占6；图片预览 占6 -->
                        <div class="layui-col-xs6 layui-col-sm6 layui-col-md6">
                            <div class="layui-form-item">
                                <label class="layui-form-label">收款截图</label>
                                <div class="layui-input-block">
                                    <input type="hidden" name="final_pay_pic" value="">
                                    <div class="layui-upload upload-container" id="upload-container">
                                        <button type="button" class="layui-btn upload_big_btn" id="upload-image-btn">
                                            <div class="btn_big_font">
                                                <i class="layui-icon layui-icon-upload btn_big_font"></i> 上传图片
                                            </div>
                                            <div style="font-size: 12px;">jpg、png、jpeg、bmp、gif格式，1M以内</div>
                                            <div>支持点击、拖拽和 Ctrl+V 上传</div>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-xs6 layui-col-sm6 layui-col-md6">
                            <div class="layui-form-item">
                                <div class="layui-input-block">
                                    <div class="image-preview-container-normal" id="image-container"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <div class="layui-input-block">
                            <button class="layui-btn" lay-submit lay-filter="formDemo">立即提交</button>
                            <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <script>
        layui.use(['form', 'layer', 'upload'], function () {
            var form = layui.form;
            var layer = layui.layer;
            var upload = layui.upload;
            var $ = layui.$;

            // 获取URL中的订单ID参数
            var orderId = request.get('id');
            var money_array = request.get('money').split('|');
            var Total_money = parseFloat(money_array[0]);
            var Pre_pay = parseFloat(money_array[1]);
            var Final_pay = Total_money - Pre_pay;
            var Original_final_pay = Final_pay; // 记录原始应付尾款，用于后续验证

            // 格式化金额显示，保留两位小数
            function formatMoney(amount) {
                return parseFloat(amount || 0).toFixed(2);
            }

            // 初始化金额显示
            $('#display-total').text(formatMoney(Total_money));
            $('#display-prepaid').text(formatMoney(Pre_pay));
            $('#display-final').text(formatMoney(Final_pay));

            // 初始化表单字段值
            $('#total_amount').val(Total_money);
            $('#prepaid_amount').val(Pre_pay);
            $('#final_money').val(Final_pay);

            // 从后端获取尾款明细数据
            layer.load(2);
            $.ajax({
                url: '/admin/order/pay_detail',
                type: 'POST',
                data: { id: orderId },
                success: function (res) {
                    layer.closeAll('loading');
                    if (res.code === 200) {
                        var orderData = res.data;
                        console.log('订单数据:', orderData); // 调试用，查看返回的字段名

                        // 处理尾款日志
                        if (orderData.Final_pay_remark && orderData.Final_pay_remark.trim() !== '') {
                            // 清空原有内容
                            $('#final-pay-remark-content').empty();

                            // 按 ### 分割日志
                            var logs = orderData.Final_pay_remark.split('###').filter(function (log) {
                                return log.trim() !== '';
                            });

                            // 遍历日志并添加到容器
                            if (logs.length > 0) {
                                logs.forEach(function (log) {
                                    // 按 | 分割每条日志的内容、操作者ID和时间
                                    var logParts = log.split('|');
                                    var logContent = logParts[0] || '';
                                    var operatorId = logParts[1] || '';
                                    var operateTime = logParts[2] || '';
                                    var final_money = logParts[3] || '';//当时实付尾款

                                    // 创建日志项元素
                                    var logItem = $('<div class="log-item"></div>');
                                    logItem.append('<div class="log-content">' + logContent + '</div>');

                                    // 添加操作者和时间信息
                                    var logMeta = $('<div class="log-meta"></div>');
                                    if (operatorId) {
                                        logMeta.append('<span class="log-operator">操作者ID: ' + operatorId + '</span>');
                                    }
                                    if (final_money) {
                                        logMeta.append('<span class="log-operator">当时实付: ￥' + final_money + '</span>');
                                    }
                                    if (operateTime) {
                                        logMeta.append('<span class="log-time">' + operateTime + '</span>');
                                    }

                                    logItem.append(logMeta);
                                    $('#final-pay-remark-content').append(logItem);
                                });

                                $('#final-pay-remark-container').show();
                            }
                        }
                    } else {
                        console.log('获取尾款明细失败:', res.msg);
                    }
                },
                error: function (xhr) {
                    layer.closeAll('loading');
                    console.log('获取尾款明细请求失败:', xhr.responseJSON ? xhr.responseJSON.msg : '网络错误');
                }
            });

            // 监听单选按钮切换
            form.on('radio(payType)', function (data) {
                if (data.value === 'partial') {
                    // 使用确认框提示
                    layer.confirm('非全额尾款将退回部分商品，确定继续吗？', {
                        icon: 3,
                        title: '提示',
                        btn: ['确定', '取消']
                    }, function (index) {
                        // 用户点击确定
                        $('#remark_container').show();
                        $('#final_money').removeAttr('readonly').val("");
                        $('#actual-payment-container').show(); // 显示实付尾款列

                        // 初始化实付尾款显示
                        $('#display-actual-final').text('0.00');

                        // 美化输入框样式
                        $('#final_money').addClass('enhanced-input').focus();

                        layer.close(index);
                    }, function () {
                        // 用户点击取消，恢复到全额尾款选项
                        $('input[name="pay_type"][value="full"]').prop('checked', true);
                        form.render('radio'); // 重新渲染表单元素
                    });
                } else {
                    // 全额尾款时
                    $('#remark_container').hide();
                    $('#actual-payment-container').hide(); // 隐藏实付尾款列
                    $('#final_money').attr('readonly', 'readonly');
                    $('#final_money').removeClass('enhanced-input');

                    // 隐藏警告信息
                    $('#amount-warning').hide();

                    // 恢复原始值
                    $('#final_money').val(formatMoney(Final_pay));
                    $('#total_amount').val(formatMoney(Total_money));

                    // 恢复显示
                    $('#display-total').text(formatMoney(Total_money));
                    $('#display-final').text(formatMoney(Final_pay));
                }
            });

            // 监听尾款金额变化
            $('#final_money').on('input', function () {
                if ($('input[name="pay_type"]:checked').val() === 'partial') {
                    var newFinalAmount = parseFloat($(this).val()) || 0;

                    // 检查尾款是否大于等于订单总额
                    if (newFinalAmount >= Total_money) {
                        // 自动切换到全额尾款
                        $('input[name="pay_type"][value="full"]').prop('checked', true);
                        form.render('radio'); // 重新渲染表单元素

                        // 触发全额尾款的处理逻辑
                        $('#remark_container').hide();
                        $('#actual-payment-container').hide(); // 隐藏实付尾款列
                        $('#final_money').attr('readonly', 'readonly');
                        $('#final_money').removeClass('enhanced-input');

                        // 恢复原始值
                        $('#final_money').val(formatMoney(Final_pay));
                        $('#total_amount').val(formatMoney(Total_money));

                        // 恢复显示
                        $('#display-total').text(formatMoney(Total_money));
                        $('#display-final').text(formatMoney(Final_pay));

                        // 提示用户
                        layer.msg('尾款金额大于等于订单总额，已自动切换为全额尾款', { icon: 1 });
                        return;
                    }

                    // 检查尾款是否超过原始尾款
                    if (newFinalAmount > Original_final_pay) {
                        $('#amount-warning').show();
                        // 将输入值限制为原始尾款金额
                        $(this).val(Original_final_pay);
                        newFinalAmount = Original_final_pay;
                    } else {
                        $('#amount-warning').hide();
                    }

                    // 更新实付尾款显示
                    $('#display-actual-final').text(formatMoney(newFinalAmount))
                        .addClass('changed');

                    // 移除高亮效果
                    setTimeout(function () {
                        $('.animated-value').removeClass('changed');
                    }, 1000);
                }
            });

            // 图片预览函数
            function appendImagePreview(containerId, filepath, filename) {
                const container = $(`#${containerId}`);
                // 清空现有内容，确保只有一张图片
                container.empty();

                const imageItem = $(`
                    <div class="image-preview-item" data-filename="${filename}" data-filepath="${filepath}">
                        <img src="${filepath}">
                        <div class="delete-btn">删除</div>
                    </div>
                `);

                // 点击图片查看大图
                imageItem.find('img').on('click', function () {
                    layer.photos({
                        photos: {
                            title: '查看图片',
                            data: [{ src: filepath }]
                        },
                        footer: false
                    });
                });

                // 删除图片
                imageItem.find('.delete-btn').on('click', function () {
                    layer.confirm('确定删除该图片吗？', { icon: 3, title: '提示' }, function (index) {
                        $.ajax({
                            url: '/admin/normal_pic_del',
                            type: 'POST',
                            data: {
                                filename: filename,
                                category: 'order'
                            },
                            success: function (res) {
                                if (res.code === 200) {
                                    // 清空图片路径
                                    $('input[name="final_pay_pic"]').val('');
                                    container.empty();
                                    layer.msg('删除成功', { icon: 1, time: 1000 });
                                }
                            },
                            error: function (data) {
                                layer.confirm('删除失败: ' + data.responseJSON.msg + '，是否移除该图片？',
                                    { icon: 3, title: '提示' },
                                    function (index) {
                                        // 清空图片路径
                                        $('input[name="final_pay_pic"]').val('');
                                        container.empty();
                                        layer.msg('移除成功', { icon: 1, time: 1000 });
                                    }
                                );
                            }
                        });
                        layer.close(index);
                    });
                });

                container.append(imageItem);
            }

            // 初始化文件上传
            upload.render({
                elem: '#upload-image-btn',
                url: '/admin/upload_normal_pic',
                multiple: true,
                data: {
                    category: 'order',
                },
                drag: true,
                before: function (obj) {
                    obj.preview(function (index, file, result) {
                        // 在预览回调中进行文件上传
                        autoCompressAndUpload(file, {
                            data: { category: 'order' },
                            success: function (res) {
                                if (res.code === 200) {
                                    const fileInfo = res.data[0];
                                    // 显示图片预览
                                    appendImagePreview('image-container', fileInfo.filepath, fileInfo.filename);
                                    // 将图片文件名保存到隐藏字段中（不是完整路径）
                                    $('input[name="final_pay_pic"]').val(fileInfo.filename);
                                    layer.msg('上传成功', { icon: 1, time: 1000 });
                                } else {
                                    layer.msg(res.msg || '上传失败', { icon: 2 });
                                }
                            },
                            error: function (error) {
                                layer.msg(error.message || '上传出错', { icon: 2 });
                            }
                        });
                    });
                    return false;
                }
            });

            // 添加粘贴上传功能
            document.addEventListener('paste', function (event) {
                const items = event.clipboardData && event.clipboardData.items;
                let file = null;

                if (items && items.length) {
                    // 遍历剪切板内容
                    for (let i = 0; i < items.length; i++) {
                        if (items[i].type.indexOf('image') !== -1) {
                            file = items[i].getAsFile();
                            break;
                        }
                    }
                }

                if (!file) {
                    return;
                }

                // 阻止默认粘贴行为
                event.preventDefault();

                // 使用通用的压缩上传函数
                autoCompressAndUpload(file, {
                    data: { category: 'order' },
                    success: function (res) {
                        if (res.code === 200) {
                            const fileInfo = res.data[0];
                            // 显示图片预览
                            appendImagePreview('image-container', fileInfo.filepath, fileInfo.filename);
                            // 将图片文件名保存到隐藏字段中（不是完整路径）
                            $('input[name="final_pay_pic"]').val(fileInfo.filename);
                            layer.msg('上传成功', { icon: 1, time: 1000 });
                        } else {
                            layer.msg(res.msg || '上传失败', { icon: 2 });
                        }
                    },
                    error: function (error) {
                        layer.msg(error.message || '上传出错', { icon: 2 });
                    }
                });
            });

            // 监听提交
            form.on('submit(formDemo)', function (data) {
                // 检查必填项
                // if (!data.field.final_pay_pic) {
                //     layer.msg('请上传收款截图', {icon: 2});
                //     return false;
                // }

                if (data.field.pay_type === 'partial' && !data.field.remark) {
                    layer.msg('非全额尾款请填写备注说明', { icon: 2 });
                    return false;
                }

                // 准备提交数据 - 转换为后端需要的格式
                var final_is_full = data.field.pay_type === 'full' ? '1' : '2';

                layer.load(2);

                // 发送请求到后端接口
                $.ajax({
                    url: '/admin/order/pay_update_final',
                    type: 'POST',
                    data: {
                        id: orderId,                           // 订单ID
                        final_is_full: final_is_full,                // 金额类型：1全额，2非全额
                        final_money: parseFloat(data.field.final_money), // 尾款金额（实付尾款或全额尾款）
                        total_money: parseFloat($('#total_amount').val()), // 订单总额（保持不变）
                        final_pay_pic: data.field.final_pay_pic,  // 收款截图
                        remark: data.field.remark || ''        // 备注（非全额时需要）
                    },
                    success: function (res) {
                        layer.closeAll('loading');
                        if (res.code === 200) {
                            layer.msg('尾款更新成功', { icon: 1, time: 1000 }, function () {
                                // 关闭当前iframe层
                                var index = parent.layer.getFrameIndex(window.name);
                                parent.layer.close(index);
                                // 刷新父页面表格
                                parent.layui.table.reload('mytable');
                            });
                        } else {
                            layer.msg(res.msg || '更新失败', { icon: 2 });
                        }
                    },
                    error: function (xhr) {
                        layer.closeAll('loading');
                        layer.msg('提交失败：' + (xhr.responseJSON ? xhr.responseJSON.msg : xhr.statusText), { icon: 2 });
                    }
                });

                return false;
            });
        });
    </script>
</body>

</html>