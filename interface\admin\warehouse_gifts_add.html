<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 赠品采购入库</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <script src="/dist/layui/layui.js"></script>
    <style>
        .layui-table-cell {
            height: auto;
            line-height: 28px;
        }

        .layui-form-select {
            margin-bottom: 10px;
        }

        .layui-input-inline {
            width: 300px !important;
        }

        .unit-label {
            line-height: 38px;
            margin-left: 5px;
        }

        .action-cell,
        .status-cell {
            text-align: center;
        }

        .msg_box_style {
            margin-bottom: 15px;
            display: flex;
            padding: 20px;
            justify-content: space-around;
            align-items: center;
            font-size: 15px;
        }
    </style>
</head>

<body style="padding: 10px;">
    <div style="display: flex; justify-content: center;">
        <div
            style="width:300px;overflow-y: auto;background-color: #f8f8f8;padding: 10px;border-right: 1px solid #e6e6e6;">
            <table class="layui-table" lay-size="sm">
                <thead>
                    <tr>
                        <th style="width: 50%;">赠品名称</th>
                        <th style="width: 25%;text-align: center;">类型</th>
                        <th style="width: 25%;text-align: center;">状态</th>
                    </tr>
                </thead>
                <tbody id="selectedGifts">
                    <!-- 动态插入已选择的赠品 -->
                </tbody>
            </table>
        </div>
        <div style="flex: 1;">
            <table id="giftsTable" lay-filter="giftsTable"></table>
        </div>
    </div>

    <div
        style="position: fixed; bottom: 0; left: 0; right: 0; background-color: #fff; padding: 10px; text-align: center; box-shadow: 0 -2px 10px rgba(0,0,0,0.1);">
        <button type="button" class="layui-btn" id="submitBtn">采购入库</button>
        <button type="button" class="layui-btn layui-btn-primary" id="cancelBtn">取消</button>
    </div>

    <!-- 添加进度条容器 -->
    <div id="uploadProgress"
        style="display: none; position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 20px; border-radius: 4px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); width: 400px; z-index: 999;">
        <div style="margin-bottom: 10px;">正在处理赠品入库...</div>
        <div class="layui-progress layui-progress-big" lay-filter="upload-progress" lay-showPercent="true">
            <div class="layui-progress-bar" lay-percent="0%"></div>
        </div>
    </div>

    <!-- 添加结果统计对话框的模板 -->
    <script type="text/html" id="resultDialogTpl">
        <div style="padding: 20px;">
            <div class="msg_box_style">
                <p>总计处理：{{d.total}}条</p>
                <p>成功入库：<span style="color: #5FB878;">{{d.success}}条</span></p>
                <p>入库失败：<span style="color: #FF5722;">{{d.fail}}条</span></p>
            </div>
            <div style="text-align: center;">
                <button type="button" class="layui-btn" onclick="continueUpload()">继续入库</button>
                <button type="button" class="layui-btn layui-btn-primary" onclick="exitUpload()">退出采购</button>
            </div>
        </div>
    </script>

    <script>
        layui.use(['table', 'layer', 'form', 'element'], function () {
            var table = layui.table;
            var layer = layui.layer;
            var form = layui.form;
            var element = layui.element;
            var $ = layui.$;

            // 存储用户输入数据的对象（跨分页保存）
            var userInputData = {};

            // 防抖函数
            function debounce(func, wait) {
                var timeout;
                return function () {
                    var context = this,
                        args = arguments;
                    clearTimeout(timeout);
                    timeout = setTimeout(function () {
                        func.apply(context, args);
                    }, wait);
                };
            }

            // 更新左侧已选赠品列表
            function updateSelectedGifts(giftId, giftName, status) {
                var $selectedGift = $('#selectedGifts').find('[data-id="' + giftId + '"]');
                if (status) {
                    if ($selectedGift.length === 0) {
                        var statusIcon = '<i class="layui-icon layui-icon-time"></i>';
                        // 检查赠品名称
                        $.ajax({
                            url: '/admin/warehouse_gift/check_by_id',
                            async: false,
                            type: 'POST',
                            data: { id: giftId },
                            success: function (res) {
                                var action = res.code === 200 ? '补货' : '新增';
                                var newRow = '<tr data-id="' + giftId + '">' +
                                    '<td>' + giftName + '</td>' +
                                    '<td class="action-cell">' + action + '</td>' +
                                    '<td class="status-cell">' + statusIcon + '</td>' +
                                    '</tr>';
                                $('#selectedGifts').append(newRow);
                            },
                            error: function () {
                                layer.msg('检查赠品名称失败', { icon: 2 });
                            }
                        });
                    }
                } else {
                    $selectedGift.remove();
                }
            }

            // 防抖处理函数
            var debouncedUpdate = debounce(function ($input) {
                var id = $input.data('id');
                var $row = $input.closest('tr');
                var giftName = $row.find('td[data-field="Name"]').text();

                if (!userInputData[id]) {
                    userInputData[id] = {};
                }

                if ($input.hasClass('price-input')) {
                    userInputData[id].price = $input.val();
                } else if ($input.hasClass('quantity-input')) {
                    userInputData[id].quantity = $input.val();
                }

                // 检查是否所有必填字段都已填写
                var isComplete = userInputData[id].price &&
                    userInputData[id].quantity;

                updateSelectedGifts(id, giftName, isComplete);
            }, 100);

            // 渲染表格
            table.render({
                elem: '#giftsTable',
                url: '/admin/gift/list',
                limit: 10,
                method: 'post',
                page: true,
                cols: [[
                    {
                        field: 'Name',
                        title: '赠品名',
                        align: 'center'
                    },
                    {
                        field: 'Factory_date',
                        title: '出厂日期',
                        align: 'center',
                        templet:function(data){
                            return data.Factory_date.split('T')[0]
                        }
                    },
                    {
                        field: 'price',
                        title: '核算价格',
                        templet: function (d) {
                            var value = userInputData[d.ID]?.price || '';
                            return '<input type="number" class="layui-input price-input" data-id="' + d.ID +
                                '" value="' + value + '" step="0.01" min="0">';
                        }
                    },
                    {
                        field: 'quantity',
                        title: '入库数量',
                        templet: function (d) {
                            var value = userInputData[d.ID]?.quantity || '';
                            return '<input type="number" class="layui-input quantity-input" data-id="' + d.ID +
                                '" value="' + value + '" min="1">';
                        }
                    }
                ]],
                response: {
                    statusName: 'code',
                    statusCode: 200,
                    msgName: 'msg',
                    countName: 'count',
                    dataName: 'data'
                },
                done: function () {
                    // 为新渲染的输入框绑定事件
                    $('.price-input, .quantity-input').on('input', function () {
                        debouncedUpdate($(this));
                    });
                }
            });

            // 提交按钮点击事件
            $('#submitBtn').on('click', function () {
                layer.confirm('是否确认将选定的赠品入库？', function (index) {
                    layer.close(index);

                    var $selectedGifts = $('#selectedGifts tr');
                    if ($selectedGifts.length === 0) {
                        layer.msg('请至少选择一个赠品并填写完整信息', { icon: 2 });
                        return;
                    }

                    // 显示进度条
                    $('#uploadProgress').show();
                    element.progress('upload-progress', '0%');

                    // 初始化计数器
                    var successCount = 0;
                    var failCount = 0;
                    var totalCount = $selectedGifts.length;
                    var currentIndex = 0;

                    // 串行处理每个赠品
                    function processNextGift() {
                        if (currentIndex >= totalCount) {
                            // 所有赠品处理完成，显示结果对话框
                            $('#uploadProgress').hide();
                            showResultDialog(successCount, failCount, totalCount);
                            return;
                        }

                        var $currentGift = $($selectedGifts[currentIndex]);
                        var id = $currentGift.data('id');

                        // 更新进度条
                        var progress = Math.round((currentIndex + 1) / totalCount * 100);
                        element.progress('upload-progress', progress + '%');

                        // 更新状态为处理中
                        $currentGift.find('.status-cell').html('<i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i>');

                        $.ajax({
                            url: '/admin/warehouse_gifts/in',
                            type: 'POST',
                            data: {
                                gift_id: parseInt(id),
                                price: parseFloat(userInputData[id].price),
                                quantity: parseFloat(userInputData[id].quantity)
                            },
                            success: function (res) {
                                var $giftRow = $('#selectedGifts').find('[data-id="' + id + '"]');
                                if (res.code === 200) {
                                    $giftRow.find('.status-cell').html('<i class="layui-icon layui-icon-ok"></i>');
                                    successCount++;
                                } else {
                                    $giftRow.find('.status-cell').html('<i class="layui-icon layui-icon-close"></i>');
                                    failCount++;
                                    layer.msg(res.msg || '赠品ID:' + id + ' 入库失败', { icon: 2 });
                                }
                                currentIndex++;
                                processNextGift();
                            },
                            error: function () {
                                var $giftRow = $('#selectedGifts').find('[data-id="' + id + '"]');
                                $giftRow.find('.status-cell').html('<i class="layui-icon layui-icon-close"></i>');
                                failCount++;
                                layer.msg('赠品ID:' + id + ' 入库失败', { icon: 2 });
                                currentIndex++;
                                processNextGift();
                            }
                        });
                    }

                    // 开始处理第一个赠品
                    processNextGift();
                });
            });

            // 显示结果统计对话框
            function showResultDialog(successCount, failCount, totalCount) {
                var content = layui.laytpl($('#resultDialogTpl').html()).render({
                    total: totalCount,
                    success: successCount,
                    fail: failCount
                });

                layer.open({
                    type: 1,
                    title: '入库完成',
                    content: content,
                    area: ['500px', 'auto'],
                    closeBtn: 0,
                    shadeClose: false
                });
            }

            // 继续上传按钮回调
            window.continueUpload = function () {
                layer.closeAll();
                // 清空已选赠品列表和用户输入数据
                $('#selectedGifts').empty();
                userInputData = {};
                // 刷新表格
                table.reload('giftsTable');
            };

            // 退出采购按钮回调
            window.exitUpload = function () {
                var index = parent.layer.getFrameIndex(window.name);
                parent.layer.close(index);
                if (typeof parent.table !== 'undefined') {
                    parent.table.reload('mytable');
                }
            };

            // 取消按钮点击事件
            $('#cancelBtn').on('click', function () {
                var index = parent.layer.getFrameIndex(window.name);
                parent.layer.close(index);
            });
        });
    </script>
</body>

</html>