<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 盘点单管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
    <style>
        .layui-table-cell {
            height: auto;
            line-height: 28px;
        }
    </style>
</head>

<body>
    <div class="layui-layout layui-layout-admin">
        <!-- 头部 -->
        <div class="layui-header">
            <div class="layui-logo">幸年堂</div>
            <ul class="layui-nav layui-layout-right">
                <li class="layui-nav-item" style="background-color:transparent!important;padding-right:10px">
                    <a href="javascript:;" style="display:flex;align-items:center;color:#fff"><img
                            src="/dist/images/user.png" class="layui-nav-img"><span class="user_name"></span></a>
                    <dl class="layui-nav-child pop_menu">
                        <dd><i class="iconfont">&#xe62f;</i><a href="change_password.html#7">用户资料</a></dd>
                        <dd><i class="iconfont">&#xe601;</i><a href="javascript:void(0)" class="refresh_cache">刷新缓存</a>
                        </dd>
                        <dd><i class="iconfont">&#xe634;</i><a href="login_out.html">退出登录</a></dd>
                    </dl>
                </li>
            </ul>
        </div>
        <!-- 导航 -->
        <ul class="layui-nav layui-nav-tree layui-nav-side" id="menuContainer" lay-bar="disabled" lay-accordion>
            <div style="margin: 50px 0;color: #bbb;text-align: center;">菜单加载中...</div>
        </ul>
        <!-- 主体内容 -->
        <div class="layui-body">
            <div class="body_child">
                <div class="layui-panel">
                    <!-- 添加搜索模块 -->
                    <div class="layui-form" style="position: absolute;top:50px;right: 50px;">
                        <div class="layui-row" style="width: 100%;">

                            <div class="layui-col-md6">
                                <div style="padding-right: 10px;">
                                    <input type="text" name="key" placeholder="请输入盘点单名称" autocomplete="off"
                                        class="layui-input" lay-affix="clear">
                                </div>
                            </div>

                            <div class="layui-col-md3">
                                <button class="layui-btn" lay-submit lay-filter="search">
                                    <i class="layui-icon">&#xe615;</i>筛选</button>
                            </div>

                            <div class="layui-col-md3">
                                <button class="layui-btn layui-btn-primary layui-border-red perm_check_btn" lay-event="add" res_id="30" onclick="add()">
                                    <i class="layui-icon">&#xe654;</i> 添加
                                </button>
                            </div>

                        </div>
                    </div>
                    <!-- 搜索模块结束 -->

                    <div class="layui-card-header">
                        <div class="layui-row" style="padding-top:10px;">
                            <div class="layui-col-md11 layui-col-sm10" style="display: flex;">
                                <div>盘点单列表</div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-padding-3">
                        <div id="data_search">
                            <div class="layui-tab layui-tab-brief" lay-filter="stockType">
                                <ul class="layui-tab-title">
                                    <li class="layui-this">全部</li>
                                    <li>药材</li>
                                    <li>赠品</li>
                                    <li>成品</li>
                                </ul>
                            </div>
                        </div>

                        <table id="mytable" lay-filter="mytable" class="layui-table" lay-skin="row"></table>
                    </div>
                </div>
                <br>
                <br>
            </div>
        </div>
    </div>

    <script type="text/html" id="TPL-bar">
            <button class="layui-btn layui-btn-xs perm_check_btn" lay-event="complete" res_id="31">盘点状态</button>
            <button class="layui-btn layui-btn-xs layui-btn-danger perm_check_btn" lay-event="del" res_id="32">删除</button>
    </script>

    <script type="text/html" id="TPL-stock-type">
        {{#  if(d.Stock_type === 0){ }}
            <span>药材</span>
        {{#  } else if(d.Stock_type === 1) { }}
            <span>赠品</span>
        {{#  } else if(d.Stock_type === 2) { }}
            <span>成品</span>
        {{#  } }}
    </script>

    <script type="text/html" id="TPL-check-mode">
        {{#  if(d.Check_mode === 0){ }}
            <span>不可多次盘点</span>
        {{#  } else if(d.Check_mode === 1) { }}
            <span>可以多次盘点</span>
        {{#  } }}
    </script>

    <script type="text/html" id="TPL-check-status">
        {{#  if(d.Check_status === 0){ }}
            <span class="layui-badge layui-bg-gray">未完成</span>
        {{#  } else if(d.Check_status === 1) { }}
            <span class="layui-badge layui-bg-green">已完成</span>
        {{#  } }}
    </script>

    <script>
        layui.use(['element', 'layer', 'util'], function () {
            var element = layui.element;
            var form = layui.form;
            var layer = layui.layer;
            var util = layui.util;
            var table = layui.table;
            var $ = layui.$;

            // 定义全局变量currentType，用于记录当前选中的状态
            let currentType = -1; // -1表示全部

            // 监听Tab切换事件
            element.on('tab(stockType)', function (data) {
                // 根据选项卡索引设置currentType
                switch (data.index) {
                    case 0: currentType = -1; break; // 全部
                    case 1: currentType = 0; break;  // 药材
                    case 2: currentType = 1; break;  // 赠品
                    case 3: currentType = 2; break;  // 成品
                }

                // 获取搜索框的值
                let searchKey = $('input[name="key"]').val();

                // 重新加载表格数据
                layer.load(2);
                table.reload('mytable', {
                    where: {
                        stock_type: currentType,
                        key: searchKey || undefined  // 如果searchKey有值就携带，没有就不传
                    }
                    , page: {
                        curr: 1
                    }
                });
            });

            // 页面顶部插入缓存的用户数据
            let local_userinfo = localStorage.getItem('local_userinfo');
            let user_name = "";
            if (local_userinfo) {
                local_userinfo = JSON.parse(local_userinfo);
                user_name = local_userinfo.Name + " ( " + local_userinfo.Roles + " )";
            } else {
                user_name = "未登录";
            }
            $('.user_name').text(user_name);
            render_menu($);
            $('.refresh_cache').click(function () {
                refresh_cache($);
            });

            // 获取用户映射数据
            let userMap = {};

            // 获取用户映射
            $.ajax({
                url: serverUrl + '/admin/user/list_low',
                type: 'POST',
                async: false,
                success: function (res) {
                    if (res.code === 200 && res.data) {
                        res.data.forEach(function (item) {
                            userMap[item.ID] = item.Name;
                        });
                    }
                }
            });

            //引入TABLE模块
            layer.load(2);
            table.render({
                elem: '#mytable'
                , even: true
                , url: serverUrl + "/admin/stock_take/list"
                , method: 'post'
                , response: {
                    statusName: 'code'
                    , statusCode: 200
                    , msgName: 'msg'
                    , countName: 'count'
                    , dataName: 'data'
                }
                , cols: [[
                    { field: 'ID', title: 'ID', align: 'center' }
                    , { field: 'Name', title: '盘点单名称' }
                    , {
                        field: 'User_id', title: '盘点人', templet: function (d) {
                            return userMap[d.User_id] || d.User_id;
                        }
                    }
                    , { field: 'Stock_type', title: '商品类型', align: 'center', templet: '#TPL-stock-type' }
                    , { field: 'Check_mode', title: '盘点方式', align: 'center', templet: '#TPL-check-mode' }
                    , { field: 'Check_status', title: '盘点状态', align: 'center', templet: '#TPL-check-status' }
                    , { field: 'Check_time', title: '盘点时间', align: 'center' }
                    , { field: 'Create_time', title: '建档时间', align: 'center' }
                    , { title: '操作', align: 'center', toolbar: '#TPL-bar', width: 200, fixed: 'right' }
                ]]
                , page: true
                , limit: 20
                , done: function () {
                    layer.closeAll('loading');
                }
            });

            //监听工具条
            table.on('tool(mytable)', function (obj) {
                var data = obj.data;
                if (obj.event === 'complete') {
                    layer.open({
                        type: 2,
                        title: '编辑盘点单',
                        shadeClose: true,
                        area: ['600px', '350px'],
                        content: 'stock_take_edit.html?id=' + data.ID
                    });
                } else if (obj.event === 'del') {
                    layer.confirm('确定要删除该盘点单吗？', function (index) {
                        layer.close(index);
                        // 发送删除请求
                        layer.load(2);
                        $.ajax({
                            url: serverUrl + '/admin/stock_take/del',
                            type: 'POST',
                            data: { id: data.ID },
                            success: function (res) {
                                layer.closeAll('loading');
                                if (res.code === 200) {
                                    layer.msg('删除成功', { icon: 1, time: 1000 });
                                    // 刷新表格
                                    table.reload('mytable');
                                } else {
                                    layer.msg(res.msg || '删除失败', { icon: 2 });
                                }
                            },
                            error: function (res) {
                                layer.closeAll('loading');
                                layer.msg(res.responseJSON.msg, { icon: 2, time: 1000 });
                            }
                        });
                    });
                }
            });

            //监听搜索
            form.on('submit(search)', function (data) {
                layer.load(2);
                data.field.stock_type = currentType; // 添加当前选中的类型
                table.reload('mytable', {
                    where: data.field
                    , page: {
                        curr: 1
                    }
                });
                return false;
            });
        });

        // 添加盘点单
        function add() {
            layer.open({
                type: 2,
                title: '添加盘点单',
                shadeClose: true,
                area: ['600px', '350px'],
                content: 'stock_take_add.html'
            });
        }
    </script>
</body>

</html>