package patient

import (
	"encoding/json"
	"fmt"
	"log"
	"mstproject/pkg/common"
	"mstproject/pkg/database"
	"net/http"
	"strings"
	"sync"
	"time"

	"github.com/gorilla/websocket"
)

// 定义WebSocket升级器
var upgrader = websocket.Upgrader{
	ReadBufferSize:  1024,
	WriteBufferSize: 1024,
	// 允许所有跨域请求
	CheckOrigin: func(r *http.Request) bool {
		return true
	},
}

// 连接管理器
type RtcRoomManager struct {
	// 按医助ID分组的连接
	assistantConns map[int][]*websocket.Conn
	// 锁，用于并发安全
	mu sync.RWMutex
}

// 全局连接管理器实例
var rtcRoomManager = &RtcRoomManager{
	assistantConns: make(map[int][]*websocket.Conn),
}

// 将房间列表标记为已通知，避免重复通知
// 用于API查询后的初始标记
var notifiedRoomsGlobal map[int]bool = make(map[int]bool)
var notifiedRoomsMutex sync.Mutex

// 添加连接
func (m *RtcRoomManager) AddConnection(assistantID int, conn *websocket.Conn) {
	m.mu.Lock()
	defer m.mu.Unlock()

	// 添加到对应医助的连接列表
	m.assistantConns[assistantID] = append(m.assistantConns[assistantID], conn)

	log.Printf("新的WebSocket连接已建立：医助ID %d，当前连接数 %d",
		assistantID, len(m.assistantConns[assistantID]))
}

// 移除连接
func (m *RtcRoomManager) RemoveConnection(assistantID int, conn *websocket.Conn) {
	m.mu.Lock()
	defer m.mu.Unlock()

	// 查找并移除连接
	conns := m.assistantConns[assistantID]
	for i, c := range conns {
		if c == conn {
			m.assistantConns[assistantID] = append(conns[:i], conns[i+1:]...)
			break
		}
	}

	// 如果该医助没有连接了，则删除键
	if len(m.assistantConns[assistantID]) == 0 {
		delete(m.assistantConns, assistantID)
	}

	log.Printf("WebSocket连接已关闭：医助ID %d，剩余连接数 %d",
		assistantID, len(m.assistantConns[assistantID]))
}

// 广播消息给指定医助的所有连接
func (m *RtcRoomManager) BroadcastToAssistant(assistantID int, message []byte) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	conns := m.assistantConns[assistantID]
	for _, conn := range conns {
		err := conn.WriteMessage(websocket.TextMessage, message)
		if err != nil {
			log.Printf("广播消息失败：%v", err)
			// 注意：此处不移除连接，因为连接可能是暂时性的问题
			// 实际错误处理会在读取消息循环中进行
		}
	}
}

// RTC房间WebSocket处理函数
func RtcRoomWebsocket(w http.ResponseWriter, r *http.Request) {
	// 获取医助ID参数
	assistantIDStr := r.URL.Query().Get("asst_id")
	if assistantIDStr == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "参数错误",
			"err":  "asst_id不能为空",
		})
		return
	}

	assistantID, err := common.CheckInt(assistantIDStr)
	if err != nil || assistantID < 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "参数错误",
			"err":  "asst_id必须是有效的整数",
		})
		return
	}

	// 升级HTTP连接为WebSocket
	conn, err := upgrader.Upgrade(w, r, nil)
	if err != nil {
		log.Printf("WebSocket升级失败: %v", err)
		return
	}

	// 添加到连接管理器
	rtcRoomManager.AddConnection(assistantID, conn)

	// 发送初始连接成功消息
	conn.WriteMessage(websocket.TextMessage, []byte(fmt.Sprintf(`{"code":200,"msg":"WebSocket连接成功","asst_id":%d}`, assistantID)))

	// 设置读取超时，避免空闲连接占用资源
	conn.SetReadDeadline(time.Time{}) // 清除读取超时，依赖心跳机制保持连接

	// 设置心跳响应处理的最大时间
	conn.SetWriteDeadline(time.Time{}) // 清除写入超时

	// 清理函数
	defer func() {
		conn.Close()
		rtcRoomManager.RemoveConnection(assistantID, conn)
	}()

	// 消息处理循环，保持连接活跃
	for {
		messageType, message, err := conn.ReadMessage()
		if err != nil {
			// 连接已关闭或出错
			if websocket.IsUnexpectedCloseError(err,
				websocket.CloseGoingAway,
				websocket.CloseAbnormalClosure) {
				log.Printf("WebSocket错误: %v", err)
			}
			break
		}

		// 处理接收到的消息
		// 检查是否是心跳消息
		if messageType == websocket.TextMessage {
			var data map[string]interface{}
			if err := json.Unmarshal(message, &data); err == nil {
				// 检查是否是ping消息
				if msgType, ok := data["type"].(string); ok && msgType == "ping" {
					// 回复pong消息
					pongMsg := map[string]interface{}{
						"type":      "pong",
						"timestamp": time.Now().UnixNano() / int64(time.Millisecond),
					}
					pongData, _ := json.Marshal(pongMsg)
					if err := conn.WriteMessage(websocket.TextMessage, pongData); err != nil {
						log.Printf("发送心跳响应失败: %v", err)
					} else {
						// log.Printf("已向医助(%d)发送心跳响应", assistantID)
					}
					continue
				}
			}
		}

		// 处理其他类型的消息...
		log.Printf("收到医助(%d)的消息: %s", assistantID, string(message))
	}
}

// 通知医助有新的RTC房间请求
func NotifyAssistantNewRtcRoom(assistantID int) {
	// 发送通知消息
	message := []byte(fmt.Sprintf(`{"code":200,"msg":"有新的视频问诊请求","asst_id":%d,"action":"refresh"}`, assistantID))
	rtcRoomManager.BroadcastToAssistant(assistantID, message)
	log.Printf("已通知医助(%d)有新的视频问诊请求", assistantID)
}

// 监听RTC房间变化并通知医助
func StartRtcRoomMonitor() {
	log.Println("启动RTC房间监控服务...")

	// 启动定期检查
	go func() {
		// 优化：减少查询频率，从5秒改为15秒
		checkInterval := 15 * time.Second

		for {
			// 只有在有WebSocket连接时才进行数据库查询
			rtcRoomManager.mu.RLock()
			hasConnections := len(rtcRoomManager.assistantConns) > 0
			rtcRoomManager.mu.RUnlock()

			if hasConnections {
				// 检查是否有新的RTC房间
				checkNewRtcRooms(notifiedRoomsGlobal)
			}

			// 等待下一次检查
			time.Sleep(checkInterval)
		}
	}()
}

// 检查新的RTC房间并通知
func checkNewRtcRooms(notifiedRoomIDs map[int]bool) {
	// 查询所有未处理的RTC房间
	sql := `
		SELECT id, asst_id
		FROM rtc_room
		WHERE status = 0
	`

	type RtcRoomNotify struct {
		ID      int `db:"id"`
		Asst_id int `db:"asst_id"`
	}

	var rooms []RtcRoomNotify
	err := database.GetAll(sql, &rooms)
	if err != nil {
		log.Printf("查询待处理RTC房间失败: %v", err)
		return
	}

	// 检查每个房间，如果未通知过则发送通知
	notifiedRoomsMutex.Lock()
	defer notifiedRoomsMutex.Unlock()

	for _, room := range rooms {
		if !notifiedRoomIDs[room.ID] {
			// 标记为已通知
			notifiedRoomIDs[room.ID] = true

			// 发送通知给医助
			NotifyAssistantNewRtcRoom(room.Asst_id)

			log.Printf("检测到新的RTC房间(ID:%d)，已通知医助(ID:%d)", room.ID, room.Asst_id)
		}
	}

	// 清理已经不存在的房间ID（状态已改变或已删除）
	cleanupNotifiedRooms(notifiedRoomIDs)
}

// 清理已通知记录
func cleanupNotifiedRooms(notifiedRoomIDs map[int]bool) {
	if len(notifiedRoomIDs) == 0 {
		return
	}

	// 构建ID列表
	var ids []any
	for id := range notifiedRoomIDs {
		ids = append(ids, id)
	}

	// 没有需要清理的ID
	if len(ids) == 0 {
		return
	}

	// 构建查询条件
	placeholders := make([]string, len(ids))
	for i := range placeholders {
		placeholders[i] = "?"
	}

	// 查询仍然需要通知的房间
	sql := fmt.Sprintf(`
		SELECT id
		FROM rtc_room
		WHERE status = 0 AND id IN (%s)
	`, strings.Join(placeholders, ","))

	type RoomID struct {
		ID int `db:"id"`
	}

	var existingRooms []RoomID
	err := database.GetAll(sql, &existingRooms, ids...)
	if err != nil {
		log.Printf("清理已通知房间记录失败: %v", err)
		return
	}

	// 创建仍然存在的房间ID集合
	existingRoomMap := make(map[int]bool)
	for _, room := range existingRooms {
		existingRoomMap[room.ID] = true
	}

	// 从通知记录中删除不再需要的房间ID
	for id := range notifiedRoomIDs {
		if !existingRoomMap[id] {
			delete(notifiedRoomIDs, id)
		}
	}
}

// 根据记录ID获取医助ID和通知
func NotifyAssistantByRecordID(recordID int) error {
	// 1. 查询记录关联的医助ID
	sql := `SELECT asst_id FROM rtc_room WHERE record_id = ? AND status = 0 LIMIT 1`
	var asstID int
	err := database.GetOne(sql, &asstID, recordID)
	if err != nil {
		return fmt.Errorf("无法查找医助ID: %v", err)
	}

	// 2. 通知对应的医助
	if asstID > 0 {
		NotifyAssistantNewRtcRoom(asstID)
		return nil
	}

	return fmt.Errorf("未找到关联的医助ID")
}

// 检查并通知所有医助有关未处理的RTC房间
func NotifyAllPendingRtcRooms() {
	// 查询所有状态为待处理的RTC房间，按医助ID分组
	sql := `SELECT asst_id, COUNT(*) as count FROM rtc_room WHERE status = 0 GROUP BY asst_id`

	type AsstRoomCount struct {
		Asst_id int `db:"asst_id"`
		Count   int `db:"count"`
	}

	var counts []AsstRoomCount
	err := database.GetAll(sql, &counts)
	if err != nil {
		log.Printf("查询待处理RTC房间失败: %v", err)
		return
	}

	// 通知每个有待处理房间的医助
	for _, item := range counts {
		if item.Count > 0 {
			message := []byte(fmt.Sprintf(`{"code":200,"msg":"您有%d个待处理的视频问诊请求","asst_id":%d,"count":%d,"action":"refresh"}`,
				item.Count, item.Asst_id, item.Count))
			rtcRoomManager.BroadcastToAssistant(item.Asst_id, message)
		}
	}
}

func markRtcRoomsAsNotified(rooms interface{}) {
	// 防止多线程竞争
	notifiedRoomsMutex.Lock()
	defer notifiedRoomsMutex.Unlock()

	// 确保全局通知map已初始化
	if notifiedRoomsGlobal == nil {
		notifiedRoomsGlobal = make(map[int]bool)
	}

	// 根据传入的房间类型处理
	switch v := rooms.(type) {
	case []struct {
		ID             int    `db:"id"`
		Department_id  int    `db:"department_id"`
		Doc_id         int    `db:"doc_id"`
		Asst_id        int    `db:"asst_id"`
		Pat_id         int    `db:"pat_id"`
		Pat_pro_id     int    `db:"pat_pro_id"`
		Status         int    `db:"status"`
		Scheduled_time string `db:"scheduled_time"`
		Name           string `db:"name"`
		Record_id      int    `db:"record_id"`
	}:
		// 处理RtcRoom结构
		for _, room := range v {
			notifiedRoomsGlobal[room.ID] = true
			log.Printf("已将RTC房间(ID:%d)标记为已通知状态", room.ID)
		}
	default:
		log.Printf("无法识别的房间类型，无法标记为已通知")
	}
}
