        #bottom_btn {
            display: flex;
            justify-content: center;
            margin-top: 50px;
        }

        .user_info {
            display: flex;
            align-items: center;
        }

        .user_info div {
            font-weight: bold;
            margin-right: 20px;
            font-size: 17px;
        }

        #video_container {
            width: 600px;
            height: 500px;
            background-color: black;
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            grid-gap: 10px;
            padding: 10px;
        }

        .video-player {
            width: 100%;
            height: 290px;
            background-color: #333;
            position: relative;
        }

        .video-player video {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .video-player .label {
            position: absolute;
            bottom: 10px;
            left: 10px;
            color: white;
            background: rgba(0, 0, 0, 0.5);
            padding: 5px;
            border-radius: 3px;
        }

        #control_buttons {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 20px;
        }

        .recording {
            background-color: #FF4D4F !important;
        }

        /* 视频容器包装器 */
        .video-container-wrapper {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 0 auto;
            max-width: 100%;
            padding: 20px;
        }

        /* 响应式调整 */
        @media screen and (max-width: 1600px) {
            .video-container-wrapper {
                margin-bottom: 30px;
            }

            #video_container {
                width: 80%;
                max-width: 800px;
                min-width: 600px;
                margin: 0 auto;
                z-index: 9999;
            }
        }

        @media screen and (max-width: 768px) {
            #video_container {
                width: 100%;
                height: auto;
                grid-template-columns: 1fr;
            }

            #control_buttons {
                flex-wrap: wrap;
            }

            .video-player {
                height: 240px;
            }
        }

        /* 表单容器 */
        .layui-form-item-container {
            padding: 20px;
        }