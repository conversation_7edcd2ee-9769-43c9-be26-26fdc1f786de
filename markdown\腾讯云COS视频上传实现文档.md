# 腾讯云COS视频上传实现文档

## 功能概述

本文档记录了在音视频通讯结束后，将录制的视频文件上传至腾讯云COS对象存储的实现细节。该功能允许用户在视频录制结束后，选择是否将视频上传至云端存储，以便长期保存和分享。

## 实现架构

整个功能分为三个主要部分：

1. **后端签名生成**：在`internal\app\admin\admin_extra_3.go`中实现，负责生成腾讯云COS的上传签名
2. **前端上传界面**：在`interface\admin\rtc_room_detail.html`中实现，提供用户交互界面
3. **配置信息**：在`pkg\config\config.go`中存储腾讯云COS的相关配置

## 配置详情

在`pkg\config\config.go`中添加了以下配置项：

```go
// 腾讯云密钥
var Qcloud_Secret_Id = "AKIDIdsytdAVildJm270mKn7ING0YxEKF0Zu"
var Qcloud_Secret_Key = "HMEtRt9GF4lIdptmS0PdicTBJLwQkt0w"

// 腾讯云COS配置
var COS_Region = "ap-nanjing"
var COS_Bucket = "mst-1339976641"
var COS_BasePath = "rtc_videos/"
```

**注意**：上述密钥和配置信息为实际部署的配置，请确保这些信息的安全性。

## 后端实现

### 签名生成函数

在`internal\app\admin\admin_extra_3.go`中实现了`Get_cos_upload_sign`函数，用于生成腾讯云COS的上传签名。最新版本使用了腾讯云COS SDK来生成预签名URL：

```go
// 生成腾讯云COS上传签名
func Get_cos_upload_sign(w http.ResponseWriter, r *http.Request) {
    api_id := config.NormalPerm
    _, isLogin := common.Check_Perm(w, r, api_id)
    if !isLogin {
        return
    }

    // 获取文件扩展名
    ext := r.FormValue("ext")
    if ext == "" {
        ext = "mp4" // 默认为mp4格式
    }

    // 生成随机文件名
    now := time.Now()
    timestamp := now.Unix()
    randomStr := common.RandStringRunes(8)
    fileName := fmt.Sprintf("%s%d_%s.%s", config.COS_BasePath, timestamp, randomStr, ext)

    // 使用腾讯云COS SDK生成预签名URL
    // 创建COS客户端
    u, _ := url.Parse(fmt.Sprintf("https://%s.cos.%s.myqcloud.com", config.COS_Bucket, config.COS_Region))
    b := &cos.BaseURL{BucketURL: u}
    client := cos.NewClient(b, &http.Client{
        Transport: &cos.AuthorizationTransport{
            SecretID:  config.Qcloud_Secret_Id,
            SecretKey: config.Qcloud_Secret_Key,
        },
    })

    ctx := context.Background()

    // 设置更长的有效期（2小时），确保上传大文件时不会过期
    expiredInSec := time.Duration(7200) * time.Second // 2小时

    // 设置请求头，确保内容类型正确
    opt := &cos.PresignedURLOptions{
        Header: &http.Header{},
    }
    opt.Header.Add("Content-Type", "video/webm")

    // 生成预签名URL
    presignedURL, err := client.Object.GetPresignedURL(ctx, http.MethodPut, fileName, config.Qcloud_Secret_Id, config.Qcloud_Secret_Key, expiredInSec, opt)
    if err != nil {
        common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
            "code": 500,
            "msg":  "生成预签名URL失败",
            "err":  err.Error(),
        })
        return
    }

    // 记录日志
    common.Add_log(fmt.Sprintf("生成COS上传签名，文件名：%s", fileName), r)

    // 返回预签名URL信息
    host := fmt.Sprintf("%s.cos.%s.myqcloud.com", config.COS_Bucket, config.COS_Region)
    common.JSONResponse(w, http.StatusOK, map[string]interface{}{
        "code": 200,
        "msg":  "获取签名成功",
        "data": map[string]interface{}{
            "presignedURL": presignedURL.String(),
            "cosKey":       fileName,
            "cosHost":      host,
            "url":          fmt.Sprintf("https://%s/%s", host, fileName),
        },
    })
}
```

主要改进：
1. 使用腾讯云COS SDK生成预签名URL，而不是手动计算签名
2. 将签名有效期从30分钟延长到2小时，以支持大文件上传
3. 添加Content-Type请求头，确保上传的内容类型正确
4. 添加日志记录，便于问题排查

### 随机字符串生成函数

在`pkg\common\common.go`中添加了`RandStringRunes`函数，用于生成随机字符串：

```go
// 生成随机字符串
func RandStringRunes(n int) string {
    var letterRunes = []rune("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789")

    // 初始化随机数生成器
    rand.Seed(time.Now().UnixNano())

    b := make([]rune, n)
    for i := range b {
        b[i] = letterRunes[rand.Intn(len(letterRunes))]
    }
    return string(b)
}
```

### 路由配置

在`internal\routes\admin.go`中添加了新的路由：

```go
// 腾讯云COS相关
mux.HandleFunc("/admin/cos/get_upload_sign", handlers.Get_cos_upload_sign)
```

## 前端实现

在`interface\admin\rtc_room_detail.html`中实现了上传腾讯云COS的功能：

### 上传确认对话框

在视频录制结束后，添加了询问是否上传至腾讯云COS的确认框：

```javascript
// 询问是否上传至腾讯云COS
layer.confirm('是否将视频上传至腾讯云COS对象存储？', {
    btn: ['上传','取消'],
    title: '上传至云存储',
    skin: 'layui-layer-molv',
    anim: 1
}, function(index){
    // 关闭确认框
    layer.close(index);
    // 上传至腾讯云COS
    uploadToCOS(blob);
});
```

### 上传函数实现

最新版本的上传函数实现，增加了更多的错误处理和日志记录：

```javascript
// 上传视频到腾讯云COS
async function uploadToCOS(blob) {
    try {
        // 创建上传进度提示框
        const uploadProgressIndex = layer.open({
            type: 1,
            title: '上传至腾讯云COS',
            closeBtn: 0,
            shade: 0.3,
            anim: 2,
            shadeClose: false,
            skin: 'layui-layer-lan',
            area: ['350px', '200px'],
            offset: 'rb', // 右下角
            content: `
                <div style="padding: 20px; background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(10px); border-radius: 8px; box-shadow: 0 4px 20px rgba(0,0,0,0.1);">
                    <div style="margin-bottom: 15px; font-size: 16px; display: flex; align-items: center;">
                        <i class="layui-icon layui-icon-upload" style="font-size: 22px; margin-right: 10px; color: #1E9FFF;"></i>
                        <span>正在上传视频文件...</span>
                    </div>
                    <div class="layui-progress layui-progress-big" lay-showpercent="true" lay-filter="uploadProgress" style="margin-top: 15px;">
                        <div class="layui-progress-bar layui-bg-blue" lay-percent="0%"></div>
                    </div>
                    <div style="margin-top: 15px; color: #666; font-size: 13px;" id="upload-status">准备上传...</div>
                </div>
            `
        });

        // 初始化进度条
        element.render('progress');
        element.progress('uploadProgress', '0%');

        // 获取签名
        $('#upload-status').text('获取上传授权...');
        const signResponse = await fetch(serverUrl + '/admin/cos/get_upload_sign', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            body: new URLSearchParams({
                ext: 'webm'
            })
        });

        if (!signResponse.ok) {
            throw new Error('获取签名失败');
        }

        const signData = await signResponse.json();
        if (signData.code !== 200) {
            throw new Error(signData.msg || '获取签名失败');
        }

        // 准备上传
        $('#upload-status').text('准备上传文件...');
        const { presignedURL, cosKey, cosHost, url } = signData.data;

        console.log('获取到预签名URL:', presignedURL);
        console.log('文件Key:', cosKey);
        console.log('COS主机:', cosHost);

        // 创建上传请求
        const xhr = new XMLHttpRequest();
        xhr.open('PUT', presignedURL, true);

        // 设置请求头
        xhr.setRequestHeader('Content-Type', 'video/webm');

        // 监听上传进度
        xhr.upload.onprogress = (e) => {
            if (e.lengthComputable) {
                const percent = Math.round((e.loaded / e.total) * 100);
                element.progress('uploadProgress', percent + '%');
                $('#upload-status').text(`已上传 ${formatFileSize(e.loaded)}/${formatFileSize(e.total)}...`);
            }
        };

        // 上传完成处理
        xhr.onload = function() {
            if (xhr.status >= 200 && xhr.status < 300) {
                // 上传成功
                $('#upload-status').text('上传成功!');
                element.progress('uploadProgress', '100%');

                // 2秒后关闭进度条
                setTimeout(() => {
                    layer.close(uploadProgressIndex);
                    layer.msg('视频已成功上传至腾讯云COS', {
                        icon: 1,
                        time: 3000
                    });
                }, 2000);
            } else {
                // 上传失败
                $('#upload-status').text(`上传失败: ${xhr.status} ${xhr.statusText}`);
                setTimeout(() => {
                    layer.close(uploadProgressIndex);
                    layer.msg('上传失败，请稍后重试', {
                        icon: 2,
                        time: 3000
                    });
                }, 2000);
            }
        };

        // 错误处理
        xhr.onerror = function() {
            console.error('上传错误:', xhr.status, xhr.statusText);
            $('#upload-status').text('网络错误，上传失败');
            setTimeout(() => {
                layer.close(uploadProgressIndex);
                layer.msg('网络错误，上传失败', {
                    icon: 2,
                    time: 3000
                });
            }, 2000);
        };

        // 添加超时处理
        xhr.timeout = 3600000; // 1小时超时
        xhr.ontimeout = function() {
            $('#upload-status').text('上传超时，请检查网络连接');
            setTimeout(() => {
                layer.close(uploadProgressIndex);
                layer.msg('上传超时，请检查网络连接', {
                    icon: 2,
                    time: 3000
                });
            }, 2000);
        };

        // 开始上传
        try {
            console.log('开始上传文件，大小:', formatFileSize(blob.size));
            xhr.send(blob);
        } catch (error) {
            console.error('发送请求时出错:', error);
            $('#upload-status').text('发送请求时出错: ' + error.message);
        }

    } catch (error) {
        console.error('上传失败:', error);
        layer.msg('上传失败: ' + error.message, {
            icon: 2,
            time: 3000
        });
    }
}
```

主要改进：
1. 使用预签名URL直接上传，而不是手动构建授权头
2. 添加Content-Type请求头，确保上传的内容类型正确
3. 增加了详细的日志记录，便于问题排查
4. 添加了请求超时处理，设置为1小时
5. 增强了错误处理，捕获更多可能的异常情况

## 优化方向

未来可以考虑以下几个方面的优化：

1. **SDK集成**：当网络环境允许时，可以考虑集成腾讯云COS的官方SDK，使用`go get -u github.com/tencentyun/cos-go-sdk-v5`安装，以获取更丰富的功能。

2. **分片上传**：对于大文件，可以实现分片上传功能，提高上传成功率和速度。

3. **上传记录**：在数据库中添加上传记录表，记录每个视频的上传状态和URL，方便后续管理和查询。

4. **权限控制**：根据不同用户角色设置不同的上传权限和存储路径。

5. **视频处理**：集成腾讯云的视频处理服务，对上传的视频进行转码、压缩等处理。

## 注意事项

1. 实际部署时需要确保腾讯云的密钥和配置信息正确，并且有足够的权限。

2. 上传签名的有效期已设置为2小时，足够上传大多数视频文件。如果上传特别大的文件，可能需要进一步延长有效期。

3. 当前实现使用了腾讯云COS SDK生成预签名URL，确保了签名的正确性和安全性。

4. 上传的视频文件格式为webm，如需支持其他格式，可以在前端和后端相应位置进行修改。

5. 上传界面使用了LAYUI组件，符合系统整体UI风格，提供了良好的用户体验。

6. 如果遇到上传失败问题，可以查看浏览器控制台的日志输出，以及服务器端的日志记录，帮助定位问题。

7. 确保腾讯云COS存储桶的权限设置正确，允许通过预签名URL进行上传操作。

8. 对于大文件上传，建议使用有线网络连接，以提高上传速度和稳定性。
