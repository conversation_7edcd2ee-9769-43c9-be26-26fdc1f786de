<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 处方管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
    <script src="/dist/js/xm-select.js"></script>
    <style>
        xm-select {
            border: 0 !important;
        }

        xm-select>.xm-body {
            min-width: 200px !important;
            padding: 10px !important;
        }
    </style>
</head>

<body>
    <div class="del_pm"></div>

    <div class="layui-layout layui-layout-admin">
        <!-- 头部 -->
        <div class="layui-header">
            <div class="layui-logo">幸年堂</div>
            <ul class="layui-nav layui-layout-right">
                <li class="layui-nav-item" style="background-color:transparent!important;padding-right:10px">
                    <a href="javascript:;" style="display:flex;align-items:center;color:#fff"><img
                            src="/dist/images/user.png" class="layui-nav-img"><span class="user_name"></span></a>
                    <dl class="layui-nav-child pop_menu">
                        <dd><i class="iconfont">&#xe62f;</i><a href="change_password.html#7">用户资料</a></dd>
                        <dd><i class="iconfont">&#xe601;</i><a href="javascript:void(0)" class="refresh_cache">刷新缓存</a>
                        </dd>
                        <dd><i class="iconfont">&#xe634;</i><a href="login_out.html">退出登录</a></dd>
                    </dl>
                </li>
            </ul>
        </div>
        <!-- 导航 -->
        <ul class="layui-nav layui-nav-tree layui-nav-side" id="menuContainer" lay-bar="disabled" lay-accordion>
            <div style="margin: 50px 0;color: #bbb;text-align: center;">菜单加载中...</div>
        </ul>
        <!-- 主体内容 -->
        <div class="layui-body">
            <div class="body_child">
                <div class="layui-panel">
                    <div class="layui-card-header">
                        <div class="layui-row" style="padding-top:10px;">
                            <div class="layui-col-md11 layui-col-sm10" style="display: flex;">
                                <div>病历列表</div>
                                <div style="font-size: 14px;margin-left: 20px;color: #999;">
                                    可以理解为“创建完订单后的病历（状态待开方或已开方）”，您也可以只看<a
                                        href="prescription_list_true.html#/admin/prescription_list.html"
                                        style="margin-left: 10px;" class="layui-badge layui-bg-red">处方数据列表</a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-padding-3" style="padding-top: 0 !important;min-height: 800px;">



                        <div id="top_data_search">
                            <div class="layui-form" style="margin: 20px 0 0 0;">
                                <div class="layui-row">
                                    <!-- 科室+医生组合 -->
                                    <div class="layui-col-md4">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label">科室</label>
                                            <div class="layui-input-block" style="display: flex; gap: 10px;">
                                                <select name="doc_dep_id" id="doc_dep_id" lay-filter="doc_dep_id"
                                                    style="width: 48%;"></select>
                                                <select name="doc_id" id="doc_id" style="width: 48%;"></select>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 售前/售后部门+人员组合 -->
                                    <div class="layui-col-md4">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label">销售</label>
                                            <div class="layui-input-block" style="display: flex; gap: 5px;">
                                                <div id="asst_dep_id" style="width:66%"></div>
                                                <select name="asst_id" id="asst_id"></select>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 患者搜索 -->
                                    <div class="layui-col-md3">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label">患者</label>
                                            <div class="layui-input-block">
                                                <input type="text" name="wd" id="dropdown_input"
                                                    lay-filter="searchFilter" placeholder="请输入电话或姓名" autocomplete="off"
                                                    class="layui-input" lay-affix="clear">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 状态筛选 -->
                                <div class="layui-row">
                                    <div class="layui-col-md4">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label">状态</label>
                                            <div class="layui-input-block">
                                                <select name="status" id="status" lay-filter="status">
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- 筛选按钮 -->
                                    <div class="layui-col-md4">
                                        <div class="layui-form-item">
                                            <div class="layui-input-block">
                                                <button class="layui-btn" lay-submit lay-filter="search"
                                                    style="min-width: 100px;">筛选</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>




                        <table id="mytable" lay-filter="mytable" class="layui-table" lay-skin="row"></table>
                    </div>
                </div>
                <br>
                <br>
            </div>
        </div>
    </div>


    <script type="text/html" id="TPL-bar">
        {{#  if(d.Status === 4){ }}
            <div class="check-order-status" data-id="{{d.ID}}">加载中...</div>
        {{#  } else if(d.Status === 5) { }}
            <a class="layui-btn layui-btn-xs perm_check_btn" lay-event="edit" res_id="39">编辑处方</a>
            <a class="layui-btn layui-btn-xs perm_check_btn" lay-event="show" res_id="125">详情</a>
        {{#  } else if(d.Status === 6) { }}
            <a class="layui-btn layui-btn-xs perm_check_btn layui-btn-disabled" res_id="39">编辑处方</a>
            <a class="layui-btn layui-btn-xs perm_check_btn" lay-event="show" res_id="125">详情</a>
        {{#  } else { }}
            /
        {{#  } }}
    </script>

    <script>
        layui.use(['element', 'layer', 'util'], function () {
            var $ = layui.$;
            var element = layui.element;
            var form = layui.form;
            var layer = layui.layer;
            var util = layui.util;
            var laydate = layui.laydate;
            var pat_pro_id = "";
            var table = layui.table;
            var dropdown = layui.dropdown;
            var record_ids = request.get('record_ids');
            if (record_ids) {
                $('.del_pm').html(`
                    <i class="iconfont">&#xe650;</i>
                    <div>注：当前页面含其它页面传来的参数进行筛选</div>
                    <a href="prescription_list.html">解绑该参数</a>
                    <a href="javascript:history.back();">返回原页面</a>
                `);
                $('.del_pm').show();
            }
            // 渲染日期
            laydate.render({
                elem: '#ID-laydate-demo'
            });
            //渲染Record_Status状态
            var html = '<option value="">请选择</option>';
            for (let i = 4; i < Record_Status.length; i++) {
                html += '<option value="' + i + '">' + Record_Status[i] + '</option>';
            }
            $('#status').html(html);
            form.render('select');

            // 页面顶部插入缓存的用户数据
            let local_userinfo = localStorage.getItem('local_userinfo');
            let user_name = "";
            if (local_userinfo) {
                local_userinfo = JSON.parse(local_userinfo);
                user_name = local_userinfo.Name + " ( " + local_userinfo.Roles + " )";
            } else {
                user_name = "未登录";
            }
            $('.user_name').text(user_name);

            render_menu($);
            $('.refresh_cache').click(function () {
                refresh_cache($);
            });
            //引入TABLE模块
            layer.load(2);
            table.render({
                elem: '#mytable'
                , even: true
                , url: serverUrl + "/admin/patient_records/list"
                , method: 'post'
                , where: {
                    status: '4,5,6',
                    record_ids: record_ids,
                }
                , response: {
                    statusName: 'code'
                    , statusCode: 200
                    , msgName: 'msg'
                    , countName: 'count'
                    , dataName: 'data'
                }
                , cols: [[
                    {
                        field: 'ID', title: '病历ID', align: 'center', templet: function (d) {
                            return "B" + d.ID;
                        }
                    },
                    {
                        title: '状态', align: 'center', templet: function (d) {
                            return Record_Status[d.Status];
                        }
                    },
                    {
                        title: '处方', align: 'center', templet: function (d) {
                            return d.Pre_id == 0 ? '<i class="iconfont">&#xeba3;</i>' : "<a href='/admin/prescription_show.html?id=" + d.Pre_id + "#/admin/prescription_list.html' class='btn_arg_pm'>C" + d.Pre_id + "</a>";
                        }
                    },
                    {
                        title: '订单', align: 'center', templet: function (d) {
                            return d.Ord_id == 0 ? '<i class="iconfont">&#xeba3;</i>' : "<a href='/admin/order_show.html?id=" + d.Ord_id + "#/admin/prescription_list.html' class='btn_arg_pm' target='_blank'>D" + d.Ord_id + "</a>";
                        }
                    },
                    //{ field: 'Pat_pro_id', title: '用户ID', align: 'center' },
                    { field: 'Pat_pro_id', title: '姓名', align: 'center' },
                    {
                        title: '性别', align: 'center', templet: function (d) {
                            return '-';
                        }
                    },
                    {
                        title: '年龄', align: 'center', templet: function (d) {
                            return '-';
                        }
                    },
                    {
                        title: '医生', align: 'center', templet: function (d) {
                            return '<div class="user_need_ajax" data-id="' + d.Doc_id + '">-</div>';
                        }
                    }
                    , {
                        title: '医生部门', align: 'center', templet: function (d) {
                            return '<div class="department_need_ajax" data-id="' + d.Department_id + '">-</div>';
                        }
                    }
                    , {
                        title: '医助', align: 'center', templet: function (d) {
                            return '<div class="user_need_ajax" data-id="' + d.Asst_id + '">-</div>';
                        }
                    }
                    , {
                        title: '医疗部门', align: 'center', templet: function (d) {
                            return '<div class="department_need_ajax" data-id="' + d.Asst_dep_id + '">-</div>';
                        }
                    }, {
                        title: '病历日期', align: 'center', templet: function (d) {
                            return d.Create_time.split('T')[0]
                        }
                    }
                    , { title: '操作', align: 'center', width: 200, toolbar: '#TPL-bar' }

                ]]
                , done: function (res, curr, count) {
                    after_table_done_rander();
                    $('.pid_link').on('click', function () {
                        let pid = $(this).data('id');
                        layer.open({
                            type: 2,
                            title: '帐号详情',
                            shade: 0.2,
                            maxmin: true,
                            area: ['500px', '690px'],
                            shadeClose: true,
                            content: 'patient_account_detail.html?id=' + pid
                        });
                    });

                    // 处理订金审核状态检查
                    $('.check-order-status').each(function () {
                        var $this = $(this);
                        var recordId = $this.data('id');

                        $.ajax({
                            url: '/admin/tools/check_order_status_to_prescription',
                            type: 'POST',
                            data: { id: recordId },
                            success: function (res) {
                                if (res.code === 200) {

                                    //订单状态：待发货、可发货、待收货、已签收、已退货、异常、待转售后、待分配、已核销
                                    //Status

                                    /*
                                    款项审核状态：0订金待审 1已付订金 2尾款待付 3尾款待审 4已付尾款
                                    订金：
                                    不付订金不会下单，所以既然下单了，默认即是订金待审核
                                    尾款：
                                    1、订单更新为签收后，自动将尾款更新为：待付尾款
                                    2、文员更新尾款，将待付尾款更新为尾款待审
                                    3、审核完尾款后，变为已付尾款
                                    */

                                    //PayReviewStatus

                                    let st1 = res.data.Status;
                                    let st2 = res.data.PayReviewStatus;
                                    let str = "";
                                    if (st1 == 0) {
                                        //待发货
                                        if (st2 > 0) {
                                            str = '<a class="layui-btn layui-btn-xs layui-bg-orange perm_check_btn" lay-event="add" res_id="38" style="width:106px">开具处方</a>'
                                        } else {
                                            str = '<button class="layui-btn layui-btn-xs layui-bg-orange layui-btn-disabled" disabled="">订金待审</button>'
                                        }
                                    } else {
                                        str = '<button class="layui-btn layui-btn-xs layui-bg-orange layui-btn-disabled" disabled="">状态异常</button>'//订单与处方状态未同步
                                    }

                                    $this.html(str);
                                } else {
                                    $this.html('<button class="layui-btn layui-btn-xs layui-bg-orange layui-btn-disabled" disabled="">异常状态</button>');
                                }
                            },
                            error: function () {
                                // 请求失败，显示禁用按钮
                                $this.html('<button class="layui-btn layui-btn-xs layui-bg-orange layui-btn-disabled" disabled="">未知状态</button>');
                            }
                        });
                    });
                },
                error: function (res) {
                    layer.closeAll('loading');
                    //layer.msg(res.responseJSON.msg, { icon: 2, time: 1000 });
                    $('.layui-table-main').html('<div class="layui-none">' + res.responseJSON.msg + '</div>');
                },
                page: true,
                limit: 15,
            })
            //监听工具条
            table.on('tool(mytable)', function (obj) {
                var data = obj.data;
                switch (obj.event) {
                    case 'edit':
                        window.location.href = 'prescription_edit.html?id=' + data.Pre_id + '#/admin/prescription_list.html';
                        break;
                    case 'show':
                        window.location.href = 'prescription_show.html?id=' + data.Pre_id + '#/admin/prescription_list.html';
                        break;
                    case 'add':
                        window.location.href = 'prescription_add.html?id=' + data.ID + '#/admin/prescription_list.html';
                        break;
                }
            });

            // 为动态生成的按钮绑定事件
            $(document).on('click', '.check-order-status .perm_check_btn[lay-event="add"]', function () {
                var recordId = $(this).closest('.check-order-status').data('id');
                window.location.href = 'prescription_add.html?id=' + recordId + '#/admin/prescription_list.html';
            });
            form.on('input-affix(searchFilter)', function (data) {
                pat_pro_id = "";
            });
            form.on('submit(search)', function (data) {
                // 获取xm-select中选中的部门ID
                if (keshiAsstSelect) {
                    let selectedDepts = keshiAsstSelect.getValue();
                    console.log('筛选按钮 - 选中的部门:', selectedDepts);
                    // 如果有选中部门，添加到表单数据
                    if (selectedDepts.length > 0) {
                        // 提取ID值并合并为逗号分隔的字符串
                        let deptIds = selectedDepts.map(item => item.Id || item.id).join(',');
                        data.field.asst_dep_id = deptIds;
                        console.log('筛选按钮 - 添加到表单的部门IDs:', deptIds);
                    }
                } else {
                    console.warn('警告: keshiAsstSelect 未初始化');
                }

                if (record_ids) {
                    data.field.record_ids = record_ids;
                }
                data.field.pat_pro_id = pat_pro_id;
                data.field.status = data.field.status ? data.field.status : '4,5,6';

                // 删除可能存在的多余字段
                if (data.field.select !== undefined) {
                    console.log('删除多余的select字段:', data.field.select);
                    delete data.field.select;
                }

                // 打印最终的筛选条件
                console.log('最终筛选条件:', data.field);

                table.reload('mytable', {
                    where: data.field,
                    page: {
                        curr: 1 //重新从第 1 页开始
                    }
                });
                return false;
            });
            //新增用户
            $('.create_btn').on('click', function () {
                window.location.href = 'order_add.html';
            });

            //从本地存储渲染部门下拉框
            let data = localStorage.getItem('local_departments') ? JSON.parse(localStorage.getItem('local_departments')) : [];
            if (data.length > 0) {
                // 渲染科室树形结构
                let treeData = format_to_treedata_department(data, global_default_store_id);
                treeData = renderDropdownItems(treeData);
                dropdown.render({
                    elem: '#Department',
                    id: 'DropdownID',
                    data: [],
                    content: '<div class="dropdown-menu">' + treeData + '</div>',
                    ready: function (elemPanel, elem) {
                        elemPanel.on('click', '.dropdown-item-leaf', function () {
                            $('#Department').val($(this).text());
                            dropdown.close('DropdownID');
                        });
                    }
                });
            } else {
                layer.msg('本地部门数据不存在，请先刷新缓存', { icon: 2, time: 2000 });
            }
            // 获取当前用户角色信息
            let currentUserIsSales = false;
            let currentUserIsDoctor = false;
            let currentUserDeptId = 0;
            let currentUserId = 0;
            let currentUserRoleIds = "";

            if (local_userinfo) {
                // 获取当前用户的角色ID、部门ID和用户ID
                currentUserRoleIds = local_userinfo.Role_ids || "";
                currentUserDeptId = local_userinfo.Department_id || 0;
                currentUserId = local_userinfo.Id || 0;

                // 判断是否为售前(3)或售后(9)用户
                if (currentUserRoleIds.split(',').includes(global_asst_role_id) || currentUserRoleIds.split(',').includes(global_after_asst_role_id)) {
                    currentUserIsSales = true;
                }

                // 判断是否为医生(4)用户
                if (currentUserRoleIds.split(',').includes(global_doctor_role_id)) {
                    currentUserIsDoctor = true;
                }
            }

            // 渲染科室下拉表，连带渲染科室下医生下拉
            layer.load(2);
            $.ajax({
                url: '/admin/department/list',
                data: {
                    name: '医疗部门',
                    store_id: global_default_store_id,
                },
                type: 'post',
                success: function (res) {
                    layer.closeAll('loading');
                    if (res.code === 200) {
                        let data = res.data;
                        let html = '<option value="">选择科室</option>';
                        for (let i = 0; i < data.length; i++) {
                            html += '<option value="' + data[i].id + '">' + data[i].name + '</option>';
                        }
                        $('#doc_dep_id').html(html);

                        // 如果当前用户是医生，设置默认选中和只读状态
                        if (currentUserIsDoctor && currentUserDeptId > 0) {
                            // 设置科室下拉框为只读
                            $('#doc_dep_id').val(currentUserDeptId);
                            $('#doc_dep_id').attr('disabled', 'disabled');
                            // 使用内联样式而不是添加类，避免影响布局
                            $('#doc_dep_id').css('background-color', '#f2f2f2');
                        }

                        form.render('select');

                        // 加载医生列表的函数
                        function loadDoctorsByDepartment(keshi_id) {
                            if (keshi_id) {
                                layer.load(2);
                                $.ajax({
                                    url: '/admin/user/list_low',
                                    data: {
                                        role_id: 4,
                                        department_id: keshi_id,
                                    },
                                    type: 'post',
                                    success: function (res) {
                                        layer.closeAll('loading');
                                        if (res.code === 200) {
                                            let data = res.data;
                                            let html = '<option value="">选择医生</option>';
                                            if (data && data.length > 0) {
                                                for (let i = 0; i < data.length; i++) {
                                                    html += '<option value="' + data[i].ID + '">' + data[i].Name + '</option>';
                                                }

                                                // 如果当前用户是医生，并且在当前科室中，设置默认选中和只读状态
                                                if (currentUserIsDoctor && currentUserDeptId == keshi_id && currentUserId > 0) {
                                                    $('#doc_id').html(html);
                                                    $('#doc_id').val(currentUserId);
                                                    $('#doc_id').attr('disabled', 'disabled');
                                                    // 使用内联样式而不是添加类，避免影响布局
                                                    $('#doc_id').css('background-color', '#f2f2f2');
                                                    form.render('select');
                                                } else {
                                                    $('#doc_id').html(html);
                                                    form.render('select');
                                                }
                                            } else {
                                                layer.msg('该科室暂无医生', { icon: 2, time: 1000 });
                                                html = '<option value="">该科室暂无医生</option>';
                                                $('#doc_id').html(html);
                                                form.render('select');
                                            }
                                        } else {
                                            layer.msg(res.msg, { icon: 2, time: 1000 });
                                        }
                                    }, error: function (res) {
                                        layer.closeAll('loading');
                                        layer.msg(res.responseJSON.msg, { icon: 2, time: 1000 });
                                    }
                                });
                            } else {
                                $('#doc_id').html('<option value="">选择医生</option>');
                                form.render('select');
                            }
                        }

                        // 如果当前用户是医生，自动触发科室选择事件加载对应的医生列表
                        if (currentUserIsDoctor && currentUserDeptId > 0) {
                            loadDoctorsByDepartment(currentUserDeptId);
                        }

                        // 监听科室选择事件
                        form.on('select(doc_dep_id)', function (data) {
                            let keshi_id = data.value;
                            loadDoctorsByDepartment(keshi_id);
                        });
                    } else {
                        layer.msg(res.msg, { icon: 2, time: 1000 });
                    }
                }, error: function (res) {
                    layer.closeAll('loading');
                    layer.msg(res.responseJSON.msg, { icon: 2, time: 1000 });
                }
            });

            // 声明全局变量，用于存储xm-select实例
            var keshiAsstSelect;

            // 获取本地缓存的部门数据
            let local_departments = localStorage.getItem('local_departments') ? JSON.parse(localStorage.getItem('local_departments')) : [];

            // 创建一个映射，用于快速查找部门
            let deptMap = {};
            local_departments.forEach(dept => {
                deptMap[dept.Id] = dept;
            });

            // 获取所有售前和售后部门的子部门
            let allSaleDepts = [];
            global_sale_department_ids.forEach(deptId => {
                local_departments.forEach(dept => {
                    if (dept.Pid === deptId) {
                        allSaleDepts.push(dept);
                    }
                });
            });

            // 再找出这些子部门的所有子部门
            let allChildDepts = [...allSaleDepts];
            allSaleDepts.forEach(dept => {
                function findChildren(parentId) {
                    local_departments.forEach(d => {
                        if (d.Pid === parentId && !allChildDepts.some(cd => cd.Id === d.Id)) {
                            allChildDepts.push(d);
                            findChildren(d.Id);
                        }
                    });
                }
                findChildren(dept.Id);
            });

            // 过滤部门数据 - 只保留用户有权限的部门及其父级部门
            let filteredDepartments = [];
            // 默认只展开售前、售后2个大层级节点
            let expandedKeys = global_sale_department_ids;
            // 判断当前用户是否为销售数据管理员
            let isDataMaster = local_userinfo && local_userinfo.Department_id === global_sale_data_master;
            let userDepIds = [];
            if (isDataMaster) {
                userDepIds = local_userinfo.Dep_ids.split(',').map(id => parseInt(id.trim(), 10)).filter(id => !isNaN(id));
            }
            if (isDataMaster && userDepIds.length > 0) {
                // 数据管理员时，由于所负载节点比较少，所以默认展开所有
                expandedKeys = true;
                // 收集用户可访问的部门及其所有父级部门的ID
                let relevantDeptIds = new Set();
                // 递归查找父级部门
                function collectParentDepts(deptId) {
                    const dept = deptMap[deptId];
                    if (dept) {
                        relevantDeptIds.add(dept.Id);
                        if (dept.Pid && dept.Pid !== 0) {
                            collectParentDepts(dept.Pid);
                        }
                    }
                }
                // 处理每个用户可访问的部门
                userDepIds.forEach(deptId => {
                    collectParentDepts(deptId);
                });
                // 只保留用户有权限的部门及其父级部门
                filteredDepartments = allChildDepts.filter(dept =>
                    relevantDeptIds.has(dept.Id)
                );
            } else {
                // 非数据管理员，显示所有部门及其父级部门
                filteredDepartments = allChildDepts;
            }

            // 按照排序值降序排列
            filteredDepartments.sort((a, b) => b.Sort - a.Sort);

            // 将部门数据转换成树形结构
            let departmentTree = convertToTree(filteredDepartments);

            // 初始化xm-select，使用全局变量
            // 判断当前用户是否为销售岗位
            if (currentUserIsSales && currentUserDeptId > 0) {
                console.log('当前用户是销售岗位，部门ID:', currentUserDeptId);
                // 使用id2department函数获取部门名称
                let deptName = id2department(currentUserDeptId, local_departments, 0);
                // 渲染为禁用状态的xm-select
                keshiAsstSelect = xmSelect.render({
                    el: '#asst_dep_id',
                    data: [{
                        Name: deptName,
                        Id: currentUserDeptId
                    }],
                    initValue: [currentUserDeptId],//就显示当前的销售岗位
                    model: {
                        label: {
                            type: 'text'
                        }
                    },
                    prop: {
                        name: 'Name',
                        value: 'Id'
                    },
                    disabled: true,
                });
                // 渲染人员下拉框 - 直接使用当前用户信息
                let html = '<option value="' + currentUserId + '">' + local_userinfo.Name + '</option>';
                $('#asst_id').html(html);
                $('#asst_id').attr('disabled', 'disabled');
                $('#asst_id').css('background-color', '#f2f2f2');
                form.render('select');

            } else {
                console.log('当前用户是非销售岗位，部门ID:', currentUserDeptId);
                // 非销售岗位用户，正常初始化xm-select
                keshiAsstSelect = xmSelect.render({
                    el: '#asst_dep_id',
                    theme: {
                        color: '#1677ff',
                    },
                    height: 'auto',
                    data: departmentTree,
                    model: {
                        label: {
                            type: 'text',
                        }
                    },
                    clickClose: false, // 多选模式下点击不关闭
                    filterable: true,
                    // 添加默认提示文字
                    tips: '请选择部门',
                    prop: {
                        name: 'Name',
                        value: 'Id',
                        children: 'children'
                    },
                    // 设置表单提交时的名称为空，避免自动提交
                    name: '',
                    toolbar: {
                        show: true,
                        list: ['ALL', 'CLEAR']
                    },
                    tree: {
                        show: true,
                        strict: true, // 保持严格模式，确保父子节点联动
                        expandedKeys: expandedKeys,
                        // 启用级联选择，确保父子节点联动
                        cascade: true,
                        // 自动展开父节点
                        autoExpandParent: true
                    },
                    on: function (data) {
                        if (data.change && data.change.length > 0) {
                            // 使用setTimeout确保在DOM更新后获取最新的选中值
                            setTimeout(function () {
                                // 获取当前所有选中的部门ID
                                if (keshiAsstSelect) {
                                    let selectedDepts = keshiAsstSelect.getValue();

                                    // 如果有选中的部门，加载这些部门的用户
                                    if (selectedDepts.length > 0) {
                                        loadDepartmentUsersAll(selectedDepts);
                                    } else {
                                        // 清空用户列表
                                        $('#asst_id').html('<option value="">选择人员</option>');
                                        form.render('select');
                                    }
                                }
                            }, 0);
                        }
                    }
                });
            }

            // 转换扁平数据为树形结构
            function convertToTree(data) {
                let result = [];
                let map = {};

                // 创建所有节点的映射
                data.forEach(function (item) {
                    map[item.Id] = {
                        ...item,
                        children: []
                    };
                });

                // 确保所有必要的父节点都存在
                let addedParentIds = new Set(); // 用于跟踪已添加的父节点ID

                data.forEach(function (item) {
                    if (item.Pid !== 0 && !map[item.Pid] && !addedParentIds.has(item.Pid)) {
                        // 如果父节点不在映射中且尚未添加，尝试从原始数据中找到它
                        const parentDept = local_departments.find(d => d.Id === item.Pid);
                        if (parentDept) {
                            // 添加父节点到映射
                            map[parentDept.Id] = {
                                ...parentDept,
                                children: []
                            };
                            // 将父节点添加到数据数组
                            data.push(parentDept);
                            // 记录已添加的父节点ID
                            addedParentIds.add(parentDept.Id);
                        }
                    }
                });

                // 移除重复的部门
                let uniqueData = [];
                let idSet = new Set();
                data.forEach(function (item) {
                    if (!idSet.has(item.Id)) {
                        uniqueData.push(item);
                        idSet.add(item.Id);
                    }
                });
                data = uniqueData;

                // 构建树结构
                data.forEach(function (item) {
                    let node = map[item.Id];
                    if (item.Pid !== 0 && map[item.Pid]) {
                        // 将当前节点添加到父节点的children中
                        map[item.Pid].children.push(node);
                    } else {
                        // 顶级节点直接添加到结果数组
                        result.push(node);
                    }
                });

                // 按Sort字段排序
                function sortBySort(arr) {
                    arr.sort(function (a, b) {
                        return b.Sort - a.Sort; // 降序排列
                    });
                    arr.forEach(function (item) {
                        if (item.children && item.children.length > 0) {
                            sortBySort(item.children);
                        }
                    });
                    return arr;
                }

                return sortBySort(result);
            }

            // 加载多个部门的用户
            function loadDepartmentUsersAll(selectedDepts) {
                layer.load(2);

                // 获取所有选中部门的ID
                let deptIds = selectedDepts.map(item => item.Id || item.id);

                // 创建查询条件，适应多个部门
                let queryParams = {
                    department_ids: deptIds.join(',')
                };

                // 确定角色类型 - 检查是否包含售后部门
                let hasSaleAfter = false;
                for (let i = 0; i < selectedDepts.length; i++) {
                    let deptId = selectedDepts[i].Id || selectedDepts[i].id;
                    let dept = filteredDepartments.find(d => d.Id == deptId);
                    if (dept && dept.Name.includes('售后')) {
                        hasSaleAfter = true;
                        break;
                    }
                }

                $.ajax({
                    url: '/admin/user/list_low',
                    data: queryParams,
                    type: 'post',
                    success: function (res) {
                        layer.closeAll('loading');
                        if (res.code === 200) {
                            let data = res.data;
                            //let html = '<option value="">选择人员</option>';
                            let html = '';
                            if (data && data.length > 0) {
                                // 按姓名排序
                                data.sort((a, b) => a.Name.localeCompare(b.Name, 'zh'));

                                for (let i = 0; i < data.length; i++) {
                                    html += '<option value="' + data[i].ID + '">' + data[i].Name + '</option>';
                                }

                                $('#asst_id').html(html);
                                form.render('select');
                            } else {
                                html = '<option value="">所选部门下暂无人员</option>';
                                $('#asst_id').html(html);
                                form.render('select');
                            }
                        } else {
                            layer.msg(res.msg, { icon: 2, time: 1000 });
                        }
                    },
                    error: function (res) {
                        layer.closeAll('loading');
                        layer.msg(res.responseJSON.msg, { icon: 2, time: 1000 });
                    }
                });
            }


            //模糊搜索用户的下拉框组件
            var inst = dropdown.render({
                elem: '#dropdown_input',
                trigger: 'keyup click',
                data: [],
                style: 'min-width: 220px; box-shadow: 1px 1px 11px rgb(0 0 0 / 11%);',
                click: function (data) {
                    if (!isEmpty(data)) {
                        this.elem.val(data.name + ' / ' + data.phone);
                        pat_pro_id = data.id;
                    }
                }
            });
            $(inst.config.elem).on('input propertychange', function () {
                var elem = $(this);
                var value = elem.val().trim();
                if (value.length > 0) {
                    $.ajax({
                        url: "/admin/patient_profile/patient_profile_phone2id",
                        type: 'post',
                        dataType: 'json',
                        data: {
                            "wd": value,
                        },
                        success: function (response) {
                            let data = response.data;
                            if (data && data.length > 0) {
                                dropdown.reloadData(inst.config.id, {
                                    data: data,
                                    templet: function (d) {
                                        var exp = new RegExp(value, 'gi');
                                        return (d.name + ' / ' + d.phone).replace(exp, function (str) {
                                            return '<span style="color: red;">' + str + '</span>';
                                        });
                                    }
                                });
                            } else {
                                dropdown.reloadData(inst.config.id, {
                                    data: [],
                                });
                            }
                        },
                        error: function (err) {
                            dropdown.reloadData(inst.config.id, {
                                data: [],
                            });
                        }
                    });
                } else {
                    dropdown.reloadData(inst.config.id, {
                        data: [],
                    });
                }
            });


            // 表格加载后
            var after_table_done_rander = function () {
                render_button($);
                // 表格内AJAX填充，整行填充
                // 获取用户信息
                let patient_profile_array = [];
                $("tbody:eq(0)").find("tr").each(function (d) {
                    patient_profile_array.push($(this).find("td:eq(4)").text());
                });
                patient_profile_array = Array.from(new Set(patient_profile_array));
                for (let i = 0; i < patient_profile_array.length; i++) {
                    $.ajax({
                        url: serverUrl + "/admin/patient_profile/detail",
                        type: "post",
                        data: {
                            id: patient_profile_array[i],
                        },
                        success: function (res) {
                            if (res.code === 200) {
                                let data = res.data;
                                $("tbody:eq(0)").find("tr").each(function () {
                                    let trid = $(this).find("td:eq(4)").text();
                                    if (trid == data.ID) {
                                        $(this).find("td:eq(4)").html('<div class="layui-table-cell laytable-cell-1-0-3" align="center">' + data.Name + '</div>');
                                        $(this).find("td:eq(5)").html('<div class="layui-table-cell laytable-cell-1-0-3" align="center">' + (data.Sex == 1 ? '男' : '女') + '</div>');
                                        $(this).find("td:eq(6)").html('<div class="layui-table-cell laytable-cell-1-0-3" align="center">' + date2age(data.Born_date) + '岁</div>');
                                    }
                                })

                            } else {
                                // console.log(res.msg);
                                layer.msg(res.msg, { icon: 2, time: 1000 });
                            }
                        }
                    });
                }











                // 从本地存储获取部门/科室数据
                let department_data = localStorage.getItem('local_departments') ? JSON.parse(localStorage.getItem('local_departments')) : [];
                if (department_data.length > 0) {
                    $('.department_need_ajax').each(function () {
                        let id = $(this).data('id');
                        let department = id2department(id, department_data, 0);
                        $(this).text(department);
                    });
                } else {
                    layer.msg('本地部门数据不存在，请先刷新缓存', { icon: 2, time: 2000 });
                }

                //高效-医助、医生等职能部分用户遍历并ID合并
                let user_arr = [];
                $('.user_need_ajax').each(function () {
                    let id = $(this).data('id');
                    user_arr.push(id);
                });
                user_arr = [...new Set(user_arr)];//去重
                layer.load(2);
                $.ajax({
                    url: '/admin/user/list_low',
                    data: {
                        user_id_list: user_arr.join(','),
                    },
                    type: 'post',
                    success: function (res) {
                        layer.closeAll('loading');
                        if (res.code === 200) {
                            let data = res.data;
                            for (let i = 0; i < data.length; i++) {
                                let id = data[i].ID;
                                let name = data[i].Name;
                                $('.user_need_ajax[data-id="' + id + '"]').text(name);
                            }
                        } else {
                            layer.msg(res.msg, { icon: 2, time: 1000 });
                        }
                    }, error: function (res) {
                        layer.closeAll('loading');
                        layer.msg(res.responseJSON.msg, { icon: 2, time: 1000 });
                    }
                });











                /*
                                // 就诊类型 - 初次还是N次
                                let Records_array = [];
                                $("tbody:eq(0)").find("tr").each(function (d) {
                                    Records_array.push($(this).find("td:eq(3)").text());
                                });
                                Records_array = Array.from(new Set(Records_array));
                                for (let i = 0; i < Records_array.length; i++) {
                                    $.ajax({
                                        url: serverUrl + "/admin/patient_records/count",
                                        type: "post",
                                        data: {
                                            id: Records_array[i],
                                        },
                                        success: function (res) {
                                            layer.closeAll('loading');
                                            if (res.code === 200) {
                                                let data = res.data;
                                                $("tbody:eq(0)").find("tr").each(function () {
                                                    let trid = $(this).find("td:eq(3)").text();
                                                    if (trid == Records_array[i]) {
                                                        $(this).find("td:eq(7)").html('<div class="layui-table-cell laytable-cell-1-0-3" align="center">' + (data == 1 ? '初诊' : data + '次诊') + '</div>');
                                                    }
                                                })
                                            }
                                        },
                                        error: function (err) {
                                            layer.closeAll('loading');
                                            $("tbody:eq(0)").find("tr").each(function () {
                                                let trid = $(this).find("td:eq(3)").text();
                                                if (trid == Records_array[i]) {
                                                    $(this).find("td:eq(7)").html('<div class="layui-table-cell laytable-cell-1-0-3" align="center">' + (data == 1 ? '初诊' : data + '次诊') + '</div>');
                                                }
                                            })
                                        }
                                    });
                                }
                                    */
















            }
        });
    </script>
</body>

</html>