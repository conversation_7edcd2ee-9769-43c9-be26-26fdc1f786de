// ----------- 新增销售数据管理员后的药物筛选开始
    var attSql string
    var is_asst bool = false
    if session.Values["role_ids"] == "3" || session.Values["role_ids"] == "9" {
        //医助（售前、售前）
        is_asst = true
        attSql += " AND asst_id = " + strconv.Itoa(session.Values["id"].(int))
    }
    // 如果不是医助角色
    if !is_asst {
        asst_id := r.FormValue("asst_id")
        if asst_id != "" {
            attSql += " AND asst_id = " + asst_id
        } else {
            asst_dep_id := r.FormValue("asst_dep_id")
            if session.Values["role_ids"] == "2" {
                // 销售数据管理员
                sql := "select dep_ids from salemaster_department where user_id = ?"
                var dep_ids string
                err := database.GetOne(sql, &dep_ids, session.Values["id"].(int))
                if err != nil {
                    common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
                        "code": 500,
                        "msg":  "读取销售数据管理员部门ID失败",
                        "err":  err.Error(),
                    })
                    return
                }
                if asst_dep_id != "" {
                    // 判断前端传递的部门ID是否在dep_ids中
                    asst_dep_ids := strings.Split(asst_dep_id, ",")
                    dep_ids_arr := strings.Split(dep_ids, ",")
                    // 将dep_ids转换为map，便于快速查找
                    dep_ids_map := make(map[string]bool)
                    for _, id := range dep_ids_arr {
                        dep_ids_map[id] = true
                    }
                    // 检查asst_dep_id中的每个元素是否都存在于dep_ids中
                    for _, id := range asst_dep_ids {
                        if !dep_ids_map[id] {
                            common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
                                "code": 500,
                                "msg":  "您请求的部门中，有您无权查看的部门，这可能是您的权限已被调整 ( " + asst_dep_id + " vs " + dep_ids + " )",
                            })
                            return
                        }
                    }
                    attSql += " AND asst_id IN (SELECT id FROM rbac_user WHERE department_id IN (" + asst_dep_id + "))"
                } else {
                    attSql += " AND asst_id IN (SELECT id FROM rbac_user WHERE department_id IN (" + dep_ids + "))"
                }
            } else {
                // 其它角色，如医生、超管、文员等；不是医助也不是销售数据管理员
                if asst_dep_id != "" {
                    attSql += " AND asst_id IN (SELECT id FROM rbac_user WHERE department_id IN (" + asst_dep_id + "))"
                }
            }
        }
    }
    // ----------- 新增销售数据管理员后的药物筛选结束