<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>选择成品药</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
    <style>
        .selected-row {
            background-color: #f2f9ff !important;
        }

        .search-box {
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }

        .search-box .layui-input {
            width: 200px;
            margin-right: 10px;
        }
    </style>
</head>

<body style="padding: 15px;">
    <!-- 搜索框 -->
    <div class="search-box">
        <input type="text" id="searchInput" placeholder="输入处方ID" class="layui-input">
        <button type="button" class="layui-btn" id="searchBtn">搜索</button>
        <button type="button" class="layui-btn layui-btn-primary" id="resetBtn">重置</button>
    </div>

    <!-- 成品药表格 -->
    <table id="finishedDrugTable" lay-filter="finishedDrugTable"></table>

    <!-- 底部固定按钮 -->
    <div
        style="position: fixed; bottom: 0; left: 0; right: 0; background-color: #fff; padding: 10px; text-align: center; box-shadow: 0 -2px 10px rgba(0,0,0,0.1);">
        <button type="button" class="layui-btn" id="submitBtn">确认选择</button>
        <button type="button" class="layui-btn layui-btn-primary" id="cancelBtn">取消</button>
    </div>

    <script>
        layui.use(['table', 'layer', 'form'], function () {
            var table = layui.table;
            var layer = layui.layer;
            var form = layui.form;
            var $ = layui.$;

            var selectedDrugId = null;
            var selectPreId = null;

            // 渲染表格
            var tableIns = table.render({
                elem: '#finishedDrugTable',
                url: '/admin/warehouse_finisheddrug/list_choose_drug', // 替换为实际的成品药接口
                method: 'post',
                height: 'full-120',
                page: true,
                cols: [[
                    { type: 'radio', fixed: 'left' },
                    { field: 'ID', title: 'ID', width: 80 },
                    {
                        field: 'Kind', title: '类型', align: 'center', templet: function (d) {
                            let kindText = '';
                            switch (d.Kind) {
                                case 0: kindText = '正常入库'; break;
                                case 1: kindText = '退货入库'; break;
                                case 2: kindText = '异常入库'; break;
                                default: kindText = '未知';
                            }
                            return kindText;
                        }
                    },
                    {
                        field: 'Pre_id', title: '原处方ID', templet: function (d) {
                            return "<a href='/admin/prescription_show.html?id=" + d.Pre_id + "#/admin/prescription_list_verify.html' target=_blank>C" + d.Pre_id + "</a>";
                        }
                    },
                    {
                        field: 'Quantity', title: '数量', align: 'center', templet: function (d) {
                            return d.Quantity + ' ' + Drug_unit[d.Unit];
                        }
                    },
                    { field: 'Notes', title: '备注', minWidth: 170 },
                    {
                        field: 'Create_time', title: '时间', minWidth: 170, templet: function (d) {
                            return Utc2time(d.Create_time);
                        }
                    },
                ]],
                response: {
                    statusName: 'code',
                    statusCode: 200,
                    msgName: 'msg',
                    countName: 'count',
                    dataName: 'data'
                }
            });

            // 监听行单击事件
            table.on('row(finishedDrugTable)', function (obj) {
                // 选中当前行
                obj.tr.addClass('selected-row').siblings().removeClass('selected-row');

                // 设置单选框选中状态
                obj.tr.find('input[type="radio"]').prop('checked', true);

                // 记录选中的成品药ID
                selectedDrugId = obj.data.ID;
                selectPreId = obj.data.Pre_id;

                // 触发表单渲染，确保单选框状态更新
                form.render('radio');
            });

            // 监听单选框点击事件
            table.on('radio(finishedDrugTable)', function (obj) {
                // 记录选中的成品药ID
                selectedDrugId = obj.data.ID;
                selectPreId = obj.data.Pre_id;
            });

            // 搜索按钮点击事件
            $('#searchBtn').on('click', function () {
                var searchValue = $('#searchInput').val();
                tableIns.reload({
                    where: {
                        key: searchValue
                    },
                    page: {
                        curr: 1
                    }
                });
            });

            // 回车键触发搜索
            $('#searchInput').on('keypress', function (e) {
                if (e.which === 13) {
                    $('#searchBtn').click();
                }
            });

            // 重置按钮点击事件
            $('#resetBtn').on('click', function () {
                $('#searchInput').val('');
                tableIns.reload({
                    where: {
                        key: ''
                    },
                    page: {
                        curr: 1
                    }
                });
            });

            // 取消按钮点击事件
            $('#cancelBtn').on('click', function () {
                // 关闭弹窗
                var index = parent.layer.getFrameIndex(window.name);
                parent.layer.close(index);
            });

            // 提交按钮点击事件
            $('#submitBtn').on('click', function () {
                if (!selectedDrugId) {
                    layer.msg('请选择一个成品药', { icon: 2 });
                    return;
                }

                // 将选中的成品药ID传递给父页面
                if (window.parent) {
                    window.parent.finishedDrugId = selectedDrugId;
                    window.parent.old_pre_id = selectPreId;
                    // 显示成功提示
                    window.parent.layer.msg('成品药选择成功！', { icon: 1 });

                    // 关闭弹窗
                    var frameIndex = parent.layer.getFrameIndex(window.name);
                    parent.layer.close(frameIndex);
                } else {
                    layer.msg('无法与父页面通信，请刷新后重试', { icon: 2 });
                }
            });
        });
    </script>
</body>

</html>