<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 角色列表</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
</head>
<style>
    body {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
        margin: 0;
    }

    .login_out_tips {
        width: 500px;
        height: 350px;
        /* background-color: rgba(255,255,255,.5); */
        border-radius: 50px;
        /* box-shadow: 0 0 10px #eee; */
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .login_out_tips i {
        font-size: 38px;
        margin-right: 10px;
    }

    .login_out_tips {
        line-height: 50px;
        text-align: center;
    }

    .login_out_tips .tips {
        font-size: 27px;
    }
</style>

<body style="background:url('/dist/images/Login-bg.jpg');background-size: cover;"></body>

<div class="login_out_tips">
    <i class="iconfont">&#xe634;</i>
    <div class="tips">退出成功</div>
</div>

<script src="/dist/layui/layui.js"></script>
<script>
    function getCookie(name) {
        var match = document.cookie.match(new RegExp('(^| )' + name + '=([^;]+)'));
        if (match) return match[2];
    }
    layui.use(['element', 'layer', 'util'], function () {
        var form = layui.form;
        var element = layui.element;
        var layer = layui.layer;
        var util = layui.util;
        var $ = layui.$;
        $.ajax({
            url: '/admin/user/logout',
            type: 'post',
            dataType: 'json',
            success: function (data) {
                $('.tips').text(data.msg);
                if (data.code == 200) {
                    localStorage.clear();
                    sessionStorage.clear();
                    setTimeout(() => {
                        window.location.href = "/";
                    }, 1000);
                }
            }
        });
        $('.login_out_tips').click(function () {
            window.location.href = "/";
        });
    });
</script>
</body>

</html>