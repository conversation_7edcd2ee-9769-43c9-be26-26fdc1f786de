<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 售后文员订单列表</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
    <script src="/dist/js/xm-select.js"></script>
    <style>
        xm-select {
            border: 0 !important;
        }

        xm-select>.xm-body {
            min-width: 200px !important;
            padding: 10px !important;
        }

        xm-select>.xm-body .xm-toolbar {
            padding: 0;
        }

        /* 树形结构样式优化 */
        .xm-tree-show {
            max-height: 400px !important;
            overflow-y: auto !important;
        }

        /* 禁用项样式 */
        .xm-option.disabled {
            background-color: #f5f5f5 !important;
            color: #aaa !important;
            cursor: not-allowed !important;
        }

        /* 选中项样式 */
        .xm-option.selected {
            background-color: #e6f4ff !important;
        }

        /* 树形图标样式 */
        .xm-tree-icon.xm-tree-icon-parent {
            border-width: 5px;
        }

        .xm-tree-icon.xm-tree-icon-parent.expand {
            border-color: transparent transparent transparent #1677ff;
        }
    </style>
    <style>
        .pay_review_status {
            width: 60px;
        }

        .layui-btn+.layui-btn {
            margin: 0;
        }

        .express_update_time {
            font-size: 12px;
        }
    </style>
</head>

<body>
    <div class="layui-layout layui-layout-admin">
        <!-- 头部 -->
        <div class="layui-header">
            <div class="layui-logo">幸年堂</div>
            <ul class="layui-nav layui-layout-right">
                <li class="layui-nav-item" style="background-color:transparent!important;padding-right:10px">
                    <a href="javascript:;" style="display:flex;align-items:center;color:#fff"><img
                            src="/dist/images/user.png" class="layui-nav-img"><span class="user_name"></span></a>
                    <dl class="layui-nav-child pop_menu">
                        <dd><i class="iconfont">&#xe62f;</i><a href="change_password.html#7">用户资料</a></dd>
                        <dd><i class="iconfont">&#xe601;</i><a href="javascript:void(0)" class="refresh_cache">刷新缓存</a>
                        </dd>
                        <dd><i class="iconfont">&#xe634;</i><a href="login_out.html">退出登录</a></dd>
                    </dl>
                </li>
            </ul>
        </div>
        <!-- 导航 -->
        <ul class="layui-nav layui-nav-tree layui-nav-side" id="menuContainer" lay-bar="disabled" lay-accordion>
            <div style="margin: 50px 0;color: #bbb;text-align: center;">菜单加载中...</div>
        </ul>
        <!-- 主体内容 -->
        <div class="layui-body">
            <div class="body_child">
                <div class="layui-panel">
                    <div class="layui-card-header">
                        <div class="layui-row" style="padding-top:10px;">
                            <div class="layui-col-md11 layui-col-sm10">售后文员订单列表</div>
                            <!-- <div class="layui-col-md1 layui-col-sm2">
                                <button type="submit" class="layui-btn layui-btn-primary layui-border-red create_btn"
                                    lay-submit=""><i class="layui-icon">&#xe654;</i> 新增</button>
                            </div> -->
                        </div>
                    </div>
                    <div class="layui-padding-3" style="padding-top: 0 !important;min-height: 800px;">



                        <!-- 通用顶部筛选 -->
                        <div id="top_data_search">
                            <div class="layui-form" style="margin: 20px 0 0 0;">
                                <div class="layui-row">
                                    <!-- 科室+医生组合 -->
                                    <div class="layui-col-md4">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label">科室</label>
                                            <div class="layui-input-block" style="display: flex; gap: 10px;">
                                                <select name="doc_dep_id" id="doc_dep_id" lay-filter="doc_dep_id"
                                                    style="width: 48%;"></select>
                                                <select name="doc_id" id="doc_id" style="width: 48%;"></select>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 售前/售后部门+人员组合 -->
                                    <div class="layui-col-md4">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label">销售</label>
                                            <div class="layui-input-block" style="display: flex;gap: 5px;">
                                                <div id="asst_dep_id" style="width:66%"></div>
                                                <select name="asst_id" id="asst_id"></select>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 患者搜索 -->
                                    <div class="layui-col-md3">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label">患者</label>
                                            <div class="layui-input-block">
                                                <input type="text" name="wd" id="dropdown_input"
                                                    lay-filter="searchFilter" placeholder="请输入电话或姓名" autocomplete="off"
                                                    class="layui-input" lay-affix="clear">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="layui-row">
                                    <div class="layui-col-md4">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label">状态</label>
                                            <div class="layui-input-block">
                                                <select name="Order_Status" id="Order_Status"
                                                    lay-filter="Order_Status"></select>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- 筛选按钮 -->
                                    <div class="layui-col-md1" style="text-align: center;">
                                        <div class="layui-form-item">
                                            <div class="layui-input-block">
                                                <button class="layui-btn" lay-submit lay-filter="search"
                                                    style="min-width: 100px;">筛选</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>








                        <table id="mytable" lay-filter="mytable" class="layui-table" lay-skin="row"></table>
                    </div>
                </div>
                <br>
                <br>
            </div>
        </div>
    </div>


    <script type="text/html" id="TPL-bar">
        <div>
            <button class="layui-btn layui-btn-xs perm_check_btn" lay-event="show" res_id="56">详情</button>
            <button class="layui-btn layui-btn-xs perm_check_btn pay_review_status {{# if(d.Pay_review_status > 0){ }}layui-btn-disabled{{# } }}" lay-event="verify_cr_pic" res_id="158" {{# if(d.Pay_review_status > 0){ }}disabled{{# } }}>
                {{# if(d.Pay_review_status > 0){ }}✓订金{{# } else { }}审核订金{{# } }}
            </button>
            <button class="layui-btn layui-btn-xs perm_check_btn {{# if(d.Pay_review_status != 2){ }}layui-btn-disabled{{# } }}" lay-event="update_final_pay" res_id="43" {{# if(d.Pay_review_status != 2){ }}disabled{{# } }}>更新尾款</button>
        </div>
        <div>
            <button class="layui-btn layui-btn-xs perm_check_btn {{# if(d.Pay_review_status != 3){ }}layui-btn-disabled{{# } }}" lay-event="verify_pay_pic" res_id="159" {{# if(d.Pay_review_status != 3){ }}disabled{{# } }}>审核尾款</button>
            <button class="layui-btn layui-btn-xs layui-btn-primary perm_check_btn layui-border-blue {{# if(d.Pay_review_status != 4){ }}layui-btn-disabled{{# } }}" lay-event="final_pay_detail"  {{# if(d.Pay_review_status != 4){ }}disabled{{# } }} res_id="159">款项明细</button>
            <button class="layui-btn layui-btn-xs layui-btn-primary perm_check_btn layui-border-orange {{# if(d.Status != 6){ }}layui-btn-disabled{{# } }}" lay-event="to_after_sales" res_id="179" {{# if(d.Status != 6){ }}disabled{{# } }}>转到售后</button>
        </div>
    </script>

    <script>
        // 显示物流信息模态框
        function showExpressInfo(express_type, tracking_num, ord_id, pat_pro_id) {
            layui.use(['layer', 'table'], function () {
                var layer = layui.layer;
                var table = layui.table;
                layer.open({
                    type: 2,
                    title: '物流信息查询',
                    area: ['600px', '900px'],
                    shadeClose: true,
                    content: 'express_info.html?express_type=' + express_type + '&tracking_num=' + tracking_num + '&ord_id=' + ord_id + '&pat_pro_id=' + pat_pro_id,
                    end: function () {
                        // 模态框关闭时重载表格
                        table.reload('mytable');
                    }
                });
            });
        }

        // 生成更新时间单元格的HTML - 提取公共函数减少冗余
        function generateUpdateTimeCell(data) {
            let updateButton = '<a href="javascript:;" class="layui-table-link" onclick="showExpressInfo(\'' +
                data.Express + '\', \'' +
                data.Tracking_num + '\', \'' +
                data.ID + '\', \'' +
                data.Pat_pro_id + '\')"><i class="layui-icon" style="font-size:14px;">&#xe669;</i> 立即更新</a>';
            return '<div>快递状态码：' + (data.express_status || '-') + '</div>' +
                '<div class="express_update_time">' + (data.express_update_time ? formatTimeAgo(Utc2time(data.express_update_time)) : '-') + '</div>' +
                '<div>' + updateButton + '</div>';
        }

        layui.use(['element', 'layer', 'util', 'form', 'laydate', 'table', 'dropdown', 'jquery'], function () {
            var element = layui.element;
            var form = layui.form;
            var layer = layui.layer;
            var util = layui.util;
            var laydate = layui.laydate;
            var pat_pro_id = "";
            var table = layui.table;
            var dropdown = layui.dropdown;
            var $ = layui.$;
            var Order_Status_default = '';
            // 渲染日期
            laydate.render({
                elem: '#ID-laydate-demo'
            });
            // 页面顶部插入缓存的用户数据
            let local_userinfo = localStorage.getItem('local_userinfo');
            let user_name = "";
            let currentUserIsSales = false;
            let currentUserIsDoctor = false;
            let currentUserDeptId = 0;
            let currentUserId = 0;
            let currentUserRoleIds = "";

            if (local_userinfo) {
                local_userinfo = JSON.parse(local_userinfo);
                user_name = local_userinfo.Name + " ( " + local_userinfo.Roles + " )";

                // 获取当前用户的角色ID、部门ID和用户ID
                currentUserRoleIds = local_userinfo.Role_ids || "";
                currentUserDeptId = local_userinfo.Department_id || 0;
                currentUserId = local_userinfo.Id || 0;

                // 判断是否为售前(3)或售后(9)用户
                if (currentUserRoleIds.split(',').includes(global_asst_role_id) || currentUserRoleIds.split(',').includes(global_after_asst_role_id)) {
                    currentUserIsSales = true;
                }

                // 判断是否为医生(4)用户
                if (currentUserRoleIds.split(',').includes(global_doctor_role_id)) {
                    currentUserIsDoctor = true;
                }
            } else {
                user_name = "未登录";
            }
            $('.user_name').text(user_name);

            render_menu($);
            $('.refresh_cache').click(function () {
                refresh_cache($);
            });
            //引入TABLE模块
            layer.load(2);
            table.render({
                elem: '#mytable'
                , even: true
                , url: serverUrl + "/admin/order/list_express"
                , method: 'post'
                , where: {
                    Order_Status: Order_Status_default
                }
                , response: {
                    statusName: 'code'
                    , statusCode: 200
                    , msgName: 'msg'
                    , countName: 'count'
                    , dataName: 'data'
                }
                , cols: [[
                    {
                        field: 'ID', title: '订单ID', align: 'center', width: 100, templet: function (d) {
                            return 'D' + d.ID;
                        }
                    }
                    , {
                        field: 'Status', title: '订单状态', align: 'center', width: 100, templet: function (d) {
                            const item = order_status_list[d.Status];
                            return `
                                <div style="display: flex; flex-direction: column; align-items: center;">
                                    <i class="iconfont" style="color: ${item.color}; font-size: 20px;">${item.icon}</i>
                                    <span>${item.name}</span>
                                </div>
                            `;
                        }
                    }
                    , {
                        field: 'Pat_pro_id', title: '患者', align: 'center', width: 140, templet: function (d) {
                            let local_departments = localStorage.getItem('local_departments') ? JSON.parse(localStorage.getItem('local_departments')) : [];
                            // 根据订单状态决定显示方式
                            let deptDisplay = '';
                            let currentDept = local_departments.find(dept => dept.Id === d.Asst_dep_id);
                            if (currentDept && currentDept.Pid) {
                                // 找到父级部门
                                let parentDept = local_departments.find(dept => dept.Id === currentDept.Pid);
                                if (parentDept) {
                                    // 显示父级部门名称 - 当前部门名称
                                    deptDisplay = parentDept.Name + ' - ' + currentDept.Name;
                                } else {
                                    deptDisplay = currentDept.Name;
                                }
                            } else {
                                deptDisplay = id2department(d.Asst_dep_id, local_departments, 0);
                            }

                            return `<div class="pat_pro_id" data-id="` + d.Pat_pro_id + `">-</div>
                                    <div data-id="` + d.Asst_dep_id + `" class="smalltext"> ` + deptDisplay + `</div>
                            `;
                        }
                    }
                    , {
                        field: 'Doc_id', title: '医生', align: 'center', width: 90, templet: function (d) {
                            return '<div class="user_need_ajax" data-id="' + d.Doc_id + '">-</div>';
                        }
                    }
                    , {
                        field: 'Asst_id', title: '医助', align: 'center', width: 90, templet: function (d) {
                            return `
                            <div class="user_need_ajax" data-id="` + d.Asst_id + `">-</div>
                            `;
                        }
                    }
                    , {
                        title: '已付/总价', align: 'center', width: 100, templet: function (d) {
                            let pre_pay = d.Pre_pay + d.Final_pay;
                            let total_money = d.Total_money;
                            let percent = pre_pay / total_money * 100;

                            // 当已付金额等于总金额时，显示完成状态图标
                            if (Math.abs(pre_pay - total_money) < 0.01) { // 使用小数点比较，避免浮点数精度问题
                                return `
                                    <div style="display: flex; flex-direction: column; align-items: center;">
                                        <div style="display: flex; flex-direction: column; align-items: center;">
                                            <i class="iconfont" style="color: #4CAF50; font-size: 20px;">&#xe6c6;</i>
                                        </div>
                                        <span>${total_money}元</span>
                                    </div>
                                `;
                            } else {
                                if (d.Pay_review_status == 4) {
                                    return `
                                    <div style="display: flex; flex-direction: column; align-items: center;">
                                        <div style="display: flex; flex-direction: column; align-items: center;">
                                            <i class="iconfont" style="color: black; font-size: 20px;">&#xe6c6;</i>
                                        </div>
                                        <span>${pre_pay}元</span>
                                    </div>
                                `;
                                } else {
                                    return `
                                    <div>`+ pre_pay + ` / ` + total_money + `</div>
                                    <div class="layui-progress"><div class="layui-progress-bar" lay-percent="` + percent + `%"></div></div>
                                    `;
                                }
                            }
                        }
                    }
                    , {
                        field: 'Pay_review_status', title: '金额状态', align: 'center', width: 100, templet: function (d) {
                            if (d.Pay_review_status > 2) {
                                return "<view style='display:block;'>" + (d.Final_is_full == 1 ? "全额" : "非全额") + "</view><view>" + Pay_review_status[d.Pay_review_status] + "</view>";
                            } else {
                                return Pay_review_status[d.Pay_review_status];
                            }
                        }
                    }
                    , {
                        field: 'Create_time', title: '最新物流信息', minWidth: 300, align: 'left', templet: function (d) {
                            return "<div class='express_info'> - </div>";
                        }
                    }
                    , {
                        field: 'Create_time', title: '更新时间', width: 170, align: 'left', templet: function (d) {
                            return generateUpdateTimeCell(d);
                        }
                    }
                    , { title: '操作', align: 'center', width: 280, toolbar: '#TPL-bar' }
                ]]
                , done: function (res, curr, count) {
                    // 解决进度条组件在TABLE中不显示的BUG
                    element.render("progress");
                    //检查菜单权限
                    render_button($);
                    //高效-医助、医生等职能部分用户遍历并ID合并
                    let user_arr = [];
                    $('.user_need_ajax').each(function () {
                        let id = $(this).data('id');
                        user_arr.push(id);
                    });
                    user_arr = [...new Set(user_arr)];//去重
                    layer.load(2);
                    $.ajax({
                        url: '/admin/user/list_low',
                        data: {
                            user_id_list: user_arr.join(','),
                        },
                        type: 'post',
                        success: function (res) {
                            layer.closeAll('loading');
                            if (res.code === 200) {
                                let data = res.data;
                                for (let i = 0; i < data.length; i++) {
                                    let id = data[i].ID;
                                    let name = data[i].Name;
                                    $('.user_need_ajax[data-id="' + id + '"]').text(name);
                                }
                            } else {
                                layer.msg(res.msg, { icon: 2, time: 1000 });
                            }
                        }, error: function (res) {
                            layer.closeAll('loading');
                            layer.msg(res.responseJSON.msg, { icon: 2, time: 1000 });
                        }
                    });
                    //高效-患者ID2姓名
                    let pat_pro_id_arr = [];
                    $('.pat_pro_id').each(function () {
                        let id = $(this).data('id');
                        pat_pro_id_arr.push(id);
                    });
                    pat_pro_id_arr = [...new Set(pat_pro_id_arr)];//去重
                    layer.load(2);
                    $.ajax({
                        url: '/admin/patient_profile/patient_profile_ids2name',
                        data: {
                            ids: pat_pro_id_arr.join(','),
                        },
                        type: 'post',
                        success: function (res) {
                            layer.closeAll('loading');
                            if (res.code === 200) {
                                let data = res.data;
                                for (let i = 0; i < data.length; i++) {
                                    let id = data[i].ID;
                                    let name = data[i].Name;
                                    $('.pat_pro_id[data-id="' + id + '"]').text(name);
                                }
                            }
                        }, error: function (res) {
                            layer.closeAll('loading');
                            layer.msg(res.responseJSON.msg, { icon: 2, time: 1000 });
                        }
                    });
                    // 高效-物流信息获取
                    let ord_ids = [];
                    // 确保res.data存在且是数组
                    if (res.data && Array.isArray(res.data)) {
                        res.data.forEach(function (item) {
                            if (item && item.Tracking_num) {
                                ord_ids.push(item.ID);
                            }
                        });
                    }

                    if (ord_ids.length > 0) {
                        // 使用递归函数逐个查询物流信息
                        function fetchExpressInfo(index) {
                            if (index >= ord_ids.length) {
                                // 全部查询完成
                                layer.closeAll('loading');
                                return;
                            }

                            let orderId = ord_ids[index];
                            $.ajax({
                                url: '/admin/express_info_by_ord_id',
                                data: {
                                    id: orderId // 传递单个订单ID
                                },
                                type: 'post',
                                success: function (res) {
                                    if (res.code === 200 && res.data) {
                                        // 获取物流记录，现在是一个对象而不是数组
                                        let expressInfo = res.data;
                                        let last_contents = expressInfo.Last_contents || '-';
                                        let last_update_time = expressInfo.Last_update_time || '-';

                                        // 为当前行添加物流信息（添加到缓存数据中）
                                        for (let i = 0; i < table.cache.mytable.length; i++) {
                                            if (table.cache.mytable[i].ID == orderId) {
                                                // 添加额外属性以供渲染
                                                table.cache.mytable[i].express_info = last_contents;
                                                table.cache.mytable[i].express_update_time = last_update_time;
                                                table.cache.mytable[i].express_status = expressInfo.Status || '-';

                                                // 更新DOM元素中对应的内容
                                                let trSelector = '.layui-table-main tr[data-index="' + i + '"]';
                                                let $tr = $(trSelector);
                                                if ($tr.length) {
                                                    // 更新物流信息列
                                                    $tr.find('.express_info').text(last_contents);

                                                    // 找到更新时间单元格并更新内容
                                                    let $updateCell = $tr.find('.express_update_time').closest('.layui-table-cell');
                                                    $updateCell.html(generateUpdateTimeCell(table.cache.mytable[i]));
                                                }
                                                break;
                                            }
                                        }
                                    }

                                    // 继续查询下一个订单的物流信息
                                    fetchExpressInfo(index + 1);
                                },
                                error: function (res) {
                                    console.error('获取物流信息失败：', res);
                                    // 尽管发生错误，仍继续查询下一个订单
                                    fetchExpressInfo(index + 1);
                                }
                            });
                        }

                        // 开始查询第一个订单
                        layer.load(2);
                        fetchExpressInfo(0);
                    }
                },
                page: true,
                limit: 10,
            })
            //监听工具条
            table.on('tool(mytable)', function (obj) {
                var data = obj.data;
                if (obj.event === 'show') {
                    window.location.href = 'order_show.html?id=' + data.ID + '#/admin/order_show.html';
                } else if (obj.event === 'update_final_pay') {
                    layer.open({
                        type: 2,
                        title: '更新尾款',
                        area: ['900px', '800px'],
                        shadeClose: true,
                        content: 'update_final_pay.html?id=' + data.ID + '&money=' + data.Total_money + '|' + data.Pre_pay
                    });
                } else if (obj.event === 'verify_cr_pic') {
                    if (confirm('您确定要审核该订金为已支付吗？')) {
                        $.ajax({
                            url: '/admin/order/pay_review_pre',
                            data: {
                                id: data.ID,
                            },
                            type: 'post',
                            success: function (res) {
                                if (res.code === 200) {
                                    layer.msg(res.msg, { icon: 1, time: 1000 });
                                    table.reload('mytable');
                                } else {
                                    layer.msg(res.msg, { icon: 2, time: 1000 });
                                }
                            }, error: function (res) {
                                layer.msg(res.responseJSON.msg, { icon: 2, time: 1000 });
                            }
                        })
                    }
                } else if (obj.event === 'verify_pay_pic') {
                    layer.open({
                        type: 2,
                        title: '审核尾款',
                        area: ['500px', '800px'],
                        shadeClose: true,
                        content: 'review_final_pay.html?id=' + data.ID
                    });
                } else if (obj.event === 'final_pay_detail') {
                    layer.open({
                        type: 2,
                        title: '尾款明细',
                        area: ['500px', '700px'],
                        shadeClose: true,
                        content: '/admin/final_pay_detail.html?id=' + data.ID,
                    });
                } else if (obj.event === 'to_after_sales') {
                    layer.open({
                        type: 2,
                        title: '转给售后',
                        area: ['500px', '720px'],
                        shadeClose: true,
                        content: '/admin/to_after_sales.html?patid=' + data.Pat_pro_id + '&id=' + data.ID,
                    });
                }
            });
            form.on('input-affix(searchFilter)', function (data) {
                pat_pro_id = "";
            });
            // 声明全局变量，用于存储xm-select实例
            var keshiAsstSelect;

            form.on('submit(search)', function (data) {
                // 添加患者ID
                data.field.pat_pro_id = pat_pro_id;

                // 获取xm-select中选中的部门ID（多选模式）
                // 确保keshiAsstSelect已经初始化
                if (typeof keshiAsstSelect !== 'undefined' && keshiAsstSelect) {
                    let selectedDepts = keshiAsstSelect.getValue();
                    // 如果有选中部门，添加到表单数据
                    if (selectedDepts.length > 0) {
                        // 提取所有选中部门的ID值，并用逗号连接
                        let deptIds = selectedDepts.map(dept => dept.value || dept.Id).join(',');
                        data.field.asst_dep_id = deptIds;
                        console.log('选中的部门IDs:', deptIds);
                    }
                }
                table.reload('mytable', {
                    where: data.field,
                    page: {
                        curr: 1 //重新从第 1 页开始
                    }
                });
                return false;
            });
            //新增用户
            $('.create_btn').on('click', function () {
                window.location.href = 'order_add.html';
            });
            //渲染状态下拉框
            let order_status_html = '<option value="">全部状态</option>';
            for (let i = 0; i < Order_Status.length-2; i++) {
                order_status_html += '<option value="' + i + '"' + (i === Order_Status_default ? ' selected' : '') + '>' + Order_Status[i] + '</option>';
            }
            $('#Order_Status').html(order_status_html);
            form.render('select');
            // 直接从本地存储获取部门数据，不再调用API
            let local_departments = localStorage.getItem('local_departments') ? JSON.parse(localStorage.getItem('local_departments')) : [];

            // 如果本地存储中没有部门数据，显示提示信息
            if (!local_departments || local_departments.length === 0) {
                layer.msg('本地部门数据不存在，请先刷新缓存', { icon: 2, time: 2000 });
            } else {

                // 1. 渲染部门下拉框（用于Department下拉菜单）
                let treeData = format_to_treedata_department(local_departments, global_default_store_id);
                treeData = renderDropdownItems(treeData);
                dropdown.render({
                    elem: '#Department',
                    id: 'DropdownID',
                    data: [],
                    content: '<div class="dropdown-menu">' + treeData + '</div>',
                    ready: function (elemPanel, elem) {
                        elemPanel.on('click', '.dropdown-item-leaf', function () {
                            $('#Department').val($(this).text());
                            dropdown.close('DropdownID');
                        });
                    }
                });

                // 定义加载医生列表的函数
                function loadDoctorsByDepartment(keshi_id) {
                    if (keshi_id) {
                        layer.load(2);
                        $.ajax({
                            url: '/admin/user/list_low',
                            data: {
                                role_id: 4,
                                department_id: keshi_id,
                            },
                            type: 'post',
                            success: function (res) {
                                layer.closeAll('loading');
                                if (res.code === 200) {
                                    let data = res.data;
                                    let html = '<option value="">选择医生</option>';
                                    if (data && data.length > 0) {
                                        for (let i = 0; i < data.length; i++) {
                                            html += '<option value="' + data[i].ID + '">' + data[i].Name + '</option>';
                                        }

                                        // 如果当前用户是医生，并且在当前科室中，设置默认选中和只读状态
                                        if (currentUserIsDoctor && currentUserDeptId == keshi_id && currentUserId > 0) {
                                            $('#doc_id').html(html);
                                            $('#doc_id').val(currentUserId);
                                            $('#doc_id').attr('disabled', 'disabled');
                                            // 使用内联样式而不是添加类，避免影响布局
                                            $('#doc_id').css('background-color', '#f2f2f2');
                                            form.render('select');
                                        } else {
                                            $('#doc_id').html(html);
                                            form.render('select');
                                        }
                                    } else {
                                        layer.msg('该科室暂无医生', { icon: 2, time: 1000 });
                                        html = '<option value="">该科室暂无医生</option>';
                                        $('#doc_id').html(html);
                                        form.render('select');
                                    }
                                } else {
                                    layer.msg(res.msg, { icon: 2, time: 1000 });
                                }
                            }, error: function (res) {
                                layer.closeAll('loading');
                                layer.msg(res.responseJSON.msg, { icon: 2, time: 1000 });
                            }
                        });
                    } else {
                        $('#doc_id').html('<option value="">选择医生</option>');
                        form.render('select');
                    }
                }

                // 2. 从本地存储中筛选医疗部门数据，用于科室下拉框
                // 筛选出医疗部门数据
                let medicalDepartments = local_departments.filter(dept =>
                    dept.Pid === global_default_store_id && dept.Name.includes('医疗部门')
                );

                // 获取医疗部门下的子部门（科室）
                let medicalChildDepts = [];
                if (medicalDepartments.length > 0) {
                    let medicalDeptId = medicalDepartments[0].Id;
                    medicalChildDepts = local_departments.filter(dept => dept.Pid === medicalDeptId);
                }

                // 渲染科室下拉框
                let medicalHtml = '<option value="">选择科室</option>';
                for (let i = 0; i < medicalChildDepts.length; i++) {
                    medicalHtml += '<option value="' + medicalChildDepts[i].Id + '">' + medicalChildDepts[i].Name + '</option>';
                }
                $('#doc_dep_id').html(medicalHtml);

                // 如果当前用户是医生，设置默认选中和只读状态
                if (currentUserIsDoctor && currentUserDeptId > 0) {
                    // 设置科室下拉框为只读
                    $('#doc_dep_id').val(currentUserDeptId);
                    $('#doc_dep_id').attr('disabled', 'disabled');
                    // 使用内联样式而不是添加类，避免影响布局
                    $('#doc_dep_id').css('background-color', '#f2f2f2');

                    // 自动加载该科室下的医生列表
                    loadDoctorsByDepartment(currentUserDeptId);
                }

                form.render('select');

                // 监听科室选择事件
                form.on('select(doc_dep_id)', function (data) {
                    let keshi_id = data.value;
                    loadDoctorsByDepartment(keshi_id);
                });

                // 3. 创建一个映射，用于快速查找部门
                let deptMap = {};
                local_departments.forEach(dept => {
                    deptMap[dept.Id] = dept;
                });

                // 获取当前用户部门ID
                let userDeptId = 0;
                if (local_userinfo && local_userinfo.Department_id) {
                    userDeptId = local_userinfo.Department_id;
                }

                // 根据当前用户部门ID筛选部门
                let filteredDepartments = [];

                // 递归查找子部门的函数
                function findChildDepartments(parentId) {
                    let children = [];
                    local_departments.forEach(dept => {
                        if (dept.Pid === parentId) {
                            children.push(dept);
                            // 递归查找子部门的子部门
                            let grandChildren = findChildDepartments(dept.Id);
                            children = children.concat(grandChildren);
                        }
                    });
                    return children;
                }

                // 如果用户有部门ID，则查找该部门下的所有子部门
                if (userDeptId > 0) {
                    // 先添加当前部门
                    const currentDept = local_departments.find(dept => dept.Id === userDeptId);
                    if (currentDept) {
                        filteredDepartments.push(currentDept);
                    }

                    // 添加所有子部门
                    const childDepts = findChildDepartments(userDeptId);
                    filteredDepartments = filteredDepartments.concat(childDepts);
                } else {
                    // 如果用户没有部门ID，则使用原来的逻辑，显示所有销售部门
                    // 获取所有售前和售后部门的子部门
                    let allSaleDepts = [];
                    global_sale_department_ids.forEach(deptId => {
                        local_departments.forEach(dept => {
                            if (dept.Pid === deptId) {
                                allSaleDepts.push(dept);
                            }
                        });
                    });

                    // 再找出这些子部门的所有子部门
                    let allChildDepts = [...allSaleDepts];
                    allSaleDepts.forEach(dept => {
                        function findChildren(parentId) {
                            local_departments.forEach(d => {
                                if (d.Pid === parentId && !allChildDepts.some(cd => cd.Id === d.Id)) {
                                    allChildDepts.push(d);
                                    findChildren(d.Id);
                                }
                            });
                        }
                        findChildren(dept.Id);
                    });

                    // 过滤部门数据 - 只保留用户有权限的部门及其父级部门
                    filteredDepartments = allChildDepts;
                }

                // 按照排序值降序排列
                filteredDepartments.sort((a, b) => b.Sort - a.Sort);

                // 将部门数据转换成树形结构
                function convertToTree(data) {
                    let result = [];
                    let map = {};

                    // 创建所有节点的映射
                    data.forEach(function (item) {
                        map[item.Id] = {
                            ...item,
                            children: []
                        };
                    });

                    // 确保所有必要的父节点都存在
                    let addedParentIds = new Set(); // 用于跟踪已添加的父节点ID

                    data.forEach(function (item) {
                        if (item.Pid !== 0 && !map[item.Pid] && !addedParentIds.has(item.Pid)) {
                            // 如果父节点不在映射中且尚未添加，尝试从原始数据中找到它
                            const parentDept = local_departments.find(d => d.Id === item.Pid);
                            if (parentDept) {
                                // 添加父节点到映射
                                map[parentDept.Id] = {
                                    ...parentDept,
                                    children: []
                                };
                                // 将父节点添加到数据数组
                                data.push(parentDept);
                                // 记录已添加的父节点ID
                                addedParentIds.add(parentDept.Id);
                            }
                        }
                    });

                    // 移除重复的部门
                    let uniqueData = [];
                    let idSet = new Set();
                    data.forEach(function (item) {
                        if (!idSet.has(item.Id)) {
                            uniqueData.push(item);
                            idSet.add(item.Id);
                        }
                    });
                    data = uniqueData;

                    // 构建树结构
                    data.forEach(function (item) {
                        let node = map[item.Id];
                        if (item.Pid !== 0 && map[item.Pid]) {
                            // 将当前节点添加到父节点的children中
                            map[item.Pid].children.push(node);
                        } else {
                            // 顶级节点直接添加到结果数组
                            result.push(node);
                        }
                    });

                    // 按Sort字段排序
                    function sortBySort(arr) {
                        arr.sort(function (a, b) {
                            return b.Sort - a.Sort; // 降序排列
                        });
                        arr.forEach(function (item) {
                            if (item.children && item.children.length > 0) {
                                sortBySort(item.children);
                            }
                        });
                        return arr;
                    }

                    return sortBySort(result);
                }

                let departmentTree = convertToTree(filteredDepartments);

                // 确定需要展开的节点
                // 如果是根据用户部门ID筛选的，则默认展开用户部门ID
                let expandedKeys = userDeptId > 0 ? [userDeptId] : global_sale_department_ids;

                // 加载多个部门的用户
                function loadDepartmentUsersAll(selectedDepts) {
                    layer.load(2);

                    // 获取所有选中部门的ID
                    let deptIds = selectedDepts.map(item => item.Id || item.id || item.value);

                    console.log('加载部门用户 - 选中的部门IDs:', deptIds);

                    // 创建查询条件，适应多个部门
                    let queryParams = {
                        department_ids: deptIds.join(',')
                    };

                    console.log('加载部门用户 - 请求参数:', queryParams);

                    // 确定角色类型 - 检查是否包含售后部门
                    let hasSaleAfter = false;
                    for (let i = 0; i < selectedDepts.length; i++) {
                        let deptId = selectedDepts[i].Id || selectedDepts[i].id || selectedDepts[i].value;
                        let dept = filteredDepartments.find(d => d.Id == deptId || d.value == deptId);
                        if (dept && (dept.Name || dept.name || '').includes('售后')) {
                            hasSaleAfter = true;
                            break;
                        }
                    }

                    $.ajax({
                        url: '/admin/user/list_low',
                        data: queryParams,
                        type: 'post',
                        success: function (res) {
                            layer.closeAll('loading');
                            if (res.code === 200) {
                                let data = res.data;
                                //let html = '<option value="">选择人员</option>';
                                let html = '';
                                if (data && data.length > 0) {
                                    // 按姓名排序
                                    data.sort((a, b) => a.Name.localeCompare(b.Name, 'zh'));

                                    for (let i = 0; i < data.length; i++) {
                                        html += '<option value="' + data[i].ID + '">' + data[i].Name + '</option>';
                                    }

                                    $('#asst_id').html(html);
                                    form.render('select');
                                } else {
                                    html = '<option value="">所选部门下暂无人员</option>';
                                    $('#asst_id').html(html);
                                    form.render('select');
                                }
                            } else {
                                layer.msg(res.msg, { icon: 2, time: 1000 });
                            }
                        },
                        error: function (res) {
                            layer.closeAll('loading');
                            layer.msg(res.responseJSON.msg, { icon: 2, time: 1000 });
                        }
                    });
                }

                // 初始化xm-select，使用全局变量
                // 检查当前用户是否为超级管理员
                const isSuperAdmin = local_userinfo && local_userinfo.Id == 1;

                // 判断当前用户是否为销售岗位（非超级管理员）
                if (!isSuperAdmin && currentUserIsSales && currentUserDeptId > 0) {
                    console.log('当前用户是销售岗位，部门ID:', currentUserDeptId);
                    // 使用id2department函数获取部门名称
                    let deptName = id2department(currentUserDeptId, local_departments, 0);
                    // 渲染为禁用状态的xm-select
                    keshiAsstSelect = xmSelect.render({
                        el: '#asst_dep_id',
                        data: [{
                            Name: deptName,
                            Id: currentUserDeptId
                        }],
                        initValue: [currentUserDeptId],//就显示当前的销售岗位
                        model: {
                            label: {
                                type: 'text'
                            }
                        },
                        prop: {
                            name: 'Name',
                            value: 'Id'
                        },
                        disabled: true,
                    });

                    // 渲染人员下拉框 - 直接使用当前用户信息
                    let html = '<option value="' + currentUserId + '">' + local_userinfo.Name + '</option>';
                    $('#asst_id').html(html);
                    $('#asst_id').attr('disabled', 'disabled');
                    $('#asst_id').css('background-color', '#f2f2f2');
                    form.render('select');

                } else {
                    console.log('当前用户是非销售岗位或超级管理员，部门ID:', currentUserDeptId);

                    // 移除旧代码，直接使用新的售后部门树形结构

                    // 非销售岗位用户，初始化xm-select，获取售后部门的所有层级数据
                    // 首先获取售后部门的所有数据
                    let afterSaleDepartments = [];

                    // 递归查找部门及其子部门
                    function findDepartmentAndChildren(deptId) {
                        // 找到当前部门
                        const currentDept = local_departments.find(dept => dept.Id === deptId);
                        if (currentDept) {
                            afterSaleDepartments.push(currentDept);

                            // 找到所有子部门
                            const childDepts = local_departments.filter(dept => dept.Pid === deptId);
                            childDepts.forEach(child => {
                                findDepartmentAndChildren(child.Id);
                            });
                        }
                    }

                    // 无论是否为超级管理员，在售前页面都只加载售前部门数据
                    findDepartmentAndChildren(global_sale_department_pre_sale);

                    // 按照排序值降序排列
                    afterSaleDepartments.sort((a, b) => b.Sort - a.Sort);

                    // 将部门数据转换为树形结构
                    function convertToTree(data) {
                        let result = [];
                        let map = {};

                        // 创建所有节点的映射
                        data.forEach(function (item) {
                            map[item.Id] = {
                                ...item,
                                children: []
                            };
                        });

                        // 构建树结构
                        data.forEach(function (item) {
                            let node = map[item.Id];

                            // 统一逻辑：如果是售前部门本身，直接添加到结果数组
                            if (item.Id === global_sale_department_pre_sale) {
                                result.push(node);
                            }
                            // 否则添加到父节点的children中
                            else if (map[item.Pid]) {
                                map[item.Pid].children.push(node);
                            }
                        });

                        return result;
                    }

                    let departmentTree = convertToTree(afterSaleDepartments);

                    // 获取当前用户的部门ID及其所有子部门ID
                    let userDeptIds = [];
                    if (currentUserDeptId > 0) {
                        userDeptIds.push(currentUserDeptId);

                        // 递归查找子部门
                        function findChildDeptIds(parentId) {
                            local_departments.forEach(dept => {
                                if (dept.Pid === parentId) {
                                    userDeptIds.push(dept.Id);
                                    findChildDeptIds(dept.Id);
                                }
                            });
                        }

                        findChildDeptIds(currentUserDeptId);
                    }

                    // 递归处理树节点，设置禁用状态
                    function processTreeNodes(nodes) {
                        return nodes.map(node => {
                            // 检查当前用户是否为超级管理员
                            const isSuperAdmin = local_userinfo && local_userinfo.Id == 1;

                            // 检查节点是否在用户的部门ID列表中
                            const isUserDept = userDeptIds.includes(node.Id);

                            // 处理子节点
                            let children = [];
                            if (node.children && node.children.length > 0) {
                                children = processTreeNodes(node.children);
                            }

                            return {
                                name: node.Name,
                                value: node.Id,
                                // 如果是超级管理员，则所有部门都不禁用；否则，只有用户所属部门可用
                                disabled: isSuperAdmin ? false : !isUserDept,
                                children: children
                            };
                        });
                    }

                    // 处理树节点，设置禁用状态
                    let processedTree = processTreeNodes(departmentTree);

                    // 找出所有包含可选项的节点ID，用于自动展开
                    // 无论是否为超级管理员，在售前页面都只展开售前部门
                    let autoExpandKeys = [global_sale_department_pre_sale];

                    // 递归查找包含可选项的父节点
                    function findParentsWithSelectableChildren(nodes, parentPath = []) {
                        nodes.forEach(node => {
                            // 当前节点路径
                            const currentPath = [...parentPath, node.value];

                            // 如果当前节点不禁用，将其所有父节点添加到展开列表
                            if (!node.disabled) {
                                // 将路径中的所有节点ID添加到自动展开列表
                                autoExpandKeys = [...autoExpandKeys, ...currentPath];
                            }

                            // 递归处理子节点
                            if (node.children && node.children.length > 0) {
                                findParentsWithSelectableChildren(node.children, currentPath);
                            }
                        });
                    }

                    // 查找所有需要自动展开的节点
                    findParentsWithSelectableChildren(processedTree);

                    // 去重
                    autoExpandKeys = [...new Set(autoExpandKeys)];

                    console.log('自动展开的节点IDs:', autoExpandKeys);

                    // 初始化xm-select，设置为多选模式
                    keshiAsstSelect = xmSelect.render({
                        el: '#asst_dep_id',
                        theme: {
                            color: '#1677ff',
                        },
                        height: 'auto',
                        data: processedTree,
                        // 不设置initValue，默认全不勾选
                        model: {
                            label: {
                                type: 'text',
                            }
                        },
                        radio: false, // 设置为多选模式
                        tree: {
                            show: true,
                            strict: true, // 严格遵守父子模式
                            expandedKeys: autoExpandKeys, // 自动展开包含可选项的节点
                            autoExpandParent: true, // 自动展开父节点
                        },
                        filterable: true,
                        // 添加默认提示文字
                        tips: '请选择部门',
                        prop: {
                            name: 'name',
                            value: 'value',
                            disabled: 'disabled'
                        },
                        // 设置表单提交时的名称
                        name: 'asst_dep_id',
                        toolbar: {
                            show: true,
                            list: ['ALL', 'REVERSE', 'CLEAR'],
                        },
                        on: function (data) {
                            if (data.change && data.change.length > 0) {
                                // 使用setTimeout确保在DOM更新后获取最新的选中值
                                setTimeout(function () {
                                    // 获取当前所有选中的部门ID
                                    if (keshiAsstSelect) {
                                        let selectedDepts = keshiAsstSelect.getValue();
                                        console.log('xm-select选择变化 - 选中的部门:', selectedDepts);

                                        // 如果有选中的部门，加载这些部门的用户
                                        if (selectedDepts.length > 0) {
                                            loadDepartmentUsersAll(selectedDepts);
                                        } else {
                                            // 清空用户列表
                                            $('#asst_id').html('<option value="">选择人员</option>');
                                            form.render('select');
                                        }
                                    }
                                }, 0);
                            }
                        }
                    });
                }

                // 如果当前用户是医生，自动触发科室选择事件加载对应的医生列表
                if (currentUserIsDoctor && currentUserDeptId > 0) {
                    loadDoctorsByDepartment(currentUserDeptId);
                }
            }

            //模糊搜索用户的下拉框组件
            var inst = dropdown.render({
                elem: '#dropdown_input',
                trigger: 'keyup click',
                data: [],
                style: 'min-width: 220px; box-shadow: 1px 1px 11px rgb(0 0 0 / 11%);',
                click: function (data) {
                    if (!isEmpty(data)) {
                        this.elem.val(data.name + ' / ' + data.phone);
                        pat_pro_id = data.id;
                    }
                }
            });
            $(inst.config.elem).on('input propertychange', function () {
                var elem = $(this);
                var value = elem.val().trim();
                if (value.length > 0) {
                    $.ajax({
                        url: "/admin/patient_profile/patient_profile_phone2id",
                        type: 'post',
                        dataType: 'json',
                        data: {
                            "wd": value,
                        },
                        success: function (response) {
                            let data = response.data;
                            if (data && data.length > 0) {
                                dropdown.reloadData(inst.config.id, {
                                    data: data,
                                    templet: function (d) {
                                        var exp = new RegExp(value, 'gi');
                                        return (d.name + ' / ' + d.phone).replace(exp, function (str) {
                                            return '<span style="color: red;">' + str + '</span>';
                                        });
                                    }
                                });
                            } else {
                                dropdown.reloadData(inst.config.id, {
                                    data: [],
                                });
                            }
                        },
                        error: function (err) {
                            dropdown.reloadData(inst.config.id, {
                                data: [],
                            });
                        }
                    });
                } else {
                    dropdown.reloadData(inst.config.id, {
                        data: [],
                    });
                }
            });
        });
    </script>
</body>

</html>