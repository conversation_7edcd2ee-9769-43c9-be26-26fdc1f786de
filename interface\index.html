<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <title>幸年堂智慧中医办公系统</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <script src="/dist/js/main.js"></script>
  <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon" />
  <link href="/dist/layui/css/layui.css" rel="stylesheet">
  <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
</head>

<body>
  <div class="video-background">
    <video autoplay loop muted playsinline>
      <source src="/dist/images/bg_video3.webm" type="video/webm">
    </video>
  </div>
  <style>
    body {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      margin: 0;
      position: relative;
      overflow: hidden;
      background: white;
    }

    .video-background {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: -1;
      overflow: hidden;
    }

    .video-background video {
      position: absolute;
      min-width: 100%;
      min-height: 100%;
      width: auto;
      height: auto;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      object-fit: cover;
      filter:
        blur(50px)
        /* 模糊度 */
        hue-rotate(-20deg)
        /* 色调,蓝色 */
        brightness(1)
        /* 增加亮度 */
        /*contrast(2) 增加对比度 */
      ;
      opacity: .5;
    }


    .login_box {
      width: 360px;
      height: 320px;
      margin: 0 auto;
      background: linear-gradient(to right bottom, white, white, #eee);
      /*      box-shadow: 0 0 20px -8px #bbb;*/
    }

    .demo-login-container {
      background-color: white;
      border-radius: 10px;
      padding: 30px;
      margin: 0 auto;
    }

    @media (max-width: 768px) {
      .login_box {
        width: 80%;
      }
    }

    .demo-login-other .layui-icon {
      position: relative;
      display: inline-block;
      margin: 0 2px;
      top: 2px;
      font-size: 26px;
    }

    .copyright {
      margin: 30px 0 10px 0;
      text-align: center;
      font-size: 12px;
      color: #999;
      /* text-shadow: 0 0 3px rgba(0, 0, 0, 0.5); */
    }

    .layui-form-item {
      margin-bottom: 35px;
    }
  </style>
  <form class="layui-form">
    <div class="demo-login-container login_box">

      <div class="layui-form-item">
        <div class="layui-input-wrap flex-center">
          <h2>幸年堂智慧中医办公系统</h2>
        </div>
      </div>

      <div class="layui-form-item">
        <div class="layui-input-wrap">
          <div class="layui-input-prefix">
            <i class="layui-icon layui-icon-username"></i>
          </div>
          <input type="text" name="phone" maxlength="11" lay-verify="required" placeholder="用户名" lay-reqtext="请填写用户名"
            autocomplete="off" class="layui-input" lay-affix="clear" onkeyup="isPhone(this)">
        </div>
      </div>
      <div class="layui-form-item">
        <div class="layui-input-wrap">
          <div class="layui-input-prefix">
            <i class="layui-icon layui-icon-password"></i>
          </div>
          <input type="password" name="pwd" maxlength="20" lay-verify="required" placeholder="密   码" lay-reqtext="请填写密码"
            autocomplete="off" class="layui-input" lay-affix="eye">
        </div>
      </div>

      <!--     <div class="layui-form-item">
      <input type="checkbox" name="remember" lay-skin="primary" title="记住密码">
      <a href="#forget" style="float: right; margin-top: 7px;">忘记密码？</a>
    </div> -->
      <div class="layui-form-item">
        <button type="submit" class="layui-btn layui-btn-fluid" lay-submit lay-filter="demo-login">登录</button>
      </div>
      <div class="copyright">© 2024 幸年堂智慧中医办公系统</div>
    </div>
  </form>
  <script src="/dist/layui/layui.js"></script>
  <script>
    layui.use(function () {
      var form = layui.form;
      var layer = layui.layer;
      var $ = layui.$;
      form.on('submit(demo-login)', function (data) {
        var field = data.field;
        layer.load(2);
        $.ajax({
          url: '/admin/user/login',
          data: field,
          type: 'post',
          dataType: 'json',
          success: function (data) {
            layer.msg(data.msg);
            if (data.code == 200) {
              // 根据用户角色跳转相应页面
              let redirect_url = "/admin/main.html";
              // if (data.data.Id == 1) {
              //   redirect_url = "/admin/interface.html"
              // }
              // 将data.data存储本地存储，用于显示当前用户信息
              localStorage.setItem('local_userinfo', JSON.stringify(data.data));
              // 获取当前用户菜单信息
              $.ajax({
                url: '/admin/res/current',
                type: 'post',
                dataType: 'json',
                success: function (res) {
                  layer.closeAll('loading');
                  // 将当前部门数据缓存本地
                  let local_departments = res.departments || [];
                  if (local_departments.length > 0) {
                    localStorage.setItem("local_departments", JSON.stringify(local_departments));
                  }
                  let data = res.data;
                  // 存储所有数据 - local_perm_res_data
                  processAndStorePermResData(data);
                  let formattedData = format_to_treedata_role_res(data);
                  // 存储菜单格式数据 - local_menu_data
                  processAndStoreMenuData(formattedData);
                  setTimeout(function () {
                    window.location.href = redirect_url;
                  }, 1000);
                },
                error: function (data) {
                  layer.closeAll('loading');
                  layer.msg(data.responseJSON.msg);
                }
              });
            }
          },
          error: function (data) {
            layer.closeAll('loading');
            layer.msg(data.responseJSON.msg);
          },
        });
        return false;
      });
    });
  </script>

</body>

</html>