<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>慕生堂 - 患者添加</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/layui/layarea_lc.js"></script>
    <script src="/dist/js/main.js"></script>
    <style>
        .has_account {
            display: none;
        }
    </style>
</head>

<body>
    <div class="layui-layout layui-layout-admin">
        <!-- 头部 -->
        <div class="layui-header">
            <div class="layui-logo">慕生堂 TS</div>
            <ul class="layui-nav layui-layout-right">
                <li class="layui-nav-item" style="background-color:transparent!important;padding-right:10px">
                    <a href="javascript:;" style="display:flex;align-items:center;color:#fff"><img
                            src="/dist/images/user.png" class="layui-nav-img"><span class="user_name"></span></a>
                    <dl class="layui-nav-child pop_menu">
                        <dd><i class="iconfont">&#xe62f;</i><a href="change_password.html#7">用户资料</a></dd>
                        <dd><i class="iconfont">&#xe601;</i><a href="javascript:void(0)" class="refresh_cache">刷新缓存</a>
                        </dd>
                        <dd><i class="iconfont">&#xe634;</i><a href="login_out.html">退出登录</a></dd>
                    </dl>
                </li>
            </ul>
        </div>
        <!-- 导航 -->
        <ul class="layui-nav layui-nav-tree layui-nav-side" id="menuContainer" lay-bar="disabled" lay-accordion>
            <div style="margin: 50px 0;color: #bbb;text-align: center;">菜单加载中...</div>
        </ul>
        <!-- 主体内容 -->
        <div class="layui-body">
            <div class="body_child">
                <div class="layui-panel">
                    <div class="layui-card-header">
                        <div class="layui-row" style="padding-top:10px;">
                            <div class="layui-col-md8">患者添加</div>
                            <!-- <div class="layui-col-md4" style="text-align: right;">
                                <button type="submit" class="layui-btn layui-btn-fluid create_btn" lay-submit=""
                                    style="max-width: 200px;">
                                    已有帐号，新建家庭成员？
                                </button>
                            </div> -->

                        </div>
                    </div>
                    <div class="layui-padding-3" style="min-height: 800px;">





                        <form class="layui-form" lay-filter="form_add" action="" onsubmit="return false">
                            <div
                                style="font-size: 15px;font-weight: bold;margin:10px 0 20px 0;border-bottom: 1px solid #efefef;padding: 10px;">
                                <span class="red_star">*</span>必填项
                            </div>
                            <div class="layui-row">
                                <div class="layui-col-xs4">

                                    <div class="layui-form-item no_account">
                                        <label class="layui-form-label">帐号</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="phone2" placeholder="帐号手机号" autocomplete="off"
                                                class="layui-input" onkeyup="isPhone(this)">
                                        </div>
                                    </div>


                                    <div class="layui-form-item has_account">
                                        <label class="layui-form-label">手机号</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="phone3" placeholder="家庭患者联系手机号" autocomplete="off"
                                                class="layui-input" onkeyup="isPhone(this)">
                                        </div>
                                    </div>


                                    <div class="layui-form-item">

                                        <div class="layui-col-xs8 no_account">
                                            <label class="layui-form-label">绑定密码</label>
                                            <div class="layui-input-block">
                                                <input name="pwd" placeholder="请输入用户密码" autocomplete="off"
                                                    class="layui-input">
                                            </div>
                                        </div>

                                        <div class="layui-col-xs8 has_account">
                                            <label class="layui-form-label">帐号</label>
                                            <div class="layui-input-block">
                                                <input type="text" name="phone" id="dropdown_input" placeholder="手机号"
                                                    autocomplete="off" class="layui-input" lay-affix="clear">
                                            </div>
                                        </div>


                                        <div class="layui-col-xs2 layui-btn layui-btn-xs role_permission"
                                            id="change_account_status" style="margin: 8px 0 0 10px;">
                                            已有帐号？
                                        </div>

                                    </div>

                                    <div class="layui-form-item">

                                        <label class="layui-form-label">关系</label>
                                        <div class="layui-input-block">
                                            <select name="relation" id="relation"></select>
                                        </div>

                                    </div>

                                    <div class="layui-form-item">
                                        <label class="layui-form-label">部门</label>
                                        <div class="layui-input-block">
                                            <input type="text" placeholder="医助是有隶属部门的，不用单独填" autocomplete="off"
                                                class="layui-input" disabled>
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">患者来源</label>
                                        <div class="layui-input-block">
                                            <select name="patient_from" id="patient_from"> </select>
                                        </div>
                                    </div>


                                </div>
                                <div class="layui-col-xs4">

                                    <div class="layui-form-item">
                                        <label class="layui-form-label">患者姓名</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="name" required placeholder="请输入内容"
                                                autocomplete="off" class="layui-input">
                                        </div>
                                    </div>

                                    <div class="layui-form-item">
                                        <label class="layui-form-label">性别</label>
                                        <div class="layui-input-block">
                                            <input type="radio" name="sex" value="1" title="男">
                                            <input type="radio" name="sex" value="0" title="女">
                                        </div>
                                    </div>

                                    <div class="layui-form-item">
                                        <label class="layui-form-label">微信号</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="weixin" required placeholder="请输入微信号"
                                                autocomplete="off" class="layui-input">
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">医助</label>
                                        <div class="layui-input-block">
                                            <input type="text" required placeholder="医助添加的，会记录他的ID" autocomplete="off"
                                                class="layui-input" disabled>
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">就诊类型</label>
                                        <div class="layui-input-block">
                                            <input type="text" required placeholder="初诊？诊断次数不用提交，系统可查。且新建用户，首次肯定是初诊"
                                                autocomplete="off" class="layui-input" disabled>
                                        </div>
                                    </div>


                                </div>
                                <div class="layui-col-xs4">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">出生日期</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="born_date" required placeholder="请输入内容"
                                                autocomplete="off" class="layui-input" id="ID-laydate-demo">
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">医生</label>
                                        <div class="layui-input-block">
                                            <select name="doctor" id="doctor">
                                            </select>
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">患者等级</label>
                                        <div class="layui-input-block">
                                            <select name="patient_level" id="patient_level">
                                                <option value="">患者等级</option>
                                                <option value="1">A级</option>
                                                <option value="2">B级</option>
                                                <option value="3">C级</option>
                                                <option value="4">D级</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div
                                style="font-size: 15px;font-weight: bold;margin:10px 0 20px 0;border-bottom: 1px solid #efefef;padding: 10px;">
                                选填项</div>
                            <div class="layui-row">
                                <div class="layui-col-xs4">

                                    <div class="layui-form-item">
                                        <label class="layui-form-label">医保卡</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="ins_card_num" placeholder="请输入医保卡号"
                                                autocomplete="off" class="layui-input">
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">身高</label>
                                        <div class="layui-input-block">
                                            <input type="number" name="height" placeholder="请输入身高" autocomplete="off"
                                                class="layui-input">
                                        </div>
                                    </div>


                                </div>
                                <div class="layui-col-xs4">

                                    <div class="layui-form-item">
                                        <label class="layui-form-label">身份证号</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="idcard" placeholder="请输入身份证号" autocomplete="off"
                                                class="layui-input">
                                        </div>
                                    </div>

                                    <div class="layui-form-item">
                                        <label class="layui-form-label">体重</label>
                                        <div class="layui-input-block">
                                            <input type="number" name="weight" placeholder="请输入体重" autocomplete="off"
                                                class="layui-input">
                                        </div>
                                    </div>



                                </div>
                                <div class="layui-col-xs4">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">参保类型</label>
                                        <div class="layui-input-block">
                                            <select name="ins_type" id="ins_type">
                                            </select>
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">婚否</label>
                                        <div class="layui-input-block">
                                            <input type="radio" name="isMarried" value="1" title="已婚">
                                            <input type="radio" name="isMarried" value="0" title="未婚">
                                        </div>
                                    </div>

                                </div>


                                <div class="layui-col-xs12">
                                    <div class="layui-form-item" id="area-picker">
                                        <div class="layui-form-label">地址</div>
                                        <div class="layui-input-inline" style="width: 200px;">
                                            <select name="province" class="province-selector" lay-filter="province-1">
                                                <option value="">请选择省</option>
                                            </select>
                                        </div>
                                        <div class="layui-input-inline" style="width: 200px;">
                                            <select name="city" class="city-selector" lay-filter="city-1">
                                                <option value="">请选择市</option>
                                            </select>
                                        </div>
                                        <div class="layui-input-inline" style="width: 200px;">
                                            <select name="county" class="county-selector" lay-filter="county-1">
                                                <option value="">请选择区</option>
                                            </select>
                                        </div>
                                        <div class="layui-input-inline" style="width:360px;">
                                            <input type="text" name="address" placeholder="请输入具体地址" autocomplete="off"
                                                class="layui-input">
                                        </div>
                                    </div>
                                </div>


                            </div>


                            <div style="display: flex; justify-content: center;margin-top: 50px;">
                                <button class="layui-btn" lay-submit lay-filter="formSubmitBtn"
                                    style="margin-right: 50px;">确认新建</button>
                                <button type="reset" class="layui-btn layui-btn-primary"
                                    onclick="history.go(-1)">取消</button>
                            </div>

                        </form>


                    </div>
                </div>
                <br>
                <br>
            </div>
        </div>
    </div>


    <script>
        layui.config({
            base: './mods/'
            , version: '1.0'
        });
        layui.use(['element', 'layer', 'util'], function () {
            var element = layui.element;
            var layer = layui.layer;
            var form = layui.form;
            var util = layui.util;
            var treeTable = layui.treeTable;
            var laydate = layui.laydate;
            var dropdown = layui.dropdown;
            var $ = layui.$;
            var patient_account_id = 0;
            layarea_lc = layui.layarea_lc;
            laydate.render({
                elem: '#ID-laydate-demo'
            });
            var has_account = false;
            // 页面顶部插入缓存的用户数据
            let local_userinfo = localStorage.getItem('local_userinfo');
            let user_name = "";
            if (local_userinfo) {
                local_userinfo = JSON.parse(local_userinfo);
                user_name = local_userinfo.Name + " ( " + local_userinfo.Roles + " )";
            } else {
                user_name = "未登录";
            }
            $('.user_name').text(user_name);
            render_menu($);
            $('.refresh_cache').click(function () {
                refresh_cache($);
            });
            //渲染关系下拉框
            $.each(FamilyRelation, function (index, value) {
                $('#relation').append($('<option>').val(index).text(value));
            });
            //渲染参保类型下拉框
            $.each(Ins_Type, function (index, value) {
                $('#ins_type').append($('<option>').val(index).text(value));
            });
            // 渲染客户来源下拉框
            $.each(Patient_From, function (index, value) {
                $('#patient_from').append($('<option>').val(index).text(value));
            });

            // 重新渲染select元素
            form.render('select');
            $('#change_account_status').click(function () {
                let that = $(this);
                if ($('.no_account').is(':hidden')) {
                    $('.no_account').show();
                    $('.has_account').hide();
                    that.text('已有帐号？');
                    has_account = false;
                } else {
                    $('.has_account').show();
                    $('.no_account').hide();
                    that.text('新建帐号？');
                    has_account = true;
                }
            });
            //渲染医生下拉框
            $.ajax({
                url: serverUrl + "/admin/user/list_low",
                type: "post",
                data: {
                    role_id: 4,
                },
                success: function (res) {
                    let data = res.data;
                    let html = '<option value="">选择医生</option>';
                    for (let i = 0; i < data.length; i++) {
                        html += '<option value="' + data[i].ID + '">' + data[i].Name + '</option>';
                    }
                    $('#doctor').html(html);
                    form.render('select');
                }
            });
            //渲染用户边输入边提示用户信息
            var inst = dropdown.render({
                elem: '#dropdown_input',
                trigger: 'keyup click',
                data: [],
                style: 'min-width: 220px; box-shadow: 1px 1px 11px rgb(0 0 0 / 11%);',
                click: function (data) {
                    if (!isEmpty(data)) {
                        this.elem.val(data.id + ' / ' + data.phone);
                        patient_account_id = data.id;
                    }
                }
            });
            $(inst.config.elem).on('input propertychange', function () {
                var elem = $(this);
                var value = elem.val().trim();
                if (value.length > 0) {
                    $.ajax({
                        url: "/admin/patient_profile/patient_phone2id",
                        type: 'post',
                        dataType: 'json',
                        data: {
                            "phone": value,
                        },
                        success: function (response) {
                            let data = response.data;
                            if (data) {
                                dropdown.reloadData(inst.config.id, {
                                    data: data,
                                    templet: function (d) {
                                        var exp = new RegExp(value, 'gi');
                                        return ('ID：' + d.id + '  电话： ' + d.phone + '  绑定微信： ' + (d.openid == 0 ? '× 未绑定' : '√ 已绑定') + '  创建时间 ' + Utc2time(d.create_time)).replace(exp, function (str) {
                                            return '<span style="color: red;">' + str + '</span>';
                                        });
                                    }
                                });
                            } else {
                                dropdown.reloadData(inst.config.id, {
                                    data: [],
                                });
                            }
                        },
                        error: function (err) {
                            dropdown.reloadData(inst.config.id, {
                                data: [],
                            });
                        }
                    });
                } else {
                    dropdown.reloadData(inst.config.id, {
                        data: [],
                    });
                }
            });
            //渲染地区选择器
            layarea_lc.render({
                elem: '#area-picker',
                name: 'name',
                change: function (res) {
                    console.log(res);
                }
            });
            //监听提交
            form.on('submit(formSubmitBtn)', function (data) {
                let field = data.field;
                field.address = field.province + '|' + field.city + '|' + field.county + '|' + field.address;

                // phone - 绑定的帐号
                // phone2 - 新建的帐号手机号
                // phone3 - 家庭患者手机号

                if (has_account) {
                    // 绑定老帐号
                    field.patient_account_id = patient_account_id;
                    if (field.phone == '') {
                        layer.msg('您选择了已有帐号，但未输入手机号，请重新输入手机号');
                        return false;
                    }
                    field.phone = field.phone3;
                } else {
                    // 创建新帐号
                    field.phone = field.phone2;
                }
                $.ajax({
                    url: serverUrl + "/admin/patient_profile/add",
                    type: "post",
                    data: field,
                    success: function (res) {
                        if (res.code == 200) {
                            layer.msg(res.msg, {
                                time: 2000,
                                icon: 1
                            }, function () {
                                location.href = "patient_profile_list.html";
                            }
                            );
                        } else {
                            layer.msg(res.msg, {
                                time: 2000,
                                icon: 2
                            });
                        }
                    },
                    error: function (res) {
                        layer.msg(res.responseJSON.msg, {
                            time: 5000,
                            icon: 2
                        });
                    }
                });
                return false;
            });
        });
    </script>
</body>

</html>