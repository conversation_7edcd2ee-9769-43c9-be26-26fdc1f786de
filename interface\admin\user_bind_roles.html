<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 角色列表</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
    <style>
        body {
            overflow-x: hidden;
        }
    </style>
</head>

<body>
    <div class="layui-padding-3" style="padding-top: 0 !important;min-height: 800px;">
        <div id="ID-tree-demo"></div>
    </div>
    <div style="position: fixed;right: 30px;top: 30px;">
        <button type="button" class="layui-btn layui-btn-sm" lay-on="getChecked">保存绑定</button>
    </div>
    <!-- <div style="position: fixed;right: 30px;top: 70px;font-size: 11px;color: #999;">带（M）证明是菜单</div> -->
    <script>
        layui.use(['element', 'layer', 'util', 'tree'], function () {
            var element = layui.element;
            var layer = layui.layer;
            var tree = layui.tree;
            var util = layui.util;
            var $ = layui.$;
            var index = parent.layer.getFrameIndex(window.name);
            // 页面顶部插入缓存的用户数据
            let local_userinfo = localStorage.getItem('local_userinfo');
            let user_name = "";
            if (local_userinfo) {
                local_userinfo = JSON.parse(local_userinfo);
                user_name = local_userinfo.Name + " ( " + local_userinfo.Roles + " )";
            } else {
                user_name = "未登录";
            }
            $('.user_name').text(user_name);

            // 互斥角色ID列表
            const exclusiveRoleIds = [3, 4, 9, 6, 14, 2]; // 医助、医生、售后、售前文员、售后文员、数据分析员

            // 记录上一次有效的选中状态
            let lastValidState = [];

            // 记录当前操作的节点ID
            let currentNodeId = null;

            // 标记是否正在处理互斥冲突
            let isProcessingConflict = false;

            // 检查角色互斥的函数
            function checkExclusiveRoles(checkedIds) {
                const hasExclusiveRole = checkedIds.some(id => exclusiveRoleIds.includes(Number(id)));
                const hasOtherRole = checkedIds.some(id => !exclusiveRoleIds.includes(Number(id)));

                // 如果同时选择了互斥角色和其他角色，返回true表示存在冲突
                return hasExclusiveRole && hasOtherRole;
            }

            // 检查是否选择了多个互斥角色
            function checkMultipleExclusiveRoles(checkedIds) {
                const selectedExclusiveRoles = checkedIds.filter(id => exclusiveRoleIds.includes(Number(id)));
                return selectedExclusiveRoles.length > 1;
            }

            // 获取所有选中的节点ID
            function getCheckedIds() {
                const checkedNodes = tree.getChecked('demo-id-1');
                return extractIds(checkedNodes);
            }

            // 提取选中项的ID
            function extractIds(nodes) {
                let ids = [];
                nodes.forEach(node => {
                    ids.push(node.id);
                    if (node.children) {
                        ids = ids.concat(extractIds(node.children));
                    }
                });
                return ids;
            }

            // 重新设置选中状态，恢复到上一个有效状态
            function resetToLastValidState() {
                // 先全部取消选中
                tree.reload('demo-id-1');

                // 重新渲染树
                setTimeout(function () {
                    // 设置为上一个有效状态
                    if (lastValidState.length > 0) {
                        tree.setChecked('demo-id-1', lastValidState);
                    }
                    isProcessingConflict = false;
                }, 100);
            }

            let user_id = location.search.split('=')[1];
            function buildTree(data) {
                const tree = [];
                data.forEach(item => {
                    // 检查角色ID是否在互斥角色列表中
                    const isExclusiveRole = exclusiveRoleIds.includes(Number(item.Id));
                    // 如果是互斥角色，在名称前加上星号
                    const roleName = isExclusiveRole ? `* ${item.Role}` : item.Role;

                    tree.push({
                        id: item.Id,
                        name: roleName, // 使用处理后的角色名称
                    });
                });
                return tree;
            }

            function renderTree(deduplicatedRole_ids) {
                $.ajax({
                    url: '/normal/role_cache_get',
                    method: 'POST',
                    success: function (response) {
                        if (response.code === 200) {
                            // const treeData = buildTree(response.data);
                            const treeData = buildTree(response.data).filter(item => item.id !== 1);
                            tree.render({
                                elem: '#ID-tree-demo',
                                data: treeData,
                                customName: {
                                    id: 'id',
                                    title: 'name',
                                    children: 'children'
                                },
                                showLine: false,
                                showCheckbox: true,
                                onlyIconControl: true,
                                id: 'demo-id-1',
                                oncheck: function (obj) {
                                    // 如果正在处理冲突，不再触发新的检查
                                    if (isProcessingConflict) return;

                                    // 保存操作的节点ID
                                    currentNodeId = obj.data.id;
                                    console.log("操作节点ID:", currentNodeId);

                                    // 获取当前所有选中的节点ID
                                    const checkedIds = getCheckedIds();
                                    console.log("当前选中的ID:", checkedIds);

                                    // 检查是否存在互斥角色冲突
                                    if (checkExclusiveRoles(checkedIds)) {
                                        isProcessingConflict = true;
                                        console.log("检测到互斥角色冲突");

                                        // 显示提示信息
                                        layer.confirm('销售与医生两个岗位与其它岗位互斥，只可独立选择。', {
                                            icon: 0,
                                            title: '角色互斥提醒',
                                            btn: ['我知道了']
                                        }, function (index) {
                                            layer.close(index);
                                            // 恢复到上一个有效状态
                                            resetToLastValidState();
                                        });

                                        return false;
                                    }

                                    // 检查是否选择了多个互斥角色
                                    if (checkMultipleExclusiveRoles(checkedIds)) {
                                        isProcessingConflict = true;
                                        console.log("检测到多个互斥角色");

                                        // 显示提示信息
                                        layer.confirm('带星号的角色只允许选择1个', {
                                            icon: 0,
                                            title: '角色互斥提醒',
                                            btn: ['我知道了']
                                        }, function (index) {
                                            layer.close(index);
                                            // 恢复到上一个有效状态
                                            resetToLastValidState();
                                        });

                                        return false;
                                    }

                                    // 更新最后一个有效状态
                                    lastValidState = [...checkedIds];
                                    console.log("保存有效状态:", lastValidState);
                                }
                            });

                            // 设置初始选中状态
                            tree.setChecked('demo-id-1', deduplicatedRole_ids);

                            // 初始化最后一个有效状态
                            lastValidState = [...deduplicatedRole_ids];
                            console.log("初始有效状态:", lastValidState);
                        } else {
                            layer.msg('获取数据失败: ' + response.msg, { icon: 2 });
                        }
                    },
                    error: function (err) {
                        layer.msg('请求失败，请稍后重试', { icon: 2 });
                    }
                });
            }

            // 获取已选中项IDS
            layer.load(2);
            $.ajax({
                url: '/admin/user/list_low',
                type: 'POST',
                data: {
                    id: user_id
                },
                success: function (res) {
                    layer.closeAll('loading');
                    if (res.code == 200) {
                        let data = res.data;
                        let uniqueRole_ids = new Set();
                        data.forEach(role => {
                            console.log(role.Role_id);
                            let Role_idsArray = role.Role_id.split(',');
                            Role_idsArray.forEach(Role_id => {
                                uniqueRole_ids.add(Number(Role_id.trim())); // 使用 trim() 去掉可能的空格
                            });
                        });
                        let deduplicatedRole_ids = Array.from(uniqueRole_ids);

                        // 调用渲染树形菜单函数
                        renderTree(deduplicatedRole_ids);
                    }
                },
                error: function (err) {
                    layer.closeAll('loading');
                    layer.msg(err.responseJSON.msg);
                }
            });

            // 按钮事件
            util.event('lay-on', {
                getChecked: function (othis) {
                    var checkedData = tree.getChecked('demo-id-1');
                    var checkedIds = extractIds(checkedData);

                    // 检查是否存在互斥角色冲突
                    if (checkExclusiveRoles(checkedIds)) {
                        layer.alert('带星号的角色只允许选择1个', {
                            icon: 0,
                            title: '无法保存'
                        });
                        return;
                    }

                    // 检查是否选择了多个互斥角色
                    if (checkMultipleExclusiveRoles(checkedIds)) {
                        layer.alert('带星号的角色只允许选择1个', {
                            icon: 0,
                            title: '无法保存'
                        });
                        return;
                    }

                    let Role_id = checkedIds.join(',');
                    // 询问用户是否同意绑定
                    layer.confirm('确认绑定吗？', {
                        title: '请确认',
                        btn: ['确认', '取消'],
                    }, function (index) {
                        layer.close(index);
                        layer.load(2);
                        $.ajax({
                            url: '/admin/user/bind_roles',
                            type: 'POST',
                            data: {
                                id: user_id,
                                role_ids: Role_id
                            },
                            success: function (res) {
                                layer.closeAll('loading');
                                if (res.code == 200) {
                                    layer.msg(res.msg);
                                    setTimeout(function () {
                                        parent.layer.close(index);
                                        parent.layui.table.reload('role_list');
                                    }, 500);
                                } else {
                                    layer.msg('绑定失败，请稍后重试');
                                }
                            },
                            error: function (err) {
                                layer.closeAll('loading');
                                layer.msg(err.responseJSON.msg);
                            }
                        });
                    })
                }
            });
        });
    </script>
</body>

</html>