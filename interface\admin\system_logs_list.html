<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 系统日志列表</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
</head>

<body>
    <div class="layui-layout layui-layout-admin">
        <!-- 头部 -->
        <div class="layui-header">
            <div class="layui-logo">幸年堂</div>
            <ul class="layui-nav layui-layout-right">
                <li class="layui-nav-item" style="background-color:transparent!important;padding-right:10px">
                    <a href="javascript:;" style="display:flex;align-items:center;color:#fff"><img
                            src="/dist/images/user.png" class="layui-nav-img"><span class="user_name"></span></a>
                    <dl class="layui-nav-child pop_menu">
                        <dd><i class="iconfont">&#xe62f;</i><a href="change_password.html#7">用户资料</a></dd>
                        <dd><i class="iconfont">&#xe601;</i><a href="javascript:void(0)" class="refresh_cache">刷新缓存</a>
                        </dd>
                        <dd><i class="iconfont">&#xe634;</i><a href="login_out.html">退出登录</a></dd>
                    </dl>
                </li>
            </ul>
        </div>
        <!-- 导航 -->
        <ul class="layui-nav layui-nav-tree layui-nav-side" id="menuContainer" lay-bar="disabled" lay-accordion>
            <div style="margin: 50px 0;color: #bbb;text-align: center;">菜单加载中...</div>
        </ul>
        <!-- 主体内容 -->
        <div class="layui-body">
            <div class="body_child">
                <div class="layui-panel">

                    <!-- 添加搜索模块 -->
                    <div class="layui-form" style="position: absolute;top:50px;right: 100px;">
                        <div class="layui-row">
                            <div class="layui-col-md9">
                                <input type="text" name="key" placeholder="请输入关键词" autocomplete="off"
                                    class="layui-input" lay-affix="clear">
                            </div>
                            <div class="layui-col-md3">
                                <div class="layui-input-inline" style="margin-left: 10px;">
                                    <button class="layui-btn" lay-submit lay-filter="search"
                                        style="width: 120px;">筛选</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- 搜索模块结束 -->

                    <div class="layui-card-header">
                        <div class="layui-row" style="padding-top:10px;">
                            <div class="layui-col-md11 layui-col-sm10" style="display: flex;">
                                <div>系统日志列表</div>
                            </div>
                        </div>
                    </div>
                
                    <div class="layui-padding-3">
                        <div id="data_search">
                            <div class="layui-tab layui-tab-brief" lay-filter="logType">
                                <ul class="layui-tab-title">
                                    <li class="layui-this">全部</li>
                                    <li>员工</li>
                                    <li>患者</li>
                                    <li>系统</li>
                                </ul>
                            </div>
                        </div>

                        <table id="mytable" lay-filter="mytable" class="layui-table" lay-skin="row"></table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script type="text/html" id="TPL-bar">
            <button class="layui-btn layui-btn-xs" lay-event="detail">详情</button>
            <button class="layui-btn layui-btn-xs layui-btn-danger" lay-event="del">删除</button>
    </script>

    <script>
        layui.use(['element', 'layer', 'util'], function () {
            var element = layui.element;
            var form = layui.form;
            var layer = layui.layer;
            var util = layui.util;
            var table = layui.table;
            var $ = layui.$;

            // 页面顶部插入缓存的用户数据
            let local_userinfo = localStorage.getItem('local_userinfo');
            let user_name = "";
            if (local_userinfo) {
                local_userinfo = JSON.parse(local_userinfo);
                user_name = local_userinfo.Name + " ( " + local_userinfo.Roles + " )";
            } else {
                user_name = "未登录";
            }
            $('.user_name').text(user_name);
            render_menu($);
            $('.refresh_cache').click(function () {
                refresh_cache($);
            });

            // 当前选中的日志类型
            var currentType = '';

            // 监听Tab切换
            element.on('tab(logType)', function (data) {
                switch (data.index) {
                    case 0: currentType = ''; break;
                    case 1: currentType = '0'; break;
                    case 2: currentType = '1'; break;
                    case 3: currentType = '2'; break;
                }
                table.reload('mytable', {
                    where: {
                        type: currentType,
                        key: $('input[name="key"]').val()
                    },
                    page: {
                        curr: 1
                    }
                });
            });

            // 加载用户数据
            var users = {};
            function loadUserData(id, userId, type) {
                // 转换参数类型为数字
                userId = parseInt(userId);
                type = parseInt(type);

                // 生成缓存key
                const cacheKey = `${type}_${userId}`;
                if (users[cacheKey]) return;

                // 系统日志
                if (type === 2) {
                    users[cacheKey] = "<div class='green_font'>系统自动化</div>";
                    return;
                }
                // 患者日志
                if (type === 1) {
                    if (userId === 0) {
                        users[cacheKey] = "未登录患者";
                        return;
                    }
                    $.ajax({
                        url: '/admin/patient_profile/detail',
                        type: 'post',
                        async: false,
                        data: {
                            id: userId
                        },
                        success: function (res) {
                            if (res.code === 200 && res.data) {
                                users[cacheKey] = res.data.Name || "未知患者";
                            } else {
                                users[cacheKey] = "未知患者";
                            }
                        },
                        error: function () {
                            users[cacheKey] = "未知患者";
                        }
                    });
                }
                // 员工日志
                if (type === 0) {
                    $.ajax({
                        url: '/admin/user/list_low',
                        type: 'post',
                        async: false,
                        data: {
                            id: userId
                        },
                        success: function (res) {
                            if (res.code === 200 && res.data && res.data.length > 0) {
                                users[cacheKey] = res.data[0].Name;
                            } else {
                                users[cacheKey] = "未知用户";
                            }
                        },
                        error: function () {
                            users[cacheKey] = "未知用户";
                        }
                    });
                }
            }

            //引入TABLE模块
            layer.load(2);
            table.render({
                elem: '#mytable'
                , even: true
                , url: '/admin/system_logs/list'
                , method: 'post'
                , response: {
                    statusName: 'code'
                    , statusCode: 200
                    , msgName: 'msg'
                    , countName: 'count'
                    , dataName: 'data'
                }
                , cols: [[
                    { field: 'ID', width: 80, title: 'ID', align: 'center' }
                    , {
                        field: 'Type', width: 120, title: '日志类型', templet: function (d) {
                            switch (d.Type) {
                                case 0: return '员工日志';
                                case 1: return '患者日志';
                                case 2: return '系统日志';
                                default: return '未知类型';
                            }
                        }
                    }
                    , {
                        field: 'User_id', width: 120, title: '操作人', templet: function (d) {
                            const cacheKey = `${d.Type}_${d.User_id}`;
                            if (!users[cacheKey]) {
                                loadUserData(d.ID, d.User_id, d.Type);
                            }
                            return users[cacheKey] || '未知用户';
                        }
                    }
                    , { field: 'Contents', title: '日志内容' }
                    , {
                        field: 'Create_time', width: 180, title: '发生时间', templet: function (d) {
                            return d.Create_time.replace('T', ' ').replace('Z', '');
                        }
                    }
                    , { title: '操作', align: 'center', toolbar: '#TPL-bar', width: 200, fixed: 'right' }
                ]]
                , page: true
                , limit: 12
                , done: function () {
                    layer.closeAll('loading');
                }
            });

            //监听工具条
            table.on('tool(mytable)', function (obj) {
                var data = obj.data;
                if (obj.event === 'detail') {
                    layer.open({
                        type: 2,
                        title: '系统日志详情',
                        area: ['800px', '720px'],
                        shadeClose: true,
                        content: 'system_logs_detail.html?id=' + data.ID
                    });
                } else if (obj.event === 'del') {
                    layer.confirm('确定要删除该日志吗？', function (index) {
                        layer.close(index);
                        layer.load(2);
                        $.ajax({
                            url: '/admin/system_logs/del',
                            type: 'POST',
                            data: { id: data.ID },
                            success: function (res) {
                                layer.closeAll('loading');
                                if (res.code === 200) {
                                    layer.msg('删除成功', { icon: 1, time: 1000 });
                                    table.reload('mytable');
                                } else {
                                    layer.msg(res.msg || '删除失败', { icon: 2 });
                                }
                            },
                            error: function (res) {
                                layer.closeAll('loading');
                                layer.msg(res.responseJSON.msg, { icon: 2, time: 1000 });
                            }
                        });
                    });
                }
            });

            //监听搜索
            form.on('submit(search)', function (data) {
                layer.load(2);
                data.field.type = currentType; // 添加当前选中的类型
                table.reload('mytable', {
                    where: data.field
                    , page: {
                        curr: 1
                    }
                });
                return false;
            });
        });
    </script>
</body>

</html>