<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>药品选择-修改</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <script src="/dist/layui/layui.js"></script>
    <style>
        .layui-table-cell {
            height: auto;
            line-height: 28px;
        }
        
        /* 添加的新样式 */
        .layui-btn-group {
            margin-bottom: 10px;
        }
        
        .layui-btn-group .layui-btn {
            margin-right: 5px;
        }
        
        .layui-input-group {
            display: flex;
            margin-bottom: 10px;
        }
        
        .layui-input-group .layui-input {
            margin-right: 5px;
        }
    </style>
</head>

<body style="padding: 10px;">
    <div class="layui-form layui-row layui-col-space10">
        <!-- 搜索框 -->
        <div class="layui-col-md4">
            <div class="layui-input-group">
                <input type="text" placeholder="请输入药品名称" class="layui-input" id="searchKey">
                <div class="layui-input-suffix">
                    <button type="button" class="layui-btn" id="searchBtn">搜索</button>
                </div>
            </div>
        </div>
    </div>
    
    <table id="medsTable" lay-filter="medsTable"></table>

    <div
        style="position: fixed; bottom: 0; left: 0; right: 0; background-color: #fff; padding: 10px; text-align: center; box-shadow: 0 -2px 10px rgba(0,0,0,0.1);">
        <button type="button" class="layui-btn" id="submitBtn">确认保存该处方药品</button>
    </div>

    <script>
        layui.use(['table', 'layer', 'form'], function () {
            var table = layui.table;
            var layer = layui.layer;
            var form = layui.form;
            var $ = layui.$;

            // 从父窗口获取数据
            var parentData = window.parent.drugModalData || {};
            var prescriptionId = parentData.prescriptionId;
            var existingDrugs = parentData.existingDrugs || [];

            // 存储所有输入的剂量值和对应的药品信息
            var drugData = {};
            
            // 将已选药品数据转换为 map
            existingDrugs.forEach(function (drug) {
                drugData[drug.wh_drug_id] = {
                    quantity: drug.quantity,
                    drug_id: drug.drug_id
                };
            });

            // 渲染表格
            table.render({
                elem: '#medsTable',
                url: '/admin/warehouse_drug/list',
                method: 'post',
                page: true,
                limit: 9,
                cols: [[
                    { field: 'ID', title: '库存ID', sort: true, align: 'center' },
                    { field: 'Drug_id', title: '药品ID', sort: true, align: 'center' },
                    { field: 'Drug_name', title: '药品名称', width: 200, sort: true },
                    { field: 'Price', title: '单价', sort: true, align: 'center' },
                    { field: 'Quantity', title: '库存量', sort: true, align: 'center' },
                    {
                        title: '数量', width: 120, templet: function (d) {
                            // 确保drugData中有完整的数据
                            if (!drugData[d.ID]) {
                                drugData[d.ID] = {
                                    drug_id: d.Drug_id,
                                    quantity: 0
                                };
                            }
                            var value = drugData[d.ID].quantity || '';
                            return '<input type="number" class="layui-input drug-quantity" ' +
                                   'data-id="' + d.ID + '" ' +
                                   'data-drug-id="' + d.Drug_id + '" ' +
                                   'value="' + value + '" min="0" step="0.1">';
                        }
                    }
                ]],
                response: {
                    statusName: 'code',
                    statusCode: 200,
                    msgName: 'msg',
                    countName: 'count',
                    dataName: 'data'
                },
                done: function (res) {
                    // 为新渲染的输入框绑定事件
                    $('.drug-quantity').each(function () {
                        var input = $(this);
                        var id = parseInt(input.data('id'));
                        var drug_id = parseInt(input.data('drug-id'));

                        // 确保drugData中有完整的数据
                        if (!drugData[id]) {
                            drugData[id] = {
                                drug_id: drug_id,
                                quantity: 0
                            };
                        } else if (!drugData[id].drug_id) {
                            drugData[id].drug_id = drug_id;
                        }

                        // 设置已存储的值
                        if (drugData[id].quantity) {
                            input.val(drugData[id].quantity);
                        }

                        // 绑定输入事件
                        input.on('input', function () {
                            var val = parseFloat($(this).val()) || 0;
                            
                            // 限制最小值为0.1（如果有值的话）
                            if (val < 1) {
                                layer.msg('数量不能小于1', { icon: 2 });
                                $(this).val(1);
                                val = 1;
                            }
                            
                            // 限制小数点后一位
                            if (val.toString().split('.')[1]?.length > 1) {
                                val = parseFloat(val.toFixed(1));
                                $(this).val(val);
                            }

                            // 存储输入值和药品信息
                            if (val > 0) {
                                drugData[id] = {
                                    quantity: val,
                                    drug_id: drug_id  // 确保保存drug_id
                                };
                            } else {
                                delete drugData[id];
                            }
                        });
                    });

                    // 保存当前页的数据到drugData，确保drug_id存在
                    res.data.forEach(function(row) {
                        if (!drugData[row.ID]) {
                            drugData[row.ID] = {
                                drug_id: row.Drug_id,
                                quantity: 0
                            };
                        } else if (!drugData[row.ID].drug_id) {
                            drugData[row.ID].drug_id = row.Drug_id;
                        }
                    });
                }
            });

            // 搜索按钮点击事件
            $('#searchBtn').on('click', function() {
                table.reload('medsTable', {
                    where: {
                        page:1,
                        key: $('#searchKey').val()
                    }
                });
            });

            // 搜索框回车事件
            $('#searchKey').on('keypress', function(e) {
                if (e.which === 13) {
                    $('#searchBtn').click();
                }
            });

            // 提交按钮点击事件
            $('#submitBtn').on('click', function () {
                var selectedData = [];

                // 遍历所有存储的药品数据
                for (var id in drugData) {
                    var data = drugData[id];
                    if (data.quantity && data.quantity > 0 && data.drug_id) {  // 确保有drug_id
                        selectedData.push({
                            wh_drug_id: parseInt(id),
                            drug_id: data.drug_id,
                            single_dose: data.quantity
                        });
                    }
                }

                if (selectedData.length === 0) {
                    layer.msg('请至少选择一个药品并输入数量', { icon: 2 });
                    return;
                }

                // 检查是否所有选中的药品都有drug_id
                var missingDrugId = selectedData.some(function(drug) {
                    return !drug.drug_id;
                });

                if (missingDrugId) {
                    layer.msg('部分药品数据不完整，请刷新页面重试', { icon: 2 });
                    return;
                }

                // 按 wh_drug_id 倒序排序
                selectedData.sort(function(a, b) {
                    return b.wh_drug_id - a.wh_drug_id;
                });

                // 保存到数据库
                layer.load(2);
                $.ajax({
                    url: '/admin/prescription/edit_drug_save',
                    type: 'POST',
                    data: {
                        pre_id: prescriptionId,
                        drugs: JSON.stringify(selectedData)
                    },
                    success: function(res) {
                        layer.closeAll('loading');
                        if (res.code === 200) {
                            layer.msg('保存成功', { icon: 1 });
                            // 确保父窗口有这个函数再调用
                            if (typeof window.parent.loadPrescriptionDrugs === 'function') {
                                window.parent.loadPrescriptionDrugs(prescriptionId);
                            }
                            // 关闭弹窗
                            var index = parent.layer.getFrameIndex(window.name);
                            parent.layer.close(index);
                            parent.layer.msg('药品数据保存成功', { icon: 1 });
                        } else {
                            layer.msg(res.msg || '保存失败', { icon: 2 });
                        }
                    },
                    error: function(xhr) {
                        layer.closeAll('loading');
                        try {
                            var response = JSON.parse(xhr.responseText);
                            layer.msg('保存失败：' + response.msg, { icon: 2 });
                        } catch (e) {
                            layer.msg('保存失败：' + xhr.responseText, { icon: 2 });
                        }
                    }
                });
            });
        });
    </script>
</body>

</html>