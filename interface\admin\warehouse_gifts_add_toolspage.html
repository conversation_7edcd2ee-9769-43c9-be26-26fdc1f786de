<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>赠品选择</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <script src="/dist/layui/layui.js"></script>
    <style>
        .layui-table-cell {
            height: auto;
            line-height: 28px;
        }
    </style>
</head>

<body style="padding: 10px;">
    <table id="giftTable" lay-filter="giftTable"></table>

    <div
        style="position: fixed; bottom: 0; left: 0; right: 0; background-color: #fff; padding: 10px; text-align: center; box-shadow: 0 -2px 10px rgba(0,0,0,0.1);">
        <button type="button" class="layui-btn" id="submitBtn">确认选择</button>
    </div>

    <script>
        layui.use(['table', 'layer', 'form'], function () {
            var table = layui.table;
            var layer = layui.layer;
            var form = layui.form;
            var $ = layui.$;

            // 渲染表格
            table.render({
                elem: '#giftTable',
                url: '/admin/warehouse_gifts/list',
                method: 'post',
                height: 'full-80',
                page: true,
                cols: [[
                    { field: 'ID', title: '库存ID', sort: true },
                    { field: 'Gift_id', title: '商品ID', sort: true },
                    { field: 'Gift_name', title: '商品名称', sort: true },
                    { field: 'Price', title: '单价', sort: true },
                    { field: 'Quantity', title: '库存量', sort: true },
                    { field: 'Shelf', title: '存放位置', sort: true,templet:function(){
                        return '<div style="color:#999;text-decoration: line-through;">已废弃</div>'
                    } },
                    {
                        title: '选择数量', width: 120, templet: function () {
                            return '<input type="number" class="layui-input gift_nums" min="0">';
                        }
                    }
                ]],
                response: {
                    statusName: 'code'
                    , statusCode: 200
                    , msgName: 'msg'
                    , countName: 'count'
                    , dataName: 'data'
                },
                done: function () {
                    // 限制输入数量不超过库存量
                    $('.gift_nums').on('input', function () {
                        var max = $(this).closest('tr').find('td[data-field="Quantity"]').text();
                        var val = $(this).val();
                        if (val > parseInt(max)) {
                            layer.msg('不能超过库存量', { icon: 2 });
                            $(this).val(max);
                        }
                    });
                }
            });

            // 提交按钮点击事件
            $('#submitBtn').on('click', function () {
                var selectedData = [];

                // 遍历表格中所有行
                $('.layui-table-body tr').each(function () {
                    var quantity = $(this).find('.gift_nums').val();
                    if (quantity && parseInt(quantity) > 0) {
                        var rowData = {
                            id: parseInt($(this).find('td[data-field="ID"]').text()),
                            wh_gift_id: parseInt($(this).find('td[data-field="ID"]').text()),
                            gift_id: parseInt($(this).find('td[data-field="Gift_id"]').text()),
                            name: $(this).find('td[data-field="Gift_name"]').text(),
                            quantity: parseInt(quantity),
                            price: parseFloat($(this).find('td[data-field="Price"]').text())
                        };
                        selectedData.push(rowData);
                    }
                });

                if (selectedData.length === 0) {
                    layer.msg('请至少选择一个赠品并输入数量', { icon: 2 });
                    return;
                }

                // 获取父页面的jQuery对象
                var parent$ = window.parent.layui.$;
                var giftTableBody = parent$('#gift_table_body');

                // 清空母页的数据
                giftTableBody.empty();

                // 遍历选中的数据
                selectedData.forEach(function (item) {
                    // 添加到父页面表格
                    var newRow = `
                        <tr>
                            <td>${item.wh_gift_id}</td>
                            <td>${item.gift_id}</td>
                            <td>${item.name}</td>
                            <td><input type="number" value="${item.quantity}" class="layui-input gift_nums" min="1" style="max-width:300px;"></td>
                            <td>${item.price}</td>
                            <td>
                                <button type="button" class="layui-btn layui-btn-danger layui-btn-sm delete-gift">删除</button>
                            </td>
                        </tr>
                    `;
                    giftTableBody.append(newRow);

                    // 绑定数量变化事件
                    // giftTableBody.find('tr:last .gift_nums').on('input', function() {
                    //     var quantity = parseInt($(this).val()) || 0;
                    //     var price = parseFloat($(this).closest('tr').find('td:eq(5)').text());
                    //     $(this).closest('tr').find('td:eq(6)').text((quantity * price).toFixed(2));
                    // });

                    // 绑定删除按钮事件
                    giftTableBody.find('tr:last .delete-gift').on('click', function() {
                        $(this).closest('tr').remove();
                    });
                });

                // 关闭弹窗
                var index = parent.layer.getFrameIndex(window.name);
                parent.layer.close(index);
            });
        });
    </script>
</body>

</html>