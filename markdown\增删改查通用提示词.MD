# 增删改查代码生成任务

## 1. 任务背景
 - 基于现有的标准化增删改查模板，生成功能相同的增删改查功能模块。这个模块包括前端页面和后端接口的完整实现。
 - 后端框架：Golang
 - 前端框架：LayUI
 - 项目根目录：E:\works\go\
 - 后端通用工具包：`pkg/common/common.go`
 - 后端数据库操作包：`pkg/database/mysql.go`

## 2. 参考文件
### 2.1 后端参考
- 业务逻辑参考文件：`internal/app/admin/normal_template.go`中的Article_category_*相关函数
- 路由文件：`internal/routes/admin.go`
- 业务逻辑修改文件：`internal/app/admin/admin_extra_2.go`

### 2.2 前端参考
目录：`interface/admin/`
参考文件：
- 列表页：article_category_list.html
- 新增页：article_category_add.html
- 编辑页：article_category_edit.html
- 详情页：article_category_detail.html
要求：
- 严格按照前端页面的命名规范、代码规范来模仿
- 模态框严格按照被模仿页面的模态框样式来实现，如：尺寸、点击空白处关闭（shadeClose: true）等
- 遇到textarea标签的，不要加layui的layedit模块，因为它已被弃用，使用原生的textarea标签即可，不要加style属性设置它的高度，使用.layui-textarea样式即可。

## 3. 数据表结构信息
执行代码生成任务前，请等待接收以下信息：

### 3.1 数据库表名
- 默认使用模块名作为表名（除非特别说明）,例如：模块名为book，则表名为book

### 3.2 字段信息
用户将提供类似如下格式的字段信息：
```
字段名      数据类型    字段说明
id          int         ID
title       varchar     标题
create_time timestamp   创建时间
```

### 3.3 字段用途
提供的字段信息将用于：
1. 后端函数实现：
   - 数据库查询字段
   - 返回数据结构
2. 前端页面实现：
   - 列表页显示字段
   - 表单字段验证
   - 详情页展示
   - 编辑页表单

### 3.4 搜索功能配置
用户将指定：
1. 搜索关键词参数名：默认使用'key'
2. 需要进行模糊匹配的字段：
   - 可能是单个字段（如：name）
   - 可能是多个字段（如：name, description）
   - 示例SQL片段：
     ```sql
     -- 单字段搜索
     WHERE name LIKE ?
     
     -- 多字段搜索
     WHERE name LIKE ? OR description LIKE ?
     ```

## 4. 开发规范
### 4.1 命名规范
假设模块名为 {module}，则：
- 后端函数命名：
  - 列表：{Module}_list()
  - 新增：{Module}_add()
  - 修改：{Module}_edit()
  - 删除：{Module}_del()
  - 详情：{Module}_detail()

- API路由命名：
  - 列表：/admin/{module}/list
  - 新增：/admin/{module}/add
  - 修改：/admin/{module}/edit
  - 删除：/admin/{module}/del
  - 详情：/admin/{module}/detail

- 前端页面命名：
  - 列表：{module}_list.html
  - 新增：{module}_add.html
  - 修改：{module}_edit.html
  - 详情：{module}_detail.html

### 4.2 代码实现步骤
1. 分析数据表结构
2. 参考Article_category模块实现对应的后端函数
3. 在路由文件中添加新模块的路由配置
4. 基于LayUI实现前端页面

### 4.2.1 后端权限控制
在实现后端函数时，开头鉴权部分是固定的，你不要改动。
可以修改的部分是api_id的值（权限ID），如果用户不明确提供api_id的话，你就将api_id的值设置为config.NormalPerm
 - 函数开头示例1：
    ```go
    func {module}_list(w http.ResponseWriter, r *http.Request) {
        api_id := 90
        _, isLogin := common.Check_Perm(w, r, api_id)
        if !isLogin {
            return
        }
        // 其他代码
        ```
 - 函数开头示例2：
    ```go
    func {module}_list(w http.ResponseWriter, r *http.Request) {
        api_id := config.NormalPerm
        _, isLogin := common.Check_Perm(w, r, api_id)
        if !isLogin {
            return
        }
        // 其他代码
    ```
- 当函数后续需要user_id的变量时，请在鉴权环节这样写：
    ```go
    func {module}_list(w http.ResponseWriter, r *http.Request) {
        api_id := 90
        session, isLogin := common.Check_Perm(w, r, api_id)
        if !isLogin {
            return
        }
        user_id := session.Values["id"] //注意，是session.Values["id"]，不是session.Values["user_id"]
        // 其他代码
        ```

### 4.3 注意事项
1. 确保API返回的字段首字母大写
2. 实现基本的参数验证和错误处理
3. 保持与参考代码一致的错误处理方式
4. 遵循LayUI的UI设计规范
5. 实现必要的数据验证和提示信息

### 4.4 前端开发规范补充
1. jQuery使用规范：
   - jQuery的`$`符号必须在`layui.use()`内部使用
   - 正确示例：
     ```javascript
     layui.use(['form', 'layer'], function(){
         var $ = layui.$;  // 在这里声明$
         // 这里可以安全使用$
         $('#myElement').click(function(){});
     });
     ```
   - 错误示例：
     ```javascript
     // 错误：在layui.use外使用$
     $(document).ready(function(){});
     
     layui.use(['form', 'layer'], function(){
         // 内部代码
     });
     ```

2. URL参数获取：
   - 必须使用系统封装的request对象获取URL参数
   - 正确示例：`var id = request.get('id');`
   - 错误示例：~~`var id = getQueryString('id');`~~
   - 修改页面(edit)在向服务端提交修改数据时，务必按照模仿页面的样子，将id的值携带上

3. 表单字段命名规则：
   - 所有表单字段的name属性必须与后端API返回的字段名完全匹配（包括大小写）
   - 示例：如果API返回字段为"Title"，则表单字段应写为：
     ```html
     <input type="text" name="Title" class="layui-input">
     ```
   - 注意：这是因为后端API返回的JSON字段均采用首字母大写的命名方式
4. 前端接收后端API返回参数中，判断其成功的code值为200，不是0；判断失败的是500；判断请求出错的为error，请参照以下示例：
   ```javascript
   layer.load(2);
   $.ajax({
       url: '/api/admin/article_category/list',
       type: 'POST',
       data: { id: id },
       success: function(res) {
           layer.closeAll('loading');
           if (res.code == 200) {
               // 成功处理
           } else {
               //失败处理
               layer.msg(res.msg, { icon: 2, time: 1000 });
           }
       },
       error: function(err) {
         //关闭LOADING，提示失败信息
        layer.closeAll('loading');
        layer.msg(err.responseJSON ? err.responseJSON.msg : '请求失败', { icon: 2, time: 1000 });
       }
   });
   ```

### 4.5 父级数据关联处理
在涉及父级关联的场景（如分类的父级分类）：
1. 系统会自动识别名为"Pid"的字段为父级ID字段
2. 在add/edit页面中，将自动从对应的list接口获取父级数据
3. 示例：如果模块为article_category，系统将自动：
   - 从"/admin/article_category/list"获取父级数据
   - 渲染为下拉选择框
   - 在编辑时自动选中当前记录的父级

## 5. 输出要求
在接收到具体的表结构和搜索配置信息后，请按照以下步骤处理：
1. 分析表结构和搜索需求，确认：
   - 必填字段
   - 需要验证的字段
   - 特殊处理的字段（如时间格式）
   - 搜索匹配字段
2. 根据字段特性和搜索需求生成相应的：
   - 后端验证和搜索逻辑
   - 前端表单验证
   - 数据展示格式
   - 搜索框实现
3. 按顺序提供完整代码实现：
   - 后端函数实现（admin_extra_2.go）
   - 路由配置（admin.go）
   - 前端页面实现（4个html文件）

每个代码块都需要包含必要的注释说明。
在修改前后端时，若发现前端页面已经存在，或者后端函数已经存在等冲突等问题时，请暂停操作，向我发出询问，等待确认。

## 6. 重要补充规范

### 6.1 列表页父ID筛选规范
1. 功能说明：
   - 列表页可能需要支持父ID（Pid）筛选
   - 该功能默认不启用，除非用户明确指定："列表页有父ID传值，其参数名为pid"
   
2. 实现要求：
   - 前端实现：
     - 在页面加载时获取URL中的pid参数：`var pid = request.get('pid');`
     - 将pid参数传递给后端接口
   
   - 后端实现：
     - 接收pid参数
     - 代码示例：
       ```go
         pid := r.FormValue("pid")
         if pid != "" {
            attSql = " and pid = ? "
            params = append(params, pid)
         }
       ```

### 6.2 前端模板严格遵循规范
1. 头部模板规范：
   必须严格按照参考模板格式实现，各前端模仿页面的头部标签，仅允许修改title内容，如列表页面：
   ```html
   <head>
       <meta charset="utf-8">
       <title>{修改这里的标题}</title>
       <meta name="renderer" content="webkit">
       <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
       <meta name="viewport" content="width=device-width,initial-scale=1">
       <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
       <link href="/dist/layui/css/layui.css" rel="stylesheet">
       <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
       <script src="/dist/layui/layui.js"></script>
       <script src="/dist/js/main.js"></script>
   </head>
   ```

2. 禁止事项：
   - 不得更改任何资源文件的路径
   - 不得删除或修改任何meta标签
   - 不得改变脚本和样式表的加载顺序

### 6.3 后端SQL查询约束
1. 基本原则：
   - 严格遵循必要的表关联
   - 禁止主观臆测添加表关联
   - 不确定的关联关系必须与用户确认

2. SQL编写规范：
   - 禁止主动推测外键关系
   - 需要关联其他表时，用户会主动指定关联条件，否则禁止使用关联查询

### 6.4 排序字段处理规范
1. 功能说明：
   - 当表结构中包含sort字段时，需要实现自动排序逻辑
   - 该功能仅在数据表包含sort字段时启用

2. 实现要求：
   - 前端实现：
     - add页面中不显示sort字段输入框
     - edit页面可以显示并允许修改sort值
   
   - 后端实现：
     ```go
     // 在add函数中，自动处理sort值
     if sortField存在 {
         var maxSort int
         err := db.Table(tableName).Select("COALESCE(MAX(sort), 0) as max_sort").Scan(&maxSort).Error
         if err != nil {
             maxSort = 0
         }
         data.Sort = maxSort + 10  // 默认递增10
     }
     ```

3.后端开发规范：
     - sort字段的处理必须在后端完成，不依赖前端传值
     - 不允许引入新的第三方库，因为现有的函数已经完全能满足增、删、改、查、分页、搜索、排序等需求
     - 所有响应必须使用 common.JSONResponse 函数
     - 所有数据库操作必须使用现有的 database 包中的函数