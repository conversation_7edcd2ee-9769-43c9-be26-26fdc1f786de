<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 供应商管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
    <style>
        .layui-table-cell {
            height: auto;
            line-height: 28px;
        }
    </style>
</head>

<body>
    <div class="layui-layout layui-layout-admin">
        <!-- 头部 -->
        <div class="layui-header">
            <div class="layui-logo">幸年堂</div>
            <ul class="layui-nav layui-layout-right">
                <li class="layui-nav-item" style="background-color:transparent!important;padding-right:10px">
                    <a href="javascript:;" style="display:flex;align-items:center;color:#fff"><img
                            src="/dist/images/user.png" class="layui-nav-img"><span class="user_name"></span></a>
                    <dl class="layui-nav-child pop_menu">
                        <dd><i class="iconfont">&#xe62f;</i><a href="change_password.html#7">用户资料</a></dd>
                        <dd><i class="iconfont">&#xe601;</i><a href="javascript:void(0)" class="refresh_cache">刷新缓存</a>
                        </dd>
                        <dd><i class="iconfont">&#xe634;</i><a href="login_out.html">退出登录</a></dd>
                    </dl>
                </li>
            </ul>
        </div>
        <!-- 导航 -->
        <ul class="layui-nav layui-nav-tree layui-nav-side" id="menuContainer" lay-bar="disabled" lay-accordion>
            <div style="margin: 50px 0;color: #bbb;text-align: center;">菜单加载中...</div>
        </ul>
        <!-- 主体内容 -->
        <div class="layui-body">
            <div class="body_child">
                <div class="layui-panel">
                    <div class="layui-card-header">
                        <div class="layui-row" style="padding-top:10px;">
                            <div class="layui-col-md11 layui-col-sm10" style="display: flex;">
                                <div>供应商列表</div>
                            </div>
                            <div class="layui-col-md1 layui-col-sm2" style="text-align: right;">
                                <button class="layui-btn layui-btn-primary layui-border-red perm_check_btn" lay-event="add" res_id="165" onclick="add()">
                                    <i class="layui-icon">&#xe654;</i> 添加
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="layui-padding-3">

                        <div id="data_search">


                            <div class="layui-form">
                                <div class="layui-row">

                                    <div class="layui-col-md3">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label">供应商</label>
                                            <div class="layui-input-block">
                                                <input type="text" name="name" placeholder="请输入供应商名称" autocomplete="off"
                                                    class="layui-input" lay-affix="clear">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="layui-col-md3">
                                        <div class="layui-form-item">
                                            <div class="layui-input-inline" style="margin-left: 10px;">
                                                <button class="layui-btn" lay-submit lay-filter="search"
                                                    style="width: 120px;">筛选</button>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                            </div>


                        </div>

                        <table id="mytable" lay-filter="mytable" class="layui-table" lay-skin="row"></table>
                    </div>
                </div>
                <br>
                <br>
            </div>
        </div>
    </div>

    <script type="text/html" id="TPL-bar">
            <button class="layui-btn layui-btn-xs perm_check_btn" lay-event="edit" res_id="166">编辑</button>
            <button class="layui-btn layui-btn-xs perm_check_btn" lay-event="detail" res_id="167">详情</button>
            <button class="layui-btn layui-btn-xs layui-btn-danger perm_check_btn" lay-event="del" res_id="166">删除</button>
    </script>

    <script>
        layui.use(['element', 'layer', 'util'], function () {
            var element = layui.element;
            var form = layui.form;
            var layer = layui.layer;
            var util = layui.util;
            var table = layui.table;
            var $ = layui.$;

            // 页面顶部插入缓存的用户数据
            let local_userinfo = localStorage.getItem('local_userinfo');
            let user_name = "";
            if (local_userinfo) {
                local_userinfo = JSON.parse(local_userinfo);
                user_name = local_userinfo.Name + " ( " + local_userinfo.Roles + " )";
            } else {
                user_name = "未登录";
            }
            $('.user_name').text(user_name);
            render_menu($);
            $('.refresh_cache').click(function () {
                refresh_cache($);
            });

            //引入TABLE模块
            layer.load(2);
            table.render({
                elem: '#mytable'
                , even: true
                , url: serverUrl + "/admin/supplier_drug/list"
                , method: 'post'
                , response: {
                    statusName: 'code'
                    , statusCode: 200
                    , msgName: 'msg'
                    , countName: 'count'
                    , dataName: 'data'
                }
                , cols: [[
                    { field: 'ID', title: 'ID', align: 'center', width: 80 }
                    , { field: 'Name', title: '供应商名称',minWidth:120 }
                    , { field: 'Code', title: '社会统一代码', width: 150 }
                    , { field: 'Contact_name', title: '联系人', width: 100 }
                    , { field: 'Contact_mobile', title: '手机号', width: 120 }
                    , { field: 'Contact_phone', title: '座机', width: 120 }
                    , { field: 'Address', title: '地址' }
                    , {
                        field: 'Status', title: '状态', align: 'center', width: 100, templet: function (d) {
                            return d.Status == 1 ? '<span class="layui-badge layui-bg-green">启用</span>' : '<span class="layui-badge layui-bg-gray">禁用</span>';
                        }
                    }
                    , { field: 'Create_time', title: '创建时间' }
                    , { field: 'Update_time', title: '更新时间' }
                    , { title: '操作', align: 'center', toolbar: '#TPL-bar', width: 280, fixed: 'right' }
                ]]
                , page: true
                , limit: 20
                , done: function () {
                    layer.closeAll('loading');
                }
            });

            //监听工具条
            table.on('tool(mytable)', function (obj) {
                var data = obj.data;
                if (obj.event === 'detail') {
                    layer.open({
                        type: 2,
                        title: '供应商详情',
                        area: ['1000px', '760px'],
                        shadeClose: true,
                        content: 'supplier_drug_detail.html?id=' + data.ID
                    });
                } else if (obj.event === 'edit') {
                    layer.open({
                        type: 2,
                        title: '编辑供应商',
                        shadeClose: true,
                        area: ['1000px', '860px'],
                        content: 'supplier_drug_edit.html?id=' + data.ID
                    });
                } else if (obj.event === 'del') {
                    layer.confirm('确定要删除该供应商吗？', function (index) {
                        layer.close(index);
                        // 发送删除请求
                        layer.load(2);
                        $.ajax({
                            url: serverUrl + '/admin/supplier_drug/del',
                            type: 'POST',
                            data: { id: data.ID },
                            success: function (res) {
                                layer.closeAll('loading');
                                if (res.code === 200) {
                                    layer.msg('删除成功', { icon: 1, time: 1000 });
                                    // 刷新表格
                                    table.reload('mytable');
                                } else {
                                    layer.msg(res.msg || '删除失败', { icon: 2 });
                                }
                            },
                            error: function (res) {
                                layer.closeAll('loading');
                                layer.msg(res.responseJSON.msg, { icon: 2, time: 1000 });
                            }
                        });
                    });
                }
            });

            //监听搜索
            form.on('submit(search)', function (data) {
                layer.load(2);
                table.reload('mytable', {
                    where: data.field
                    , page: {
                        curr: 1
                    }
                });
                return false;
            });
        });

        // 添加供应商
        function add() {
            layer.open({
                type: 2,
                title: '添加供应商',
                shadeClose: true,
                area: ['1000px', '860px'],
                content: 'supplier_drug_add.html'
            });
        }
    </script>
</body>

</html>