package database

import (
	"database/sql"
	"fmt"
	"log"

	_ "github.com/go-sql-driver/mysql"

	"mstproject/pkg/config"

	"github.com/jmoiron/sqlx"
)

// DB 实例化数据库连接
var DB *sqlx.DB

// InitDB 初始化数据库连接
func InitDB() *sqlx.DB {
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?parseTime=true", config.DBPatient, config.DBPassword, config.DBHost, config.DBPort, config.DBName)
	db, err := sqlx.Connect("mysql", dsn)
	if err != nil {
		log.Fatal("Unable to connect to the database:", err)
	}
	return db
}

// QueryRow 查询单行数据

func GetRow(query string, dest interface{}, args ...interface{}) error {
	row := DB.QueryRowx(query, args...)
	return row.StructScan(dest) // 将结果扫描到 dest
}

func GetAll(query string, dest interface{}, args ...interface{}) error {
	return DB.Select(dest, query, args...)
}

func GetOne(query string, dest interface{}, args ...interface{}) error {
	return DB.Get(dest, query, args...)
}

// Exec 执行写操作
func Query(query string, args ...interface{}) (sql.Result, error) {
	return DB.Exec(query, args...)
}

// Close 关闭数据库连接
func Close() {
	if DB != nil {
		DB.Close()
	}
}

// 执行事务
type SQLExec struct {
	Query string
	Args  []interface{}
}

func ExecuteTransaction(sqls []SQLExec) error {
	// 事务，普通版本
	tx, err := DB.Begin()
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %v", err)
	}

	// 遍历执行每条SQL语句
	for _, sqlExec := range sqls {
		_, err := tx.Exec(sqlExec.Query, sqlExec.Args...)
		if err != nil {
			tx.Rollback() // 发生错误，回滚事务
			return fmt.Errorf("failed to execute query: %v, error: %v", sqlExec.Query, err)
		}
	}

	// 提交事务
	err = tx.Commit()
	if err != nil {
		tx.Rollback() // 提交失败，回滚事务
		return fmt.Errorf("failed to commit transaction: %v", err)
	}

	return nil
}
func ExecuteTransaction_with_affectedRows(sqls []SQLExec) (int64, error) {
	// 事务，带影响行数版本
	tx, err := DB.Begin()
	if err != nil {
		return 0, fmt.Errorf("failed to begin transaction: %v", err)
	}

	var totalAffectedRows int64 = 0

	// 遍历执行每条SQL语句
	for _, sqlExec := range sqls {
		result, err := tx.Exec(sqlExec.Query, sqlExec.Args...)
		if err != nil {
			tx.Rollback() // 发生错误，回滚事务
			return 0, fmt.Errorf("failed to execute query: %v, error: %v", sqlExec.Query, err)
		}

		// 获取受影响的行数
		RowsAffected, _ := result.RowsAffected()
		totalAffectedRows += RowsAffected
	}

	// 提交事务
	err = tx.Commit()
	if err != nil {
		tx.Rollback() // 提交失败，回滚事务
		return 0, fmt.Errorf("failed to commit transaction: %v", err)
	}

	return totalAffectedRows, nil
}

// 新增：事务查询结构体 2025-01-24，新增处方时所用
type TxQuery struct {
	Query string
	Args  []interface{}
}

// 新增：执行事务并返回最后插入的ID和影响行数 2025-01-24，新增处方时所用
func ExecuteTxWithResult(queries []TxQuery) (lastInsertID int64, affectedRows int64, err error) {
	tx, err := DB.Beginx() // 使用 Beginx() 替代 Begin() 以获得更好的错误处理
	if err != nil {
		return 0, 0, fmt.Errorf("begin transaction failed: %v", err)
	}
	defer func() {
		if p := recover(); p != nil {
			tx.Rollback()
			panic(p) // 重新抛出 panic
		} else if err != nil {
			tx.Rollback()
		}
	}()

	for i, q := range queries {
		result, err := tx.Exec(q.Query, q.Args...)
		if err != nil {
			return 0, 0, fmt.Errorf("execute query failed: %v", err)
		}

		// 获取第一条INSERT语句的LastInsertId
		if i == 0 {
			lastInsertID, err = result.LastInsertId()
			if err != nil {
				return 0, 0, fmt.Errorf("get last insert id failed: %v", err)
			}
		}

		// 累计受影响行数
		rows, err := result.RowsAffected()
		if err != nil {
			return 0, 0, fmt.Errorf("get affected rows failed: %v", err)
		}
		affectedRows += rows
	}

	if err = tx.Commit(); err != nil {
		return 0, 0, fmt.Errorf("commit transaction failed: %v", err)
	}

	return lastInsertID, affectedRows, nil
}
