# 幸年堂系统 - 通用视觉升级摘要

本文档总结了系统界面视觉升级的核心要点，可作为后续页面美化的参考标准。

## 设计理念

- **玻璃态设计 (Glass Morphism)**: 半透明背景配合模糊效果，提升现代感
- **渐变色点缀**: 关键元素使用渐变色，增强视觉层次
- **微动效**: 为交互元素添加平滑过渡动画，提升用户体验
- **阴影分层**: 使用不同层次的阴影区分内容区块
- **圆角统一**: 统一使用适度圆角，保持界面柔和感

## CSS 样式规范

### 1. 全局样式

```css
body {
    background-color: #f5f7fa;
}
```

### 2. 区块标题样式

```css
.line_font {
    font-size: 17px;
    font-weight: bold;
    margin: 10px 0 20px 0;
    border-bottom: 1px solid rgba(0,0,0,0.05);
    padding: 10px;
    color: #333;
    position: relative;
}

.line_font:after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, #1E9FFF, #5FB878);
    border-radius: 3px;
}
```

### 3. 信息区块样式

```css
.info-section {
    background: rgba(255, 255, 255, 0.9);
    padding: 25px;
    border-radius: 8px;
    margin-bottom: 25px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
}

.info-section:hover {
    box-shadow: 0 8px 25px rgba(0,0,0,0.08);
    transform: translateY(-3px);
}
```

### 4. 信息项样式

```css
.info-label {
    color: #666;
    padding-right: 15px;
    text-align: right;
    width: 100px;
    display: inline-block;
    font-size: 14px;
}

.info-content {
    color: #333;
    font-weight: 500;
    position: relative;
    transition: all 0.3s;
}

.info-item {
    margin: 15px 0;
    line-height: 24px;
    transition: all 0.3s;
}

.info-item:hover {
    transform: translateX(5px);
}
```

### 5. 文本区域样式

```css
.text-area-content {
    background: rgba(249, 249, 249, 0.8);
    padding: 15px;
    border-radius: 8px;
    min-height: 60px;
    white-space: pre-wrap;
    border: 1px solid rgba(0,0,0,0.05);
    transition: all 0.3s;
}

.text-area-content:hover {
    background: rgba(249, 249, 249, 1);
    box-shadow: 0 3px 10px rgba(0,0,0,0.03);
}
```

### 6. 标签/徽章样式

```css
.perm_item_rows {
    display: inline-block;
    padding: 5px 12px;
    background: rgba(30, 159, 255, 0.1);
    border-radius: 4px;
    margin: 3px;
    color: #1E9FFF;
    transition: all 0.3s;
}

.perm_item_rows:hover {
    background: rgba(30, 159, 255, 0.2);
    transform: translateY(-2px);
}
```

### 7. 按钮样式优化

```css
.save_local {
    padding: 8px 20px;
    border-radius: 6px;
    transition: all 0.3s;
    background: linear-gradient(to right, #f5f5f5, #ffffff);
    border-color: #e6e6e6;
}

.save_local:hover {
    background: linear-gradient(to right, #ffffff, #f5f5f5);
    box-shadow: 0 3px 8px rgba(0,0,0,0.08);
    border-color: #d9d9d9;
}
```

### 8. 图片/二维码样式

```css
#img_avatar {
    width: 230px;
    height: 230px;
    border-radius: 12px;
    border: none;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: all 0.3s;
}

#img_avatar:hover {
    transform: scale(1.02);
    box-shadow: 0 8px 20px rgba(0,0,0,0.15);
}
```

### 9. 页面标题样式

```html
<div class="layui-card-header" style="border-bottom: 1px solid rgba(0,0,0,0.05); padding: 15px 20px;">
    <div class="layui-row" style="padding-top:5px;">
        <div class="layui-col-md8">
            <span style="font-size: 18px; font-weight: 500; color: #333; position: relative; padding-left: 12px;">
                <span style="position: absolute; left: 0; top: 0; bottom: 0; width: 4px; background: linear-gradient(to bottom, #1E9FFF, #5FB878); border-radius: 2px;"></span>
                页面标题
            </span>
        </div>
    </div>
</div>
```

## 错误提示样式

### 1. CSS 样式

```css
.error-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(5px);
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.error-overlay.show {
    opacity: 1;
}

.error-card {
    width: 400px;
    text-align: center;
    box-shadow: 0 2px 20px rgba(0,0,0,0.1);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.95);
    transform: translateY(20px);
    transition: all 0.3s ease;
}

.error-overlay.show .error-card {
    transform: translateY(0);
}
```

### 2. 错误提示 HTML 模板

```javascript
// 创建模糊覆盖层和错误信息显示
let errorMsg = err.responseJSON ? err.responseJSON.msg : '请求失败，请稍后重试';
let blurOverlay = `
    <div class="error-overlay">
        <div class="error-card layui-card">
            <div class="layui-card-body">
                <p style="font-size: 16px; padding: 20px 10px; color: #333;">${errorMsg}</p>
                <div style="padding: 10px 0 20px 0;">
                    <button class="layui-btn layui-btn-normal return-btn" style="padding: 0 30px;">返回</button>
                </div>
            </div>
        </div>
    </div>
`;
$('body').append(blurOverlay);

// 添加动画效果
setTimeout(function() {
    $('.error-overlay').addClass('show');
}, 10);

// 添加返回按钮点击事件
$('.return-btn').click(function () {
    $('.error-overlay').removeClass('show');
    setTimeout(function() {
        window.history.go(-1);
        $('.error-overlay').remove();
    }, 300);
});
```

## 实施建议

1. **统一样式表**: 考虑将这些样式整合到一个统一的CSS文件中，便于全局应用
2. **组件化**: 将常用UI元素（如错误提示）封装为可复用的组件
3. **渐进式应用**: 可以逐页面应用这些样式，确保系统整体视觉一致性
4. **响应式优化**: 确保在不同设备上都能获得良好的视觉体验
5. **性能考量**: 适度使用动画和过渡效果，避免在低性能设备上造成卡顿

## 注意事项

- 玻璃态效果在某些浏览器可能不被完全支持，需要做好降级处理
- 过多的动画效果可能影响页面性能，应适度使用
- 确保颜色对比度符合可访问性标准，便于所有用户使用
- 保持与LAYUI框架的设计语言一致性
