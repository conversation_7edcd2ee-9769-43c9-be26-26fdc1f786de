后端模块（gift）的“列表、增加、修改、详情、删除”编写

模仿目标文件：\internal\app\admin\admin_extra_2.go
目标文件中的具体函数：
Supplier_drug_list()
Supplier_drug_add()
Supplier_drug_edit()
Supplier_drug_detail()
Supplier_drug_del()

你需要修改（模仿）的页面：\internal\app\admin\admin_extra_2.go
你需要新增的函数依次为：
Gift_list()
Gift_add()
Gift_edit()
Gift_detail()
Gift_del()
api_id依次为：118、71、174、175、176

数据表：gifts
字段：
id	int	ID
name	        varchar	    赠品名称
harvestTime	    date	    采收时间
price	        decimal	    参考价格
create_time	    timestamp	创建时间
