# 开发文档

## 赠品模块 (Gift)

### 数据表结构
表名：gifts
| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | int | ID |
| name | varchar | 赠品名称 |
| factory_date | date | 生产日期 |
| price | decimal | 参考价格 |
| create_time | timestamp | 创建时间 |

### API接口
1. 赠品列表 (api_id: 118)
   - 函数：`Gift_list()`
   - 功能：获取赠品列表，支持分页和名称搜索
   - 参数：
     - limit: 每页数量
     - page: 页码
     - key: 搜索关键词（赠品名称）

2. 赠品添加 (api_id: 71)
   - 函数：`Gift_add()`
   - 功能：添加新的赠品
   - 参数：
     - name: 赠品名称
     - factory_date: 生产日期
     - price: 参考价格

3. 赠品修改 (api_id: 174)
   - 函数：`Gift_edit()`
   - 功能：修改现有赠品信息
   - 参数：
     - id: 赠品ID
     - name: 赠品名称
     - factory_date: 生产日期
     - price: 参考价格

4. 赠品详情 (api_id: 175)
   - 函数：`Gift_detail()`
   - 功能：获取单个赠品的详细信息
   - 参数：
     - id: 赠品ID

5. 赠品删除 (api_id: 176)
   - 函数：`Gift_del()`
   - 功能：删除赠品
   - 参数：
     - id: 赠品ID

### 实现位置
所有接口都实现在文件：`internal/app/admin/admin_extra_2.go` 