<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 药材品种维护</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
</head>

<body>
    <div class="del_pm"></div>
    <div class="layui-layout layui-layout-admin">
        <!-- 头部 -->
        <div class="layui-header">
            <div class="layui-logo">幸年堂</div>
            <ul class="layui-nav layui-layout-right">
                <li class="layui-nav-item" style="background-color:transparent!important;padding-right:10px">
                    <a href="javascript:;" style="display:flex;align-items:center;color:#fff"><img
                            src="/dist/images/user.png" class="layui-nav-img"><span class="user_name"></span></a>
                    <dl class="layui-nav-child pop_menu">
                        <dd><i class="iconfont">&#xe62f;</i><a href="change_password.html#7">用户资料</a></dd>
                        <dd><i class="iconfont">&#xe601;</i><a href="javascript:void(0)" class="refresh_cache">刷新缓存</a>
                        </dd>
                        <dd><i class="iconfont">&#xe634;</i><a href="login_out.html">退出登录</a></dd>
                    </dl>
                </li>
            </ul>
        </div>
        <!-- 导航 -->
        <ul class="layui-nav layui-nav-tree layui-nav-side" id="menuContainer" lay-bar="disabled" lay-accordion>
            <div style="margin: 50px 0;color: #bbb;text-align: center;">菜单加载中...</div>
        </ul>
        <!-- 主体内容 -->
        <div class="layui-body">
            <div class="body_child">
                <div class="layui-panel">
                    <div class="layui-card-header">
                        <div class="layui-row" style="padding-top:10px;">
                            <div class="layui-col-md11 layui-col-sm10" style="display: flex;">
                                <div>药材品种维护</div>
                            </div>
                            <div class="layui-col-md1 layui-col-sm2" style="text-align: right;">
                                <button class="layui-btn layui-btn-primary layui-border-red perm_check_btn" lay-event="add" res_id="165" onclick="add()">
                                    <i class="layui-icon">&#xe654;</i> 添加
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="layui-padding-3">

                        <div id="data_search">


                            <div class="layui-form">
                                <div class="layui-row">

                                    <div class="layui-col-md3">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label">药材名称</label>
                                            <div class="layui-input-block">
                                                <input type="text" name="key" placeholder="请输入药材名称" autocomplete="off"
                                                    class="layui-input" lay-affix="clear">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="layui-col-md3">
                                        <div class="layui-form-item">
                                            <div class="layui-input-inline" style="margin-left: 10px;">
                                                <button class="layui-btn" lay-submit lay-filter="search"
                                                    style="width: 120px;">筛选</button>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                            </div>


                        </div>

                        <table id="mytable" lay-filter="mytable" class="layui-table" lay-skin="row"></table>
                    </div>
                </div>
                <br>
                <br>
            </div>
        </div>
    </div>

    <script type="text/html" id="TPL-bar">
            <button class="layui-btn layui-btn-xs perm_check_btn" lay-event="edit" res_id="166">编辑</button>
            <button class="layui-btn layui-btn-xs perm_check_btn" lay-event="detail" res_id="167">详情</button>
            <button class="layui-btn layui-btn-xs layui-btn-danger perm_check_btn" lay-event="del" res_id="166">删除</button>
    </script>

    <script>
        layui.use(['element', 'layer', 'util'], function () {
            var element = layui.element;
            var form = layui.form;
            var layer = layui.layer;
            var util = layui.util;
            var table = layui.table;
            var pid = request.get("id") ? request.get("id") : 0;
            var $ = layui.$;
            var id = request.get('id');
            if (id) {
                $('.del_pm').html(`
                    <i class="iconfont">&#xe650;</i>
                    <div>注：当前页面含其它页面传来的参数进行筛选</div>
                    <a href="drug_list.html">解绑该参数</a>
                    <a href="javascript:history.back();">返回原页面</a>
                `);
                $('.del_pm').show();
            } else {
                id = 0;
            }

            // 页面顶部插入缓存的用户数据
            let local_userinfo = localStorage.getItem('local_userinfo');
            let user_name = "";
            if (local_userinfo) {
                local_userinfo = JSON.parse(local_userinfo);
                user_name = local_userinfo.Name + " ( " + local_userinfo.Roles + " )";
            } else {
                user_name = "未登录";
            }
            $('.user_name').text(user_name);
            render_menu($);
            $('.refresh_cache').click(function () {
                refresh_cache($);
            });

            // 表格加载完成后的回调函数
            function after_table_done_rander() {
                // 收集所有需要查询的用户ID
                let user_arr = [];
                $('.user_need_ajax').each(function () {
                    let id = $(this).data('id');
                    if (id && id !== '0') {  // 只收集非0的用户ID
                        user_arr.push(id);
                    }
                });

                // 如果有需要查询的用户ID
                if (user_arr.length > 0) {
                    user_arr = [...new Set(user_arr)]; // 去重
                    layer.load(2);
                    $.ajax({
                        url: '/admin/user/list_low',
                        data: {
                            id: user_arr,
                        },
                        type: 'post',
                        success: function (res) {
                            layer.closeAll('loading');
                            if (res.code === 200) {
                                let data = res.data;
                                for (let i = 0; i < data.length; i++) {
                                    let id = data[i].ID;
                                    let name = data[i].Name;
                                    $('.user_need_ajax[data-id="' + id + '"]').text(name);
                                }
                            } else {
                                layer.msg(res.msg, { icon: 2, time: 1000 });
                            }
                        },
                        error: function (res) {
                            layer.closeAll('loading');
                            layer.msg(res.responseJSON.msg, { icon: 2, time: 1000 });
                        }
                    });
                }
            }

            //引入TABLE模块
            layer.load(2);
            table.render({
                elem: '#mytable'
                , even: true
                , url: serverUrl + "/admin/drug/list"
                ,
                where: {
                    pid: id,
                }
                , method: 'post'
                , response: {
                    statusName: 'code'
                    , statusCode: 200
                    , msgName: 'msg'
                    , countName: 'count'
                    , dataName: 'data'
                }
                , cols: [[
                    { field: 'ID',sort:true, title: 'ID', align: 'center', width: 80 }
                    , { field: 'Name', title: '药材名称' }
                    , { field: 'Code', title: '批号', width: 130 }
                    , { field: 'Spec', title: '规格', width: 80 }
                    , { field: 'Property', title: '性味', width: 90, align: 'center' }
                    , {
                        title: '采收时间', width: 120, templet: function (d) {
                            return d.HarvestTime ? d.HarvestTime.split('T')[0] : '';
                        }
                    }
                    , {
                        title: '效期', width: 60, align: 'center', templet(d) {
                            return d.Validity_Days + '天'
                        }
                    }
                    , {
                        title: '参考价格', width: 100, align: 'center', templet(d) {
                            return d.Price + '元/' + d.Unit
                        }
                    }
                    , {
                        field: 'Update_user_id', title: '维护人', templet: function (d) {
                            return d.Update_user_id === 0 ? '从未' : '<div class="user_need_ajax" data-id="' + d.Update_user_id + '">-</div>'
                        }
                    }
                    , {
                        title: '上次维护', width: 180, align: 'center', templet: function (d) {
                            return d.Update_time != '0001-01-01T00:00:00Z' ? d.Update_time.replace('T', ' ').replace('Z', '') : '从未';
                        }
                    }
                    , {
                        title: '建档时间', width: 180, align: 'center', templet: function (d) {
                            return d.Create_time.replace('T', ' ').replace('Z', '');
                        }
                    }
                    , { title: '操作', align: 'center', toolbar: '#TPL-bar', width: 300, fixed: 'right' }
                ]]
                , page: true
                , limit: 12
                , done: function () {
                    layer.closeAll('loading');
                    after_table_done_rander();
                }
            });

            //监听工具条
            table.on('tool(mytable)', function (obj) {
                var data = obj.data;
                if (obj.event === 'detail') {
                    layer.open({
                        type: 2,
                        title: '药材详情',
                        area: ['1000px', '880px'],
                        shadeClose: true,
                        content: 'drug_detail.html?id=' + data.ID
                    });
                } else if (obj.event === 'edit') {
                    layer.open({
                        type: 2,
                        title: '编辑药材',
                        shadeClose: true,
                        area: ['1000px', '830px'],
                        content: 'drug_edit.html?id=' + data.ID
                    });
                } else if (obj.event === 'del') {
                    layer.confirm('确定要删除该药材吗？', function (index) {
                        layer.close(index);
                        // 发送删除请求
                        layer.load(2);
                        $.ajax({
                            url: serverUrl + '/admin/drug/del',
                            type: 'POST',
                            data: { id: data.ID },
                            success: function (res) {
                                layer.closeAll('loading');
                                if (res.code === 200) {
                                    layer.msg('删除成功', { icon: 1, time: 1000 });
                                    // 刷新表格
                                    table.reload('mytable');
                                } else {
                                    layer.msg(res.msg || '删除失败', { icon: 2 });
                                }
                            },
                            error: function (res) {
                                layer.closeAll('loading');
                                layer.msg(res.responseJSON.msg, { icon: 2, time: 1000 });
                            }
                        });
                    });
                }
            });

            //监听搜索
            form.on('submit(search)', function (data) {
                layer.load(2);
                data.field.id = id;
                console.log(data.field)
                table.reload('mytable', {
                    where: data.field
                    , page: {
                        curr: 1
                    }
                });
                return false;
            });

            // 添加药材函数定义
            window.add = function () {
                layer.open({
                    type: 2,
                    title: '添加药材',
                    shadeClose: true,
                    area: ['1000px', '830px'],
                    content: 'drug_add.html?id=' + pid
                });
            };
        });
    </script>
</body>

</html>