<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 订单修改</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/layui/layarea_lc.js"></script>
    <script src="/dist/js/main.js"></script>
    <style>
        #floating-submit {
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .image-preview-container {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-top: 15px;
        }

        .image-preview-item {
            position: relative;
            width: 150px;
            height: 150px;
        }

        .image-preview-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 4px;
            cursor: pointer;
        }

        .image-preview-item .delete-btn {
            position: absolute;
            bottom: 5px;
            left: 50%;
            transform: translateX(-50%);
            padding: 2px 10px;
            background-color: rgba(255, 255, 255, 0.9);
            border: 1px solid #ddd;
            border-radius: 3px;
            cursor: pointer;
        }

        .delete-btn:hover {
            background-color: #f56c6c;
            color: white;
        }

        h3 {
            margin: 30px 0 20px 0;
            border-left: 3px solid #666;
            padding-left: 15px;
        }

        .multi_record_check_result {
            display: none;
        }
    </style>
</head>

<body>
    <div class="layui-layout layui-layout-admin">
        <!-- 头部 -->
        <div class="layui-header">
            <div class="layui-logo">幸年堂</div>
            <ul class="layui-nav layui-layout-right">
                <li class="layui-nav-item" style="background-color:transparent!important;padding-right:10px">
                    <a href="javascript:;" style="display:flex;align-items:center;color:#fff">
                        <img src="/dist/images/user.png" class="layui-nav-img">
                        <span class="user_name"></span>
                    </a>
                    <dl class="layui-nav-child pop_menu">
                        <dd><i class="iconfont">&#xe62f;</i><a href="change_password.html#7">用户资料</a></dd>
                        <dd><i class="iconfont">&#xe601;</i><a href="javascript:void(0)" class="refresh_cache">刷新缓存</a>
                        </dd>
                        <dd><i class="iconfont">&#xe634;</i><a href="login_out.html">退出登录</a></dd>
                    </dl>
                </li>
            </ul>
        </div>
        <!-- 导航 -->
        <ul class="layui-nav layui-nav-tree layui-nav-side" id="menuContainer" lay-bar="disabled" lay-accordion>
            <div style="margin: 50px 0;color: #bbb;text-align: center;">菜单加载中...</div>
        </ul>

        <!-- 主体内容 -->
        <div class="layui-body">
            <form class="layui-form" lay-filter="form_edit" onsubmit="return false">
                <div class="body_child">
                    <div class="layui-panel">
                        <div class="layui-card-header">
                            <div class="layui-row">
                                <div class="layui-col-md11 layui-col-sm10">订单基础数据修改</div>
                            </div>
                        </div>

                        <div class="layui-padding-5" style="padding-top: 0 !important;min-height: 800px;">

                            <h3>患者基础信息</h3>
                            <!-- 填充患者基础信息，接口/admin/patient_profile/detail -->
                            <div class="layui-form">
                                <div id="patient_data" class="layui-row">
                                    <i
                                        class='layui-icon layui-icon-loading-1 layui-anim layui-anim-rotate layui-anim-loop'></i>
                                </div>
                            </div>


                            <h3>病历及基础信息</h3>

                            <!-- 病历ID显示 -->
                            <div class="layui-row">
                                <div class="layui-col-md8">
                                    <div style="display: flex;">
                                        <div style="margin: 0 0 30px 40px;display: flex;align-items: center;">
                                            <span style="font-size: 18px;font-weight: bold;">病历ID：</span>
                                            <span style="font-size: 18px;font-weight: bold;" class="record_ids"></span>
                                        </div>
                                        <div style="margin: 0 0 30px 40px;display: flex;align-items: center;">
                                            <span>医助：</span>
                                            <span id="asst_name">-</span>
                                        </div>
                                        <div style="margin: 0 0 30px 40px;display: flex;align-items: center;">
                                            <span>医生：</span>
                                            <span id="doctor_name">-</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="layui-row layui-form">
                                <div class="layui-form-item" id="area-picker">
                                    <label class="layui-form-label">收货地址：</label>
                                    <div class="layui-input-block">
                                        <div class="layui-input-inline" style="width: 200px;">
                                            <select name="province" class="province-selector" lay-filter="province-1">
                                                <option value="">请选择省</option>
                                            </select>
                                        </div>
                                        <div class="layui-input-inline" style="width: 200px;">
                                            <select name="city" class="city-selector" lay-filter="city-1">
                                                <option value="">请选择市</option>
                                            </select>
                                        </div>
                                        <div class="layui-input-inline" style="width: 200px;">
                                            <select name="county" class="county-selector" lay-filter="county-1">
                                                <option value="">请选择区</option>
                                            </select>
                                        </div>
                                        <div class="layui-input-inline" style="width:360px;">
                                            <input type="text" name="address_detail" placeholder="请输入具体地址"
                                                autocomplete="off" class="layui-input">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <h3>金额及截图</h3>

                            <div class="layui-row layui-form">
                                <div class="layui-col-md2">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">订单总额：</label>
                                        <div class="layui-input-block">
                                            <input type="number" name="total_money" class="layui-input" min="1"
                                                max="10000">
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-col-md2">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">已付定金：</label>
                                        <div class="layui-input-block">
                                            <input type="number" name="pre_pay" class="layui-input" min="0" max="10000">
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-col-md2">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">已付尾款：</label>
                                        <div class="layui-input-block">
                                            <input type="number" name="final_pay" class="layui-input" min="0"
                                                max="10000">
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-col-md2">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">待收：</label>
                                        <div class="layui-input-block">
                                            <div class="layui-form-mid owing"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="layui-row layui-form">
                                <div class="layui-col-md3">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">订单状态：</label>
                                        <div class="layui-form-mid status">-</div>
                                    </div>
                                </div>
                                <div class="layui-col-md3">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">创建时间：</label>
                                        <div class="layui-form-mid create_time">-</div>
                                    </div>
                                </div>
                                <div class="layui-col-md3">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">发货时间：</label>
                                        <div class="layui-input-inline">
                                            <div class="layui-input-wrap">
                                                <div class="layui-input-prefix">
                                                    <i class="layui-icon layui-icon-date"></i>
                                                </div>
                                                <input type="text" class="layui-input" id="Delivery_time"
                                                    placeholder="请选择发货时间" name="Delivery_time">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 图片上传相关HTML -->
                            <div class="layui-form-item">
                                <div class="layui-col-md6">
                                    <!-- 收款凭证 -->
                                    <div class="layui-upload layui-padding-4" style="border: 1px solid #eee;">
                                        <div class="layui-upload-btn"
                                            style="display: flex;align-items: center;justify-content: flex-start;">
                                            <button type="button" class="layui-btn upload_big_btn"
                                                id="ID-upload-demo-btn-1">
                                                <div class="btn_big_font">
                                                    <i class="layui-icon layui-icon-upload btn_big_font"></i> 代收款凭证上传
                                                </div>
                                                <div>jpg、png、jpeg、bmp、gif格式，2M以内</div>
                                                <div>可点选上传和拖拽上传</div>
                                            </button>
                                        </div>
                                        <div class="image-preview-container" id="photo_cr_pic_container"></div>
                                    </div>
                                </div>
                                <div class="layui-col-md6">
                                    <!-- 收款截图 -->
                                    <div class="layui-upload layui-padding-4"
                                        style="border: 1px solid #eee;margin-left: 8px;">
                                        <div class="layui-upload-btn"
                                            style="display: flex;align-items: center;justify-content: flex-start;">
                                            <button type="button" class="layui-btn upload_big_btn"
                                                id="ID-upload-demo-btn-2">
                                                <div class="btn_big_font">
                                                    <i class="layui-icon layui-icon-upload btn_big_font"></i> 收款截图上传
                                                </div>
                                                <div>jpg、png、jpeg、bmp、gif格式，2M以内</div>
                                                <div>可点选上传和拖拽上传</div>
                                            </button>
                                        </div>
                                        <div class="image-preview-container" id="photo_pay_pic_container"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="layui-row" style="display: flex;justify-content: center;margin-top:50px;">
                                <button class="layui-btn" lay-submit lay-filter="formSubmitBtn"
                                    style="margin-right: 50px;">确认修改</button>
                                <button type="reset" class="layui-btn layui-btn-primary"
                                    onclick="history.go(-1)">取消</button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>





            <div class="body_child">
                <div class="layui-panel">
                    <div class="layui-card-header">
                        <div class="layui-row">
                            <div class="layui-col-md11 layui-col-sm10">订单赠品数据修改</div>
                        </div>
                    </div>
                    <div class="layui-padding-5" style="padding-top: 0 !important;min-height: 300px;">
                        <!-- 为了让程序更加清晰赠品数据额外拉出来 -->
                        <div style="display: flex;align-items: center;justify-content: space-between;">
                            <h3>赠品列表</h3>
                        </div>
                        <table class="layui-table">
                            <thead>
                                <tr>
                                    <th>库存ID</th>
                                    <th>赠品ID</th>
                                    <th>赠品名称</th>
                                    <th>数量</th>
                                    <th>单价</th>
                                    <th>总价</th>
                                </tr>
                            </thead>
                            <tbody id="gift_table_body">
                                <!-- 数据将由 JavaScript 动态添加 -->
                            </tbody>
                        </table>
                        <div class="layui-row" style="display: flex;justify-content: center;margin-top:50px;">
                            <button class="layui-btn" id="add_gift_btn">赠品管理</button>
                        </div>
                    </div>
                </div>
            </div>





        </div>
    </div>


    <script>
        // 将 orderId 定义为全局变量
        var orderId = '';

        // 将 loadGiftData 提升为全局函数
        function loadGiftData() {
            layui.use(['layer'], function () {
                var layer = layui.layer;
                var $ = layui.$;
                layer.load(2);
                $.ajax({
                    url: serverUrl + "/admin/order/gift_data",
                    type: "post",
                    data: { id: orderId },
                    success: function (res) {
                        layer.closeAll('loading');
                        let gift_data = res.data;
                        let html = '';
                        if (gift_data && gift_data.length > 0) {
                            for (let i = 0; i < gift_data.length; i++) {
                                html += `
                                <tr data-gift-id="${gift_data[i].Gift_id}">
                                    <td>${gift_data[i].Wh_gift_id}</td>
                                    <td>${gift_data[i].Gift_id}</td>
                                    <td>${gift_data[i].Name}</td>
                                    <td><input value="${gift_data[i].Nums}" type="hidden">${gift_data[i].Nums}</td>
                                    <td>${gift_data[i].Price}</td>
                                    <td>${(gift_data[i].Nums * gift_data[i].Price).toFixed(2)}</td>
                                </tr>
                            `;
                            }
                        } else {
                            html = '<div style="padding:30px 0;">当前订单未选择赠品</div>';
                        }
                        $('#gift_table_body').html(html);
                    },
                    error: function (xhr) {
                        layer.closeAll('loading');
                        layer.msg('加载失败：' + xhr.responseText, { icon: 2, time: 2000 });
                    }
                });
            });
        }

        layui.use(['element', 'layer', 'util', 'layarea_lc'], function () {
            var element = layui.element;
            var form = layui.form;
            var layer = layui.layer;
            var util = layui.util;
            var upload = layui.upload;
            var laydate = layui.laydate;
            var layarea_lc = layui.layarea_lc;
            var $ = layui.$;
            var Record_ids = '';
            // 初始化日期选择器
            laydate.render({
                elem: '#Delivery_time',
                type: 'date'
            });
            // 获取订单ID并赋值给全局变量
            orderId = request.get('id');
            if (!orderId) {
                layer.msg('参数错误', { icon: 2, time: 1000 });
                return false;
            }
            //根据处方ID求出来的病历ID来填充患者基础信息
            var render_patient_info = function (record_ids) {
                layer.load(2);
                $.ajax({
                    url: serverUrl + "/admin/patient_profile/get_patient_ids_by_record_ids",
                    type: "post",
                    data: { record_ids: record_ids },
                    success: function (res) {
                        layer.closeAll('loading');
                        let data = res.data;
                        let html = '';
                        if (data.length > 1) {
                            layer.msg("如订单出现多个患者信息，则属于历史遗留，后续已对患者规则进行了限制：1个订单，只允许1个帐号下的1个患者", {
                                icon: 0,
                                time: 10000
                            });
                        }
                        for (let i = 0; i < data.length; i++) {
                            html += `
                                        <!-- 第1大行 -->
                                        <div class="layui-col-xs3">
                                            <div class="info-item">
                                                <span class="info-label">手机号码：</span>
                                                <span class="info-content">${data[i].Phone || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">患者姓名：</span>
                                                <span class="info-content">${data[i].Name || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">患者性别：</span>
                                                <span class="info-content">${data[i].Sex == 0 ? '女' : '男' || '-'}</span>
                                            </div>
                                        </div>

                                        <div class="layui-col-xs3">
                                            <div class="info-item">
                                                <span class="info-label">关系：</span>
                                                <span class="info-content">${FamilyRelation[data[i].Relation] || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">身高：</span>
                                                <span class="info-content">${data[i].Height || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">体重：</span>
                                                <span class="info-content">${data[i].Weight || '-'}</span>
                                            </div>
                                        </div>

                                        <div class="layui-col-xs3">
                                            <div class="info-item">
                                                <span class="info-label">身份证号：</span>
                                                <span class="info-content">${data[i].Idcard || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">年龄：</span>
                                                <span class="info-content">${date2age(data[i].Born_date)}岁</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">医保卡：</span>
                                                <span class="info-content">${data[i].Ins_card_num || '-'}</span>
                                            </div>
                                        </div>

                                        <div class="layui-col-xs3">
                                            <div class="info-item">
                                                <span class="info-label">患者来源：</span>
                                                <span class="info-content">${Patient_From[data[i].Patient_from] || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">患者等级：</span>
                                                <span class="info-content">${data[i].Level + '级' || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">微信号：</span>
                                                <span class="info-content">${data[i].Weixin || '-'}</span>
                                            </div>
                                        </div>

                                        
                                        <div class="layui-col-xs6">
                                            <div class="info-item">
                                                <span class="info-label">患者备注：</span>
                                                <span class="info-content">${data[i].Customer_notes || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">既往病史：</span>
                                                <span class="info-content">${data[i].Medical_history || '-'}</span>
                                            </div>
                                        </div>
                                        <div class="layui-col-xs6">
                                            <div class="info-item">
                                                <span class="info-label">主诉：</span>
                                                <span class="info-content">${data[i].Chief_complaint || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">过敏史：</span>
                                                <span class="info-content">${data[i].Allergies || '-'}</span>
                                            </div>
                                        </div>
                                        <div class="layui-col-xs12">
                                            <div class="info-item">
                                                <span class="info-label">详细地址：</span>
                                                <span class="info-content">${removeAllPipe(data[i].Address) || '-'}</span>
                                            </div>
                                        </div>
                            `;
                        }
                        $('#patient_data').html(html);
                    },
                    error: function () {
                        layer.msg('获取患者信息失败', { icon: 2 });
                    }
                });
            }
            // 页面顶部插入缓存的用户数据
            let local_userinfo = localStorage.getItem('local_userinfo');
            let user_name = "";
            if (local_userinfo) {
                local_userinfo = JSON.parse(local_userinfo);
                user_name = local_userinfo.Name + " ( " + local_userinfo.Roles + " )";
            } else {
                user_name = "未登录";
            }
            $('.user_name').text(user_name);

            render_menu($);
            $('.refresh_cache').click(function () {
                refresh_cache($);
            });

            // 加载订单详情数据
            layer.load(2);
            $.ajax({
                url: serverUrl + "/admin/order/detail",
                type: "post",
                async: false,
                data: { id: orderId },
                success: function (res) {
                    layer.closeAll('loading');
                    let data = res.data;
                    Record_ids = data.Record_ids;
                    render_patient_info(Record_ids);

                    // 渲染病历ID链接
                    var record_ids_links = Record_ids.split(',').map(function (id) {
                        return `<a href="/admin/patient_records_show.html?id=${id}#/admin/patient_records_list.html" class="btn_arg_pm" target="_blank">B${id}</a>`;
                    }).join('');
                    $('.record_ids').html(record_ids_links);

                    // 渲染表单数据
                    form.val('form_edit', {
                        'total_money': data.Total_money,
                        'pre_pay': data.Pre_pay,
                        'final_pay': data.Final_pay || 0,
                        'status': data.Status,
                        "address_detail": data.Address.split('|')[3],
                    });

                    // 控制已付尾款输入框的状态
                    if (data.Status == 3) {
                        // 只有当订单状态为3时，才允许输入已付尾款
                        $('input[name="final_pay"]').prop('disabled', false);
                    } else {
                        // 其他状态下，禁用已付尾款输入框
                        $('input[name="final_pay"]').prop('disabled', true);
                        $('input[name="final_pay"]').val(0);
                    }

                    // 渲染"订单状态"和"发货时间"2处文本
                    $('.status').text(Order_Status[data.Status] + " （ 状态不可修改，由别的环节触发 ）");
                    let delivery_time = data.Delivery_time.split('T')[0];
                    $('#Delivery_time').val(delivery_time);
                    $('.create_time').text(data.Create_time.replace('T', ' ').replace('Z', ''));

                    // 渲染待收款项
                    let finalPay = parseFloat(data.Final_pay || 0);
                    let totalMoney = parseFloat(data.Total_money);
                    let prePay = parseFloat(data.Pre_pay);
                    $('.owing').text((totalMoney - prePay - finalPay).toFixed(2));

                    // 渲染代收款凭证
                    if (data.Cr_pic) {
                        data.Cr_pic.split('\n').forEach(function (filename) {
                            if (filename) {
                                appendImagePreview(
                                    'photo_cr_pic_container',
                                    `/static/uploads/normal_pics/photo_cr_pic/${filename}`,
                                    filename
                                );
                            }
                        });
                    }
                    // 渲染收款截图
                    if (data.Pay_pic) {
                        data.Pay_pic.split('\n').forEach(function (filename) {
                            if (filename) {
                                appendImagePreview(
                                    'photo_pay_pic_container',
                                    `/static/uploads/normal_pics/photo_pay_pic/${filename}`,
                                    filename
                                );
                            }
                        });
                    }
                    // 渲染地址
                    let address_arr = data.Address.split('|');
                    layarea_lc.render({
                        elem: '#area-picker',
                        name: 'name',
                        data: {
                            province: address_arr[0],
                            city: address_arr[1],
                            county: address_arr[2]
                        },
                        change: function (res) {
                            // console.log(res);
                            // form.val("form_edit", {
                            //     "address_detail": res.province + '|' + res.city + '|' + res.county + '|' + address_arr[3]
                            // });
                        }
                    });
                    // 加载医助、医生姓名
                    let user_id_list = data.Asst_id + ',' + data.Doc_id;
                    $.ajax({
                        url: '/admin/user/list_low',
                        data: {
                            user_id_list: user_id_list,
                        },
                        type: 'post',
                        success: function (res) {
                            if (res.code == 200) {
                                const asstName = res.data.find(data => data.Role_id === "3").Name;
                                const doctorName = res.data.find(data => data.Role_id === "4").Name;
                                $('#asst_name').text(asstName);
                                $('#doctor_name').text(doctorName);
                            }
                        }
                    });
                    form.render('select');
                },
                error: function (xhr) {
                    layer.closeAll('loading');
                    layer.msg('加载失败：' + xhr.responseText, { icon: 2, time: 2000 });
                }
            });

            // 初始加载赠品数据
            loadGiftData();

            // 图片预览函数
            function appendImagePreview(containerId, filepath, filename) {
                const container = $(`#${containerId}`);
                const imageItem = $(`
                    <div class="image-preview-item" data-filename="${filename}" data-filepath="${filepath}">
                        <img src="${filepath}">
                        <div class="delete-btn">删除</div>
                    </div>
                `);

                imageItem.find('img').on('click', function () {
                    layer.photos({
                        photos: {
                            title: '查看图片',
                            data: [{ src: filepath }]
                        },
                        footer: false
                    });
                });

                imageItem.find('.delete-btn').on('click', function () {
                    layer.confirm('确定删除该图片吗？', { icon: 3, title: '提示' }, function (index) {
                        // 添加 AJAX 删除请求
                        $.ajax({
                            url: '/admin/normal_pic_del',
                            type: 'POST',
                            data: {
                                filename: filename,
                                category: filepath.split('/')[4] // 从路径中获取类别
                            },
                            success: function (res) {
                                if (res.code === 200) {
                                    imageItem.remove();
                                    layer.msg('删除成功', { icon: 1, time: 1000 });
                                }
                            },
                            error: function (data) {
                                layer.confirm('删除失败: ' + data.responseJSON.msg + '，是否移除该图片？',
                                    { icon: 3, title: '提示' },
                                    function (index) {
                                        imageItem.remove();
                                        layer.msg('移除成功', { icon: 1, time: 1000 });
                                    }
                                );
                            }
                        });
                        layer.close(index);
                    });
                });

                container.append(imageItem);
            }

            // 初始化文件上传
            upload.render({
                elem: '#ID-upload-demo-btn-1',
                url: '/admin/upload_normal_pic',
                multiple: true,
                data: {
                    category: 'photo_cr_pic',
                },
                done: function (res) {
                    if (res.code === 200) {
                        const fileInfo = res.data[0];
                        appendImagePreview('photo_cr_pic_container', fileInfo.filepath, fileInfo.filename);
                        layer.msg('上传成功', { icon: 1, time: 1000 });
                    } else {
                        layer.msg('上传失败: ' + res.msg, { icon: 2, time: 1000 });
                    }
                }
            });

            upload.render({
                elem: '#ID-upload-demo-btn-2',
                url: '/admin/upload_normal_pic',
                multiple: true,
                data: {
                    category: 'photo_pay_pic',
                },
                done: function (res) {
                    if (res.code === 200) {
                        const fileInfo = res.data[0];
                        appendImagePreview('photo_pay_pic_container', fileInfo.filepath, fileInfo.filename);
                        layer.msg('上传成功', { icon: 1, time: 1000 });
                    } else {
                        layer.msg('上传失败: ' + res.msg, { icon: 2, time: 1000 });
                    }
                }
            });

            // 添加赠品按钮点击事件
            $('#add_gift_btn').on('click', function () {
                // 先获取当前订单的赠品数据
                $.ajax({
                    url: '/admin/order/gift_data',
                    type: 'post',
                    data: { id: orderId },
                    success: function (res) {
                        if (res.code === 200) {
                            // 格式化赠品数据
                            var existingGifts = res.data.map(function (item) {
                                return {
                                    id: item.ID,
                                    Wh_gift_id: item.Wh_gift_id,
                                    quantity: item.Nums,
                                    name: item.Name,
                                    price: item.Price
                                };
                            });

                            // 先设置全局变量，供iframe使用
                            window.giftModalData = {
                                orderId: orderId,
                                existingGifts: existingGifts
                            };

                            // 打开模态框
                            layer.open({
                                type: 2,
                                title: '管理订单赠品',
                                area: ['900px', '720px'],
                                shadeClose: true,
                                content: '/admin/warehouse_gifts_edit_toolspage.html'
                            });
                        } else {
                            layer.msg('获取赠品数据失败：' + res.msg, { icon: 2 });
                        }
                    }
                });
            });

            // 监听金额变化
            $('input[name="total_money"], input[name="pre_pay"], input[name="final_pay"]').on('input', function () {
                let totalMoney = parseFloat($('input[name="total_money"]').val()) || 0;
                let prePay = parseFloat($('input[name="pre_pay"]').val()) || 0;
                let finalPay = parseFloat($('input[name="final_pay"]').val()) || 0;

                if (this.name === 'total_money') {
                    if (totalMoney > 10000) totalMoney = 10000;
                    if (totalMoney < 1) totalMoney = 1;
                    $(this).val(totalMoney);

                    if (prePay > totalMoney) {
                        $('input[name="pre_pay"]').val(totalMoney);
                        prePay = totalMoney;
                    }

                    if (finalPay > (totalMoney - prePay)) {
                        $('input[name="final_pay"]').val(totalMoney - prePay);
                        finalPay = totalMoney - prePay;
                    }
                } else if (this.name === 'pre_pay') {
                    if (prePay > totalMoney) prePay = totalMoney;
                    if (prePay < 0) prePay = 0;
                    $(this).val(prePay);

                    if (finalPay > (totalMoney - prePay)) {
                        $('input[name="final_pay"]').val(totalMoney - prePay);
                        finalPay = totalMoney - prePay;
                    }
                } else if (this.name === 'final_pay') {
                    if (finalPay > (totalMoney - prePay)) finalPay = totalMoney - prePay;
                    if (finalPay < 0) finalPay = 0;
                    $(this).val(finalPay);
                }

                $('.owing').text((totalMoney - prePay - finalPay).toFixed(2));
            });

            // 表单提交
            form.on('submit(formSubmitBtn)', function (data) {
                let field = data.field;
                field.address = field.province + '|' + field.city + '|' + field.county + '|' + field.address_detail;
                // 收集图片数据
                let photo_cr_pics = [];
                $('#photo_cr_pic_container .image-preview-item').each(function () {
                    photo_cr_pics.push($(this).data('filename'));
                });
                field.cr_pic = photo_cr_pics.join('\n');

                let photo_pay_pics = [];
                $('#photo_pay_pic_container .image-preview-item').each(function () {
                    photo_pay_pics.push($(this).data('filename'));
                });
                field.pay_pic = photo_pay_pics.join('\n');

                // 发送请求
                layer.load(2);
                $.ajax({
                    url: serverUrl + '/admin/order/edit',
                    type: 'POST',
                    data: {
                        id: orderId,
                        total_money: field.total_money,
                        pre_pay: field.pre_pay,
                        final_pay: field.final_pay,
                        status: field.status,
                        cr_pic: field.cr_pic,
                        pay_pic: field.pay_pic,
                        delivery_time: field.Delivery_time,
                        address: field.address,
                    },
                    success: function (res) {
                        layer.closeAll('loading');
                        if (res.code === 200) {
                            layer.msg('修改成功', { icon: 1, time: 1000 }, function () {
                                // window.location.href = 'order_list.html';
                            });
                        } else {
                            layer.msg(res.msg, { icon: 2 });
                        }
                    },
                    error: function (xhr) {
                        layer.closeAll('loading');
                        layer.msg('保存失败：' + xhr.responseText, { icon: 2 });
                    }
                });

                return false;
            });
        });
    </script>
</body>

</html>