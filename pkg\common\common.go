package common

import (
	"bytes"
	"context"
	"crypto/md5"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"image/png"
	"io"
	"math/rand"
	"mime/multipart"
	"mstproject/pkg/config"
	"mstproject/pkg/database"
	"net/http"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/dgrijalva/jwt-go"
	"github.com/google/uuid"
	"github.com/gorilla/sessions"
	"github.com/mozillazg/go-pinyin"
	"github.com/redis/go-redis/v9"
	"github.com/skip2/go-qrcode"
)

var SessionStore *sessions.CookieStore

func init() {
	SessionStore = sessions.NewCookieStore([]byte(config.SessionKey))
	SessionStore.Options = &sessions.Options{
		Path:     "/",
		MaxAge:   config.Session_MaxAge, // 单位S
		Secure:   false,                 // 允许 HTTP 上发送
		HttpOnly: true,                  // 防止客户端脚本访问 Cookie
		SameSite: http.SameSiteLaxMode,  // 防止 CSRF 攻击
	}
}

func Bejson(input string) string {
	var data map[string]interface{}
	err := json.Unmarshal([]byte(input), &data)
	if err != nil {
		return err.Error()
	}
	bytes, err := json.Marshal(data)
	if err != nil {
		return err.Error()
	}
	return string(bytes)
}

func Md5Hash(s string) string {
	h := md5.New()
	h.Write([]byte(s + config.Md5Salt))
	return fmt.Sprintf("%x", h.Sum(nil))
}

func DebugSql(sql string, params ...interface{}) string {
	finalSQL := sql
	for _, param := range params {
		finalSQL = strings.Replace(finalSQL, "?", fmt.Sprintf("'%v'", param), 1)
	}
	finalSQL = strings.Replace(finalSQL, "\n", " ", -1)
	finalSQL = strings.Replace(finalSQL, "\t", "  ", -1)
	finalSQL = regexp.MustCompile(`\s+`).ReplaceAllString(finalSQL, " ")
	return finalSQL
}

func CheckInt(str string) (int, error) {
	re := regexp.MustCompile(`^\d+$`)
	if re.MatchString(str) {
		value, err := strconv.Atoi(str)
		if err != nil {
			return -1, errors.New("整型检测-转换失败")
		}
		return value, nil
	}
	return -1, errors.New("整型检测 - 未通过 - " + str)
}

func CheckFloat(str string) (float64, error) {
	re := regexp.MustCompile(`^[+-]?([0-9]*[.])?[0-9]+$`) // 浮点数正则表达式
	if re.MatchString(str) {
		value, err := strconv.ParseFloat(str, 64)
		if err != nil {
			return -1, errors.New("浮点型检测-转换失败")
		}
		return value, nil
	}
	return -1, errors.New("浮点型检测 - 未通过 - " + str)
}

func CheckStr(str string, specialChars ...string) (string, error) {
	// 设置默认的特殊字符为空字符串
	var extraChars string
	if len(specialChars) > 0 {
		extraChars = specialChars[0]
	}
	// 构造正则表达式，添加额外的特殊符号
	allowedChars := `a-zA-Z0-9@.\-,，、。?？[\]{_}|!#：！` + extraChars
	re := regexp.MustCompile(`^[` + allowedChars + `\x{4e00}-\x{9fa5}]+$`)

	if re.MatchString(str) {
		return str, nil
	} else {
		return "", errors.New("字符串检测 - 未通过 - " + str)
	}
}

func CheckName(name string) (string, error) {
	pattern := `^[\p{Han}]{2,}$`
	match, _ := regexp.MatchString(pattern, name)
	if !match {
		return "", errors.New("用户姓名【" + name + "】格式校验未通过")
	}
	return name, nil
}

func CheckPhone(phone string) (string, error) {
	// 检查手机号码数据格式
	re := regexp.MustCompile(`^1[3456789]\d{9}$`)
	phone = strings.TrimSpace(phone)
	if !re.MatchString(phone) {
		return "", errors.New("错误的电话号码格式")
	}
	return phone, nil
}
func IsPhoneNumber(s string) bool {
	// 匹配3~11位数字，主要用于提示
	match, _ := regexp.MatchString(`^\d{3,11}$`, s)
	return match
}

func CheckPassword(pwd string) (string, error) {
	pattern := `^.{6,}$`
	match, _ := regexp.MatchString(pattern, pwd)
	if !match {
		return "", errors.New("密码长度至少6位")
	}
	return pwd, nil
}

func IsAllChinese(str string) bool {
	pattern := `^[\p{Han}]+$`
	match, _ := regexp.MatchString(pattern, str)
	return match
}

func Curl(url string) (string, error) {
	resp, err := http.Get(url)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("failed to fetch URL: status code %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body) // 使用 io.ReadAll 替代 ioutil.ReadAll
	if err != nil {
		return "", err
	}

	return string(body), nil
}

// JSONResponse 写入 JSON 数据到 HTTP 响应
func JSONResponse(w http.ResponseWriter, statusCode int, data interface{}) {
	w.Header().Set("Content-Type", "application/json")
	jsonData, err := json.Marshal(data)
	if err != nil {
		http.Error(w, "Internal Server Error", http.StatusInternalServerError)
		return
	}
	w.WriteHeader(statusCode)
	w.Write(jsonData)
}

func HandleErrorResponse(w http.ResponseWriter, status int, message string, err error) {
	if w != nil {
		JSONResponse(w, status, map[string]interface{}{
			"code":  status,
			"msg":   message,
			"error": err.Error(),
		})
	} else {
		fmt.Println(message, ":", err)
	}
}

func GetTimestamp() string {
	return strconv.FormatInt(time.Now().UnixMilli(), 10)
}

func GetTime() string {
	return time.Now().Format("2006-01-02 15:04:05")
}

func Check_Perm(w http.ResponseWriter, r *http.Request, api_id int) (*sessions.Session, bool) {
	if r.Method != http.MethodPost {
		http.Error(w, "Invalid request method", http.StatusMethodNotAllowed)
		return nil, false
	}
	session, err := SessionStore.Get(r, "admin_sessions")
	// fmt.Println(session)
	if err != nil {
		JSONResponse(w, http.StatusUnauthorized, map[string]interface{}{
			"code": 500,
			"msg":  "鉴权失败",
		})
		return nil, false
	}
	role_ids := session.Values["role_ids"]
	// 判断是不是宽松鉴权（适用任何角色，只要登录就可以访问）
	if api_id == config.NormalPerm && role_ids != nil {
		return session, true
	}
	// fmt.Println(role_ids)
	// 超级管理员或特殊权限管理员
	if role_ids_str, ok := role_ids.(string); ok {
		role_ids_arr := strings.Split(role_ids_str, ",")
		if InArray(role_ids_arr, "1") {
			return session, true
		}
	}
	// 判断预请求的api_id是否在perm_ids中
	perm_ids, _ := session.Values["perm_ids"].(string)
	perm_ids_arr := strings.Split(perm_ids, ",")
	if InArray(perm_ids_arr, strconv.Itoa(api_id)) {
		return session, true
	} else {
		JSONResponse(w, http.StatusUnauthorized, map[string]interface{}{
			"code": 500,
			"msg":  "权限不足 - " + strconv.Itoa(api_id),
		})
		return nil, false
	}
}

// 后台管理操作日志记录 - 针对医院职能部门用户，即WEB后台使用者
func Add_log(contents string, r *http.Request) {
	session, _ := SessionStore.Get(r, "admin_sessions")
	sql := "insert into system_logs(type,user_id,contents,ip,useragent) values(?,?,?,?,?)"
	// fmt.Println("Add_log", DebugSql(sql, 0, session.Values["id"], contents, Get_ip(r), r.Header.Get("User-Agent")))
	_, err := database.Query(sql, 0, session.Values["id"], contents, Get_ip(r), r.Header.Get("User-Agent"))
	if err != nil {
		fmt.Println("日志记录失败：", err, Get_ip(r), time.Now().Format("2006-01-02 15:04:05"), contents)
	}
}

// 也是员工日志，但是小程序端执行的，无SESSION，USERID为POST的user_id
func Add_log_worker_in_wx(contents string, user_id int, r *http.Request, useragent string) {
	sql := "insert into system_logs(type,contents,user_id,ip,useragent) values(?,?,?,?,?)"
	// fmt.Println("Add_log_worker_in_wx", DebugSql(sql, 0, contents, user_id, Get_ip(r), useragent))
	_, err := database.Query(sql, 0, contents, user_id, Get_ip(r), useragent)
	if err != nil {
		fmt.Println("日志记录失败：", err, Get_ip(r), time.Now().Format("2006-01-02 15:04:05"), contents)
	}
}

// 小程序记录操作日志，如修改用户资料 - 针对患者
func Add_log_wxapp(contents string, patient_profile int, r *http.Request, useragent string) {
	sql := "insert into system_logs(type,contents,user_id,ip,useragent) values(?,?,?,?,?)"
	// fmt.Println("Add_log_wxapp", DebugSql(sql, 1, contents, patient_profile, Get_ip(r), useragent))
	_, err := database.Query(sql, 1, contents, patient_profile, Get_ip(r), useragent)
	if err != nil {
		fmt.Println("日志记录失败：", err, Get_ip(r), time.Now().Format("2006-01-02 15:04:05"), contents)
	}
}

// 系统自动化记录日志
func Add_log_sys_auto(contents string, r *http.Request) {
	sql := "insert into system_logs(type,contents,ip,useragent) values(?,?,?,?)"
	// fmt.Println("Add_log_sys_auto", DebugSql(sql, 2, contents, Get_ip(r), r.Header.Get("User-Agent")))
	_, err := database.Query(sql, 2, contents, Get_ip(r), r.Header.Get("User-Agent"))
	if err != nil {
		fmt.Println("日志记录失败：", err, Get_ip(r), time.Now().Format("2006-01-02 15:04:05"), contents)
	}
}

// 生成文件名
func Generate_filename(ext string) string {
	return Md5Hash(GetTimestamp()) + "_" + uuid.New().String() + "." + ext
}

// 输出一码通的BASE64数据
func QRCode_Create(qrcodeString string, w http.ResponseWriter) (string, error) {
	qr, err := qrcode.New(qrcodeString, qrcode.Medium)
	if err != nil {
		return "", err
	}
	qrImg := qr.Image(512)
	var buf bytes.Buffer
	if err := png.Encode(&buf, qrImg); err != nil {
		return "", err
	}
	base64Img := base64.StdEncoding.EncodeToString(buf.Bytes())
	return base64Img, nil
}

func Redis_client(w http.ResponseWriter) (*redis.Client, error) {
	redisHost := config.REDIS_HOST
	redisPort := config.REDIS_PORT
	// 统一使用全局配置的REDIS_DBNUM
	client := redis.NewClient(&redis.Options{
		Addr:     redisHost + ":" + redisPort,
		Password: config.REDIS_PASSWORD,
		DB:       config.REDIS_DBNUM, // 全局配置的数据库编号
	})
	_, err := client.Ping(context.Background()).Result()
	if err != nil {
		return nil, err
	}
	return client, nil
}

func InArray(arr []string, val string) bool {
	for _, item := range arr {
		if item == val {
			return true
		}
	}
	return false
}
func Get_ip(r *http.Request) string {
	ip := r.Header.Get("X-Forwarded-For")
	if ip == "" {
		ip = r.Header.Get("X-Real-Ip")
	}
	if ip == "" {
		ip = r.RemoteAddr
	}
	return ip
}

// 生成JWTTOKEN
func GenerateJWTToken(patientid int) (string, error) {
	exp := time.Now().Add(time.Duration(config.Jwt_Expire_Time) * time.Second).Unix()
	claims := jwt.MapClaims{
		"patientid": patientid,
		"exp":       exp,
	}
	fmt.Println("JWT到期时间：", time.Unix(exp, 0).Format("2006-01-02 15:04:05"))
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString([]byte(config.JWT_SECRET_KEY))
	if err != nil {
		return "", err
	}
	return tokenString, nil
}

// 检查JWTTOKEN是否有效
func ValidateJWTToken(tokenString string) (jwt.MapClaims, error) {
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method %v", token.Header["alg"])
		}
		return []byte(config.JWT_SECRET_KEY), nil
	})
	if err != nil {
		return nil, err
	}
	if claims, ok := token.Claims.(jwt.MapClaims); ok && token.Valid {
		return claims, nil
	}
	return nil, fmt.Errorf("invalid token")
}

// 将JSON 格式的数据缓存到 Redis 中，支持JSON与字符串
func Redis_Cache_Item(redis_client *redis.Client, ctx context.Context, key string, item interface{}, expiration time.Duration) error {
	var value []byte
	var err error
	// 判断 item 是否是字符串，直接转换
	switch v := item.(type) {
	case string:
		value = []byte(v) // 直接将字符串转换为字节切片
	default:
		value, err = json.Marshal(item) // 其他类型仍然进行JSON序列化
		if err != nil {
			return fmt.Errorf("接口信息序列化失败: %w", err)
		}
	}
	if err = redis_client.Set(ctx, key, value, expiration).Err(); err != nil {
		return fmt.Errorf("缓存接口信息失败: %w", err)
	}
	return nil
}

// 从 Redis 中获取 JSON 格式的数据
func Redis_Cache_Get(ctx context.Context, redisClient *redis.Client, id int, prefix string) (interface{}, error) {
	if id > 0 {
		// 获取单个键的值
		key := fmt.Sprintf("%s:%d", prefix, id)
		val, err := redisClient.Get(ctx, key).Result()
		if err == redis.Nil {
			return nil, fmt.Errorf("缓存中不存在键: %s", key)
		} else if err != nil {
			return nil, fmt.Errorf("从Redis读取键失败: %s, 错误: %v", key, err)
		}

		// 尝试将结果解析为JSON
		var parsedData map[string]interface{}
		if jsonErr := json.Unmarshal([]byte(val), &parsedData); jsonErr == nil {
			return parsedData, nil
		}
		return val, nil // 如果无法解析为JSON，直接返回字符串
	}
	// 获取所有匹配的键
	keys, err := redisClient.Keys(ctx, fmt.Sprintf("%s:*", prefix)).Result()
	if err != nil {
		return nil, fmt.Errorf("获取匹配键失败, 错误: %v", err)
	}

	results := make([]map[string]interface{}, 0, len(keys))
	for _, key := range keys {
		val, err := redisClient.Get(ctx, key).Result()
		if err != nil {
			continue // 跳过读取失败的键
		}

		// 尝试将结果解析为JSON
		var parsedData map[string]interface{}
		if jsonErr := json.Unmarshal([]byte(val), &parsedData); jsonErr == nil {
			results = append(results, parsedData)
		} else {
			results = append(results, map[string]interface{}{"key": key, "value": val})
		}
	}
	return results, nil
}

// 检测上传文件是否是图片
func Check_Is_Image(fileHeader *multipart.FileHeader) (bool, []byte, error) {
	file, err := fileHeader.Open()
	if err != nil {
		return false, nil, err
	}
	defer file.Close()

	// 直接读取文件内容到内存
	fileData, err := io.ReadAll(file)
	if err != nil {
		return false, nil, err
	}

	mimeType := http.DetectContentType(fileData)

	supportedTypes := map[string]bool{
		"image/jpeg":    true,
		"image/png":     true,
		"image/gif":     true,
		"image/svg+xml": true,
		"image/avif":    true,
	}

	// 检测 SVG 的特定情况
	if mimeType == "text/plain; charset=utf-8" || strings.Contains(mimeType, "xml") {
		if bytes.Contains(fileData, []byte("<svg")) { // 通过内容检查 SVG
			mimeType = "image/svg+xml"
		}
	}

	if supportedTypes[mimeType] {
		return true, fileData, nil
	}

	return false, nil, fmt.Errorf("不支持的文件类型: %s", mimeType)
}

// 微信小程序JWT鉴权之附加家庭成员用户ID鉴权
func Is_in_family(w http.ResponseWriter, patientid int, pat_pro_id int) error {
	sql := "SELECT id FROM patient_profile WHERE pid = ? and id = ?"
	var family_member_id int
	err := database.GetOne(sql, &family_member_id, patientid, pat_pro_id)
	if err != nil {
		return err
	} else {
		return nil
	}
}

// 主要用于缓存检测中，遇到ID为0时RESULT返回[]时无法触发缓存
func Is_empty_array(result interface{}) bool {
	switch v := result.(type) {
	case []map[string]interface{}:
		return len(v) == 0
	default:
		return false
	}
}

// 调试客户端POST的数据集合
// func PrintPostData(r *http.Request) (map[string]interface{}, error) {
// 	// 解析表单数据
// 	err := r.ParseForm()
// 	if err != nil {
// 		return nil, fmt.Errorf("failed to parse form: %v", err)
// 	}

// 	// 创建一个 map 存储所有 POST 数据
// 	postData := make(map[string]interface{})

// 	// 遍历解析后的表单数据
// 	for key, values := range r.PostForm {
// 		if len(values) == 1 {
// 			postData[key] = values[0] // 单个值的情况
// 		} else {
// 			postData[key] = values // 多值的情况
// 		}
// 	}

// 	// 打印 POST 数据（以 JSON 格式输出，便于调试）
// 	jsonData, _ := json.MarshalIndent(postData, "", "  ")
// 	fmt.Printf("POST Data: %s\n", string(jsonData))

//		return postData, nil
//	}
func Debug_Post(r *http.Request) {
	// 直接函数内打印，不返回值
	err := r.ParseForm()
	if err != nil {
		fmt.Printf("Failed to parse form: %v\n", err)
		return
	}
	postData := make(map[string]interface{})
	for key, values := range r.PostForm {
		if len(values) == 1 {
			postData[key] = values[0]
		} else {
			postData[key] = values
		}
	}
	// fmt.Println("All POST Data:", postData)
	jsonData, _ := json.MarshalIndent(postData, "", "  ")
	fmt.Printf("POST Data: %s\n", string(jsonData))
}

// 字符串转BASE64
func Str2Base64(s string) string {
	return base64.StdEncoding.EncodeToString([]byte(s))
}

// 判断是否纯数字
// IsNumeric 检查字符串是否为纯数字
func IsNumeric(s string) bool {
	_, err := strconv.Atoi(s)
	return err == nil
}

// 汉字转拼音首字母函数
func Name2pinyin(name string) string {
	// 设置拼音转换的参数，使用首字母模式
	args := pinyin.NewArgs()
	args.Style = pinyin.FirstLetter

	// 将汉字转换为拼音首字母数组
	pinyinArray := pinyin.Pinyin(name, args)

	// 拼接首字母
	var result strings.Builder
	for _, py := range pinyinArray {
		if len(py) > 0 {
			result.WriteString(py[0])
		}
	}

	return result.String()
}

// 生成随机字符串
func RandStringRunes(n int) string {
	var letterRunes = []rune("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789")

	// 初始化随机数生成器
	rand.Seed(time.Now().UnixNano())

	b := make([]rune, n)
	for i := range b {
		b[i] = letterRunes[rand.Intn(len(letterRunes))]
	}
	return string(b)
}

// Redis_Clear_All 清除所有Redis数据库中的所有数据
func Redis_Clear_All(w http.ResponseWriter, r *http.Request) error {
	// 连接 Redis
	redisHost := config.REDIS_HOST
	redisPort := config.REDIS_PORT

	// 尝试清除默认的16个数据库，以及配置的数据库编号
	dbsToClean := make([]int, 0, 17)

	// 添加0-15这16个默认数据库
	for db := 0; db < 16; db++ {
		dbsToClean = append(dbsToClean, db)
	}

	// 如果配置的REDIS_DBNUM不在0-15范围内，也添加到清理列表
	if config.REDIS_DBNUM >= 16 || config.REDIS_DBNUM < 0 {
		dbsToClean = append(dbsToClean, config.REDIS_DBNUM)
	}

	// 清理所有需要清理的数据库
	for _, db := range dbsToClean {
		client := redis.NewClient(&redis.Options{
			Addr:     redisHost + ":" + redisPort,
			Password: config.REDIS_PASSWORD,
			DB:       db,
		})

		// 检查连接
		ctx := context.Background()
		_, err := client.Ping(ctx).Result()
		if err != nil {
			client.Close()
			continue // 如果连接失败，跳过该数据库
		}

		// 清除当前数据库所有数据
		err = client.FlushDB(ctx).Err()
		if err != nil {
			client.Close()
			return fmt.Errorf("清除Redis数据库%d失败: %w", db, err)
		}

		client.Close()
	}

	// 记录日志
	if r != nil {
		Add_log_sys_auto("清除所有 Redis 缓存", r)
	}

	return nil
}

// Get_department_children_ids 获取指定部门ID的所有子部门ID
func Get_department_children_ids(departmentID int) ([]int, error) {
	redis_client, err := Redis_client(nil)
	if err != nil {
		return nil, fmt.Errorf("REDIS连接失败: %v", err)
	}
	defer redis_client.Close()
	ctx := context.Background()
	result, err := Redis_Cache_Get(ctx, redis_client, 0, "department")
	if err != nil || result == nil || Is_empty_array(result) {
		// 如果缓存不存在，重新缓存
		needsCache := result == nil || Is_empty_array(result)
		if err != nil {
			needsCache = needsCache || strings.Contains(err.Error(), "缓存中不存在键")
		}

		if needsCache {
			fmt.Println("部门缓存不存在，自动调用缓存设置函数写入缓存...")

			// 使用database包直接实现缓存设置，避免循环导入
			if err := cacheDepartments(ctx, redis_client); err != nil {
				return nil, fmt.Errorf("自动设置部门缓存失败: %v", err)
			}

			// 重新获取缓存
			result, err = Redis_Cache_Get(ctx, redis_client, 0, "department")
			if err != nil || result == nil || Is_empty_array(result) {
				return nil, fmt.Errorf("设置缓存后仍无法获取部门数据: %v", err)
			}
		} else {
			return nil, fmt.Errorf("获取部门缓存失败: %v", err)
		}
	}
	departments, ok := result.([]map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("部门数据类型转换失败")
	}
	var childDepartmentIDs []int
	for _, dept := range departments {
		pid, ok := dept["Pid"].(float64)
		if ok && int(pid) == departmentID {
			if id, ok := dept["Id"].(float64); ok {
				childDepartmentIDs = append(childDepartmentIDs, int(id))
			}
		}
	}
	return childDepartmentIDs, nil
}

// cacheDepartments 内部函数，用于设置部门缓存
func cacheDepartments(ctx context.Context, redis_client *redis.Client) error {
	type Department struct {
		Id      int    `db:"id"`
		Pid     int    `db:"pid"`
		Name    string `db:"name"`
		Details string `db:"details"`
		Sort    int    `db:"sort"`
	}
	sql := "select id,pid,name,sort,details from department order by sort desc"
	var departments []Department
	if err := database.GetAll(sql, &departments); err != nil {
		return fmt.Errorf("数据库查询失败: %v", err)
	}

	// 清除旧的缓存
	keys, err := redis_client.Keys(ctx, "department:*").Result()
	if err != nil {
		return fmt.Errorf("获取 Redis 键失败: %v", err)
	}
	// 使用 DEL 命令删除找到的键
	if len(keys) > 0 {
		_, delErr := redis_client.Del(ctx, keys...).Result()
		if delErr != nil {
			fmt.Printf("删除键失败: %v\n", delErr)
		}
	}

	// 写入新的缓存
	for _, department := range departments {
		if err = Redis_Cache_Item(redis_client, ctx, fmt.Sprintf("department:%d", department.Id), department, 0); err != nil {
			return fmt.Errorf("写入缓存失败: %v", err)
		}
	}

	fmt.Println("科室信息缓存成功")
	return nil
}
