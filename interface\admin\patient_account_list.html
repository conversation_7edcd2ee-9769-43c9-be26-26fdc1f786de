<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 患者账户列表</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
    <script src="/dist/js/xm-select.js"></script>
    <style>
        xm-select {
            border: 0 !important;
        }

        xm-select>.xm-body {
            min-width: 200px !important;
            padding: 10px !important;
        }
    </style>
</head>

<body>
    <div class="layui-layout layui-layout-admin">
        <!-- 头部 -->
        <div class="layui-header">
            <div class="layui-logo">幸年堂</div>

        <!-- 添加浮动提交按钮的容器 -->
        <div id="floating-submit"
            style="display: none; position: fixed; left: 50%; bottom: -100px; transform: translateX(-50%); transition: all 0.3s ease; z-index: 1000; box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);">
            <button class="layui-btn layui-btn-lg" style="padding: 0 60px;">
                <i class="layui-icon layui-icon-share"></i> 批量转移用户
            </button>
        </div>
            <ul class="layui-nav layui-layout-right">
                <li class="layui-nav-item" style="background-color:transparent!important;padding-right:10px">
                    <a href="javascript:;" style="display:flex;align-items:center;color:#fff"><img
                            src="/dist/images/user.png" class="layui-nav-img"><span class="user_name"></span></a>
                    <dl class="layui-nav-child pop_menu">
                        <dd><i class="iconfont">&#xe62f;</i><a href="change_password.html#7">用户资料</a></dd>
                        <dd><i class="iconfont">&#xe601;</i><a href="javascript:void(0)" class="refresh_cache">刷新缓存</a>
                        </dd>
                        <dd><i class="iconfont">&#xe634;</i><a href="login_out.html">退出登录</a></dd>
                    </dl>
                </li>
            </ul>
        </div>
        <!-- 导航 -->
        <ul class="layui-nav layui-nav-tree layui-nav-side" id="menuContainer" lay-bar="disabled" lay-accordion>
            <div style="margin: 50px 0;color: #bbb;text-align: center;">菜单加载中...</div>
        </ul>
        <!-- 主体内容 -->
        <div class="layui-body">
            <div class="body_child">
                <div class="layui-panel">
                    <div class="layui-card-header">
                        <div class="layui-row" style="padding-top:10px;">
                            <div class="layui-col-md11 layui-col-sm10">患者账户列表</div>
                        </div>
                    </div>
                    <div class="layui-padding-3" style="padding-top: 0 !important;min-height: 800px;">
                        <div id="top_data_search">
                            <div class="layui-form" style="margin: 20px 0 0 0;">
                                <div class="layui-row">


                                    <!-- 售前/售后部门+人员组合 -->
                                    <div class="layui-col-md5">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label">销售</label>
                                            <div class="layui-input-block" style="display: flex;gap: 5px;">
                                                <div id="asst_dep_id" style="width:66%"></div>
                                                <select name="asst_id" id="asst_id"></select>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 患者搜索 -->
                                    <div class="layui-col-md4">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label">患者</label>
                                            <div class="layui-input-block">
                                                <input type="text" name="wd" id="dropdown_input"
                                                    lay-filter="searchFilter" placeholder="请输入电话" autocomplete="off"
                                                    class="layui-input" lay-affix="clear">
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 筛选按钮 -->
                                    <div class="layui-col-md1" style="text-align: center;">
                                        <div class="layui-form-item">
                                            <div class="layui-input-block">
                                                <button class="layui-btn" lay-submit lay-filter="search"
                                                    style="min-width: 100px;">筛选</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <table id="mytable" lay-filter="mytable" class="layui-table" lay-skin="row"></table>
                    </div>
                </div>
                <br>
                <br>
            </div>
        </div>
    </div>

    <script type="text/html" id="TPL-bar">
            <a class="layui-btn layui-btn-xs perm_check_btn" lay-event="edit" res_id="47">编辑</a>
            <a class="layui-btn layui-btn-xs layui-btn-warm perm_check_btn" lay-event="profiles" res_id="48">用户列表</a>
            <a class="layui-btn layui-btn-xs layui-btn-normal perm_check_btn" lay-event="transfer" res_id="184">转移</a>
            <a class="layui-btn layui-btn-danger layui-btn-xs perm_check_btn" lay-event="delete" res_id="148">删除</a>
    </script>

    <script>
        // 声明全局变量，用于存储xm-select实例
        var keshiAsstSelect;

        layui.use(['element', 'layer', 'util', 'table', 'form', 'dropdown'], function () {
            var element = layui.element;
            var form = layui.form;
            var layer = layui.layer;
            var util = layui.util;
            var table = layui.table;
            var dropdown = layui.dropdown;
            var $ = layui.$;

            // 页面顶部插入缓存的用户数据
            let local_userinfo = localStorage.getItem('local_userinfo');
            let user_name = "";
            if (local_userinfo) {
                local_userinfo = JSON.parse(local_userinfo);
                user_name = local_userinfo.Name + " ( " + local_userinfo.Roles + " )";
            } else {
                user_name = "未登录";
            }
            $('.user_name').text(user_name);

            render_menu($);
            $('.refresh_cache').click(function () {
                refresh_cache($);
            });
            //引入TABLE模块
            layer.load(2);
            table.render({
                elem: '#mytable'
                , even: true
                , url: serverUrl + "/admin/patient_account/list"
                , method: 'post'
                , response: {
                    statusName: 'code'
                    , statusCode: 200
                    , msgName: 'msg'
                    , countName: 'count'
                    , dataName: 'data'
                }
                , cols: [[
                    { type: 'checkbox', fixed: 'left' }
                    , { field: 'ID', width: 100, title: 'ID', align: 'center' }
                    , { field: 'Phone', width: 150, title: '手机号' }
                    , {
                        field: 'Is_bind', width: 100, title: '绑定', align: 'center', templet: function (d) {
                            return '<span class="bind-status-' + d.ID + '" data-id="' + d.ID + '" data-status="' + d.Is_bind + '" style="cursor:pointer;">' +
                                (d.Is_bind == 1 ? '<span class="layui-badge layui-bg-green">已绑定</span>' : '<span class="layui-badge layui-bg-gray">小程序码</span>') +
                                '</span>';
                        }
                    }
                    , {
                        field: 'Asst_id', title: '医助', templet: function (d) {
                            if (d.Asst_id == 0) {
                                return '未绑定';
                            } else {
                                return '<span class="asst-name-' + d.ID + '" data-asst-id="' + d.Asst_id + '">加载中...</span>';
                            }
                        }
                    }
                    , {
                        field: 'Status', title: '状态', align: 'center', templet: function (d) {
                            return d.Status == 1 ? '<span class="layui-badge layui-bg-green">正常</span>' : '<span class="layui-badge layui-bg-gray">停用</span>';
                        }
                    }
                    , {
                        field: 'Is_transfer', title: '环节', align: 'center', templet: function (d) {
                            return d.Is_transfer == 1 ? '<span class="layui-badge layui-bg-blue">售后</span>' : '<span class="layui-badge layui-bg-gray">售前</span>';
                        }
                    }
                    , {
                        field: 'Last_login_ip', title: '最后登录IP', templet: function (d) {
                            return d.Last_login_ip == '0' ? '-' : d.Last_login_ip;
                        }
                    }
                    , {
                        field: 'Last_login_time', width: 180, title: '最后登录时间', templet: function (d) {
                            return d.Last_login_time === '0001-01-01T00:00:00Z' ? '-' : Utc2time(d.Last_login_time);
                        }
                    }
                    , {
                        field: 'Create_time', width: 180, title: '创建时间', templet: function (d) {
                            return d.Create_time === '0001-01-01T00:00:00Z' ? '-' : Utc2time(d.Create_time);
                        }
                    }

                    , { title: '操作', align: 'center', width: 400, toolbar: '#TPL-bar' }
                ]]
                , done: function (res, curr, count) {
                    render_button($);
                    layer.closeAll('loading');

                    // 收集所有医助ID
                    let asstIds = [];
                    $('[class^="asst-name-"]').each(function () {
                        let asstId = $(this).data('asst-id');
                        if (asstId && asstId > 0 && !asstIds.includes(asstId)) {
                            asstIds.push(asstId);
                        }
                    });

                    // 如果有医助ID，批量获取医助姓名
                    if (asstIds.length > 0) {
                        $.ajax({
                            url: '/admin/user/list_low',
                            type: 'post',
                            data: {
                                user_id_list: asstIds.join(',')
                            },
                            success: function (res) {
                                if (res.code == 200 && res.data.length > 0) {
                                    // 创建医助ID到姓名的映射
                                    let asstMap = {};
                                    for (let i = 0; i < res.data.length; i++) {
                                        asstMap[res.data[i].ID] = res.data[i].Name;
                                    }

                                    // 更新所有医助名称
                                    $('[class^="asst-name-"]').each(function () {
                                        let asstId = $(this).data('asst-id');
                                        if (asstId && asstMap[asstId]) {
                                            $(this).text(asstMap[asstId]);
                                        } else {
                                            $(this).text('未知');
                                        }
                                    });
                                } else {
                                    $('[class^="asst-name-"]').text('未知');
                                }
                            },
                            error: function () {
                                $('[class^="asst-name-"]').text('未知');
                            }
                        });
                    }

                    // 绑定状态点击事件处理
                    $('[class^="bind-status-"]').on('click', function () {
                        let id = $(this).data('id');
                        let status = $(this).data('status');

                        if (status == 1) {
                            // 已绑定状态，点击解绑
                            layer.confirm('确定要解除该用户的微信绑定吗？', {
                                btn: ['确定', '取消']
                            }, function (index) {
                                $.ajax({
                                    url: serverUrl + "/patient/un_bind_user",
                                    type: "post",
                                    data: {
                                        id: id
                                    },
                                    success: function (res) {
                                        if (res.code == 200) {
                                            layer.msg("解绑成功", { icon: 1, time: 1000 });
                                            // 重新加载表格
                                            table.reload('mytable');
                                        } else {
                                            layer.msg(res.msg || "解绑失败", { icon: 2, time: 1000 });
                                        }
                                    },
                                    error: function (err) {
                                        layer.msg(err.responseJSON ? err.responseJSON.msg : "请求失败", { icon: 2, time: 1000 });
                                    }
                                });
                                layer.close(index);
                            });
                        } else {
                            // 未绑定状态，显示小程序码
                            layer.load(2);
                            $.ajax({
                                url: '/admin/get_wx_bind_user_acode',
                                type: 'post',
                                data: { user_id: id },
                                dataType: 'json',
                                success: function (res) {
                                    layer.closeAll('loading');
                                    if (res.code == 200) {
                                        // 弹出图片层
                                        layer.open({
                                            type: 1,
                                            title: '患者微信小程序码',
                                            area: ['350px', '400px'],
                                            shadeClose: true,
                                            content: '<div style="text-align:center;padding:20px;"><img src="' + res.data.base64Img + '" style="max-width:100%;"></div>'
                                        });
                                    } else {
                                        layer.msg(res.msg || '获取小程序码失败', { icon: 2, time: 2000 });
                                    }
                                },
                                error: function (xhr) {
                                    layer.closeAll('loading');
                                    layer.msg('请求失败，请稍后再试', { icon: 2, time: 2000 });
                                }
                            });
                        }
                    });
                },
                error: function (res) {
                    $('.layui-table-main').html('<div class="layui-none"><i class="layui-icon layui-icon-tips-fill"></i> ' + res.responseJSON.msg + '</div>');
                },
                page: true,
                limit: 12,
            })
            //监听工具条
            table.on('tool(mytable)', function (obj) {
                var data = obj.data;
                if (obj.event === 'edit') {
                    layer.open({
                        type: 2,
                        title: '帐号详情',
                        shade: 0.2,
                        maxmin: true,
                        area: ['520px', '770px'],
                        shadeClose: true,
                        content: 'patient_account_detail.html?id=' + data.ID
                    });
                } else if (obj.event === 'profiles') {
                    // 跳转到患者个人信息列表页面，并传递账户ID参数
                    window.location.href = 'patient_profile_list.html?patient_account_id=' + data.ID + "#/admin/patient_profile_list.html";
                } else if (obj.event === 'transfer') {
                    // 打开转移用户模态框
                    layer.open({
                        type: 2,
                        title: '转移用户',
                        area: ['500px', '800px'],
                        shadeClose: true,
                        content: '/admin/transfer_user.html?pat_id=' + data.ID,
                        end: function() {
                            // 模态框关闭后刷新表格
                            table.reload('mytable');
                        }
                    });
                } else if (obj.event === 'delete') {
                    layer.confirm('确定要删除该患者账户吗？此操作不可恢复！', function (index) {
                        $.ajax({
                            url: serverUrl + "/admin/patient_account/del",
                            type: "post",
                            data: {
                                id: data.ID
                            },
                            success: function (res) {
                                if (res.code == 200) {
                                    layer.msg(res.msg, { icon: 1, time: 1000 });
                                    obj.del();
                                } else {
                                    layer.msg(res.msg, { icon: 2, time: 1000 });
                                }
                            },
                            error: function (res) {
                                layer.closeAll('loading');
                                layer.msg(res.responseJSON.msg);
                            }
                        });
                        layer.close(index);
                    });
                }
            });
            // 添加表单提交事件监听器
            // 监听表格复选框选择
            table.on('checkbox(mytable)', function (obj) {
                var checkStatus = table.checkStatus('mytable');
                var selectedData = checkStatus.data;

                // 显示/隐藏浮动提交按钮
                var floatingSubmit = $('#floating-submit');
                if (selectedData.length > 0) {
                    if (floatingSubmit.css('display') === 'none') {
                        floatingSubmit.css('display', 'block');
                        setTimeout(function () {
                            floatingSubmit.css('bottom', '20px');
                        }, 50);
                    }
                } else {
                    floatingSubmit.css('bottom', '-100px');
                    setTimeout(function () {
                        floatingSubmit.css('display', 'none');
                    }, 300);
                }
            });

            form.on('submit(search)', function (data) {
                // 获取xm-select中选中的部门ID
                if (keshiAsstSelect) {
                    let selectedDepts = keshiAsstSelect.getValue();
                    console.log('筛选按钮 - 选中的部门:', selectedDepts);
                    // 如果有选中部门，添加到表单数据
                    if (selectedDepts.length > 0) {
                        // 提取ID值并合并为逗号分隔的字符串
                        let deptIds = selectedDepts.map(item => item.Id || item.id).join(',');
                        data.field.asst_dep_id = deptIds;
                        console.log('筛选按钮 - 添加到表单的部门IDs:', deptIds);
                    }
                } else {
                    console.warn('警告: keshiAsstSelect 未初始化');
                }

                // 获取搜索框的值
                let searchValue = $('#dropdown_input').val();
                // 如果搜索框有值，则添加到搜索条件中
                if (searchValue) {
                    data.field.pat_id = pat_id;
                }

                // 删除可能存在的多余字段
                if (data.field.select !== undefined) {
                    console.log('删除多余的select字段:', data.field.select);
                    delete data.field.select;
                }

                // 打印最终的筛选条件
                console.log('最终筛选条件:', data.field);

                // 重载表格
                table.reload('mytable', {
                    where: data.field,
                    page: {
                        curr: 1 //重新从第 1 页开始
                    }
                });
                return false;
            });

            // 获取当前用户信息
            let currentUserIsSales = false;
            let currentUserDeptId = 0;
            let currentUserId = 0;
            let currentUserRoleIds = "";
            let pat_id = 0;

            if (local_userinfo) {
                // 获取当前用户的角色ID、部门ID和用户ID
                currentUserRoleIds = local_userinfo.Role_ids || "";
                currentUserDeptId = local_userinfo.Department_id || 0;
                currentUserId = local_userinfo.Id || 0;

                // 判断是否为售前(3)或售后(9)用户
                if (currentUserRoleIds.split(',').includes(global_asst_role_id) || currentUserRoleIds.split(',').includes(global_after_asst_role_id)) {
                    currentUserIsSales = true;
                }
            }

            // 获取本地缓存的部门数据
            let local_departments = localStorage.getItem('local_departments') ? JSON.parse(localStorage.getItem('local_departments')) : [];

            // 创建一个映射，用于快速查找部门
            let deptMap = {};
            local_departments.forEach(dept => {
                deptMap[dept.Id] = dept;
            });

            // 获取所有售前和售后部门的子部门
            let allSaleDepts = [];
            global_sale_department_ids.forEach(deptId => {
                local_departments.forEach(dept => {
                    if (dept.Pid === deptId) {
                        allSaleDepts.push(dept);
                    }
                });
            });

            // 再找出这些子部门的所有子部门
            let allChildDepts = [...allSaleDepts];
            allSaleDepts.forEach(dept => {
                function findChildren(parentId) {
                    local_departments.forEach(d => {
                        if (d.Pid === parentId && !allChildDepts.some(cd => cd.Id === d.Id)) {
                            allChildDepts.push(d);
                            findChildren(d.Id);
                        }
                    });
                }
                findChildren(dept.Id);
            });

            // 过滤部门数据 - 只保留用户有权限的部门及其父级部门
            let filteredDepartments = [];
            // 默认只展开售前、售后2个大层级节点
            let expandedKeys = global_sale_department_ids;
            // 判断当前用户是否为销售数据管理员
            let isDataMaster = local_userinfo && local_userinfo.Department_id === global_sale_data_master;
            let userDepIds = [];
            if (isDataMaster) {
                userDepIds = local_userinfo.Dep_ids.split(',').map(id => parseInt(id.trim(), 10)).filter(id => !isNaN(id));
            }
            if (isDataMaster && userDepIds.length > 0) {
                // 数据管理员时，由于所负载节点比较少，所以默认展开所有
                expandedKeys = true;
                // 收集用户可访问的部门及其所有父级部门的ID
                let relevantDeptIds = new Set();
                // 递归查找父级部门
                function collectParentDepts(deptId) {
                    const dept = deptMap[deptId];
                    if (dept) {
                        relevantDeptIds.add(dept.Id);
                        if (dept.Pid && dept.Pid !== 0) {
                            collectParentDepts(dept.Pid);
                        }
                    }
                }
                // 处理每个用户可访问的部门
                userDepIds.forEach(deptId => {
                    collectParentDepts(deptId);
                });
                // 只保留用户有权限的部门及其父级部门
                filteredDepartments = allChildDepts.filter(dept =>
                    relevantDeptIds.has(dept.Id)
                );
            } else {
                // 非数据管理员，显示所有部门及其父级部门
                filteredDepartments = allChildDepts;
            }

            // 按照排序值降序排列
            filteredDepartments.sort((a, b) => b.Sort - a.Sort);

            // 将部门数据转换成树形结构
            let departmentTree = convertToTree(filteredDepartments);

            // 初始化xm-select，使用全局变量
            // 判断当前用户是否为销售岗位
            if (currentUserIsSales && currentUserDeptId > 0) {
                console.log('当前用户是销售岗位，部门ID:', currentUserDeptId);
                // 使用id2department函数获取部门名称
                let deptName = id2department(currentUserDeptId, local_departments, 0);
                // 渲染为禁用状态的xm-select
                keshiAsstSelect = xmSelect.render({
                    el: '#asst_dep_id',
                    data: [{
                        Name: deptName,
                        Id: currentUserDeptId
                    }],
                    initValue: [currentUserDeptId],//就显示当前的销售岗位
                    model: {
                        label: {
                            type: 'text'
                        }
                    },
                    prop: {
                        name: 'Name',
                        value: 'Id'
                    },
                    disabled: true,
                });
                // 渲染人员下拉框 - 直接使用当前用户信息
                let html = '<option value="' + currentUserId + '">' + local_userinfo.Name + '</option>';
                $('#asst_id').html(html);
                $('#asst_id').attr('disabled', 'disabled');
                $('#asst_id').css('background-color', '#f2f2f2');
                form.render('select');

            } else {
                console.log('当前用户是非销售岗位，部门ID:', currentUserDeptId);
                // 非销售岗位用户，正常初始化xm-select
                keshiAsstSelect = xmSelect.render({
                    el: '#asst_dep_id',
                    theme: {
                        color: '#1677ff',
                    },
                    height: 'auto',
                    data: departmentTree,
                    model: {
                        label: {
                            type: 'text',
                        }
                    },
                    clickClose: false, // 多选模式下点击不关闭
                    filterable: true,
                    // 添加默认提示文字
                    tips: '请选择部门',
                    prop: {
                        name: 'Name',
                        value: 'Id',
                        children: 'children'
                    },
                    // 设置表单提交时的名称为空，避免自动提交
                    name: '',
                    toolbar: {
                        show: true,
                        list: ['ALL', 'CLEAR']
                    },
                    tree: {
                        show: true,
                        strict: true, // 保持严格模式，确保父子节点联动
                        expandedKeys: expandedKeys,
                        // 启用级联选择，确保父子节点联动
                        cascade: true,
                        // 自动展开父节点
                        autoExpandParent: true
                    },
                    on: function (data) {
                        if (data.change && data.change.length > 0) {
                            // 使用setTimeout确保在DOM更新后获取最新的选中值
                            setTimeout(function () {
                                // 获取当前所有选中的部门ID
                                if (keshiAsstSelect) {
                                    let selectedDepts = keshiAsstSelect.getValue();

                                    // 如果有选中的部门，加载这些部门的用户
                                    if (selectedDepts.length > 0) {
                                        loadDepartmentUsersAll(selectedDepts);
                                    } else {
                                        // 清空用户列表
                                        $('#asst_id').html('<option value="">选择人员</option>');
                                        form.render('select');
                                    }
                                }
                            }, 0);
                        }
                    }
                });
            }

            // 转换扁平数据为树形结构
            function convertToTree(data) {
                let result = [];
                let map = {};

                // 创建所有节点的映射
                data.forEach(function (item) {
                    map[item.Id] = {
                        ...item,
                        children: []
                    };
                });

                // 确保所有必要的父节点都存在
                let addedParentIds = new Set(); // 用于跟踪已添加的父节点ID

                data.forEach(function (item) {
                    if (item.Pid !== 0 && !map[item.Pid] && !addedParentIds.has(item.Pid)) {
                        // 如果父节点不在映射中且尚未添加，尝试从原始数据中找到它
                        const parentDept = local_departments.find(d => d.Id === item.Pid);
                        if (parentDept) {
                            // 添加父节点到映射
                            map[parentDept.Id] = {
                                ...parentDept,
                                children: []
                            };
                            // 将父节点添加到数据数组
                            data.push(parentDept);
                            // 记录已添加的父节点ID
                            addedParentIds.add(parentDept.Id);
                        }
                    }
                });

                // 移除重复的部门
                let uniqueData = [];
                let idSet = new Set();
                data.forEach(function (item) {
                    if (!idSet.has(item.Id)) {
                        uniqueData.push(item);
                        idSet.add(item.Id);
                    }
                });
                data = uniqueData;

                // 构建树结构
                data.forEach(function (item) {
                    let node = map[item.Id];
                    if (item.Pid !== 0 && map[item.Pid]) {
                        // 将当前节点添加到父节点的children中
                        map[item.Pid].children.push(node);
                    } else {
                        // 顶级节点直接添加到结果数组
                        result.push(node);
                    }
                });

                // 按Sort字段排序
                function sortBySort(arr) {
                    arr.sort(function (a, b) {
                        return b.Sort - a.Sort; // 降序排列
                    });
                    arr.forEach(function (item) {
                        if (item.children && item.children.length > 0) {
                            sortBySort(item.children);
                        }
                    });
                    return arr;
                }

                return sortBySort(result);
            }

            // 加载多个部门的用户
            function loadDepartmentUsersAll(selectedDepts) {
                layer.load(2);

                // 获取所有选中部门的ID
                let deptIds = selectedDepts.map(item => item.Id || item.id);

                // 创建查询条件，适应多个部门
                let queryParams = {
                    department_ids: deptIds.join(',')
                };

                // 确定角色类型 - 检查是否包含售后部门
                let hasSaleAfter = false;
                for (let i = 0; i < selectedDepts.length; i++) {
                    let deptId = selectedDepts[i].Id || selectedDepts[i].id;
                    let dept = filteredDepartments.find(d => d.Id == deptId);
                    if (dept && dept.Name.includes('售后')) {
                        hasSaleAfter = true;
                        break;
                    }
                }

                $.ajax({
                    url: '/admin/user/list_low',
                    data: queryParams,
                    type: 'post',
                    success: function (res) {
                        layer.closeAll('loading');
                        if (res.code === 200) {
                            let data = res.data;
                            // let html = '<option value="">选择人员</option>';//节省后端资源
                            let html = '';
                            if (data && data.length > 0) {
                                // 按姓名排序
                                data.sort((a, b) => a.Name.localeCompare(b.Name, 'zh'));

                                for (let i = 0; i < data.length; i++) {
                                    html += '<option value="' + data[i].ID + '">' + data[i].Name + '</option>';
                                }

                                $('#asst_id').html(html);
                                form.render('select');
                            } else {
                                html = '<option value="">所选部门下暂无人员</option>';
                                $('#asst_id').html(html);
                                form.render('select');
                            }
                        } else {
                            layer.msg(res.msg, { icon: 2, time: 1000 });
                        }
                    },
                    error: function (res) {
                        layer.closeAll('loading');
                        layer.msg(res.responseJSON.msg, { icon: 2, time: 1000 });
                    }
                });
            }

            // 模糊搜索患者账户的下拉框组件
            var inst = dropdown.render({
                elem: '#dropdown_input',
                trigger: 'keyup click',
                data: [],
                style: 'min-width: 220px; box-shadow: 1px 1px 11px rgb(0 0 0 / 11%);',
                click: function (data) {
                    if (!isEmpty(data)) {
                        this.elem.val(data.name + ' / ' + data.phone);
                        pat_id = data.pid;
                    }
                }
            });

            // 监听输入框输入事件
            $(inst.config.elem).on('input propertychange', function () {
                var elem = $(this);
                var value = elem.val().trim();
                if (value.length > 0) {
                    $.ajax({
                        url: "/admin/patient_profile/patient_profile_phone2id",
                        type: 'post',
                        dataType: 'json',
                        data: {
                            "wd": value,
                        },
                        success: function (response) {
                            let data = response.data;
                            if (data && data.length > 0) {
                                dropdown.reloadData(inst.config.id, {
                                    data: data,
                                    templet: function (d) {
                                        var exp = new RegExp(value, 'gi');
                                        return (d.name + ' / ' + d.phone).replace(exp, function (str) {
                                            return '<span style="color: red;">' + str + '</span>';
                                        });
                                    }
                                });
                            } else {
                                dropdown.reloadData(inst.config.id, {
                                    data: [],
                                });
                            }
                        },
                        error: function (err) {
                            dropdown.reloadData(inst.config.id, {
                                data: [],
                            });
                        }
                    });
                } else {
                    dropdown.reloadData(inst.config.id, {
                        data: [],
                    });
                }
            });

            // 辅助函数：判断对象是否为空
            function isEmpty(obj) {
                return obj === undefined || obj === null || obj === '';
            }

            // 点击批量转移按钮的处理
            $('#floating-submit').on('click', function () {
                var checkStatus = table.checkStatus('mytable');
                var selectedData = checkStatus.data;
                if (selectedData.length === 0) {
                    layer.msg('请选择至少一条记录', { icon: 2 });
                    return;
                }

                // 收集选中的用户ID
                var selectedIds = selectedData.map(function (item) {
                    return item.ID;
                });

                // 打开批量转移模态框
                layer.open({
                    type: 2,
                    title: '批量转移用户',
                    area: ['500px', '800px'],
                    shadeClose: true,
                    content: '/admin/batch_transfer_user.html?user_ids=' + selectedIds.join(','),
                    end: function() {
                        // 模态框关闭后刷新表格
                        table.reload('mytable');
                    }
                });
            });
        });
    </script>
</body>

</html>