<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>药品选择-新增</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
    <style>
        .layui-table-cell {
            height: auto;
            line-height: 28px;
        }

        /* 添加的新样式 */
        .layui-btn-group {
            margin-bottom: 10px;
        }

        .layui-btn-group .layui-btn {
            margin-right: 5px;
        }

        .layui-input-group {
            display: flex;
            margin-bottom: 10px;
        }

        .layui-input-group .layui-input {
            margin-right: 5px;
        }

        /* 页面布局样式 */
        .page-container {
            display: flex;
            height: calc(100vh - 110px);
        }

        /* 左侧已选药品样式 */
        .selected-drugs-panel {
            width: 280px;
            overflow-y: auto;
            background-color: #f8f8f8;
            padding: 10px;
            border-right: 1px solid #e6e6e6;
            margin-right: 10px;
        }

        .selected-drugs-panel .panel-title {
            font-weight: bold;
            font-size: 16px;
            margin-bottom: 10px;
            text-align: center;
            color: #1E9FFF;
        }

        .no-drugs-selected {
            text-align: center;
            color: #999;
            padding: 20px 0;
        }

        /* 左侧药品列表悬停效果 */
        #selectedDrugsList tr:hover {
            background-color: #f0f9ff;
        }

        #selectedDrugsList .delete-drug-btn {
            opacity: 0.7;
            transition: opacity 0.3s;
        }

        #selectedDrugsList .delete-drug-btn:hover {
            opacity: 1;
        }

        /* 表格容器样式 - 添加滚动支持 */
        .table-container {
            position: relative;
            /* overflow-y: auto; */
            flex: 1;
        }

        /* 键盘显示时的表格容器样式 */
        .keyboard-visible .table-container {
            height: calc(100vh - 280px) !important;
            margin-bottom: 220px;
        }

        /* 虚拟键盘样式 */
        #virtualKeyboard {
            position: fixed;
            bottom: 60px;
            left: 0;
            right: 0;
            background-color: #f1f1f1;
            padding: 10px;
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.2);
            text-align: center;
            display: none;
            z-index: 1000;
        }

        #keyboardInput {
            font-size: 18px;
            margin-bottom: 5px;
            background-color: white;
            padding: 5px 10px;
            border-radius: 3px;
            min-height: 24px;
        }

        .keyboard-row {
            display: flex;
            justify-content: center;
            margin-bottom: 5px;
        }

        .key {
            width: 40px;
            height: 40px;
            margin: 2px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: white;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            font-weight: bold;
        }

        /* 行选中样式 - 增强显示效果 */
        .selected-row {
            background-color: #e6f7ff !important;
            border: 2px solid #1E9FFF !important;
        }

        /* 当前输入焦点样式 - 增强显示效果 */
        .input-focus {
            border: 2px solid #FF5722 !important;
            box-shadow: 0 0 8px rgba(255, 87, 34, 0.6) !important;
            background-color: #fff8e1 !important;
        }

        /* 数量输入弹窗样式 */
        .quantity-input-modal {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 300px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.3);
            z-index: 2000;
            padding: 20px;
            text-align: center;
            display: none;
        }

        .quantity-input-modal .drug-name {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .quantity-input-modal .inventory {
            margin-bottom: 20px;
            color: #666;
        }

        .quantity-input-modal .quantity-field {
            font-size: 18px;
            padding: 10px;
            border: 2px solid #1E9FFF;
            border-radius: 4px;
            background-color: #f8f8f8;
            text-align: center;
            margin-bottom: 5px;
        }

        .quantity-input-modal .note {
            font-size: 12px;
            color: #999;
            margin-top: 10px;
        }

        /* 操作提示样式 */
        #tipBox {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 400px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
            z-index: 3000;
            padding: 20px;
            text-align: left;
            display: none;
        }

        #tipBox h3 {
            text-align: center;
            margin-bottom: 15px;
            color: #1E9FFF;
        }

        #tipBox ul {
            padding-left: 20px;
            margin-bottom: 20px;
        }

        #tipBox ul li {
            margin-bottom: 10px;
            line-height: 1.6;
        }

        #tipBox .note {
            font-size: 13px;
            color: #666;
            font-style: italic;
            margin-bottom: 15px;
            padding: 10px;
            background-color: #f9f9f9;
            border-left: 3px solid #1E9FFF;
        }

        #tipBox .tip-btn {
            text-align: center;
        }
    </style>
</head>

<body style="padding: 10px;">
    <!-- 操作小技巧提示框 -->
    <div id="tipBox">
        <h3>操作小技巧</h3>
        <ul>
            <li>翻页：左右键</li>
            <li>选择药品：上下键</li>
            <li>提交数据：Ctrl+Enter</li>
            <li>清除数据：移到当前数据所在行，按回退键、Del均可</li>
            <li>左侧面板：显示已选择的所有药品，点击删除图标可移除药品</li>
        </ul>
        <div class="note">
            注：搜索后，想输入数量，要先按下回车。这是为了避免数字也是药材名的一部分的可能性。但当您输入药材数量时想切换药材搜索，直接按字母就可以搜索，因为药材数量不可能有字母。<br>
            已选择的药品会显示在左侧面板中，即使翻页或搜索其他药品，也能清楚看到已选择的药品。点击红色删除按钮可快速移除不需要的药品。
        </div>
        <div class="tip-btn">
            <button type="button" class="layui-btn" id="tipConfirmBtn">我知道了</button>
        </div>
    </div>

    <!-- 页面主容器 -->
    <div class="page-container">
        <!-- 左侧已选药品面板 -->
        <div class="selected-drugs-panel">
            <!-- <div class="panel-title">已选择药品</div> -->
            <table class="layui-table" lay-size="sm">
                <thead>
                    <tr>
                        <th style="width: 57%;">药品名称</th>
                        <th style="width: 25%;">数量</th>
                        <th style="width: 18%; text-align: center;">操作</th>
                    </tr>
                </thead>
                <tbody id="selectedDrugsList">
                    <!-- 动态插入已选择的药品 -->
                </tbody>
            </table>
            <div id="noDrugsSelected" class="no-drugs-selected">
                暂无选择药品
            </div>
        </div>

        <!-- 添加表格容器div -->
        <div class="table-container layui-table-view layui-table-view-1 layui-form layui-border-box">
            <table id="medsTable" lay-filter="medsTable"></table>
        </div>
    </div>

    <!-- 虚拟键盘 -->
    <div id="virtualKeyboard">
        <div id="keyboardInput"></div>
        <div class="keyboard-row">
            <div class="key">Q</div>
            <div class="key">W</div>
            <div class="key">E</div>
            <div class="key">R</div>
            <div class="key">T</div>
            <div class="key">Y</div>
            <div class="key">U</div>
            <div class="key">I</div>
            <div class="key">O</div>
            <div class="key">P</div>
        </div>
        <div class="keyboard-row">
            <div class="key">A</div>
            <div class="key">S</div>
            <div class="key">D</div>
            <div class="key">F</div>
            <div class="key">G</div>
            <div class="key">H</div>
            <div class="key">J</div>
            <div class="key">K</div>
            <div class="key">L</div>
        </div>
        <div class="keyboard-row">
            <div class="key">Z</div>
            <div class="key">X</div>
            <div class="key">C</div>
            <div class="key">V</div>
            <div class="key">B</div>
            <div class="key">N</div>
            <div class="key">M</div>
        </div>
    </div>

    <!-- 数量输入弹窗 -->
    <div class="quantity-input-modal" id="quantityInputModal">
        <div class="drug-name" id="modalDrugName">木鳖子</div>
        <div class="inventory" id="modalInventory">库存量：9999997</div>
        <div class="quantity-field" id="modalQuantityField">请在此处输入数量</div>
        <div class="note">注：回车后确认并显示第1行，按上下键，切换上下行</div>
    </div>

    <div
        style="position: fixed; bottom: 0; left: 0; right: 0; background-color: #fff; padding: 10px; text-align: center; box-shadow: 0 -2px 10px rgba(0,0,0,0.1);">
        <button type="button" class="layui-btn" id="submitBtn">确认选择</button>
        <button type="button" class="layui-btn layui-btn-primary" id="cancelBtn">清空重新</button>
    </div>

    <script>
        layui.use(['table', 'layer', 'form'], function () {
            var table = layui.table;
            var layer = layui.layer;
            var form = layui.form;
            var $ = layui.$;
            window.focus();
            // 存储所有选中药品的数据对象
            var selectedDrugsData = {};
            // 添加一个变量来记录选择顺序
            var selectionOrder = [];

            // 定义查询参数对象
            var queryParams = {
                limit: 10,
                key: '',
                category_id: ''
            };

            // 键盘操作状态管理
            var keyboardState = {
                mode: 'search', // 'search', 'selectRow', 'inputQuantity'
                selectedRowIndex: -1,
                inputBuffer: '',
                $currentInput: null,
                searchPending: false, // 新增：标记是否有待处理的搜索请求
                recentlySwitchedRow: false // 新增：标记是否刚刚通过上下键切换了行
            };

            // 更新左侧已选择药品面板
            function updateSelectedDrugsPanel() {
                var $selectedDrugsList = $('#selectedDrugsList');
                var $noDrugsSelected = $('#noDrugsSelected');

                // 清空当前列表
                $selectedDrugsList.empty();

                // 如果没有选中药品，显示提示信息
                if (selectionOrder.length === 0) {
                    $noDrugsSelected.show();
                    return;
                }

                // 隐藏"暂无选择药品"提示
                $noDrugsSelected.hide();

                // 使用selectionOrder数组确保按照用户选择顺序显示
                // 从头到尾遍历，这样最先添加的会显示在最上面
                selectionOrder.forEach(function (id) {
                    var drug = selectedDrugsData[id];
                    if (drug && drug.single_dose && parseFloat(drug.single_dose) > 0) {
                        var newRow = $('<tr>').attr('data-id', drug.wh_meds_id);
                        newRow.append($('<td>').text(drug.name));
                        newRow.append($('<td>').text(drug.single_dose));
                        // 使用layui图标创建更美观的删除按钮
                        var deleteBtn = $('<button>')
                            .addClass('layui-btn layui-btn-danger layui-btn-xs delete-drug-btn')
                            .html('<i class="layui-icon layui-icon-delete"></i>');
                        newRow.append($('<td>').css('text-align', 'center').append(deleteBtn));
                        $selectedDrugsList.append(newRow);
                    }
                });
            }

            // 初始化页面加载时设置为选择行模式
            $(document).ready(function () {
                // 检查是否显示操作提示
                checkAndShowTips();

                // 确保初始状态为选择行模式
                keyboardState.mode = 'selectRow'; // 改为selectRow而不是search
                keyboardState.inputBuffer = '';
                keyboardState.selectedRowIndex = 0; // 设置为第一行
                keyboardState.$currentInput = null;

                // 设置搜索框为只读，使用虚拟键盘输入
                $('#searchKey').attr('readonly', true);

                // 确保焦点在文档上，以便能捕获键盘事件
                $(document).focus();

                console.log('页面初始化完成，当前模式:', keyboardState.mode);

                // 初始化左侧面板
                updateSelectedDrugsPanel();

                // 在表格渲染完成后，选中第一行
                setTimeout(function () {
                    var $rows = $('.layui-table-box .layui-table-body tbody tr');
                    if ($rows.length > 0) {
                        // 清除所有行的选中状态和输入框焦点
                        $rows.removeClass('selected-row');
                        $('.meds-dose').removeClass('input-focus');

                        // 选中第一行并设置状态
                        var $firstRow = $rows.eq(0);
                        $firstRow.addClass('selected-row');
                        keyboardState.selectedRowIndex = 0;

                        // 滚动到第一行
                        try {
                            $firstRow[0].scrollIntoView({ block: 'center' });
                        } catch (e) {
                            console.error('滚动到第一行出错:', e);
                        }

                        console.log('表格渲染完成后自动选中第一行');

                        // 如果当前是数量输入模式，则设置相应的焦点
                        if (keyboardState.mode === 'inputQuantity') {
                            var $input = $firstRow.find('.meds-dose');
                            $input.addClass('input-focus');
                            keyboardState.$currentInput = $input;

                            // 显示数量输入模态框
                            showQuantityInputModal($firstRow);
                        }
                    }
                }, 200); // 给表格一点渲染时间
            });

            // 渲染表格
            var tableIns = table.render({
                elem: '#medsTable',
                url: '/admin/warehouse_drug/list',
                method: 'post',
                where: queryParams,
                page: true,
                cols: [[
                    { field: 'ID', title: '库存ID', sort: true, align: 'center' },
                    { field: 'Drug_id', title: '药品ID', sort: true, align: 'center' },
                    { field: 'Drug_name', title: '药品名称', sort: true },
                    { field: 'Price', title: '单价', sort: true, align: 'center' },
                    { field: 'Quantity', title: '库存量', sort: true, align: 'center' },
                    {
                        title: '数量', templet: function (d) {
                            var value = selectedDrugsData[d.ID]?.single_dose || '';
                            return '<input type="number" class="layui-input meds-dose" data-id="' + d.ID + '" min="1" max="999999" step="1" value="' + value + '" onkeyup="this.value = this.value > 999999 ? 999999 : this.value;" readonly>';
                        }
                    }
                ]],
                response: {
                    statusName: 'code'
                    , statusCode: 200
                    , msgName: 'msg'
                    , countName: 'count'
                    , dataName: 'data'
                },
                height: 'full-100',
                done: function (res) {
                    // 标记搜索请求已完成
                    keyboardState.searchPending = false;
                    console.log('表格渲染完成，当前模式:', keyboardState.mode, '数据行数:', res.data?.length || 0);

                    // 限制数量的输入范围
                    $('.meds-dose').on('input', function () {
                        updateQuantityInput($(this));
                    });

                    // 添加数量输入框的点击事件处理
                    $('.meds-dose').on('click', function (e) {
                        // 阻止事件冒泡，防止触发其他事件
                        e.stopPropagation();

                        // 获取当前点击的输入框
                        var $input = $(this);
                        var $row = $input.closest('tr');

                        // 清除之前的状态
                        $('.layui-table-box .layui-table-body tbody tr').removeClass('selected-row');
                        $('.meds-dose').removeClass('input-focus');

                        // 设置当前行为选中状态
                        $row.addClass('selected-row');
                        $input.addClass('input-focus');

                        // 更新状态
                        keyboardState.mode = 'inputQuantity';
                        keyboardState.$currentInput = $input;
                        keyboardState.inputBuffer = $input.val() || '';

                        // 获取行索引
                        var $rows = $('.layui-table-box .layui-table-body tbody tr');
                        var rowIndex = $rows.index($row);
                        keyboardState.selectedRowIndex = rowIndex;

                        console.log('点击输入框，行索引:', rowIndex);

                        // 隐藏虚拟键盘，只显示数量输入模态框
                        hideVirtualKeyboard();

                        // 显示数量输入模态框
                        showQuantityInputModal($row);

                        // 将焦点放在文档上，以便捕获键盘事件
                        $(document).focus();
                    });

                    // 回填已选中的数据
                    Object.keys(selectedDrugsData).forEach(function (id) {
                        var $input = $('.meds-dose[data-id="' + id + '"]');
                        if ($input.length) {
                            $input.val(selectedDrugsData[id].single_dose);
                        }
                    });

                    // 获取表格行
                    var $rows = $('.layui-table-box .layui-table-body tbody tr');
                    console.log('表格渲染完成后的行数:', $rows.length);

                    // 如果表格有行，则始终默认选中第一行（无论当前是什么模式）
                    if ($rows.length > 0) {
                        // 清除所有行的选中状态和输入框焦点
                        $rows.removeClass('selected-row');
                        $('.meds-dose').removeClass('input-focus');

                        // 选中第一行并设置状态
                        var $firstRow = $rows.eq(0);
                        $firstRow.addClass('selected-row');
                        keyboardState.selectedRowIndex = 0;

                        // 滚动到第一行
                        try {
                            $firstRow[0].scrollIntoView({ block: 'center' });
                        } catch (e) {
                            console.error('滚动到第一行出错:', e);
                        }

                        console.log('表格渲染完成后自动选中第一行');

                        // 如果当前是数量输入模式，则设置相应的焦点
                        if (keyboardState.mode === 'inputQuantity') {
                            var $input = $firstRow.find('.meds-dose');
                            $input.addClass('input-focus');
                            keyboardState.$currentInput = $input;

                            // 显示数量输入模态框
                            showQuantityInputModal($firstRow);
                        }
                    }

                    // 添加表格行点击事件
                    $('.layui-table-box .layui-table-body tbody').on('click', 'tr', function (e) {
                        // 如果点击的是输入框，不处理（让输入框的点击事件处理）
                        if ($(e.target).hasClass('meds-dose') || $(e.target).closest('.meds-dose').length > 0) {
                            return;
                        }

                        // 清除之前的选中状态
                        $('.layui-table-box .layui-table-body tbody tr').removeClass('selected-row');
                        $('.meds-dose').removeClass('input-focus');

                        // 设置当前行为选中状态
                        var $row = $(this);
                        $row.addClass('selected-row');

                        // 获取行索引
                        var $rows = $('.layui-table-box .layui-table-body tbody tr');
                        var rowIndex = $rows.index($row);

                        // 更新状态
                        keyboardState.mode = 'selectRow';
                        keyboardState.selectedRowIndex = rowIndex;
                        keyboardState.$currentInput = null;
                        keyboardState.inputBuffer = '';

                        console.log('点击表格行，行索引:', rowIndex);

                        // 隐藏键盘和模态框
                        hideVirtualKeyboard();
                        hideQuantityInputModal();
                    });
                }
            });

            // 更新数量输入框的值并保存数据
            function updateQuantityInput($input) {
                var val = $input.val();
                var id = $input.data('id');

                // 如果输入为空或0，从选中数据中删除该条记录
                if (!val || parseFloat(val) === 0) {
                    delete selectedDrugsData[id];
                    // 从选择顺序数组中也移除
                    var index = selectionOrder.indexOf(id);
                    if (index > -1) {
                        selectionOrder.splice(index, 1);
                    }
                    updateSelectedDrugsPanel(); // 更新左侧面板
                    return;
                }

                // 允许输入0
                if (val < 0) {
                    $input.val(0);
                    delete selectedDrugsData[id];
                    // 从选择顺序数组中也移除
                    var index = selectionOrder.indexOf(id);
                    if (index > -1) {
                        selectionOrder.splice(index, 1);
                    }
                    updateSelectedDrugsPanel(); // 更新左侧面板
                    return;
                }
                // 如果大于0，则最小值为1
                if (val < 1) {
                    layer.msg('数量不能小于1', { icon: 2 });
                    $input.val(1);
                    val = 1;
                }
                // 限制最大值为100
                if (val > 999999) {
                    $input.val(999999);
                    val = 999999;
                }
                // 限制小数点后一位
                if (val.toString().split('.')[1]?.length > 1) {
                    val = parseFloat(val).toFixed(1);
                    $input.val(val);
                }

                // 保存选中的药品数据
                var $row = $input.closest('tr');

                // 检查是否是新选择的药品
                var isNewSelection = !selectedDrugsData[id];

                selectedDrugsData[id] = {
                    wh_meds_id: $row.find('td[data-field="ID"]').text(),
                    meds_id: $row.find('td[data-field="Drug_id"]').text(),
                    name: $row.find('td[data-field="Drug_name"]').text(),
                    price: $row.find('td[data-field="Price"]').text(),
                    stock: $row.find('td[data-field="Quantity"]').text(),
                    single_dose: parseFloat(val)
                };

                // 如果是新选择的药品，添加到选择顺序数组中
                if (isNewSelection) {
                    selectionOrder.push(id);
                }

                // 更新左侧面板
                updateSelectedDrugsPanel();
            }

            // 添加对分页容器的事件委托
            $(document).on('click', '.layui-laypage-prev:not(.layui-disabled)', function () {
                console.log('点击了上一页按钮');
            });

            $(document).on('click', '.layui-laypage-next:not(.layui-disabled)', function () {
                console.log('点击了下一页按钮');
            });

            // 搜索按钮点击事件
            $('#searchBtn').on('click', function () {
                executeSearch();
            });

            // 执行搜索函数 - 将搜索逻辑提取为单独的函数
            function executeSearch() {
                if (keyboardState.searchPending) return; // 如果有待处理的搜索，则不执行

                keyboardState.searchPending = true; // 标记搜索开始
                queryParams.key = keyboardState.inputBuffer; // 使用inputBuffer而不是searchKey的值
                console.log('执行搜索:', queryParams.key);

                table.reload('medsTable', {
                    where: queryParams,
                    page: {
                        curr: 1 // 重置到第一页
                    }
                });
            }

            // 处理输入类型变化
            function handleInputTypeChange(isLetter) {
                if (isLetter) {
                    // 如果输入的是字母，切换到搜索模式
                    hideQuantityInputModal(); // 隐藏数量输入面板
                    keyboardState.mode = 'search'; // 设置模式为搜索

                    // 显示虚拟键盘
                    showVirtualKeyboard();

                    // 执行搜索
                    executeSearch();
                } else {
                    // 如果输入的是数字，根据当前模式决定行为
                    if (keyboardState.mode === 'inputQuantity') {
                        // 如果当前是数量输入模式，继续保持
                        // 确保数量输入模态框显示
                        var $rows = $('.layui-table-box .layui-table-body tbody tr');
                        if ($rows.length > 0) {
                            var rowIndex = keyboardState.selectedRowIndex;
                            if (rowIndex >= 0 && rowIndex < $rows.length) {
                                var $currentRow = $rows.eq(rowIndex);
                                // 如果模态框未显示，则显示它
                                if ($('#quantityInputModal').css('display') === 'none') {
                                    showQuantityInputModal($currentRow);
                                }
                            }
                        }
                    } else {
                        // 如果当前是搜索模式，执行搜索
                        executeSearch();
                    }
                }
            }

            // 修改提交按钮点击事件
            $('#submitBtn').on('click', function () {
                submitSelection();
            });

            // 添加搜索延迟变量
            var searchTimeout = null;
            var pendingInputBuffer = '';

            // 提交选择的药品
            function submitSelection() {
                // 使用selectionOrder数组确保按照用户选择顺序处理药品
                var selectedData = [];

                // 确保按照选择顺序添加药品
                selectionOrder.forEach(function (id) {
                    var item = selectedDrugsData[id];
                    if (item && item.single_dose && parseFloat(item.single_dose) > 0) {
                        selectedData.push(item);
                    }
                });

                if (selectedData.length === 0) {
                    layer.msg('请至少选择一个药品并输入数量', { icon: 2 });
                    return;
                }

                // 获取父页面的jQuery对象
                var parent$ = window.parent.layui.$;
                var medsTableBody = parent$('#prescription_drug_table_body');

                // 第一步：先从表格中删除所有即将添加的药品中ID重复的行
                // 收集需要删除的行索引和药品名称
                var rowsToDelete = [];
                var duplicateDrugNames = [];

                // 获取所有即将添加的药品的ID列表
                var newDrugIds = selectedData.map(function (item) {
                    return item.wh_meds_id;
                });

                // 查找表格中与新药品ID匹配的行
                medsTableBody.find('tr').each(function (index) {
                    var $row = $(this);
                    var whDrugId = $row.find('td:first').text();
                    if (newDrugIds.includes(whDrugId)) {
                        // 记录要删除的行索引
                        rowsToDelete.push(index);
                        // 记录药品名称（第3列是药品名称）
                        var drugName = $row.find('td').eq(2).text();
                        duplicateDrugNames.push(drugName);
                    }
                });

                // 如果有重复药品，显示提示信息
                if (duplicateDrugNames.length > 0) {
                    var msg = "您本次添加，与之前选择的数据有重复，分别为：" + duplicateDrugNames.join("、") + "。现已为您删除，以最新的添加数据为准。";
                    parent.layer.msg(msg, { icon: 0, time: 3000 });
                }

                // 从后向前删除行（避免索引变化问题）
                rowsToDelete.sort(function (a, b) { return b - a; }).forEach(function (index) {
                    medsTableBody.find('tr').eq(index).remove();
                });

                // 第二步：将所有新选择的药品添加到表格末尾
                selectedData.forEach(function (item) {
                    var newRow = `
                        <tr>
                            <td>${item.wh_meds_id}</td>
                            <td>${item.meds_id}</td>
                            <td>${item.name}</td>
                            <td>${item.price}</td>
                            <td>${item.stock}</td>
                            <td>${item.single_dose}</td>
                            <td>
                                <button type="button" class="layui-btn layui-btn-danger layui-btn-sm delete-row">删除</button>
                            </td>
                        </tr>
                    `;

                    // 直接添加新行到表格末尾
                    medsTableBody.append(newRow);
                });

                // 关闭弹窗
                var index = parent.layer.getFrameIndex(window.name);
                parent.layer.close(index);
            }

            // ============= 键盘操作相关函数 =============

            // 选中特定行
            function selectRow(index) {
                // 清除之前的选中状态
                console.log('执行selectRow, 索引:', index, '当前模式:', keyboardState.mode);

                // 尝试多种选择器找到表格行
                var $rows = $('#medsTable tbody tr');
                if ($rows.length === 0) {
                    $rows = $('.layui-table-box .layui-table-body tbody tr');
                    console.log('使用layui特定选择器找到行数:', $rows.length);
                }

                if ($rows.length === 0) {
                    console.warn('selectRow: 无法找到表格行');
                    return;
                }

                // 清除样式时根据当前模式决定
                if (keyboardState.mode === 'inputQuantity') {
                    // 在输入数量模式下清除所有选中状态
                    $rows.removeClass('selected-row');
                    $('.meds-dose').removeClass('input-focus');
                } else if (keyboardState.mode === 'selectRow') {
                    // 在选择行模式下只清除行的选中状态
                    $rows.removeClass('selected-row');
                } else {
                    // 搜索模式下全部清除
                    $rows.removeClass('selected-row');
                    $('.meds-dose').removeClass('input-focus');
                }

                // 确保索引在有效范围内
                if (index < 0) index = 0;
                if (index >= $rows.length) index = $rows.length - 1;

                // 选中当前行并更新状态
                var $currentRow = $rows.eq(index);
                $currentRow.addClass('selected-row');
                keyboardState.selectedRowIndex = index;
                console.log('已选中行:', index, $currentRow);

                // 如果是输入数量模式，设置输入焦点
                if (keyboardState.mode === 'inputQuantity') {
                    keyboardState.$currentInput = $currentRow.find('.meds-dose');
                    keyboardState.$currentInput.addClass('input-focus');
                }

                // 滚动到可见区域
                try {
                    $currentRow[0].scrollIntoView({ block: 'center' });
                } catch (e) {
                    console.error('滚动到第一行出错:', e);
                }
            }

            // 显示虚拟键盘
            function showVirtualKeyboard() {
                $('#virtualKeyboard').show();
            }

            // 隐藏虚拟键盘
            function hideVirtualKeyboard() {
                $('#virtualKeyboard').hide();
            }

            // 更新虚拟键盘显示的输入内容和搜索框
            function updateVirtualKeyboardInput(text) {
                $('#keyboardInput').text(text);
                $('#searchKey').val(text);  // 确保searchKey与keyboardInput同步
            }

            // 全局键盘事件监听
            $(document).on('keydown', function (e) {
                // 已经捕获的键不触发默认行为
                if ([8, 13, 37, 38, 39, 40].includes(e.keyCode)) {
                    e.preventDefault();
                }

                // 处理Ctrl+Enter快捷键，触发提交按钮点击
                if (e.keyCode === 13 && e.ctrlKey) {
                    layer.load(2);
                    console.log('检测到Ctrl+Enter快捷键，触发提交按钮');
                    $('#submitBtn').trigger('click');
                    return;
                }

                // 处理Backspace键 (8) 和 DEL键 (46) - 在任何非空输入状态下可用
                if (e.keyCode === 8 || e.keyCode === 46) {
                    // 在搜索模式下，删除一个字符并触发搜索
                    if (keyboardState.mode === 'search' && keyboardState.inputBuffer.length > 0) {
                        keyboardState.inputBuffer = keyboardState.inputBuffer.slice(0, -1);
                        updateVirtualKeyboardInput(keyboardState.inputBuffer);
                        executeSearch();
                    }
                    // 在数量输入模式下，删除一个字符并更新模态框和输入框
                    else if (keyboardState.mode === 'inputQuantity') {
                        // 完全清空输入
                        keyboardState.inputBuffer = '';

                        // 更新模态框显示
                        $('#modalQuantityField').text('请在此处输入数量');

                        // 更新输入框的值
                        if (keyboardState.$currentInput) {
                            keyboardState.$currentInput.val('');
                            updateQuantityInput(keyboardState.$currentInput);
                        }

                        // 更新左侧面板
                        updateSelectedDrugsPanel();
                    }
                    return; // 不再执行后续的模式处理
                }

                // 处理字母键输入 (65-90: A-Z) - 在任何模式下，字母键都触发搜索模式
                if (e.keyCode >= 65 && e.keyCode <= 90) {
                    // 如果不是搜索模式，切换到搜索模式
                    if (keyboardState.mode !== 'search') {
                        // 清除所有选中状态
                        $('.layui-table-box .layui-table-body tbody tr').removeClass('selected-row');
                        $('.meds-dose').removeClass('input-focus');
                        hideQuantityInputModal();

                        // 切换到搜索模式
                        keyboardState.mode = 'search';
                        keyboardState.selectedRowIndex = -1;
                        keyboardState.$currentInput = null;
                        keyboardState.inputBuffer = String.fromCharCode(e.keyCode);
                        pendingInputBuffer = keyboardState.inputBuffer; // 保存到待处理缓冲区

                        // 显示虚拟键盘并更新输入
                        showVirtualKeyboard();
                        updateVirtualKeyboardInput(keyboardState.inputBuffer);

                        // 使用延迟执行搜索，避免快速输入漏字
                        clearTimeout(searchTimeout);
                        searchTimeout = setTimeout(function () {
                            // 确保使用最新的输入缓冲区
                            keyboardState.inputBuffer = pendingInputBuffer;
                            executeSearch();
                        }, 200); // 200毫秒延迟，可根据需要调整

                        return; // 不再执行后续的模式处理
                    } else {
                        // 已经是搜索模式，累加输入
                        keyboardState.inputBuffer += String.fromCharCode(e.keyCode);
                        pendingInputBuffer = keyboardState.inputBuffer; // 保存到待处理缓冲区
                        updateVirtualKeyboardInput(keyboardState.inputBuffer);
                        showVirtualKeyboard();

                        // 使用延迟执行搜索，避免快速输入漏字
                        clearTimeout(searchTimeout);
                        searchTimeout = setTimeout(function () {
                            // 确保使用最新的输入缓冲区
                            keyboardState.inputBuffer = pendingInputBuffer;
                            executeSearch();
                        }, 200); // 200毫秒延迟，可根据需要调整

                        return; // 不再执行后续的模式处理
                    }
                }

                // 处理数字键 (48-57: 0-9, 96-105: 小键盘0-9) - 在selectRow模式下，直接进入inputQuantity模式
                if ((e.keyCode >= 48 && e.keyCode <= 57) || (e.keyCode >= 96 && e.keyCode <= 105)) {
                    var char;
                    if (e.keyCode >= 96 && e.keyCode <= 105) {
                        // 小键盘数字转换为普通数字
                        char = String.fromCharCode(e.keyCode - 48);
                    } else {
                        char = String.fromCharCode(e.keyCode);
                    }

                    // 根据当前模式决定行为
                    if (keyboardState.mode === 'selectRow') {
                        console.log('selectRow模式下输入数字，直接切换到inputQuantity模式');

                        // 获取当前选中行
                        var $rows = $('.layui-table-box .layui-table-body tbody tr');
                        if ($rows.length === 0 || keyboardState.selectedRowIndex < 0) {
                            return;
                        }

                        var rowIndex = keyboardState.selectedRowIndex;
                        if (rowIndex >= $rows.length) rowIndex = $rows.length - 1;

                        var $currentRow = $rows.eq(rowIndex);

                        // 先切换到输入数量模式，再设置初始数字
                        keyboardState.mode = 'inputQuantity';
                        keyboardState.inputBuffer = char; // 设置为第一个数字

                        // 设置当前行的输入框为焦点
                        var $input = $currentRow.find('.meds-dose');
                        $input.addClass('input-focus');
                        $input.val(char);
                        keyboardState.$currentInput = $input;

                        // 确保虚拟键盘被隐藏
                        hideVirtualKeyboard();

                        // 显示数量输入模态框
                        showQuantityInputModal($currentRow);

                        return; // 阻止继续处理
                    }
                    else if (keyboardState.mode === 'search') {
                        // 在搜索模式下继续累加数字
                        keyboardState.inputBuffer += char;
                        pendingInputBuffer = keyboardState.inputBuffer;
                        updateVirtualKeyboardInput(keyboardState.inputBuffer);

                        // 使用延迟执行搜索，避免快速输入漏字
                        clearTimeout(searchTimeout);
                        searchTimeout = setTimeout(function () {
                            keyboardState.inputBuffer = pendingInputBuffer;
                            executeSearch();
                        }, 200);

                        return; // 阻止继续处理
                    }
                    else if (keyboardState.mode === 'inputQuantity') {
                        // 在数量输入模式下，累加数字
                        keyboardState.inputBuffer += char;

                        // 更新模态框显示
                        $('#modalQuantityField').text(keyboardState.inputBuffer);

                        // 更新输入框的值
                        if (keyboardState.$currentInput) {
                            keyboardState.$currentInput.val(keyboardState.inputBuffer);
                            updateQuantityInput(keyboardState.$currentInput);
                        }

                        return; // 阻止继续处理
                    }
                }

                // 处理分页的左右箭头键 (所有模式下均可使用)
                if (e.keyCode === 37 || e.keyCode === 39) {  // 左右箭头键
                    // 左箭头键 - 上一页
                    if (e.keyCode === 37) {
                        handlePagination('prev');
                    }
                    // 右箭头键 - 下一页
                    else if (e.keyCode === 39) {
                        handlePagination('next');
                    }
                    return; // 箭头键处理完毕，不再执行下面的模式处理
                }

                // 根据当前模式处理键盘事件
                switch (keyboardState.mode) {
                    case 'search':
                        handleSearchModeKeydown(e);
                        break;
                    case 'selectRow':
                        handleSelectRowModeKeydown(e);
                        break;
                    case 'inputQuantity':
                        handleInputQuantityModeKeydown(e);
                        break;
                }
            });

            // 搜索模式的键盘事件处理
            function handleSearchModeKeydown(e) {
                // 字母、数字键处理 (48-57: 0-9, 65-90: A-Z)
                if ((e.keyCode >= 48 && e.keyCode <= 57) || (e.keyCode >= 65 && e.keyCode <= 90)) {
                    var char = String.fromCharCode(e.keyCode);
                    var isNumber = e.keyCode >= 48 && e.keyCode <= 57;
                    var isLetter = e.keyCode >= 65 && e.keyCode <= 90;

                    keyboardState.inputBuffer += char;
                    pendingInputBuffer = keyboardState.inputBuffer; // 保存到待处理缓冲区
                    updateVirtualKeyboardInput(keyboardState.inputBuffer);
                    showVirtualKeyboard();

                    // 使用延迟执行搜索，避免快速输入漏字
                    clearTimeout(searchTimeout);
                    searchTimeout = setTimeout(function () {
                        // 确保使用最新的输入缓冲区
                        keyboardState.inputBuffer = pendingInputBuffer;
                        // 处理输入类型变化
                        handleInputTypeChange(isLetter);
                    }, 200); // 200毫秒延迟
                }
                // 处理回车键处理：从搜索模式切换到输入数量模式
                else if (e.keyCode === 13 && !e.ctrlKey) {
                    console.log('搜索模式下按下回车键');

                    // 检查是否有正在进行的搜索请求
                    if (keyboardState.searchPending) {
                        console.log('搜索请求尚未完成，等待数据加载...');
                        // 设置一个标记，表示回车事件待处理
                        var waitForSearchInterval = setInterval(function () {
                            if (!keyboardState.searchPending) {
                                clearInterval(waitForSearchInterval);
                                // 再次检查是否有表格行
                                handleSearchEnterKey();
                            }
                        }, 100); // 每100ms检查一次
                        return;
                    }

                    // 处理搜索模式下的回车键
                    handleSearchEnterKey();
                }
            }

            // 处理搜索模式下的回车键逻辑
            function handleSearchEnterKey() {
                // 尝试多种选择器找到表格行
                var $rows = $('#medsTable tbody tr');
                if ($rows.length === 0) {
                    $rows = $('.layui-table-box .layui-table-body tbody tr');
                    console.log('使用layui特定选择器找到行数:', $rows.length);
                }

                console.log('找到表格行数量:', $rows.length);

                // 如果没有行，提示用户搜索结果为空
                if ($rows.length === 0) {
                    console.log('没有找到表格行，无法显示模态框');
                    layer.msg('没有找到药品，请重新搜索', { icon: 0 });
                    return;
                }

                // 直接切换到输入数量模式
                keyboardState.mode = 'inputQuantity';
                keyboardState.selectedRowIndex = 0; // 设置为第一行
                keyboardState.inputBuffer = '';

                // 选中第一行并立即设置焦点
                $rows.removeClass('selected-row');
                $('.meds-dose').removeClass('input-focus');

                var $currentRow = $rows.eq(0);
                $currentRow.addClass('selected-row');
                var $input = $currentRow.find('.meds-dose');
                $input.addClass('input-focus');
                keyboardState.$currentInput = $input;
                console.log('已选中第一行并设置焦点');

                // 滚动到可见区域
                try {
                    $currentRow[0].scrollIntoView({ block: 'center' });
                } catch (e) {
                    console.error('滚动到第一行出错:', e);
                }

                // 先隐藏虚拟键盘，再显示数量输入模态框
                hideVirtualKeyboard();

                // 显示数量输入模态框
                showQuantityInputModal($currentRow);
            }

            // 选择行模式的键盘事件处理
            function handleSelectRowModeKeydown(e) {
                // 上下键处理
                if (e.keyCode === 38) { // 上键
                    console.log('选择行模式下按上键');
                    // 选择上一行
                    var newIndex = keyboardState.selectedRowIndex - 1;
                    selectRow(newIndex);
                    console.log('向上选择行:', newIndex);
                }
                else if (e.keyCode === 40) { // 下键
                    console.log('选择行模式下按下键');
                    // 选择下一行
                    var newIndex = keyboardState.selectedRowIndex + 1;
                    selectRow(newIndex);
                    console.log('向下选择行:', newIndex);
                }
                // 回车键处理：切换到输入数量模式
                else if (e.keyCode === 13 && !e.ctrlKey) {
                    console.log('选择行模式下按下回车键, 当前选中行索引:', keyboardState.selectedRowIndex);

                    // 获取当前选中行
                    var $rows = $('.layui-table-box .layui-table-body tbody tr');
                    if ($rows.length === 0) {
                        console.warn('选择行模式：找不到表格行');
                        return;
                    }

                    // 确保索引在有效范围内
                    var rowIndex = keyboardState.selectedRowIndex;
                    if (rowIndex < 0) rowIndex = 0;
                    if (rowIndex >= $rows.length) rowIndex = $rows.length - 1;

                    var $currentRow = $rows.eq(rowIndex);

                    // 进入数量输入模式
                    keyboardState.mode = 'inputQuantity';
                    keyboardState.inputBuffer = '';

                    // 设置当前行的输入框为焦点
                    var $input = $currentRow.find('.meds-dose');
                    $input.addClass('input-focus');
                    keyboardState.$currentInput = $input;

                    // 确保虚拟键盘被隐藏
                    hideVirtualKeyboard();

                    // 显示数量输入模态框
                    showQuantityInputModal($currentRow);
                }
                // 数字键直接进入数量输入模式
                else if ((e.keyCode >= 48 && e.keyCode <= 57) || (e.keyCode >= 96 && e.keyCode <= 105)) {
                    console.log('选择行模式下按下数字键，直接进入数量输入模式');

                    // 获取当前选中行
                    var $rows = $('.layui-table-box .layui-table-body tbody tr');
                    if ($rows.length === 0 || keyboardState.selectedRowIndex < 0) {
                        return;
                    }

                    var rowIndex = keyboardState.selectedRowIndex;
                    if (rowIndex >= $rows.length) rowIndex = $rows.length - 1;

                    var $currentRow = $rows.eq(rowIndex);

                    // 先切换到输入数量模式
                    keyboardState.mode = 'inputQuantity';

                    // 设置初始数字
                    var char;
                    if (e.keyCode >= 96 && e.keyCode <= 105) {
                        // 小键盘数字转换为普通数字
                        char = String.fromCharCode(e.keyCode - 48);
                    } else {
                        char = String.fromCharCode(e.keyCode);
                    }
                    keyboardState.inputBuffer = char;

                    // 设置当前行的输入框为焦点
                    var $input = $currentRow.find('.meds-dose');
                    $input.addClass('input-focus');
                    $input.val(char);
                    keyboardState.$currentInput = $input;

                    // 确保虚拟键盘被隐藏
                    hideVirtualKeyboard();

                    // 显示数量输入模态框
                    showQuantityInputModal($currentRow);

                    // 更新模态框中的数量显示
                    $('#modalQuantityField').text(char);
                }
            }

            // 显示数量输入模态框
            function showQuantityInputModal($row) {
                if (!$row || $row.length === 0) {
                    console.error('showQuantityInputModal: 无效的行元素', $row);
                    return;
                }

                try {
                    console.log('显示数量输入模态框，行元素:', $row);

                    // 获取药品信息
                    var drugName = $row.find('td[data-field="Drug_name"]').text();
                    var inventory = $row.find('td[data-field="Quantity"]').text();

                    console.log('获取到的药品信息:', { drugName, inventory });
                    console.log('表格行内容:', $row.html());

                    if (!drugName) {
                        console.warn('未能获取到药品名称，尝试直接获取DOM内容');
                        // 尝试不同的方式获取内容
                        drugName = $row.find('td:eq(2)').text();
                        console.log('尝试用td:eq(2)获取药品名称:', drugName);

                        if (!drugName) {
                            // 遍历所有单元格查看内容
                            var cellContents = [];
                            $row.find('td').each(function (i) {
                                cellContents.push(i + ': ' + $(this).text());
                            });
                            console.log('所有单元格内容:', cellContents);

                            drugName = $row.find('td').eq(2).text() || '未知药品';
                        }
                    }

                    if (!inventory) {
                        console.warn('未能获取到库存量，尝试直接获取DOM内容');
                        inventory = $row.find('td:eq(4)').text();
                        console.log('尝试用td:eq(4)获取库存量:', inventory);

                        if (!inventory) {
                            inventory = $row.find('td').eq(4).text() || '0';
                        }
                    }

                    // 获取当前行的数量输入框值
                    var $input = $row.find('.meds-dose');
                    var currentValue = $input.val() || '';

                    // 如果没有从keyboardState获取到输入，则尝试从输入框获取
                    if (!keyboardState.inputBuffer && currentValue) {
                        keyboardState.inputBuffer = currentValue;
                    }

                    // 更新模态框内容
                    $('#modalDrugName').text(drugName);
                    $('#modalInventory').text('库存量：' + inventory);

                    // 如果inputBuffer为空但当前输入框有值，使用当前输入框的值
                    if (!keyboardState.inputBuffer && currentValue) {
                        $('#modalQuantityField').text(currentValue);
                    }
                    // 如果inputBuffer有值，显示inputBuffer
                    else if (keyboardState.inputBuffer) {
                        $('#modalQuantityField').text(keyboardState.inputBuffer);
                    }
                    // 都没有值，显示提示文本
                    else {
                        $('#modalQuantityField').text('请在此处输入数量');
                    }

                    // 显示模态框并应用动画
                    $('#quantityInputModal').css('display', 'block');
                } catch (error) {
                    console.error('显示模态框出错:', error);
                }
            }

            // 隐藏数量输入模态框
            function hideQuantityInputModal() {
                $('#quantityInputModal').css('display', 'none');
            }

            // 输入数量模式的键盘事件处理
            function handleInputQuantityModeKeydown(e) {
                // 数字键处理 (48-57: 0-9, 96-105: 小键盘0-9, 190/110: .)
                if ((e.keyCode >= 48 && e.keyCode <= 57) ||
                    (e.keyCode >= 96 && e.keyCode <= 105) ||
                    e.keyCode === 190 || e.keyCode === 110) {
                    var char;
                    if (e.keyCode >= 96 && e.keyCode <= 105) {
                        // 小键盘数字转换为普通数字
                        char = String.fromCharCode(e.keyCode - 48);
                    } else if (e.keyCode === 110 || e.keyCode === 190) {
                        char = '.';
                    } else {
                        char = String.fromCharCode(e.keyCode);
                    }

                    // 限制小数点后只能输入一位
                    if (char === '.' && keyboardState.inputBuffer.includes('.')) {
                        return;
                    }

                    // 当前行是否是刚刚被选中的
                    var justSwitchedRow = keyboardState.recentlySwitchedRow === true;
                    // 清除行切换标记
                    keyboardState.recentlySwitchedRow = false;

                    // 如果是刚刚通过上下键切换的行，或者模态框中的文本与输入缓冲区相同，则覆盖而不是追加
                    if (justSwitchedRow ||
                        $('#modalQuantityField').text() === keyboardState.inputBuffer ||
                        keyboardState.inputBuffer === '') {
                        keyboardState.inputBuffer = char;
                    } else {
                        // 累加输入
                        keyboardState.inputBuffer += char;
                    }

                    // 更新模态框显示
                    $('#modalQuantityField').text(keyboardState.inputBuffer);

                    // 更新输入框的值
                    if (keyboardState.$currentInput) {
                        keyboardState.$currentInput.val(keyboardState.inputBuffer);
                        updateQuantityInput(keyboardState.$currentInput);
                    }
                }
                // 回车键处理：确认输入，返回搜索模式
                else if (e.keyCode === 13 && !e.ctrlKey) {
                    console.log('输入数量模式下按下回车键，确认输入并切换到选择行模式');

                    // 验证并保存当前输入
                    if (keyboardState.$currentInput) {
                        updateQuantityInput(keyboardState.$currentInput);
                    }

                    // 保存当前选中的行，以便后续重新应用样式
                    var currentRowIndex = keyboardState.selectedRowIndex;

                    // 切换到选择行模式（而不是搜索模式）
                    keyboardState.mode = 'selectRow';
                    keyboardState.inputBuffer = '';
                    keyboardState.recentlySwitchedRow = false;

                    // 清除输入框的焦点样式
                    $('.meds-dose').removeClass('input-focus');

                    // 确保当前行仍然保持选中状态
                    var $rows = $('.layui-table-box .layui-table-body tbody tr');
                    if ($rows.length > 0 && currentRowIndex >= 0 && currentRowIndex < $rows.length) {
                        // 先清除所有行的选中状态
                        $rows.removeClass('selected-row');
                        // 然后为当前行添加选中状态
                        $rows.eq(currentRowIndex).addClass('selected-row');
                    }

                    keyboardState.$currentInput = null;

                    // 隐藏键盘和模态框
                    hideVirtualKeyboard();
                    hideQuantityInputModal();

                    // 确保左侧面板更新
                    updateSelectedDrugsPanel();
                }
                // 上下键处理：在输入模式中也可以切换行
                else if (e.keyCode === 38) { // 上键
                    // 保存当前输入
                    if (keyboardState.$currentInput) {
                        updateQuantityInput(keyboardState.$currentInput);
                    }

                    // 先清空输入缓冲区
                    keyboardState.inputBuffer = '';

                    // 获取当前行和新行
                    var $rows = $('.layui-table-box .layui-table-body tbody tr');
                    var newIndex = keyboardState.selectedRowIndex - 1;
                    if (newIndex < 0) newIndex = 0;

                    // 选中新行
                    selectRow(newIndex);

                    // 使用新行的数据更新模态框，但不隐藏模态框也不重新显示
                    var $newRow = $rows.eq(newIndex);
                    updateQuantityModalContent($newRow);

                    // 标记刚刚切换了行
                    keyboardState.recentlySwitchedRow = true;

                    // 更新左侧面板
                    updateSelectedDrugsPanel();
                }
                else if (e.keyCode === 40) { // 下键
                    // 保存当前输入
                    if (keyboardState.$currentInput) {
                        updateQuantityInput(keyboardState.$currentInput);
                    }

                    // 先清空输入缓冲区
                    keyboardState.inputBuffer = '';

                    // 获取当前行和新行
                    var $rows = $('.layui-table-box .layui-table-body tbody tr');
                    var newIndex = keyboardState.selectedRowIndex + 1;
                    if (newIndex >= $rows.length) newIndex = $rows.length - 1;

                    // 选中新行
                    selectRow(newIndex);

                    // 使用新行的数据更新模态框，但不隐藏模态框也不重新显示
                    var $newRow = $rows.eq(newIndex);
                    updateQuantityModalContent($newRow);

                    // 标记刚刚切换了行
                    keyboardState.recentlySwitchedRow = true;

                    // 更新左侧面板
                    updateSelectedDrugsPanel();
                }
            }

            // 新增函数：更新数量输入模态框内容而不重新显示
            function updateQuantityModalContent($row) {
                if (!$row || $row.length === 0) {
                    console.error('updateQuantityModalContent: 无效的行元素', $row);
                    return;
                }

                try {
                    console.log('更新数量输入模态框内容，行元素:', $row);

                    // 获取药品信息
                    var drugName = $row.find('td[data-field="Drug_name"]').text();
                    var inventory = $row.find('td[data-field="Quantity"]').text();

                    // 如果无法通过data-field获取，尝试其他方式
                    if (!drugName) {
                        drugName = $row.find('td:eq(2)').text();
                        if (!drugName) {
                            drugName = $row.find('td').eq(2).text() || '未知药品';
                        }
                    }

                    if (!inventory) {
                        inventory = $row.find('td:eq(4)').text();
                        if (!inventory) {
                            inventory = $row.find('td').eq(4).text() || '0';
                        }
                    }

                    // 更新模态框内容
                    $('#modalDrugName').text(drugName);
                    $('#modalInventory').text('库存量：' + inventory);

                    // 获取当前行的数量输入框
                    var $input = $row.find('.meds-dose');
                    var currentValue = $input.val() || '';

                    // 设置inputBuffer为当前行的值
                    keyboardState.inputBuffer = currentValue;
                    keyboardState.$currentInput = $input;

                    // 更新模态框中的数量显示
                    if (currentValue) {
                        $('#modalQuantityField').text(currentValue);
                    } else {
                        $('#modalQuantityField').text('请在此处输入数量');
                    }

                    console.log('模态框内容更新完成，当前数量值:', currentValue);

                    // 标记刚刚切换了行
                    keyboardState.recentlySwitchedRow = true;

                } catch (error) {
                    console.error('更新模态框内容出错:', error);
                }
            }

            // 虚拟键盘点击事件处理
            $('.key').on('click', function () {
                var key = $(this).text();
                console.log('虚拟键盘点击:', key);

                // 检查输入类型
                var isNumber = /^[0-9]$/.test(key);
                var isLetter = /^[A-Z]$/.test(key);

                // 如果点击的是字母键，在任何模式下都触发搜索模式
                if (isLetter) {
                    // 如果不是搜索模式，切换到搜索模式
                    if (keyboardState.mode !== 'search') {
                        // 清除所有选中状态
                        $('.layui-table-box .layui-table-body tbody tr').removeClass('selected-row');
                        $('.meds-dose').removeClass('input-focus');
                        hideQuantityInputModal();

                        // 重置并切换到搜索模式
                        keyboardState.mode = 'search';
                        keyboardState.selectedRowIndex = -1;
                        keyboardState.$currentInput = null;
                        keyboardState.inputBuffer = key;
                        pendingInputBuffer = key; // 保存到待处理缓冲区
                    } else {
                        // 已经是搜索模式，累加输入
                        keyboardState.inputBuffer += key;
                        pendingInputBuffer = keyboardState.inputBuffer; // 保存到待处理缓冲区
                    }

                    // 更新显示
                    updateVirtualKeyboardInput(keyboardState.inputBuffer);
                    showVirtualKeyboard();

                    // 使用延迟执行搜索，避免快速输入漏字
                    clearTimeout(searchTimeout);
                    searchTimeout = setTimeout(function () {
                        // 确保使用最新的输入缓冲区
                        keyboardState.inputBuffer = pendingInputBuffer;
                        executeSearch();
                    }, 200); // 200毫秒延迟，可根据需要调整

                    return;
                }

                // 如果是在搜索模式下点击数字键
                if (keyboardState.mode === 'search' && isNumber) {
                    keyboardState.inputBuffer += key;
                    pendingInputBuffer = keyboardState.inputBuffer; // 保存到待处理缓冲区
                    updateVirtualKeyboardInput(keyboardState.inputBuffer);
                    showVirtualKeyboard();

                    // 使用延迟执行搜索，避免快速输入漏字
                    clearTimeout(searchTimeout);
                    searchTimeout = setTimeout(function () {
                        // 确保使用最新的输入缓冲区
                        keyboardState.inputBuffer = pendingInputBuffer;
                        executeSearch();
                    }, 200); // 200毫秒延迟，可根据需要调整
                }
                // 如果是在数量输入模式下点击数字键或小数点
                else if (keyboardState.mode === 'inputQuantity') {
                    // 在数量输入模式下应该不再使用虚拟键盘，使用模态框代替
                    // 这里保留代码以防极端情况下虚拟键盘可见

                    // 只允许数字和小数点
                    if (/^[0-9.]$/.test(key)) {
                        // 限制小数点后只能输入一位
                        if (key === '.' && keyboardState.inputBuffer.includes('.')) {
                            return;
                        }

                        keyboardState.inputBuffer += key;

                        // 更新模态框显示
                        $('#modalQuantityField').text(keyboardState.inputBuffer);

                        // 更新输入框的值
                        if (keyboardState.$currentInput) {
                            keyboardState.$currentInput.val(keyboardState.inputBuffer);
                            updateQuantityInput(keyboardState.$currentInput);
                        }

                        // 确保虚拟键盘被隐藏，应使用模态框
                        hideVirtualKeyboard();
                    }
                }
            });

            // 添加左侧面板的点击交互 - 点击删除按钮直接删除，不需要确认
            $('#selectedDrugsList').on('click', '.delete-drug-btn', function (e) {
                // 阻止事件冒泡
                e.stopPropagation();

                var id = $(this).closest('tr').data('id');

                // 直接删除，不再弹出确认框
                delete selectedDrugsData[id];

                // 从选择顺序数组中也移除
                var index = selectionOrder.indexOf(id);
                if (index > -1) {
                    selectionOrder.splice(index, 1);
                }

                // 如果当前表格中有该药品，清空其输入框
                var $input = $('.meds-dose[data-id="' + id + '"]');
                if ($input.length > 0) {
                    $input.val('');
                }

                // 更新左侧面板
                updateSelectedDrugsPanel();
            });

            // 处理分页操作辅助函数
            function handlePagination(direction) {
                // direction: 'prev' 或 'next'
                var selector = direction === 'prev' ? '.layui-laypage-prev:not(.layui-disabled)' : '.layui-laypage-next:not(.layui-disabled)';
                var btnText = direction === 'prev' ? '上一页' : '下一页';

                // 尝试查找分页按钮
                var $btn = $(selector);

                if ($btn.length > 0) {
                    console.log('找到' + btnText + '按钮，触发点击');
                    // 尝试两种方式触发点击，增加可靠性
                    try {
                        $btn.trigger('click');
                        // 额外再触发一次原生点击事件
                        setTimeout(function () {
                            if ($btn[0] && typeof $btn[0].click === 'function') {
                                $btn[0].click();
                            }

                            // 在翻页操作完成后，延迟一点时间等待表格渲染完成，然后选中第一行
                            setTimeout(function () {
                                // 重置行索引到第一行
                                keyboardState.selectedRowIndex = 0;

                                // 清除所有行的选中状态和输入框焦点
                                $('.layui-table-box .layui-table-body tbody tr').removeClass('selected-row');
                                $('.meds-dose').removeClass('input-focus');

                                // 清空输入缓冲区和模态框的值
                                keyboardState.inputBuffer = '';
                                $('#modalQuantityField').text('请在此处输入数量');

                                // 尝试获取表格行
                                var $rows = $('.layui-table-box .layui-table-body tbody tr');
                                if ($rows.length > 0) {
                                    // 选中第一行
                                    var $firstRow = $rows.eq(0);
                                    $firstRow.addClass('selected-row');

                                    // 滚动到可见区域
                                    try {
                                        $firstRow[0].scrollIntoView({ block: 'center' });
                                    } catch (e) {
                                        console.error('滚动到第一行出错:', e);
                                    }

                                    console.log('翻页后自动选中第一行');
                                }
                            }, 150); // 给表格渲染留出一点时间
                        }, 100);
                    } catch (e) {
                        console.error('触发' + btnText + '点击失败:', e);
                    }
                    return true;
                } else {
                    console.log(btnText + '按钮不存在或已禁用');
                    return false;
                }
            }

            // 操作提示相关的函数
            function checkAndShowTips() {
                // 检查是否已经显示过操作提示
                var tipsShown = getCookie('warehouse_drug_add_tips_shown');

                if (!tipsShown) {
                    // 如果没有显示过，显示提示框
                    $('#tipBox').show();

                    // 绑定确认按钮点击事件
                    $('#tipConfirmBtn').off('click').on('click', function () {
                        // 隐藏提示框
                        $('#tipBox').hide();

                        // 设置cookie，标记已经显示过提示
                        setCookie('warehouse_drug_add_tips_shown', 'true', 3650); // 10年有效期
                    });
                }
            }

            // 初始化状态
            keyboardState.mode = 'selectRow'; // 改为selectRow
            keyboardState.inputBuffer = '';
            updateVirtualKeyboardInput('');
            //清空重选
            $("#cancelBtn").click(function(){
                // 使用layui的确认框询问用户
                layer.confirm('确定要清空当前选择并重新选择吗？', {
                    btn: ['确定','取消'], // 按钮文本
                    title: '操作确认', // 标题
                    anim: 1, // 动画效果
                    icon: 3 // 问号图标
                }, function(index){
                    // 用户点击确定后的回调
                    layer.close(index); // 关闭确认框
                    // 刷新当前页面
                    window.location.reload();
                });
            });
        });
    </script>
</body>

</html>