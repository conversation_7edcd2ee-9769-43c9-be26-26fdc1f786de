package patient

import (
	"mstproject/pkg/common"
	"mstproject/pkg/database"
	"net/http"
	"strings"
)

// 病历问诊告知单同意时间
func Records_notice_sheet_agree_time(w http.ResponseWriter, r *http.Request) {
	// jwt鉴权
	authHeader := r.Header.Get("Authorization")
	tokenString := strings.TrimPrefix(authHeader, "Bearer ")
	claims, err := common.ValidateJWTToken(tokenString)
	if err != nil {
		// token过期提示：Token is expired
		common.JSONResponse(w, http.StatusUnauthorized, map[string]interface{}{
			"code":  500,
			"msg":   "鉴权失败 - token",
			"error": err.Error(),
		})
		return
	}
	patientid := claims["patientid"].(float64)
	if patientid == 0 {
		common.JSONResponse(w, http.StatusUnauthorized, map[string]interface{}{
			"code":  500,
			"msg":   "鉴权失败 - patientid",
			"error": "Invalid token: patientid is missing",
		})
		return
	}
	// 病历ID
	record_id, err := common.CheckInt(r.FormValue("record_id"))
	if err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "参数错误",
			"err":  err.Error(),
		})
		return
	}
	sql := "select notice_sheet_time from patient_records where id = ? and pat_id = ?"
	var notice_sheet_time string
	_ = database.GetOne(sql, &notice_sheet_time, record_id, patientid)
	if notice_sheet_time == "0001-01-01T00:00:00Z" || notice_sheet_time == "" {
		notice_sheet_time = "0"
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "查询成功",
		"data": notice_sheet_time,
		// "sql":  common.DebugSql(sql, record_id, patientid),
	})
}

// 病历问诊告知单同意时间
func Records_notice_sheet_update(w http.ResponseWriter, r *http.Request) {
	// jwt鉴权
	authHeader := r.Header.Get("Authorization")
	tokenString := strings.TrimPrefix(authHeader, "Bearer ")
	claims, err := common.ValidateJWTToken(tokenString)
	if err != nil {
		// token过期提示：Token is expired
		common.JSONResponse(w, http.StatusUnauthorized, map[string]interface{}{
			"code":  500,
			"msg":   "鉴权失败 - token",
			"error": err.Error(),
		})
		return
	}
	patientid := claims["patientid"].(float64)
	if patientid == 0 {
		common.JSONResponse(w, http.StatusUnauthorized, map[string]interface{}{
			"code":  500,
			"msg":   "鉴权失败 - patientid",
			"error": "Invalid token: patientid is missing",
		})
		return
	}
	// 病历ID
	record_id, err := common.CheckInt(r.FormValue("record_id"))
	if err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "参数错误",
			"err":  err.Error(),
		})
		return
	}
	sql := "update patient_records set notice_sheet_time = now() where id = ? and pat_id = ?"
	_, err = database.Query(sql, record_id, patientid)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "更新失败",
			"err":  err.Error(),
			// "sql":  common.DebugSql(sql, record_id, patientid),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "更新成功",
		// "sql":  common.DebugSql(sql, record_id, patientid),
	})
}
