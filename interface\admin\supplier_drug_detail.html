<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 供应商详情</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
    <style>
        .image-preview-container {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-top: 15px;
        }

        .image-preview-item {
            position: relative;
            width: 150px;
            height: 150px;
        }

        .image-preview-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 4px;
            cursor: pointer;
        }
    </style>
</head>

<body>
    <div class="layui-padding-3">
        <div class="layui-row">
            <!-- 第一列 -->
            <div class="layui-col-md6">
                <div class="layui-form-item">
                    <label class="layui-form-label">供应商名称</label>
                    <div class="layui-input-block">
                        <div class="detail-text" id="name"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">社会代码</label>
                    <div class="layui-input-block">
                        <div class="detail-text" id="code"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">供应商地址</label>
                    <div class="layui-input-block">
                        <div class="detail-text" id="address"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">开户银行</label>
                    <div class="layui-input-block">
                        <div class="detail-text" id="bank_name"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">银行账号</label>
                    <div class="layui-input-block">
                        <div class="detail-text" id="bank_account"></div>
                    </div>
                </div>
            </div>

            <!-- 第二列 -->
            <div class="layui-col-md6">
                <div class="layui-form-item">
                    <label class="layui-form-label">联系人姓名</label>
                    <div class="layui-input-block">
                        <div class="detail-text" id="contact_name"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">联系人手机</label>
                    <div class="layui-input-block">
                        <div class="detail-text" id="contact_mobile"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">联系人座机</label>
                    <div class="layui-input-block">
                        <div class="detail-text" id="contact_phone"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">创建时间</label>
                    <div class="layui-input-block">
                        <div class="detail-text" id="created_time"></div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">更新时间</label>
                    <div class="layui-input-block">
                        <div class="detail-text" id="update_time"></div>
                    </div>
                </div>

            </div>
        </div>

        <!-- 经营范围单独占一行 -->
        <div class="layui-row">
            <div class="layui-col-md12">
                <div class="layui-form-item">
                    <label class="layui-form-label">经营范围</label>
                    <div class="layui-input-block">
                        <div class="detail-text" id="business_scope"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 供应商资质图片 -->
        <div class="layui-row">
            <div class="layui-col-md12">
                <div class="layui-form-item">
                    <label class="layui-form-label">供应商资质</label>
                    <div class="layui-input-block">
                        <div class="image-preview-container" id="qualification-container"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-row">
            <div class="layui-col-md12">
                <div class="layui-form-item">
                    <label class="layui-form-label">状态</label>
                    <div class="layui-input-block">
                        <div class="detail-text" id="status"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部按钮 -->
        <div class="layui-form-item" style="text-align: center;">
            <button type="button" class="layui-btn layui-btn-primary" onclick="closeModalWindow()">关闭</button>
        </div>
    </div>

    <script>
        layui.use(['layer'], function () {
            var layer = layui.layer;
            var $ = layui.$;

            // 获取URL参数
            var id = request.get('id');
            if (!id) {
                layer.msg('缺少必要参数', { icon: 2 });
                return;
            }

            // 图片预览函数
            function appendImagePreview(containerId, filepath) {
                const container = $(`#${containerId}`);
                const imageItem = $(`
                    <div class="image-preview-item">
                        <img src="${filepath}">
                    </div>
                `);

                imageItem.find('img').on('click', function () {
                    layer.photos({
                        photos: {
                            title: '查看图片',
                            data: [{ src: filepath }]
                        },
                        footer: false
                    });
                });

                container.append(imageItem);
            }

            // 加载数据
            layer.load(2);
            $.ajax({
                url: serverUrl + '/admin/supplier_drug/detail',
                type: 'POST',
                data: { id: id },
                success: function (res) {
                    layer.closeAll('loading');
                    if (res.code === 200) {
                        var data = res.data;
                        $('#name').text(data.Name || '--');
                        $('#code').text(data.Code || '--');
                        $('#address').text(data.Address || '--');
                        $('#bank_name').text(data.Bank_name || '--');
                        $('#bank_account').text(data.Bank_account || '--');
                        $('#contact_name').text(data.Contact_name || '--');
                        $('#contact_mobile').text(data.Contact_mobile || '--');
                        $('#contact_phone').text(data.Contact_phone || '--');
                        $('#business_scope').text(data.Business_scope || '--');
                        $('#status').html(data.Status == 1 ?
                            '<span class="layui-badge layui-bg-green">启用</span>' :
                            '<span class="layui-badge layui-bg-gray">禁用</span>');
                        $('#created_time').text(data.Create_time || '--');
                        $('#update_time').text(data.Update_time || '--');

                        // 渲染资质图片
                        if (data.Qualification) {
                            data.Qualification.split('\n').forEach(function (filename) {
                                if (filename) {
                                    appendImagePreview(
                                        'qualification-container',
                                        `/static/uploads/normal_pics/qualification/${filename}`
                                    );
                                }
                            });
                        } else {
                            $('#qualification-container').html('<div class="detail-text">暂无资质图片</div>');
                        }
                    } else {
                        layer.msg(res.msg || '加载失败', { icon: 2 });
                    }
                },
                error: function () {
                    layer.closeAll('loading');
                    layer.msg('网络错误', { icon: 2 });
                }
            });
        });
    </script>
</body>

</html>