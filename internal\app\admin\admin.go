package admin

import (
	"bytes"
	"database/sql"
	"fmt"
	"image"
	"image/jpeg"
	"image/png"
	"io"
	"mstproject/pkg/common"
	"mstproject/pkg/config"
	"mstproject/pkg/database"
	"net/http"
	"os"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/nfnt/resize"
)

// 部门（科室）列表 25
func Department_list(w http.ResponseWriter, r *http.Request) {
	api_id := config.NormalPerm
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	limit := r.FormValue("limit")
	if limit == "" {
		limit = "10"
	}
	store_id := r.FormValue("store_id")
	if store_id == "" {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "门店ID不得为空",
		})
		return
	}
	name := r.<PERSON>alue("name")
	if name == "" {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "科室名称不得为空",
		})
		return
	}
	type DepartmentHasCatagory struct {
		Id      int    `json:"id"`
		PID     int    `json:"pid"`
		Name    string `json:"name"`
		Details string `json:"details"`
		Sort    int    `json:"sort"`
	}
	sql := "select id,pid,name,details,sort from department where pid in(select id from department where pid = ? and name = ?) limit ?"
	var department_has_catagory []DepartmentHasCatagory
	err := database.GetAll(sql, &department_has_catagory, store_id, name, limit)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败",
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "ok",
		"data": department_has_catagory,
	})
}

// 部门（科室）添加 26
func Department_add(w http.ResponseWriter, r *http.Request) {
	api_id := 26
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	pid := r.FormValue("pid")
	if pid == "" {
		pid = "0"
	}
	name := r.FormValue("name")
	if name == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "部门名称不能为空",
		})
		return
	}
	sql := "SELECT count(id) FROM department WHERE pid = ? and name = ?"
	var data_exist int
	err := database.GetOne(sql, &data_exist, pid, name)
	if err != nil || data_exist > 0 {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "当前部门已存在",
		})
		return
	}

	// 查询当前最大的SORT
	sql = "SELECT IFNULL(max(sort),10) + 10 FROM department"
	var max_sort int
	err = database.GetOne(sql, &max_sort)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败",
			// "sql":  common.DebugSql(sql, pid),
		})
		return
	}

	details := r.FormValue("details")
	sql = "insert into department (pid,name,details,sort) values (?,?,?,?)"
	result, err := database.Query(sql, pid, name, details, max_sort)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code":  500,
			"msg":   "数据添加失败",
			"error": err.Error(),
			// "sql":   common.DebugSql(sql, pid, name, details),
		})
		return
	}
	RowsAffected, _ := result.RowsAffected()
	new_id, _ := result.LastInsertId()
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":         200,
		"msg":          "数据添加成功",
		"RowsAffected": RowsAffected,
		"new_id":       new_id,
	})
}

// 部门（科室）详情 106
func Department_detail(w http.ResponseWriter, r *http.Request) {
	api_id := 106
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	id, err := common.CheckInt(r.FormValue("id"))
	if id < 1 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "参数不能为空",
		})
		return
	}
	type Department struct {
		Id      int    `json:"id"`
		PID     int    `json:"pid"`
		Name    string `json:"name"`
		Details string `json:"details"`
		Sort    int    `json:"sort"`
	}
	var department Department
	sql := "select id,pid,name,details,sort from department where id = ?"
	err = database.GetRow(sql, &department, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败",
			// "sql":  common.DebugSql(sql, id),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "ok",
		"data": department,
	})
}

// 部门（科室）修改 27
func Department_edit(w http.ResponseWriter, r *http.Request) {
	api_id := 27
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	id, _ := common.CheckInt(r.FormValue("id"))
	name, _ := common.CheckStr(r.FormValue("name"))
	pid := r.FormValue("pid")
	if pid == "" {
		pid = "0"
	}
	is_top := r.FormValue("is_top")
	if is_top == "0" {
		pid = "0"
	}
	if id < 1 || name == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "参数不能为空",
		})
		return
	}
	details := r.FormValue("details")
	sql := "update department set name=?,details=?,pid=? where id=?"
	result, err := database.Query(sql, name, details, pid, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code":  500,
			"msg":   "数据修改失败",
			"error": err.Error(),
			// "sql":   common.DebugSql(sql, name, details, id),
		})
		return
	}
	// 上传ICON图片
	is_uploaded := false
	iconfile := r.MultipartForm.File["iconfile"]
	if len(iconfile) > 0 {
		isImage, fileData, err := common.Check_Is_Image(iconfile[0])
		if isImage && err == nil {
			filepath := config.Dist_catagory + "/uploads/icons/department_" + strconv.Itoa(id) + ".svg"
			if outFile, err := os.OpenFile(filepath, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, 0666); err == nil {
				defer outFile.Close()
				if _, writeErr := outFile.Write(fileData); writeErr == nil {
					is_uploaded = true
				}
			}
		}
	}
	// 输出JSON
	RowsAffected, _ := result.RowsAffected()
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":         200,
		"msg":          "数据修改成功",
		"is_uploaded":  is_uploaded,
		"RowsAffected": RowsAffected,
		// "sql":          common.DebugSql(sql, name, id),
	})
	if RowsAffected > 0 {
		common.Add_log(fmt.Sprintf("修改部门（科室），ID：%d，影响行数：%d", id, RowsAffected), r)
	}
}

// 部门（科室）删除 28
func Department_del(w http.ResponseWriter, r *http.Request) {
	api_id := 28
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	id, err := common.CheckInt(r.FormValue("id"))
	if id < 1 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}
	sql := "delete from department where id=?"
	result, err := database.Query(sql, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据删除失败",
		})
		return
	}
	RowsAffected, _ := result.RowsAffected()
	// 删除ICON图片
	filepath := config.Dist_catagory + "/uploads/icons/department_" + strconv.Itoa(id) + ".svg"
	if _, err := os.Stat(filepath); err == nil {
		os.Remove(filepath)
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":         200,
		"msg":          "数据删除成功",
		"RowsAffected": RowsAffected,
		// "sql":  common.DebugSql(sql, Perm),
	})
	if RowsAffected > 0 {
		common.Add_log(fmt.Sprintf("删除部门（科室），ID：%d", id), r)
	}
}

// 药品分类信息展示
func Drug_category_detail(w http.ResponseWriter, r *http.Request) {
	api_id := 29
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	id, err := common.CheckInt(r.FormValue("id"))
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "ID不得为空",
		})
		return
	}
	// 字段：id,name,status
	type DrugCategory struct {
		ID          int    `db:"id"`
		Name        string `db:"name"`
		Status      int    `db:"status"`
		Description string `db:"description"`
		Create_time string `db:"create_time"` // 创建时间
	}
	sql := "SELECT id, name, status,description,create_time FROM drug_category WHERE id = ?"
	var drug_category DrugCategory
	err = database.GetRow(sql, &drug_category, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败",
			"sql":  common.DebugSql(sql, id),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "ok",
		"data": drug_category,
	})

}

// 药品分类列表 29
func Drug_category_list(w http.ResponseWriter, r *http.Request) {
	api_id := 29
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	// 分页参数
	limit, err := strconv.Atoi(r.FormValue("limit"))
	if err != nil || limit < 1 {
		limit = 10
	}
	page, err := strconv.Atoi(r.FormValue("page"))
	if err != nil || page < 1 {
		page = 1
	}
	offset := (page - 1) * limit
	// 搜索参数
	var attSql string
	var params []interface{}

	// 药名搜索
	name := r.FormValue("name")
	if name != "" {
		name = strings.TrimSpace(name)
		attSql += " AND (name like ? or description like ?)"
		params = append(params, "%"+name+"%", "%"+name+"%")
	}

	// 总数
	var count int
	sql := "SELECT COUNT(id) FROM drug_category WHERE 1 " + attSql
	err = database.GetOne(sql, &count, params...)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "读取数据总数失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, params...),
		})
		return
	}

	// 字段：id,name,status
	type DrugCategory struct {
		ID          int    `db:"id"`
		Name        string `db:"name"`
		Status      int    `db:"status"`
		Description string `db:"description"`
		Create_time string `db:"create_time"` // 创建时间
	}
	var drug_category []DrugCategory
	sql = "SELECT id, name, status,description,create_time FROM drug_category WHERE 1 " + attSql + " ORDER BY id DESC LIMIT ?,?"
	params = append(params, offset, limit)
	err = database.GetAll(sql, &drug_category, params...)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败",
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "ok",
		"data": drug_category,
	})
}

// 药品分类添加 30
func Drug_category_add(w http.ResponseWriter, r *http.Request) {
	api_id := 30
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	name := r.FormValue("name")
	if name == "" {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "分类名称不得为空",
		})
		return
	}
	description := r.FormValue("description")
	if description == "" {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "分类描述不得为空",
		})
		return
	}
	status, err := common.CheckInt(r.FormValue("status"))
	if err != nil {
		status = 1
	}
	sql := "insert into drug_category (name,status,description) values (?,?,?)"
	result, err := database.Query(sql, name, status, description)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据添加失败",
			"sql":  common.DebugSql(sql, name, status, description),
		})
		return
	}
	new_id, _ := result.LastInsertId()
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":         200,
		"msg":          "数据添加成功",
		"RowsAffected": new_id,
		// "sql":  common.DebugSql(sql, Perm),
	})
	common.Add_log(fmt.Sprintf("添加药品分类，ID：%d", new_id), r)
}

// 药品分类修改 31
func Drug_category_edit(w http.ResponseWriter, r *http.Request) {
	api_id := 31
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	id, err := common.CheckInt(r.FormValue("id"))
	if id < 1 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}
	name, err := common.CheckStr(r.FormValue("Name"))
	if err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}
	status, err := common.CheckInt(r.FormValue("Status"))
	if err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}
	description := r.FormValue("Description")
	if description == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "描述不得为空",
		})
		return
	}
	sql := "update drug_category set status=?,name=?,description=? where id=?"
	result, err := database.Query(sql, status, name, description, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据修改失败",
		})
		return
	}
	RowsAffected, _ := result.RowsAffected()
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":         200,
		"msg":          "数据修改成功",
		"RowsAffected": RowsAffected,
		// "sql":  common.DebugSql(sql, Perm),
	})
	if RowsAffected > 0 {
		common.Add_log(fmt.Sprintf("修改药品分类，ID：%d，影响行数：%d", id, RowsAffected), r)
	}
}

// 药品分类删除 32
func Drug_category_del(w http.ResponseWriter, r *http.Request) {
	api_id := 32
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	id, err := common.CheckInt(r.FormValue("id"))
	if id < 1 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}
	// 先查询该药品分类下面还有没有药品数据，有的话禁止删除
	sql := "SELECT count(id) FROM drug WHERE pid=?"
	var drug_exist int
	err = database.GetOne(sql, &drug_exist, id)
	if err != nil || drug_exist > 0 {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "删除失败，请先确认该类目下已无数据",
			// "sql":  common.DebugSql(sql, id),
		})
		return
	}

	sql = "delete from drug_category where id=?"
	result, err := database.Query(sql, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据删除失败",
		})
		return
	}
	RowsAffected, _ := result.RowsAffected()
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":         200,
		"msg":          "数据删除成功",
		"RowsAffected": RowsAffected,
		// "sql":  common.DebugSql(sql, Perm),
	})
	if RowsAffected > 0 {
		common.Add_log(fmt.Sprintf("删除药品分类，ID：%d", id), r)
	}
}

// 处方列表 37
func Prescription_list(w http.ResponseWriter, r *http.Request) {
	api_id := 37
	// 处方字段如下：id doc_id asst_id pat_id diagnosis tx_plan status create_time
	session, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	// 带分页的查询
	attSql := ""
	if session.Values["role_ids"] == "3" {
		attSql += " AND asst_id =  " + strconv.Itoa(session.Values["id"].(int))
	} else if session.Values["role_ids"] == "4" {
		attSql += " AND doc_id =  " + strconv.Itoa(session.Values["id"].(int))
	} else if session.Values["role_ids"] == "9" {
		attSql += " AND asst_id = " + strconv.Itoa(session.Values["id"].(int))
	}
	doc_id, _ := common.CheckInt(r.FormValue("doc_id"))
	if doc_id > 0 {
		attSql += " AND doc_id = " + strconv.Itoa(doc_id)
	}
	asst_id, _ := common.CheckInt(r.FormValue("asst_id"))
	if asst_id > 0 {
		attSql += " AND asst_id = " + strconv.Itoa(asst_id)
	}
	pat_id, _ := common.CheckInt(r.FormValue("pat_id"))
	if pat_id > 0 {
		attSql += " AND pat_id = " + strconv.Itoa(pat_id)
	}
	pat_pro_id, _ := common.CheckInt(r.FormValue("pat_pro_id"))
	if pat_pro_id > 0 {
		attSql += " AND pat_pro_id = " + strconv.Itoa(pat_pro_id)
	}
	diagnosis, _ := common.CheckStr(r.FormValue("diagnosis"))
	if diagnosis != "" {
		attSql += " AND diagnosis LIKE '%" + diagnosis + "%'"
	}
	tx_plan, _ := common.CheckStr(r.FormValue("tx_plan"))
	if tx_plan != "" {
		attSql += " AND tx_plan LIKE '%" + tx_plan + "%'"
	}
	status := r.FormValue("status")
	if status != "" {
		if strings.Contains(status, ",") {
			attSql += " AND status IN (" + status + ")"
		} else {
			attSql += " AND status = " + status
		}
	}
	// 分页参数
	limit, err := strconv.Atoi(r.FormValue("limit"))
	if err != nil || limit < 10 {
		limit = 10
	}
	page, err := strconv.Atoi(r.FormValue("page"))
	if err != nil || page < 1 {
		page = 1
	}
	offset := (page - 1) * limit
	// 总数
	var count int
	sql := "SELECT COUNT(id) FROM prescription WHERE 1 " + attSql
	err = database.GetOne(sql, &count)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "读取数据总数失败",
		})
		return
	}
	// 数据查询
	type Prescription struct { // 处方
		ID          int    `db:"id"`
		Doc_id      int    `db:"doc_id"`
		Asst_id     int    `db:"asst_id"`
		Pat_id      int    `db:"pat_id"`
		Pat_pro_id  int    `db:"pat_pro_id"`
		Diagnosis   string `db:"diagnosis"`
		Tx_plan     string `db:"tx_plan"`
		Status      int    `db:"status"`
		Create_time string `db:"create_time"`
	}
	sql = "SELECT id,doc_id,asst_id,pat_id,pat_pro_id,diagnosis,tx_plan,status,create_time FROM prescription WHERE 1 " + attSql + " ORDER BY id DESC limit ? OFFSET ?"
	var prescription []Prescription
	err = database.GetAll(sql, &prescription, limit, offset)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败",
			"err":  err.Error(),
			// "sql":  common.DebugSql(sql, limit, offset),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":  200,
		"msg":   "ok",
		"data":  prescription,
		"count": count,
		"sql":   common.DebugSql(sql, limit, offset),
	})
}

// 处方详情 38
func Prescription_detail(w http.ResponseWriter, r *http.Request) {
	api_id := 125
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	id, err := common.CheckInt(r.FormValue("id"))
	if id < 1 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}
	type Prescription struct {
		ID               int    `db:"id"`
		Ord_id           int    `db:"ord_id"`
		Record_id        int    `db:"record_id"`
		Doc_id           int    `db:"doc_id"`
		Asst_id          int    `db:"asst_id"`
		Pat_id           int    `db:"pat_id"`
		Status           int    `db:"status"`
		Pat_pro_id       int    `db:"pat_pro_id"`
		Finished_drug_id int    `db:"finished_drug_id"`
		Dosage           string `db:"dosage"`
		Askfor           string `db:"askfor"`
		Diagnosis        string `db:"diagnosis"`
		Tx_plan          string `db:"tx_plan"`
		Tx_day           int    `db:"tx_day"`
		Tx_type          string `db:"tx_type"`
		TotalDoses       int    `db:"totalDoses"`
		Rejection        string `db:"rejection"`
		Verify_1         int    `db:"verify_1"`
		Verify_1_user_id int    `db:"verify_1_user_id"`
		Verify_1_desc    string `db:"verify_1_desc"`
		Verify_2         int    `db:"verify_2"`
		Verify_2_user_id int    `db:"verify_2_user_id"`
		Verify_2_desc    string `db:"verify_2_desc"`
		Verify_3         int    `db:"verify_3"`
		Verify_3_user_id int    `db:"verify_3_user_id"`
		Verify_3_desc    string `db:"verify_3_desc"`
		Verify_4         int    `db:"verify_4"`
		Verify_4_user_id int    `db:"verify_4_user_id"`
		Verify_4_desc    string `db:"verify_4_desc"`
		Create_time      string `db:"create_time"`
	}

	sql := `

			SELECT
			id,
			ord_id,
			record_id,
			doc_id,
			asst_id,
			pat_id,
			pat_pro_id,
			finished_drug_id,
			diagnosis,
			tx_plan,
			status,
			dosage,
			askfor,
			tx_day,
			tx_type,
			totalDoses,
			rejection,
			verify_1,
			verify_1_user_id,
			verify_1_desc,
			verify_2,
			verify_2_user_id,
			verify_2_desc,
			verify_3,
			verify_3_user_id,
			verify_3_desc,
			verify_4,
			verify_4_user_id,
			verify_4_desc,
			create_time
			FROM
			prescription
			WHERE
			id = ?


	`
	var prescription Prescription
	err = database.GetOne(sql, &prescription, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败",
			"err":  err.Error(),
			// "sql":  common.DebugSql(sql, id),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "ok",
		"data": prescription,
	})
}

// 处方创建 38
// func Prescription_add(w http.ResponseWriter, r *http.Request) {
// 	api_id := 38
// 	_, isLogin := common.Check_Perm(w, r, api_id)
// 	if !isLogin {
// 		return
// 	}
// 	doc_id, err := common.CheckInt(r.FormValue("doc_id"))
// 	if doc_id < 1 || err != nil {
// 		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
// 			"code": 500,
// 			"msg":  "医生ID不能为空",
// 		})
// 		return
// 	}
// 	asst_id, err := common.CheckInt(r.FormValue("asst_id"))
// 	if asst_id < 1 || err != nil {
// 		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
// 			"code": 500,
// 			"msg":  "医助（售前）ID不能为空",
// 		})
// 		return
// 	}
// 	pat_id, err := common.CheckInt(r.FormValue("pat_id"))
// 	if pat_id < 1 || err != nil {
// 		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
// 			"code": 500,
// 			"msg":  "患者ID不能为空",
// 		})
// 		return
// 	}
// 	pat_pro_id, err := common.CheckInt(r.FormValue("pat_pro_id"))
// 	if pat_pro_id < 1 || err != nil {
// 		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
// 			"code": 500,
// 			"msg":  "患者信息ID不能为空",
// 		})
// 		return
// 	}
// 	diagnosis, err := common.CheckStr(r.FormValue("diagnosis"))
// 	if diagnosis == "" || err != nil {
// 		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
// 			"code": 500,
// 			"msg":  "诊断不能为空",
// 		})
// 		return
// 	}
// 	tx_plan, err := common.CheckStr(r.FormValue("tx_plan"))
// 	if tx_plan == "" || err != nil {
// 		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
// 			"code": 500,
// 			"msg":  "治疗方案不能为空",
// 		})
// 		return
// 	}
// 	status, err := common.CheckInt(r.FormValue("status"))
// 	if status < 1 || err != nil {
// 		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
// 			"code": 500,
// 			"msg":  "处方状态不能为空",
// 		})
// 		return
// 	}
// 	Record_id, err := common.CheckInt(r.FormValue("record_id"))
// 	if Record_id < 1 || err != nil {
// 		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
// 			"code": 500,
// 			"msg":  "病历ID不能为空",
// 		})
// 		return
// 	}
// 	// // 检测当天有没有相同医生、相同患者的处方，如果有，则不能创建
// 	// sql := "select count(id) from prescription where doc_id = ? and pat_id = ? and TIMESTAMPDIFF(SECOND, create_time, NOW()) <= 10"
// 	// var count int
// 	// err = database.GetOne(sql, &count, doc_id, pat_id)
// 	// if err != nil {
// 	// 	common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
// 	// 		"code": 500,
// 	// 		"msg":  "查询数据失败",
// 	// 		"err":  err.Error(),
// 	// 		// "sql":  common.DebugSql(sql, doc_id, pat_id),
// 	// 	})
// 	// 	return
// 	// }
// 	// if count > 0 {
// 	// 	common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
// 	// 		"code": 500,
// 	// 		"msg":  "10秒内您创建了相同医生、相同患者的处方，请注意勿重复提交错误数据",
// 	// 	})
// 	// 	return
// 	// }

// 	// 检测有没有相同病历的处方，如果有则阻止，因为病历与处方是1V1的关系
// 	sql := "select count(id) from prescription where Record_id = ?"
// 	var count int
// 	err = database.GetOne(sql, &count, Record_id)
// 	if err != nil {
// 		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
// 			"code": 500,
// 			"msg":  "查询数据失败",
// 			"err":  err.Error(),
// 			// "sql":  common.DebugSql(sql, Record_id),
// 		})
// 		return
// 	}
// 	if count > 0 {
// 		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
// 			"code": 500,
// 			"msg":  "病历ID已存在处方，请勿重复提交",
// 		})
// 		return
// 	}

//		sql = "INSERT INTO prescription(doc_id,asst_id,pat_id,pat_pro_id,diagnosis,tx_plan,status,Record_id) VALUES(?,?,?,?,?,?,?,?)"
//		result, err := database.Query(sql, doc_id, asst_id, pat_id, pat_pro_id, diagnosis, tx_plan, status, Record_id)
//		if err != nil {
//			common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
//				"code": 500,
//				"msg":  "添加数据失败",
//				// "sql":  common.DebugSql(sql, doc_id, asst_id, pat_id, pat_pro_id, diagnosis, tx_plan, status, Record_id),
//			})
//			return
//		}
//		new_id, _ := result.LastInsertId()
//		common.JSONResponse(w, http.StatusOK, map[string]interface{}{
//			"code":   200,
//			"msg":    "添加成功",
//			"new_id": new_id,
//		})
//	}
//
// 单独删除处方对应的中药材数据
func Prescription_Delete_drug(w http.ResponseWriter, r *http.Request) {
	api_id := 40
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	pre_id, err := common.CheckInt(r.FormValue("pre_id"))
	if pre_id < 1 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "处方ID不能为空",
			"err":  err.Error(),
		})
		return
	}
	sql := "select id from prescription where id = ? and status < 3"
	var count int
	err = database.GetOne(sql, &count, pre_id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "对不起，当前处方状态不允许删除药材",
			"err":  err.Error(),
			// "sql":  common.DebugSql(sql, pre_id),
		})
		return
	}
	pre_drug_id, err := common.CheckInt(r.FormValue("pre_drug_id"))
	if pre_drug_id < 1 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "药房ID不能为空",
			"err":  err.Error(),
		})
		return
	}
	sql = "DELETE FROM prescription_drug WHERE id = ? and pre_id = ?"
	result, err := database.Query(sql, pre_drug_id, pre_id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "删除数据失败",
			"sql":  common.DebugSql(sql, pre_drug_id, pre_id),
		})
		return
	}
	RowsAffected, _ := result.RowsAffected()
	common.JSONResponse(w, http.StatusOK, map[string]any{
		"code":         200,
		"msg":          "删除成功",
		"RowsAffected": RowsAffected,
	})
	if RowsAffected > 0 {
		common.Add_log(fmt.Sprintf("删除处方（%d）的药材ID：%d, r)", pre_id, pre_drug_id), r)
	}
}

// 处方删除 40
func Prescription_del(w http.ResponseWriter, r *http.Request) {
	api_id := 40
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	id, err := common.CheckInt(r.FormValue("id"))
	if id < 1 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}
	sql := "DELETE FROM prescription WHERE id = ?"
	result, err := database.Query(sql, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "删除数据失败",
			// "sql":  common.DebugSql(sql, id),
		})
		return
	}
	RowsAffected, _ := result.RowsAffected()
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":         200,
		"msg":          "删除成功",
		"RowsAffected": RowsAffected,
	})
	if RowsAffected > 0 {
		common.Add_log(fmt.Sprintf("删除处方处方ID：%d", id), r)
	}
}

// 订单详情 56
func Order_detail(w http.ResponseWriter, r *http.Request) {
	api_id := 56
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	// 获取订单 ID
	id, err := common.CheckInt(r.FormValue("id"))
	if id < 1 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "无效的订单 ID",
		})
		return
	}
	type Order struct {
		ID            int     `db:"id"`
		Record_ids    string  `db:"record_ids"`
		Doc_dep_id    int     `db:"doc_dep_id"`
		Doc_id        int     `db:"doc_id"`
		Asst_dep_id   int     `db:"asst_dep_id"`
		Asst_id       int     `db:"asst_id"`
		Pat_id        int     `db:"pat_id"`
		Pat_pro_id    int     `db:"pat_pro_id"`
		Express       string  `db:"express"`
		Tracking_num  string  `db:"tracking_num"`
		Cr_pic        string  `db:"cr_pic"`
		Pay_pic       string  `db:"pay_pic"`
		Total_money   float64 `db:"total_money"`
		Pre_pay       float64 `db:"pre_pay"`
		Final_pay     float64 `db:"final_pay"`
		Status        int     `db:"status"`
		Finish_time   string  `db:"finish_time"`
		Create_time   string  `db:"create_time"`
		Delivery_time string  `db:"delivery_time"`
		Final_pay_pic string  `db:"final_pay_pic"`
		Address       string  `db:"address"`
		Final_is_full int     `db:"final_is_full"`
	}
	// 查询订单数据
	var order Order
	sql := `SELECT id,record_ids,doc_dep_id,doc_id,asst_dep_id,asst_id,pat_id,pat_pro_id,express,tracking_num,cr_pic,pay_pic,total_money,pre_pay,final_pay,status,finish_time,delivery_time,final_pay_pic,create_time,address,final_is_full FROM orders WHERE id = ?`
	err = database.GetRow(sql, &order, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "查询订单数据失败",
			"err":  err.Error(),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "ok",
		"data": order,
		"sql":  common.DebugSql(sql, id),
	})
}

// 订单修改 43
// func Order_edit(w http.ResponseWriter, r *http.Request) {
// 	api_id := 43
// 	_, isLogin := common.Check_Perm(w, r, api_id)
// 	if !isLogin {
// 		return
// 	}
// 	id, err := common.CheckInt(r.FormValue("id"))
// 	if id < 1 || err != nil {
// 		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
// 			"code": 500,
// 			"msg":  err.Error(),
// 		})
// 		return
// 	}
// 	asst_id, err := common.CheckInt(r.FormValue("asst_id"))
// 	if asst_id < 1 || err != nil {
// 		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
// 			"code": 500,
// 			"msg":  "医助（售前）ID不能为空",
// 		})
// 		return
// 	}
// 	express, err := common.CheckStr(r.FormValue("express"))
// 	if express == "" || err != nil {
// 		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
// 			"code": 500,
// 			"msg":  "快递公司不能为空",
// 		})
// 		return
// 	}
// 	tracking_num, err := common.CheckStr(r.FormValue("tracking_num")) // 快递单号
// 	if tracking_num == "" || err != nil {
// 		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
// 			"code": 500,
// 			"msg":  "快递单号不能为空",
// 		})
// 		return
// 	}
// 	cr_pic, err := common.CheckStr(r.FormValue("cr_pic"))
// 	if cr_pic == "" || err != nil {
// 		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
// 			"code": 500,
// 			"msg":  "上传的代收款凭证不能为空",
// 		})
// 		return
// 	}
// 	pay_pic, err := common.CheckStr(r.FormValue("pay_pic"))
// 	if pay_pic == "" || err != nil {
// 		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
// 			"code": 500,
// 			"msg":  "上传的支付截图不能为空",
// 		})
// 		return
// 	}
// 	total_money, err := common.CheckFloat(r.FormValue("total_money"))
// 	if total_money < 0 || err != nil {
// 		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
// 			"code": 500,
// 			"msg":  "总金额不能为空",
// 		})
// 		return
// 	}
// 	pre_pay, err := common.CheckFloat(r.FormValue("pre_pay"))
// 	if pre_pay < 0 || err != nil {
// 		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
// 			"code": 500,
// 			"msg":  "预付金额不能为空",
// 		})
// 		return
// 	}
// 	final_pay, err := common.CheckFloat(r.FormValue("final_pay"))
// 	if final_pay < 0 || err != nil {
// 		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
// 			"code": 500,
// 			"msg":  "最终支付金额不能为空",
// 		})
// 		return
// 	}
// 	status, err := common.CheckInt(r.FormValue("status"))
// 	if status < 1 || err != nil {
// 		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
// 			"code": 500,
// 			"msg":  "订单状态不能为空",
// 		})
// 		return
// 	}
// 	sql := "UPDATE orders SET asst_id =?,express =?,tracking_num =?,cr_pic =?,pay_pic =?,total_money =?,pre_pay =?,final_pay =?,status =? WHERE id = ?"
// 	result, err := database.Query(sql, asst_id, express, tracking_num, cr_pic, pay_pic, total_money, pre_pay, final_pay, status, id)
// 	if err != nil {
// 		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
// 			"code": 500,
// 			"msg":  "修改数据失败",
// 			// "sql":  common.DebugSql(sql, asst_id, express, tracking_num, cr_pic, pay_pic, total_money, pre_pay, final_pay, status, id),
// 		})
// 		return
// 	}
// 	RowsAffected, _ := result.RowsAffected()
// 	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
// 		"code":         200,
// 		"msg":          "修改成功",
// 		"RowsAffected": RowsAffected,
// 	})
// 	if RowsAffected > 0 {
// 		common.Add_log(fmt.Sprintf("成功修改订单ID为%d，影响行数：%d", id, RowsAffected), r)
// 	}
// }

// 订单删除 44
func Order_del(w http.ResponseWriter, r *http.Request) {
	api_id := 44
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	id, err := common.CheckInt(r.FormValue("id"))
	if id < 1 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}
	sql := "DELETE FROM orders WHERE id = ?"
	result, err := database.Query(sql, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "删除数据失败",
			// "sql":  common.DebugSql(sql, id),
		})
		return
	}
	RowsAffected, _ := result.RowsAffected()
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":         200,
		"msg":          "删除成功",
		"RowsAffected": RowsAffected,
	})
	if RowsAffected > 0 {
		common.Add_log(fmt.Sprintf("成功删除订单ID为%d", id), r)
	}
}

// 帐号详情
func Patient_account_detail(w http.ResponseWriter, r *http.Request) {
	api_id := 146
	session, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	id, err := common.CheckInt(r.FormValue("id"))
	if id < 1 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "id不能为空",
		})
		return
	}
	// 查询患者信息
	type Patient_account struct {
		ID              int    `db:"id"`
		Phone           string `db:"phone"`
		Is_bind         int    `db:"is_bind"`
		Status          int    `db:"status"`
		Create_time     string `db:"create_time"`
		Last_login_ip   string `db:"last_login_ip"`
		Last_login_time string `db:"last_login_time"`
		Asst_id         int    `db:"asst_id"`
		Is_transfer     int    `db:"is_transfer"`
	}
	sql := "SELECT id,phone,asst_id,is_transfer,last_login_ip,ifnull(last_login_time,'未登录')last_login_time,case when openid = '0' then 0 else 1 end is_bind,status,create_time,asst_id FROM patient_account WHERE id = ?"
	var patient_account Patient_account
	err = database.GetRow(sql, &patient_account, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "查询患者信息失败",
			"err":  err.Error(),
			// "sql":  common.DebugSql(sql, id),
		})
		return
	}
	// 根据权限处理手机号显示方式 - GetRow
	if session.Values["role_ids"] != "1" {
		if len(patient_account.Phone) == 11 || len(patient_account.Phone) == 13 {
			patient_account.Phone = patient_account.Phone[:5] + "****" + patient_account.Phone[len(patient_account.Phone)-2:]
		} else {
			patient_account.Phone = "手机号码格式错误"
		}
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "ok",
		"data": patient_account,
	})
}

// 患者帐号修改
func Patient_account_edit(w http.ResponseWriter, r *http.Request) {
	api_id := 47
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	var attSql string
	var params []interface{}
	id, err := common.CheckInt(r.FormValue("id"))
	if id < 1 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "id不能为空",
		})
		return
	}
	phone := r.FormValue("phone")
	if phone != "" {
		attSql += ", phone = ?"
		params = append(params, phone)
	}
	status := r.FormValue("status")
	if status != "" {
		attSql += ", status = ?"
		params = append(params, status)
	}
	pwd := r.FormValue("pwd")
	if pwd != "" {
		pwd = common.Md5Hash(pwd)
		attSql += ", pwd = ?"
		params = append(params, pwd)
	}
	attSql = attSql[1:]
	params = append(params, id)
	sql := "update patient_account set " + attSql + " where id = ?"
	result, err := database.Query(sql, params...)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "更新数据失败",
		})
		return
	}
	RowsAffected, _ := result.RowsAffected()
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":         200,
		"msg":          "修改成功",
		"RowsAffected": RowsAffected,
	})
	if RowsAffected > 0 {
		common.Add_log(fmt.Sprintf("成功修改患者帐号ID为%d", id), r)
	}
}

// 患者，PHONE 2 ID（目的是求ID，但也会带出姓名与电话）
func Patient_phone2id(w http.ResponseWriter, r *http.Request) {
	api_id := config.NormalPerm
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	phone := r.FormValue("phone")
	if len(phone) < 2 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]any{
			"code": 500,
			"msg":  "手机号长度不能小于2",
		})
		return
	}

	// 使用切片类型接收多个患者信息
	type Patient_account struct {
		ID          int    `json:"id"`
		Openid      int    `json:"openid"`
		Create_time string `json:"create_time"`
		Phone       string `json:"phone"`
	}

	sql := "SELECT id,phone,case when openid = '0' then 0 else 1 end as openid,create_time FROM patient_account WHERE phone LIKE ?"
	var patient_account []Patient_account // 定义为切片
	err := database.GetAll(sql, &patient_account, "%"+phone+"%")
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "查询患者信息失败",
			"err":  err.Error(),
		})
		return
	}

	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "ok",
		"data": patient_account, // 返回的是一个切片
	})
}

// 患者详情
func Patient_profile_detail(w http.ResponseWriter, r *http.Request) {
	api_id := 147
	session, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	id, err := common.CheckInt(r.FormValue("id"))
	if id < 1 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "id不能为空",
		})
		return
	}
	//如果角色是医生、售前、医助，则加上数据细分条件
	var attSql string
	if session.Values["role_ids"] == "3" || session.Values["role_ids"] == "9" {
		attSql = " AND asst_id = " + strconv.Itoa(session.Values["id"].(int))
	} else if session.Values["role_ids"] == "4" {
		attSql = " AND doc_id = " + strconv.Itoa(session.Values["id"].(int))
	} else if session.Values["role_ids"] == "2" {
		// 销售数据管理员
		attSql = " AND asst_id in(SELECT id from rbac_user where department_id in(" + session.Values["dep_ids"].(string) + ")) "
	}
	// 查询患者信息
	type Patient_profile struct {
		ID              int       `db:"id"`              // 主键
		Pid             int       `db:"pid"`             // 患者帐号id
		Asst_id         int       `db:"asst_id"`         // 销售id
		Doc_id          int       `db:"doc_id"`          // 医生id
		Support_id      int       `db:"support_id"`      // 支援人员id
		Relation        int       `db:"relation"`        // 与患者关系
		Phone           string    `db:"phone"`           // 手机号
		Name            string    `db:"name"`            // 姓名
		Sex             string    `db:"sex"`             // 性别
		Born_date       time.Time `db:"born_date"`       // 出生日期
		Idcard          string    `db:"idcard"`          // 身份证号
		Weixin          string    `db:"weixin"`          // 微信号
		Ins_card_num    string    `db:"ins_card_num"`    // 医保卡号
		Ins_type        string    `db:"ins_type"`        // 医保类型
		Height          float64   `db:"height"`          // 身高
		Weight          float64   `db:"weight"`          // 体重
		Allergies       string    `db:"allergies"`       // 过敏史
		Medical_history string    `db:"medical_history"` // 既往病史
		Patient_type    int       `db:"patient_type"`    // 患者类型
		Patient_from    int       `db:"patient_from"`    // 信息来源
		Address         string    `db:"address"`         // 地址
		Ismarried       int       `db:"ismarried"`       // 是否已婚
		Status          int       `db:"status"`          // 状态
		Customer_notes  string    `db:"customer_notes"`  // 客户备注
		Last_time       string    `db:"last_time"`       // 最后跟进时间
		Level           string    `db:"level"`           // 级别
		Create_time     string    `db:"create_time"`     // 创建时间
		Chief_complaint string    `db:"chief_complaint"` // 主诉
	}
	sql := "select id,pid,asst_id,doc_id,support_id,relation,phone,name,sex,born_date,idcard,weixin,ins_card_num,ins_type,height,weight,allergies,medical_history,patient_type,patient_from,address,ismarried,level,status,customer_notes,chief_complaint,last_time,create_time FROM patient_profile WHERE id = ? " + attSql
	var patient_profile Patient_profile
	err = database.GetRow(sql, &patient_profile, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "患者不存在，或者您权限未被允许",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, id),
		})
		return
	}
	if session.Values["role_ids"] != "1" {
		if len(patient_profile.Phone) == 11 || len(patient_profile.Phone) == 13 {
			patient_profile.Phone = patient_profile.Phone[:5] + "****" + patient_profile.Phone[len(patient_profile.Phone)-2:]
		} else {
			patient_profile.Phone = "手机号码格式错误"
		}
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "ok",
		"data": patient_profile,
		"sql":  common.DebugSql(sql, id),
	})
}

// 患者添加前的检测
func Patient_profile_before_add(w http.ResponseWriter, r *http.Request) {
	api_id := 49
	session, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	name := r.FormValue("name")
	if name == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "患者姓名不能为空",
		})
		return
	}
	born_date := r.FormValue("born_date")
	if born_date == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "出生日期不能为空",
		})
		return
	}
	sex := r.FormValue("sex")
	if sex == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "性别不能为空",
		})
		return
	}
	type Patient_profile struct {
		ID          int       `db:"id"`
		Name        string    `db:"name"`
		Phone       string    `db:"phone"`
		Sex         string    `db:"sex"`
		Born_date   time.Time `db:"born_date"`
		Create_time string    `db:"create_time"`
	}
	sql := "select id,name,phone,sex,born_date,create_time from patient_profile where asst_id = ? and name = ? and sex = ? and born_date = ?"
	var patient_profile []Patient_profile
	err := database.GetAll(sql, &patient_profile, session.Values["id"], name, sex, born_date)
	if err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, session.Values["id"], name, sex, born_date),
		})
		return
	}
	if len(patient_profile) == 0 {
		common.JSONResponse(w, http.StatusOK, map[string]any{
			"code": 500,
			"msg":  "当前查询数据为0，通过",
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]any{
		"code": 200,
		"msg":  "查到相似数据，请注意辨别数据后再进行下一步",
		"data": patient_profile,
	})
}

// 患者添加 49
func Patient_profile_add(w http.ResponseWriter, r *http.Request) {
	api_id := 49
	session, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	pid := r.FormValue("patient_account_id")
	var phone string
	var err error
	if pid == "" {
		phone = r.FormValue("phone")
		if phone == "" {
			// 如果用户输入的不是手机号，则系统为其自动生成一个不会重复的时间戳，注：是表patient_account中的phone字段，不得重复
			// 锁表 - 同时锁定patient_account和patient_profile表
			_, err := database.Query("LOCK TABLES patient_account WRITE, patient_profile WRITE")
			if err != nil {
				common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
					"code": 500,
					"msg":  "锁表失败",
					"err":  err.Error(),
				})
				return
			}

			// 确保函数结束时解锁表
			defer func() {
				_, unlockErr := database.Query("UNLOCK TABLES")
				if unlockErr != nil {
					// 只记录错误，不影响响应
					fmt.Println("解锁表失败:", unlockErr.Error())
				}
			}()

			// 生成时间戳作为手机号
			timestamp := time.Now().UnixNano() / 1e6 // 毫秒级时间戳
			phone = fmt.Sprintf("%d", timestamp)

			// 验证时间戳是否已存在
			var existingId int
			for {
				checkSql := "SELECT id FROM patient_account WHERE phone = ?"
				err = database.GetOne(checkSql, &existingId, phone)
				if err != nil {
					// 没有找到记录，说明时间戳可用
					break
				}

				// A如果找到了记录，生成新的时间戳再试
				time.Sleep(1 * time.Millisecond) // 等待1毫秒确保新的时间戳
				timestamp = time.Now().UnixNano() / 1e6
				phone = fmt.Sprintf("%d", timestamp)
			}
		} else {
			phone, err = common.CheckPhone(phone)
			if err != nil {
				common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
					"code": 500,
					"msg":  "手机号码格式错误",
					"err":  err.Error(),
				})
				return
			}
			sql := "select id from patient_account where phone = ?"
			var id int
			err = database.GetOne(sql, &id, phone)
			if err == nil {
				common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
					"code": 500,
					"msg":  "该手机号码已在【用户帐号表】中注册",
				})
				return
			}
		}
	}
	//查询该手机号有没有在患者profile表中
	sql_profile := "SELECT id FROM patient_profile WHERE phone = ?"
	var id_profile int
	err = database.GetOne(sql_profile, &id_profile, phone)
	if err == nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "该手机号码已在【用户数据表】中注册",
		})
		return
	}
	//关系
	relation, err := common.CheckInt(r.FormValue("relation"))
	if relation < 0 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "与患者关系不能为空",
		})
		return
	}
	name, err := common.CheckStr(r.FormValue("name"))
	if name == "" || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "姓名不能为空",
		})
		return
	}

	sex := r.FormValue("sex")
	if sex == "" {
		sex = "0"
	}

	bornDate := r.FormValue("born_date")
	if bornDate == "" {
		bornDate = "0000-00-00"
	}

	idcard := r.FormValue("idcard")
	if idcard == "" {
		idcard = "0"
	}
	weixin := r.FormValue("weixin")
	if weixin == "" {
		weixin = "未登记"
	}

	// 用户级别
	level := r.FormValue("patient_level")
	if level == "" {
		level = "1"
	}
	doc_id := r.FormValue("doctor")
	if doc_id == "" {
		doc_id = "0"
	}

	patientFrom := r.FormValue("patient_from")
	if patientFrom == "" {
		patientFrom = "0"
	}

	insCardNum := r.FormValue("ins_card_num")
	if insCardNum == "" {
		insCardNum = "0"
	}
	// ins_type, ins_type_array 分析器需要进一步处理
	insType := r.FormValue("ins_type")
	if insType == "" {
		insType = "0"
	}

	height := r.FormValue("height")
	if height == "" {
		height = "0"
	} else {
		height2, err := strconv.Atoi(height)
		if err != nil || height2 > 300 {
			common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
				"code": 500,
				"msg":  "身高格式不正确",
			})
			return
		}
	}
	weight := r.FormValue("weight")
	if weight == "" {
		weight = "0"
	} else {
		weight2, err := strconv.Atoi(weight)
		if err != nil || weight2 > 300 {
			common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
				"code": 500,
				"msg":  "体重格式不正确",
			})
			return
		}
	}

	allergies := r.FormValue("allergies")
	medicalHistory := r.FormValue("medical_history")
	address := r.FormValue("address")
	isMarried := r.FormValue("isMarried")
	if isMarried == "" {
		isMarried = "0"
	}

	status := 1 // 设置默认状态为正常
	customerNotes := r.FormValue("customer_notes")
	// 如果PID是空，则是新建用户，需要插入帐户表
	var new_id int64
	if pid == "" {
		sql_account := "INSERT INTO patient_account(phone) VALUES(?)"
		result, err := database.Query(sql_account, phone)
		if err != nil {
			common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
				"code": 500,
				"msg":  "新建用户 - 帐号表插入失败",
				"err":  err.Error(),
				// "sql":  common.DebugSql(sql_account, phone, pwd),
			})
			return
		}
		new_id, _ = result.LastInsertId()
	} else {
		new_id, _ = strconv.ParseInt(pid, 10, 64)
	}
	sql := "INSERT INTO patient_profile(pid, asst_id, doc_id, support_id, relation, phone, name, sex, born_date, idcard, weixin, ins_card_num, ins_type, height, weight, allergies, medical_history, patient_from, address, ismarried, status, customer_notes,level) VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)"
	result, err := database.Query(sql, new_id, session.Values["id"], doc_id, 0, relation, phone, name, sex, bornDate, idcard, weixin, insCardNum, insType, height, weight, allergies, medicalHistory, patientFrom, address, isMarried, status, customerNotes, level)
	if err != nil {
		common.JSONResponse(w, http.StatusOK, map[string]interface{}{
			"code":       200,
			"msg":        "帐号新建成功，用户数据创建失败，请可以拿该帐号继续创建：" + phone,
			"err":        err.Error(),
			"account_id": new_id,
			"sql":        common.DebugSql(sql, new_id, session.Values["id"], doc_id, 0, relation, phone, name, sex, bornDate, idcard, weixin, insCardNum, insType, height, weight, allergies, medicalHistory, patientFrom, address, isMarried, status, customerNotes, level),
		})
		return
	}
	new_id2, _ := result.LastInsertId()
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":       200,
		"msg":        "患者添加成功",
		"account_id": new_id,
		"profile_id": new_id2,
	})
}

// 患者删除
func Patient_profile_del(w http.ResponseWriter, r *http.Request) {
	common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
		"code": 500,
		"msg":  "现阶段用户暂时不支持删除",
	})
	// api_id := 148
	// _, isLogin := common.Check_Perm(w, r, api_id)
	// if !isLogin {
	// 	return
	// }
	// id, err := common.CheckInt(r.FormValue("id"))
	// if id < 1 || err != nil {
	// 	common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
	// 		"code": 500,
	// 		"msg":  "患者id不能为空",
	// 	})
	// 	return
	// }
	// sql := "DELETE FROM patient_profile WHERE id = ?"
	// result, err := database.Query(sql, id)
	// if err != nil {
	// 	common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
	// 		"code": 500,
	// 		"msg":  "删除失败",
	// 		"err":  err.Error(),
	// 		"sql":  common.DebugSql(sql, id),
	// 	})
	// 	return
	// }
	// RowsAffected, _ := result.RowsAffected()
	// common.JSONResponse(w, http.StatusOK, map[string]interface{}{
	// 	"code":         200,
	// 	"msg":          "删除成功",
	// 	"RowsAffected": RowsAffected,
	// })
}

// 患者修改 50
func Patient_profile_edit(w http.ResponseWriter, r *http.Request) {
	api_id := 50
	session, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	id, err := common.CheckInt(r.FormValue("id"))
	if id < 1 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "患者id不能为空",
		})
		return
	}
	attSql := ""
	Tips := "数据更新成功"
	// 求出患者用户表原本的电话号码
	type Profile struct {
		Phone string
		Pid   string
	}
	var profile Profile
	sql := "select phone, pid from patient_profile where id = ?"
	err = database.GetRow(sql, &profile, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]any{
			"code": 500,
			"msg":  "获取原用户手机号码失败",
			"err":  err.Error(),
		})
		return
	}
	// 当用户是医助角色时，判断该患者是否属于他
	if session.Values["role_ids"] == "3" || session.Values["role_ids"] == "9" {
		// 根据上面求得的PID找出该帐号所绑定的医助ID
		sql_asst_id := "select asst_id from patient_account where id = ?"
		var asst_id int
		err = database.GetOne(sql_asst_id, &asst_id, profile.Pid)
		if err != nil {
			common.JSONResponse(w, http.StatusInternalServerError, map[string]any{
				"code": 500,
				"msg":  "获取医助ID失败",
				"err":  err.Error(),
			})
			return
		}
		if asst_id != session.Values["id"] {
			common.JSONResponse(w, http.StatusBadRequest, map[string]any{
				"code": 500,
				"msg":  fmt.Sprintf("对不起，该患者不属于您  (%v - %v)", asst_id, session.Values["id"]),
				// "sql":  common.DebugSql(sql_asst_id, profile.Pid),
			})
			return
		}
	}

	// 获取POST的号码
	phone, err := common.CheckPhone(r.FormValue("phone"))
	if err == nil && phone != profile.Phone { //证明用户选择了修改电话，且填了正确的电话号码格式，且电话与数据库中的不同
		// 检查用户表
		sql_check := "SELECT id FROM patient_profile WHERE phone = ? AND id != ?" //查询患者用户表有无重复
		// fmt.Println(common.DebugSql(sql_check, phone, id))
		var id_check_profile int
		err_profile := database.GetOne(sql_check, &id_check_profile, phone, id)
		// 检查帐号表
		sql_check = "SELECT id FROM patient_account WHERE phone = ? AND id != (select pid from patient_profile where id = ?)" //查询患者帐号表有无重复
		// fmt.Println(common.DebugSql(sql_check, phone, id))
		var id_check_account int
		err_account := database.GetOne(sql_check, &id_check_account, phone, id)
		// 证明存在相同手机号，不允许修改
		if err_profile == nil || err_account == nil {
			has_table := ""
			if err_profile == nil {
				has_table += " [用户表] "
			}
			if err_account == nil {
				has_table += " [帐号表] "
			}
			Tips = "用户数据修改成功 - 手机号未更新，因为该号码已存在于" + has_table
		} else {
			Tips = "用户数据修改成功 - 手机号更新成功"
			attSql += " , phone = '" + phone + "' "
		}
	}
	relation, err := common.CheckInt(r.FormValue("relation"))
	if relation < 0 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "与患者关系不能为空",
		})
		return
	}
	name, err := common.CheckStr(r.FormValue("name"))
	if name == "" || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "姓名不能为空",
		})
		return
	}
	sex, err := common.CheckStr(r.FormValue("sex"))
	if sex == "" || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "性别不能为空",
		})
		return
	}
	address, err := common.CheckStr(r.FormValue("address"))
	if address == "" || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "地址不能为空",
		})
		return
	}
	isMarried, _ := common.CheckInt(r.FormValue("isMarried"))
	if isMarried > 0 {
		isMarried = 1
	} else {
		isMarried = 0
	}
	medical_history := r.FormValue("medical_history")
	if medical_history == "" {
		medical_history = "无"
	}
	allergies := r.FormValue("allergies")
	if allergies == "" {
		allergies = "无"
	}
	chief_complaint := r.FormValue("chief_complaint")
	if chief_complaint == "" {
		chief_complaint = "无"
	}
	customer_notes := r.FormValue("customer_notes")
	if customer_notes == "" {
		customer_notes = "无"
	}

	ins_card_num := r.FormValue("ins_card_num")
	if ins_card_num == "" {
		ins_card_num = "无"
	}

	ins_type := r.FormValue("ins_type")
	if ins_type == "" {
		ins_type = "0"
	}

	weight := r.FormValue("weight")
	if weight == "" {
		weight = "0"
	}

	height := r.FormValue("height")
	if height == "" {
		height = "0"
	}

	weixin := r.FormValue("weixin")
	if weixin == "" {
		weixin = "0"
	}

	patient_type := r.FormValue("patient_type")
	if patient_type == "" {
		patient_type = "0"
	}

	patient_from := r.FormValue("patient_from")
	if patient_from == "" {
		patient_from = "0"
	}

	level := r.FormValue("patient_level")
	if level == "" {
		level = "A"
	}

	idcard := r.FormValue("idcard")
	if idcard == "" {
		idcard = "未登记"
	}

	born_date := r.FormValue("born_date")
	if born_date == "" {
		born_date = "0"
	}

	doc_id := r.FormValue("doctor")
	if doc_id == "" {
		doc_id = "0"
	}

	sql = "UPDATE patient_profile SET relation =?, name =?, sex =?, address =?,doc_id=?, ismarried =?, medical_history =?, allergies =?, chief_complaint =?, customer_notes =?, ins_card_num =?, ins_type =?, weight =?, height =?, weixin =?, patient_type =?, patient_from =?, level =?, idcard =?, born_date =? " + attSql + " WHERE id = ?"

	// 检查是否需要进行事务处理
	if attSql != "" && relation == 0 {
		Tips = "用户数据修改成功 - 由于家庭关系为本人，则除了更新用户手机号外，还更新了帐号手机号(" + profile.Phone + " -> " + phone + ")"

		// 创建事务SQL数组
		var sqlExecs []database.SQLExec

		// 第一条SQL：更新patient_profile表
		sqlExecs = append(sqlExecs, database.SQLExec{
			Query: sql,
			Args:  []any{relation, name, sex, address, doc_id, isMarried, medical_history, allergies, chief_complaint, customer_notes, ins_card_num, ins_type, weight, height, weixin, patient_type, patient_from, level, idcard, born_date, id},
		})

		// 第二条SQL：更新patient_account表中的phone
		accountSql := "UPDATE patient_account SET phone = ? WHERE id = (SELECT pid FROM patient_profile WHERE id = ?)"
		sqlExecs = append(sqlExecs, database.SQLExec{
			Query: accountSql,
			Args:  []any{phone, id},
		})

		// 执行事务
		rowsAffected, err := database.ExecuteTransaction_with_affectedRows(sqlExecs)
		if err != nil {
			common.JSONResponse(w, http.StatusInternalServerError, map[string]any{
				"code": 500,
				"msg":  "事务执行失败",
				"err":  err.Error(),
			})
			return
		}

		common.JSONResponse(w, http.StatusOK, map[string]interface{}{
			"code":         200,
			"msg":          Tips,
			"RowsAffected": rowsAffected,
		})

		if rowsAffected > 0 {
			common.Add_log(Tips, r)
		}
		return
	} else {
		// 非事务的原始处理方式
		result, err := database.Query(sql, relation, name, sex, address, doc_id, isMarried, medical_history, allergies, chief_complaint, customer_notes, ins_card_num, ins_type, weight, height, weixin, patient_type, patient_from, level, idcard, born_date, id)
		if err != nil {
			common.JSONResponse(w, http.StatusInternalServerError, map[string]any{
				"code": 500,
				"msg":  "更新数据失败",
				"err":  err.Error(),
				"sql":  common.DebugSql(sql, relation, name, sex, address, doc_id, isMarried, medical_history, allergies, chief_complaint, customer_notes, ins_card_num, ins_type, weight, height, weixin, patient_type, patient_from, level, idcard, born_date, id),
			})
			return
		}

		RowsAffected, _ := result.RowsAffected()
		common.JSONResponse(w, http.StatusOK, map[string]interface{}{
			"code":         200,
			"msg":          Tips,
			"RowsAffected": RowsAffected,
		})

		if RowsAffected > 0 {
			common.Add_log(fmt.Sprintf("成功修改患者资料，ID为%d", id), r)
		}
	}
}

// 病历详情 58
func Patient_records_detail(w http.ResponseWriter, r *http.Request) {
	api_id := 58
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	id, err := common.CheckInt(r.FormValue("id"))
	if id < 1 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "病历id不能为空",
		})
		return
	}
	// 病历详情
	type Patient_records struct {
		ID                         int     `db:"id"`                         // 主键
		Pat_id                     int     `db:"pat_id"`                     // 用户帐号id
		Pat_pro_id                 int     `db:"pat_pro_id"`                 // 用户资料id
		Pre_id                     int     `db:"pre_id"`                     // 绑定的处方ID
		Doc_id                     int     `db:"doc_id"`                     // 医生ID
		Department_id              int     `db:"department_id"`              // 科室ID
		Asst_id                    int     `db:"asst_id"`                    // 医助ID
		Chief_complaint            string  `db:"chief_complaint"`            // 主诉
		History_of_present_illness string  `db:"history_of_present_illness"` // 现病史
		Past_medical_history       string  `db:"past_medical_history"`       // 既往病史
		Personal_history           string  `db:"personal_history"`           // 个人史
		Family_history             string  `db:"family_history"`             // 家族史
		Allergy_history            string  `db:"allergy_history"`            // 过敏史
		Diagnosis_information      string  `db:"diagnosis_information"`      // 诊断信息
		Treatment_plan             string  `db:"treatment_plan"`             // 治疗方案
		Discharge_time             string  `db:"discharge_time"`             // 核销时间
		Status                     int     `db:"status"`                     // 状态
		Triad                      string  `db:"triad"`                      // 三高
		Tx_day                     int     `db:"tx_day"`                     // 疗程-天数
		Tx_type                    string  `db:"tx_type"`                    // 疗程-剂型
		Re_chief_complaint         string  `db:"re_chief_complaint"`         // 复诊主诉
		Urination                  string  `db:"urination"`                  // 大小便情况
		Last_medical               string  `db:"last_medical"`               // 上次用药情况
		Now_needs                  string  `db:"now_needs"`                  // 现需治疗
		Photo_tongue               *string `db:"photo_tongue"`               // 舌苔照
		Photo_sheet                *string `db:"photo_sheet"`                // 检查单
		Past_medication_history    string  `db:"past_medication_history"`    // 过往用药记录
		Last_medication_time       string  `db:"last_medication_time"`       // 上次用药时间
		Scheduled_time             string  `db:"scheduled_time"`             // 预约时间
		Create_time                string  `db:"create_time"`                // 建档时间
		Name                       string  `db:"name"`                       // 用户姓名
		Phone                      string  `db:"phone"`                      // 用户手机号
		Sex                        string  `db:"sex"`                        // 用户性别
		Born_Date                  string  `db:"born_date"`                  // 用户出生日期
		Tonguedesc                 string  `db:"tonguedesc"`                 // 舌象描述
	}
	var patient_records Patient_records
	sql := `
	SELECT
		a.id,
		a.pat_id,
		a.pat_pro_id,
		a.doc_id,
		a.asst_id,
		b.name,
		b.phone,
		b.sex,
		b.born_date,
		a.chief_complaint,
		a.history_of_present_illness,
		a.tonguedesc,
		a.past_medical_history,
		a.personal_history,
		a.family_history,
		a.allergy_history,
		a.diagnosis_information,
		a.treatment_plan,
		a.discharge_time,
		a.status,
		a.create_time,
		a.pre_id,
		a.department_id,
		a.triad,
		a.tx_day,
		a.tx_type,
		a.re_chief_complaint,
		a.urination,
		a.last_medical,
		a.now_needs,
		a.photo_tongue,
		a.photo_sheet,
		a.scheduled_time,
		a.past_medication_history,
		a.last_medication_time
	FROM
		patient_records AS a
	LEFT JOIN
		patient_profile AS b ON a.pat_pro_id = b.id
	WHERE
		a.id = ?`
	err = database.GetOne(sql, &patient_records, id)

	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败，请确认是否有关联数据已被删除，如用户数据",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, id),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":  200,
		"msg":   "ok",
		"data":  patient_records,
		"count": 1,
		"sql":   common.DebugSql(sql, id),
	})
}

// 病历添加 52
func Patient_records_add(w http.ResponseWriter, r *http.Request) {
	api_id := 52
	session, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	pat_pro_id, err := common.CheckInt(r.FormValue("pat_pro_id"))
	if pat_pro_id < 1 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "用户资料id不能为空",
		})
		return
	}
	type Patient_profile_data struct {
		Pat_id int `db:"pat_id"` // 用户帐号ID
		Doc_id int `db:"doc_id"` // 医生ID
	}
	sql := "SELECT pid as pat_id,doc_id FROM patient_profile WHERE id = ?"
	var patient_profile_data Patient_profile_data
	err = database.GetRow(sql, &patient_profile_data, pat_pro_id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "读取用户资料失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, pat_pro_id),
		})
		return
	}
	pat_id := patient_profile_data.Pat_id
	old_doc_id := patient_profile_data.Doc_id
	// 病历添加

	// 病历添加
	chief_complaint := r.FormValue("chief_complaint")
	past_medical_history := r.FormValue("past_medical_history")
	history_of_present_illness := r.FormValue("history_of_present_illness")
	personal_history := r.FormValue("personal_history")
	family_history := r.FormValue("family_history")
	allergy_history := r.FormValue("allergy_history")
	diagnosis_information := r.FormValue("diagnosis_information")
	treatment_plan := r.FormValue("treatment_plan")
	tonguedesc := r.FormValue("tonguedesc")

	// 获取医生ID和医助ID
	doc_id, err := common.CheckInt(r.FormValue("doc_id")) // 新增医生ID
	if err != nil || doc_id < 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "医生ID不能为空",
		})
		return
	}
	//获取部门ID
	department_id, err := common.CheckInt(r.FormValue("department_id"))
	if err != nil || department_id < 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "部门ID不能为空",
		})
		return
	}
	// 查询当前医生所在的部门ID与传递过来的部门ID是否一致
	var department_id_int int
	sql = "SELECT department_id FROM rbac_user WHERE id = ?"
	err = database.GetOne(sql, &department_id_int, doc_id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "读取医生部门ID失败",
			"err":  err.Error(),
		})
		return
	}
	if department_id_int != department_id {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "医生不属于该部门，无法添加病历",
		})
		return
	}
	// 医助ID，即添加该病历的用户
	var asst_id int
	var asst_dep_id int
	if session.Values["id"] == 1 {
		asst_dep_id, err = common.CheckInt(r.FormValue("asst_dep_id"))
		if err != nil || asst_dep_id < 1 {
			common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
				"code": 500,
				"msg":  "您当前是超级管理员角色，添加病历需指定医助所属售前部门",
			})
			return
		}
		asst_id, err = common.CheckInt(r.FormValue("asst_id"))
		if err != nil || asst_id < 1 {
			common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
				"code": 500,
				"msg":  "您当前是超级管理员角色，添加病历需指定医助",
			})
			return
		}
	} else {
		asst_id = session.Values["id"].(int)
		sql = "SELECT department_id FROM rbac_user WHERE id = ?"
		err = database.GetOne(sql, &asst_dep_id, asst_id)
		if err != nil {
			common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
				"code": 500,
				"msg":  "读取医助部门ID失败",
				"err":  err.Error(),
			})
			return
		}
	}

	// 先查询数据库中有没有相同主诉的病历，如果有，则不允许添加
	var count int
	sql = "SELECT count(id) FROM patient_records WHERE pat_pro_id = ? and DATEDIFF(CURDATE(), create_time) = 0 AND chief_complaint = ?"
	err = database.GetOne(sql, &count, pat_pro_id, chief_complaint)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "读取数据总数失败",
			"err":  err.Error(),
		})
		return
	}
	if count > 0 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "同用户同主诉的病历当天已存在，不允许重复添加",
		})
		return
	}

	// 获取新添加字段的数据
	triad := r.FormValue("triad") // 三高
	tx_day := r.FormValue("tx_day")
	if tx_day == "" {
		tx_day = "0"
	}
	tx_type := r.FormValue("tx_type")                       // 疗程-剂型
	re_chief_complaint := r.FormValue("re_chief_complaint") // 复诊主诉
	urination := r.FormValue("urination")                   // 大小便情况
	last_medical := r.FormValue("last_medical")             // 上次用药情况
	now_needs := r.FormValue("now_needs")                   // 现需治疗
	photo_tongue := r.FormValue("tongue_photos")            // 舌苔照
	photo_sheet := r.FormValue("sheet_photos")
	past_medication_history := r.FormValue("past_medication_history") // 即往用药史
	last_medication_time := r.FormValue("last_medication_time")       // 上次用药时间
	if last_medication_time == "" {
		last_medication_time = "0000-00-00 00:00:00"
	}
	var msg = "病历添加成功"
	sqls := []database.SQLExec{
		{Query: "INSERT INTO patient_records(pat_id, pat_pro_id, chief_complaint, history_of_present_illness, past_medical_history, personal_history, family_history, allergy_history, diagnosis_information, treatment_plan,tonguedesc, doc_id, department_id, triad, tx_day, tx_type, re_chief_complaint, urination, last_medical, now_needs, photo_tongue, photo_sheet, past_medication_history, last_medication_time,asst_id,asst_dep_id) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)",
			Args: []any{
				pat_id, pat_pro_id, chief_complaint, history_of_present_illness, past_medical_history, personal_history, family_history, allergy_history, diagnosis_information, treatment_plan, tonguedesc, doc_id, department_id, triad, tx_day, tx_type, re_chief_complaint, urination, last_medical, now_needs, photo_tongue, photo_sheet, past_medication_history, last_medication_time, asst_id, asst_dep_id,
			}},
	}

	// 正确的做法是在切片定义后根据条件添加元素
	if old_doc_id != doc_id {
		sqls = append(sqls, database.SQLExec{
			Query: "UPDATE patient_profile set doc_id = ? where id = ?",
			Args: []any{
				doc_id, pat_pro_id,
			},
		})
		msg = "病历添加成功，同时将患者隶属医生ID由 " + strconv.Itoa(old_doc_id) + "换绑为" + strconv.Itoa(doc_id)
	}

	RowsAffected, err := database.ExecuteTransaction_with_affectedRows(sqls)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "病历添加失败",
			"err":  err.Error(),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  msg,
		"sql":  common.DebugSql(sql, pat_id, pat_pro_id, chief_complaint, history_of_present_illness, past_medical_history, personal_history, family_history, allergy_history, diagnosis_information, treatment_plan, tonguedesc, doc_id, department_id, triad, tx_day, tx_type, re_chief_complaint, urination, last_medical, now_needs, photo_tongue, photo_sheet, past_medication_history, last_medication_time, asst_id, asst_dep_id),
	})
	if RowsAffected > 0 && old_doc_id != doc_id {
		common.Add_log(msg, r)
	}
}

// 病历修改 53

func Patient_records_edit(w http.ResponseWriter, r *http.Request) {
	api_id := 53
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	id, err := common.CheckInt(r.FormValue("id"))
	if id < 1 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "病历id不能为空",
		})
		return
	}

	// 创建参数列表
	params := []any{}
	attSql := ""

	chief_complaint := r.FormValue("chief_complaint") // 主诉
	attSql += ", chief_complaint = ?"
	params = append(params, chief_complaint)

	history_of_present_illness := r.FormValue("history_of_present_illness") //现病史
	attSql += ", history_of_present_illness = ?"
	params = append(params, history_of_present_illness)

	past_medical_history := r.FormValue("past_medical_history") //既往病史
	attSql += ", past_medical_history = ?"
	params = append(params, past_medical_history)

	personal_history := r.FormValue("personal_history") //个人史
	attSql += ", personal_history = ?"
	params = append(params, personal_history)

	family_history := r.FormValue("family_history") //家族史
	attSql += ", family_history = ?"
	params = append(params, family_history)

	allergy_history := r.FormValue("allergy_history") //过敏史
	attSql += ", allergy_history = ?"
	params = append(params, allergy_history)

	diagnosis_information := r.FormValue("diagnosis_information") //诊断信息
	attSql += ", diagnosis_information = ?"
	params = append(params, diagnosis_information)

	treatment_plan := r.FormValue("treatment_plan") //治疗方案
	attSql += ", treatment_plan = ?"
	params = append(params, treatment_plan)

	discharge_time := r.FormValue("discharge_time") //出院时间
	if discharge_time == "" {
		discharge_time = "0000-00-00 00:00:00"
	}
	attSql += ", discharge_time = ?"
	params = append(params, discharge_time)

	// 注释，以便以后回忆：
	// 在线诊室模块也会使用修改病历函数，它比普通修改病历会多传递一个rtc_room_id参数，判断如果有这个参数，则表示用户完成了问诊，触发修改rtc_room_id状态
	// 开始
	rtc_room_id := r.FormValue("rtc_room_id")
	if rtc_room_id != "" {
		sql := "update rtc_room set status = 1, finish_time = UNIX_TIMESTAMP() where id = ?"
		_, err = database.Query(sql, rtc_room_id)
		if err != nil {
			common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
				"code": 500,
				"msg":  "更新rtc_room状态失败",
				"err":  err.Error(),
			})
			return
		}
		attSql += ", status = 2"
	} else {
		status := r.FormValue("status")
		if status != "" {
			attSql += ", status = ?"
			params = append(params, status)
		}
	}
	// 结束
	doc_id := r.FormValue("doc_id")
	need_update_pat_pro_id := 0
	need_update_doc_id := 0
	old_doc_id := 0
	if doc_id != "" {
		// 查询当前病历的状态是不是0，如果不是的话，终止提交，提示用户，当前病历状态已不允许换绑医生
		type record_status struct {
			Pat_pro_id int `db:"pat_pro_id"`
			Status     int `db:"status"`
			Old_doc_id int `db:"old_doc_id"`
		}
		sql := "select pat_pro_id,status,doc_id as old_doc_id from patient_records where id = ?"
		var record record_status
		err = database.GetOne(sql, &record, id)
		if err != nil {
			common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
				"code": 500,
				"msg":  "接收到换绑医生请求，但在查询当前病历状态时查询失败。",
				"err":  err.Error(),
			})
			return
		}
		if record.Status != 0 {
			common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
				"code": 500,
				"msg":  "对不起，当前病历状态【" + config.Record_Status[record.Status] + "】已不允许换绑医生。",
			})
			return
		}
		need_update_pat_pro_id = record.Pat_pro_id
		old_doc_id = record.Old_doc_id
		need_update_doc_id, err = strconv.Atoi(doc_id)
		if err != nil {
			// 处理错误，例如记录日志或返回错误信息给客户端
			common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
				"code": 500,
				"msg":  "提供的医生ID无效，请检查输入。",
			})
			return
		}
		// 此时代表可以更新该病历下的医生ID，注意下面要再加个表的更新：把该患者的医生ID绑定为该医生ID
		attSql += ", doc_id = ?"
		params = append(params, doc_id)
	}

	asst_id := r.FormValue("asst_id")
	if asst_id != "" {
		attSql += ", asst_id = ?"
		params = append(params, asst_id)
	}

	triad := r.FormValue("triad") //三高情况
	attSql += ", triad = ?"
	params = append(params, triad)

	tx_day := r.FormValue("tx_day") //意向剂型天数
	if tx_day == "" {
		tx_day = "0"
	}
	attSql += ", tx_day = ?"
	params = append(params, tx_day)

	tx_type := r.FormValue("tx_type") //意向剂型
	attSql += ", tx_type = ?"
	params = append(params, tx_type)

	re_chief_complaint := r.FormValue("re_chief_complaint") //复诊主诉
	attSql += ", re_chief_complaint = ?"
	params = append(params, re_chief_complaint)

	urination := r.FormValue("urination") //大小便情况
	attSql += ", urination = ?"
	params = append(params, urination)

	last_medical := r.FormValue("last_medical") //上次用药情况
	attSql += ", last_medical = ?"
	params = append(params, last_medical)

	now_needs := r.FormValue("now_needs") //现需治疗
	attSql += ", now_needs = ?"
	params = append(params, now_needs)

	photo_tongue := r.FormValue("tongue_photos")
	attSql += ", photo_tongue = ?"
	params = append(params, photo_tongue)

	photo_sheet := r.FormValue("sheet_photos")
	attSql += ", photo_sheet = ?"
	params = append(params, photo_sheet)

	past_medication_history := r.FormValue("past_medication_history") //用药史
	attSql += ", past_medication_history = ?"
	params = append(params, past_medication_history)

	tonguedesc := r.FormValue("tonguedesc") //舌象描述
	attSql += ", tonguedesc = ?"
	params = append(params, tonguedesc)

	last_medication_time := r.FormValue("last_medication_time") //上次用药时间
	attSql += ", last_medication_time = ?"
	params = append(params, last_medication_time)

	if len(params) == 0 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "您没有提交任何修改的数据",
		})
		return
	}

	// 在参数列表中添加id
	params = append(params, id)
	attSql = attSql[1:]

	// 使用事务执行SQL
	tx, err := database.DB.Begin()
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "开始事务失败",
			"err":  err.Error(),
		})
		return
	}

	var result sql.Result
	var errExec error
	var msg = "病历修改成功"
	if need_update_pat_pro_id > 0 && need_update_doc_id > 0 && need_update_doc_id != old_doc_id {
		// 先执行更新用户关联医生的SQL
		sql1 := "UPDATE patient_profile SET doc_id = ? WHERE id = ?"
		_, errExec = tx.Exec(sql1, need_update_doc_id, need_update_pat_pro_id)
		if errExec != nil {
			tx.Rollback()
			common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
				"code": 500,
				"msg":  "更新用户关联医生失败",
				"err":  errExec.Error(),
				"sql":  common.DebugSql(sql1, need_update_doc_id, need_update_pat_pro_id),
			})
			return
		}
		// fmt.Println(common.DebugSql(sql1, need_update_doc_id, need_update_pat_pro_id))
		if need_update_doc_id != old_doc_id {
			msg = "病历修改成功 - 医生ID由" + strconv.Itoa(old_doc_id) + "换绑为" + strconv.Itoa(need_update_doc_id)
		}

	}
	// 只更新病历记录
	sqlQuery := "UPDATE patient_records SET " + attSql + " WHERE id = ?"
	result, errExec = tx.Exec(sqlQuery, params...)
	if errExec != nil {
		tx.Rollback()
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "更新病历记录失败",
			"err":  errExec.Error(),
			"sql":  common.DebugSql(sqlQuery, params...),
		})
		return
	}

	// 提交事务
	if err = tx.Commit(); err != nil {
		tx.Rollback()
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "提交事务失败",
			"err":  err.Error(),
		})
		return
	}

	// 获取影响的行数
	rowsAffected, _ := result.RowsAffected()
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":          200,
		"msg":           msg,
		"rows_affected": rowsAffected,
	})

	// 添加操作日志
	if rowsAffected > 0 {
		common.Add_log(fmt.Sprintf("%s | 病历ID：%d", msg, id), r)
	}
}

// 病历审核
func Patient_records_review(w http.ResponseWriter, r *http.Request) {
	api_id := 54
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	id, err := common.CheckInt(r.FormValue("id"))
	if id < 1 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "病历id不能为空",
		})
		return
	}
	status, err := common.CheckInt(r.FormValue("status"))
	if (status != 0 && status != 1) || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "状态值只能为0或1",
		})
		return
	}
	sql := "UPDATE patient_records SET status = ? WHERE id = ?"
	result, err := database.Query(sql, status, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "病历审核失败",
			"err":  err.Error(),
			// "sql":  common.DebugSql(sql, status, id),
		})
		return
	}
	RowsAffected, _ := result.RowsAffected()
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":          200,
		"msg":           "病历审核完成",
		"rows_affected": RowsAffected,
	})
	if RowsAffected > 0 {
		common.Add_log(fmt.Sprintf("病历审核成功，病历ID：%d", id), r)
	}
}

// 患者（病历）跟进表：patient_records_follow_up
// 其字段包括：id,record_id,pat_id,asst_id,follow_type,files,contents,ntext_reminder,create_time
// 患者（病历）跟进_列表 59
func Patient_records_follow_list(w http.ResponseWriter, r *http.Request) {
	api_id := 59
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	page, err := strconv.Atoi(r.FormValue("page"))
	if err != nil || page < 1 {
		page = 1
	}
	limit, err := strconv.Atoi(r.FormValue("limit"))
	if err != nil || limit < 10 {
		limit = 10
	}
	offset := (page - 1) * limit
	record_id, err := common.CheckInt(r.FormValue("record_id"))
	if record_id < 0 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "病历id不能为空",
		})
		return
	}
	var count int
	sql := "SELECT count(id) FROM patient_records_follow_up WHERE record_id = ?"
	err = database.GetOne(sql, &count, record_id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据总数失败",
			"err":  err.Error(),
			// "sql":  common.DebugSql(sql, record_id),
		})
		return
	}
	type Patient_records_follow_up struct {
		ID             int    `db:"id"`
		Record_id      int    `db:"record_id"`
		Pat_id         int    `db:"pat_id"`
		Asst_id        int    `db:"asst_id"`
		Follow_type    int    `db:"follow_type"`
		Files          string `db:"files"`
		Contents       string `db:"contents"`
		Ntext_reminder string `db:"ntext_reminder"`
		Create_time    string `db:"create_time"`
	}
	sql = "SELECT id, record_id, pat_id, asst_id, follow_type, files, contents, ntext_reminder, create_time FROM patient_records_follow_up WHERE record_id = ? ORDER BY create_time DESC LIMIT ? OFFSET ?"
	var patient_records_follow_up []Patient_records_follow_up
	err = database.GetAll(sql, &patient_records_follow_up, record_id, limit, offset)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败",
			"err":  err.Error(),
			// "sql":  common.DebugSql(sql, record_id, limit, offset),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":  200,
		"msg":   "ok",
		"count": count,
		"data":  patient_records_follow_up,
	})
}

// 患者（病历）跟进_详情 60
func Patient_records_follow_detail(w http.ResponseWriter, r *http.Request) {
	api_id := 60
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	id, err := common.CheckInt(r.FormValue("id"))
	if id < 1 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "跟进id不能为空",
		})
		return
	}
	type Patient_records_follow_up struct {
		ID             int    `db:"id"`
		Record_id      int    `db:"record_id"`
		Pat_id         int    `db:"pat_id"`
		Asst_id        int    `db:"asst_id"`
		Follow_type    int    `db:"follow_type"`
		Files          string `db:"files"`
		Contents       string `db:"contents"`
		Ntext_reminder string `db:"ntext_reminder"`
		Create_time    string `db:"create_time"`
	}
	sql := "SELECT id, record_id, pat_id, asst_id, follow_type, files, contents, ntext_reminder, create_time FROM patient_records_follow_up WHERE id = ?"
	var patient_records_follow_up Patient_records_follow_up
	err = database.GetRow(sql, &patient_records_follow_up, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败",
			"err":  err.Error(),
			// "sql":  common.DebugSql(sql, id),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "ok",
		"data": patient_records_follow_up,
	})
}

// 患者（病历）跟进_添加 61
func Patient_records_follow_add(w http.ResponseWriter, r *http.Request) {
	api_id := 61
	session, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	record_id, err := common.CheckInt(r.FormValue("record_id"))
	if record_id < 1 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "病历id不能为空",
		})
		return
	}
	pat_id, err := common.CheckInt(r.FormValue("pat_id"))
	if pat_id < 1 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "患者id不能为空",
		})
		return
	}
	// asst_id, err := common.CheckInt(r.FormValue("asst_id"))
	// if asst_id < 1 || err != nil {
	// 	common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
	// 		"code": 500,
	// 		"msg":  "医生助理id不能为空",
	// 	})
	// 	return
	// }
	asst_id := session.Values["id"]
	follow_type, err := common.CheckInt(r.FormValue("follow_type"))
	if follow_type < 0 || follow_type > 2 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "跟进类型不正确",
		})
		return
	}
	directory_type := []string{"pics", "voices", "videos"}

	files := r.MultipartForm.File["files"]
	file_names := ""
	if len(files) > 0 {
		// 遍历文件列表
		for _, fileHeader := range files {
			// 打开上传的文件
			file, err := fileHeader.Open()
			if err != nil {
				common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
					"code": 500,
					"msg":  "打开文件失败",
				})
				return
			}
			defer file.Close()

			// 解码图像
			img, _, err := image.Decode(file)
			if err != nil {
				common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
					"code": 500,
					"msg":  "读取图片失败",
				})
				return
			}

			// 检查宽度并缩放
			if img.Bounds().Dx() > 1000 {
				// 按比例缩放图像
				ratio := float64(1000) / float64(img.Bounds().Dx())
				newHeight := uint(float64(img.Bounds().Dy()) * ratio)
				img = resize.Resize(1000, newHeight, img, resize.Lanczos3)
			}

			// 生成一个唯一的文件名
			filename := common.Generate_filename("jpg")
			// 保存文件到服务器
			filepath := config.Dist_catagory + "/uploads/" + directory_type[follow_type] + "/" + filename
			outFile, err := os.OpenFile(filepath, os.O_WRONLY|os.O_CREATE, 0666)
			if err != nil {
				common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
					"code": 500,
					"msg":  "保存文件失败",
				})
				return
			}
			defer outFile.Close()

			// 保存图像
			err = jpeg.Encode(outFile, img, nil) // 如果是png图像，则使用png.Encode()
			if err != nil {
				common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
					"code": 500,
					"msg":  "图像保存失败",
				})
				return
			}
			file_names += filename + ","
		}
	}

	file_names = strings.TrimRight(file_names, ",")
	contents := r.FormValue("contents")
	if contents == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "内容不能为空",
		})
		return
	}
	ntext_reminder, err := common.CheckStr(r.FormValue("ntext_reminder"))
	if ntext_reminder == "" || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "提醒不能为空",
		})
		return
	}
	if ntext_reminder == "0" {
		ntext_reminder = "0000-00-00 00:00:00"
	}

	sql := "INSERT INTO patient_records_follow_up (record_id, pat_id, asst_id, follow_type, files, contents, ntext_reminder) VALUES (?,?,?,?,?,?,?)"
	result, err := database.Query(sql, record_id, pat_id, asst_id, follow_type, file_names, contents, ntext_reminder)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据添加失败",
			"err":  err.Error(),
			// "sql":  common.DebugSql(sql, record_id, pat_id, asst_id, follow_type, file_names, contents, ntext_reminder),
		})
		return
	}
	new_id, _ := result.LastInsertId()
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":   200,
		"msg":    "数据添加成功",
		"new_id": new_id,
	})
}

// 仓库管理，商品列表 62
func Warehouse_drug_list(w http.ResponseWriter, r *http.Request) {
	api_id := 62
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	// 带分页的查询
	attSql := ""
	var params []interface{}
	key, _ := common.CheckStr(r.FormValue("key"))
	if key != "" {
		if _, err := strconv.Atoi(key); err == nil {
			attSql = " AND b.code LIKE ?"
			params = append(params, "%"+key+"%")
		} else {
			// attSql += " and (b.name like ? or b.name_py like ? or a.last_supplier like ?)"
			// params = append(params, "%"+key+"%", "%"+key+"%", "%"+key+"%")
			attSql += " and b.name_py like ? "
			params = append(params, "%"+key+"%")
		}
	}
	// 分页参数
	limit, err := strconv.Atoi(r.FormValue("limit"))
	if err != nil || limit < 1 {
		limit = 10
	}
	page, err := strconv.Atoi(r.FormValue("page"))
	if err != nil || page < 1 {
		page = 1
	}
	attSql += " and a.quantity > 0 "
	offset := (page - 1) * limit
	var count int
	sql := "SELECT COUNT(DISTINCT a.id) FROM warehouse_drug as a left join drug as b on a.drug_id=b.id WHERE 1=1 " + attSql
	// fmt.Println(sql)
	err = database.GetOne(sql, &count, params...)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据总数失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, params...),
		})
		return
	}
	type Warehouse_drug struct {
		ID            int     `db:"id"`
		Drug_id       int     `db:"drug_id"`       //药品id
		Code          string  `db:"code"`          //药品批号
		Drug_name     string  `db:"drug_name"`     //药品名称
		Last_supplier string  `db:"last_supplier"` //最新供应商
		Cost_price    float64 `db:"cost_price"`    //进价
		Price         float64 `db:"price"`         //售价
		Min_stock     int     `db:"min_stock"`     //下限预警值
		Is_for_check  int     `db:"is_for_check"`  //是否参于盘点
		Quantity      float64 `db:"quantity"`      //库存数量
		Exp_date      string  `db:"exp_date"`      //有效期
		Spec          string  `db:"spec"`          //规格
		Create_time   string  `db:"create_time"`   //建立时间
	}
	// 查询数据
	// sql = "SELECT a.id,a.drug_id,a.cost_price,a.price,a.quantity,a.exp_date,a.create_time,b.name FROM warehouse_drug as a left join drug as b on a.drug_id=b.id WHERE 1=1 " + attSql + " ORDER BY a.id DESC LIMIT ? OFFSET ?"
	sql = `
			SELECT
			a.id,
			a.drug_id,
			a.last_supplier,
			a.cost_price,
			a.price,
			a.min_stock,
			a.is_for_check,
			a.quantity,
			a.exp_date,
			a.create_time,
			b.name drug_name,
			b.code,
			b.spec
			FROM
			warehouse_drug AS a
			LEFT JOIN drug AS b ON a.drug_id = b.id
			WHERE
			1 = 1
			` + attSql + `
			ORDER BY
			a.id DESC
			LIMIT ? OFFSET ?
	`
	params = append(params, limit, offset)
	var warehouse_drug []Warehouse_drug
	err = database.GetAll(sql, &warehouse_drug, params...)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, params...),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":  200,
		"msg":   "ok",
		"count": count,
		"data":  warehouse_drug,
		"sql":   common.DebugSql(sql, params...),
	})
}

// 库存商品详情 63
func Warehouse_drug_detail(w http.ResponseWriter, r *http.Request) {
	api_id := 63
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	id, err := common.CheckInt(r.FormValue("id"))
	if id < 1 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "商品id不能为空",
		})
		return
	}
	type Warehouse_drug struct {
		ID            int     `db:"id"`
		Drug_id       int     `db:"drug_id"`       //药品id
		Last_supplier string  `db:"last_supplier"` //最新供应商
		Price         float64 `db:"price"`         //售价
		Cost_price    float64 `db:"cost_price"`    //进价
		Min_stock     int     `db:"min_stock"`     //下限预警值
		Is_for_check  int     `db:"is_for_check"`  //是否参于盘点
		Quantity      float64 `db:"quantity"`      //库存数量
		Exp_date      string  `db:"exp_date"`      //有效期
		Create_time   string  `db:"create_time"`   //建立时间
		Drug_name     string  `db:"drug_name"`     //药品名称
		Code          string  `db:"code"`          //药品批号
		Spec          string  `db:"spec"`          //规格
		Unit          string  `db:"unit"`          //单位
		Market_price  float64 `db:"market_price"`  //市场价
	}
	sql := `
			SELECT
			a.id,
			a.drug_id,
			a.last_supplier,
			a.cost_price,
			a.price,
			a.min_stock,
			a.is_for_check,
			a.quantity,
			a.exp_date,
			a.create_time,
			b.name drug_name,
			b.code,
			b.unit,
			b.spec,
			b.price market_price
			FROM
			warehouse_drug AS a
			LEFT JOIN drug AS b ON a.drug_id = b.id
			WHERE
			a.id = ?
	`
	var warehouse_drug Warehouse_drug
	err = database.GetOne(sql, &warehouse_drug, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, id),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "ok",
		"data": warehouse_drug,
	})
}

// 新增库存商品 64
func Warehouse_drug_add(w http.ResponseWriter, r *http.Request) {
	api_id := 64
	session, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	drug_id, err := common.CheckInt(r.FormValue("drug_id"))
	if drug_id < 1 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "商品名称不能为空",
		})
		return
	}
	price, err := common.CheckFloat(r.FormValue("price"))
	if price < 0 || err != nil { // 价格不能为负数
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "商品价格不能为空",
		})
		return
	}
	quantity, err := common.CheckInt(r.FormValue("quantity"))
	if quantity < 0 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "商品数量不能为空",
		})
		return
	}
	exp_date, err := common.CheckStr(r.FormValue("exp_date"))
	if exp_date == "" || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "商品有效期不能为空",
		})
		return
	}
	notes, err := common.CheckStr(r.FormValue("notes"))
	if notes == "" || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "备注不能为空",
		})
		return
	}
	// 先查下有没有已经添加过相同的库存
	sql := "SELECT id FROM warehouse_drug WHERE drug_id = ? and price = ? and quantity = ? and exp_date = ? limit 1"
	var hasid int
	err = database.GetOne(sql, &hasid, drug_id, price, quantity, exp_date)
	if err == nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "该商品已经添加过库存，请勿重复添加",
			// "sql":  common.DebugSql(sql, drug_id, price, quantity, exp_date),
		})
		return
	}
	sql = "INSERT INTO warehouse_drug (drug_id,price,quantity,exp_date) VALUES (?,?,?,?)"
	result, err := database.Query(sql, drug_id, price, quantity, exp_date)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "库存新增失败",
			"err":  err.Error(),
			// "sql":  common.DebugSql(sql, drug_id, price, quantity, exp_date),
		})
		return
	}
	new_id, _ := result.LastInsertId()
	sql = "insert into warehouse_drug_log (pid,user_id,kind,old_data,new_data,change_data,notes) values (?,?,?,?,?,?,?)"
	_, err = database.Query(sql, new_id, session.Values["id"], 1, 0, quantity, quantity, notes)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "库存新增日志记录失败",
			"err":  err.Error(),
			// "sql":  common.DebugSql(sql, new_id, session.Values["id"], 0, quantity, notes),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":   200,
		"msg":    "库存新增成功",
		"new_id": new_id,
	})
}

// 供应商ID2NAME
func Sup_id2name(w http.ResponseWriter, r *http.Request) {
	api_id := config.NormalPerm
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	attrSql := ""
	//ID
	id, err := common.CheckInt(r.FormValue("id"))
	if err == nil && id > 0 {
		attrSql += " and a.id = " + strconv.Itoa(id)
	}
	type Sup_drug struct {
		ID   int    `db:"id"`
		Name string `db:"name"`
	}
	sql := "SELECT id,name FROM supplier_drug WHERE 1 " + attrSql
	var suppliers []Sup_drug
	err = database.GetAll(sql, &suppliers)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败",
			"err":  err.Error(),
			// "sql":  common.DebugSql(sql),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "ok",
		"data": suppliers,
	})
}

// 原料_出入库日志列表 67
func Warehouse_drug_log_list(w http.ResponseWriter, r *http.Request) {
	api_id := 67
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	limit, err := strconv.Atoi(r.FormValue("limit"))
	if err != nil || limit <= 0 {
		limit = 5
	}
	page, err := strconv.Atoi(r.FormValue("page"))
	if err != nil || page <= 0 {
		page = 1
	}
	offset := (page - 1) * limit
	var count int
	var params []interface{}
	attSql := ""
	kind := r.FormValue("kind")
	if kind == "0" || kind == "1" || kind == "2" {
		attSql += " and kind =?"
		params = append(params, kind)
	}
	key := r.FormValue("key")
	if key != "" {
		attSql += " and (batch like ? or notes like ?)"
		params = append(params, "%"+key+"%", "%"+key+"%")
	}
	countSql := "SELECT COUNT(id) FROM warehouse_drug_log where 1 " + attSql
	err = database.GetOne(countSql, &count, params...)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "读取数据总数失败",
			"sql":  common.DebugSql(countSql, params...),
		})
		return
	}
	type Log struct {
		ID          int     `db:"id"`
		Pid         int     `db:"pid"`
		Sup_id      int     `db:"sup_id"`
		User_id     int     `db:"user_id"`
		Kind        int     `db:"kind"`
		Batch       string  `db:"batch"`
		Old_data    float64 `db:"old_data"`
		Change_data float64 `db:"change_data"`
		New_data    float64 `db:"new_data"`
		Notes       string  `db:"notes"`
		Create_time string  `db:"create_time"`
	}
	sql := "SELECT id,pid,sup_id,user_id,kind,batch,old_data,change_data,new_data,notes,create_time FROM warehouse_drug_log where 1 " + attSql + " ORDER BY id DESC limit ? OFFSET ?"
	var logs []Log
	params = append(params, limit, offset)
	err = database.GetAll(sql, &logs, params...)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "读取数据失败",
			// "sql":  common.DebugSql(sql, params...),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":  200,
		"msg":   "读取成功",
		"data":  logs,
		"count": count,
	})
}

// 原料_出入库日志详情 68
func Warehouse_drug_log_detail(w http.ResponseWriter, r *http.Request) {
	api_id := 68
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	id, err := common.CheckInt(r.FormValue("id"))
	if err != nil || id < 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "日志ID不能为空",
		})
		return
	}
	type Log struct {
		UserID     int     `db:"user_id"`
		UserName   string  `db:"name"`
		Kind       int     `db:"kind"`
		OldData    float64 `db:"old_data"`
		ChangeData float64 `db:"change_data"`
		NewData    float64 `db:"new_data"`
		Notes      string  `db:"notes"`
		CreateTime string  `db:"create_time"`
	}
	sql := "SELECT a.user_id,b.name,a.kind,a.old_data,a.change_data,a.new_data,a.notes,a.create_time FROM warehouse_drug_log as a left join rbac_user as b on a.user_id = b.id WHERE a.id = ?"
	var log Log
	err = database.GetRow(sql, &log, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "读取数据失败",
			// "sql":  common.DebugSql(sql),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "读取成功",
		"data": log,
	})
}

// 仓库管理，赠品列表 69
func Warehouse_gifts_list(w http.ResponseWriter, r *http.Request) {
	api_id := 69
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	// 分页参数
	limit, err := strconv.Atoi(r.FormValue("limit"))
	if err != nil || limit < 1 {
		limit = 10
	}
	page, err := strconv.Atoi(r.FormValue("page"))
	if err != nil || page < 1 {
		page = 1
	}
	offset := (page - 1) * limit
	attSql := ""
	params := []interface{}{}
	// 关键字搜索
	key := r.FormValue("key")
	if key != "" {
		attSql += " and b.name like ?"
		params = append(params, "%"+key+"%")
	}
	// 查询总数
	sql := "SELECT COUNT(a.id) as count FROM warehouse_gifts as a left join gifts as b on b.id = a.gift_id where 1" + attSql
	var count int
	err = database.GetOne(sql, &count, params...)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据总数失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, params...),
		})
		return
	}
	type Warehouse_gifts struct {
		ID          int     `db:"id"`
		Gift_id     string  `db:"gift_id"`
		Gift_name   string  `db:"gift_name"`
		Price       float64 `db:"price"`
		Quantity    float64 `db:"quantity"`
		Create_time string  `db:"create_time"`
	}
	params = append(params, limit, offset)
	// 查询数据
	sql = "SELECT a.id,a.gift_id,a.price,a.quantity,a.create_time,b.name gift_name FROM warehouse_gifts as a left join gifts as b on b.id = a.gift_id where 1 " + attSql + " ORDER BY a.id DESC LIMIT ? OFFSET ?"
	var warehouse_gifts []Warehouse_gifts
	err = database.GetAll(sql, &warehouse_gifts, params...)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, params...),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":  200,
		"msg":   "ok",
		"data":  warehouse_gifts,
		"count": count,
	})
}

// 库存赠品详情 70
func Warehouse_gifts_detail(w http.ResponseWriter, r *http.Request) {
	api_id := 70
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	id, err := common.CheckInt(r.FormValue("id"))
	if id < 1 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "赠品id不能为空",
		})
		return
	}
	type Warehouse_gifts struct {
		ID          int     `db:"id"`
		Gift_id     string  `db:"gift_id"`
		Price       float64 `db:"price"`
		Quantity    float64 `db:"quantity"`
		Create_time string  `db:"create_time"`
	}
	sql := "SELECT id,gift_id,price,quantity,create_time FROM warehouse_gifts WHERE id = ?"
	var warehouse_gifts Warehouse_gifts
	err = database.GetRow(sql, &warehouse_gifts, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败",
			"err":  err.Error(),
			// "sql":  common.DebugSql(sql, id),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "ok",
		"data": warehouse_gifts,
	})
}

// 新增库存赠品 71
func Warehouse_gifts_add(w http.ResponseWriter, r *http.Request) {
	api_id := 71
	session, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	gift_id, err := common.CheckInt(r.FormValue("gift_id"))
	if gift_id < 1 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "赠品ID不能为空",
		})
		return
	}
	price, err := common.CheckFloat(r.FormValue("price"))
	if price < 0 || err != nil { // 价格不能为负数
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "赠品价格不能为空",
		})
		return
	}
	quantity, err := common.CheckInt(r.FormValue("quantity"))
	if quantity < 0 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "赠品数量不能为空",
		})
		return
	}
	notes, err := common.CheckStr(r.FormValue("notes"))
	if notes == "" || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "备注不能为空",
		})
		return
	}
	// 先查下有没有已经添加过相同的库存
	sql := "SELECT id FROM warehouse_gifts WHERE gift_id = ? and price = ? and quantity = ? limit 1"
	var hasid int
	err = database.GetOne(sql, &hasid, gift_id, price, quantity)
	if err == nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "该赠品已经添加过库存，请勿重复添加",
		})
		return
	}
	sql = "INSERT INTO warehouse_gifts (gift_id,price,quantity) VALUES (?,?,?)"
	result, err := database.Query(sql, gift_id, price, quantity)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "库存新增失败",
			"err":  err.Error(),
			// "sql":  common.DebugSql(sql, gift_id, price, quantity),
		})
		return
	}
	new_id, _ := result.LastInsertId()
	sql = "insert into warehouse_gifts_log (pid,user_id,kind,old_data,new_data,change_data,notes) values (?,?,?,?,?,?,?)"
	_, err = database.Query(sql, new_id, session.Values["id"], 1, 0, quantity, quantity, notes)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "库存新增日志记录失败",
			"err":  err.Error(),
			// "sql":  common.DebugSql(sql, new_id, session.Values["id"], 0, quantity, notes),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":   200,
		"msg":    "库存新增成功",
		"new_id": new_id,
	})
}

// 库存出库 73
func Warehouse_gifts_out(w http.ResponseWriter, r *http.Request) {
	api_id := 73
	session, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	id, err := common.CheckInt(r.FormValue("id"))
	if id < 1 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "出库库存ID不能为空",
		})
		return
	}
	quantity, err := common.CheckFloat(r.FormValue("quantity"))
	if quantity < 0 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "出库数量不能为空",
		})
		return
	}
	notes, err := common.CheckStr(r.FormValue("notes"))
	if notes == "" || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "备注不能为空",
		})
		return
	}
	// 查询当前补货赠品还剩余的库存量
	sql := "SELECT quantity FROM warehouse_gifts WHERE id = ? limit 1"
	var old_data float64
	err = database.GetOne(sql, &old_data, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "出库赠品查询失败",
			"err":  err.Error(),
			// "sql":  common.DebugSql(sql, id),
		})
		return
	}
	new_data := old_data - quantity
	if new_data < 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "出库数量不能大于库存数量",
		})
		return
	}
	// 开始补货，库存表与库存日志使用事务以保持数据一致性
	// 事务开始
	sqls := []database.SQLExec{
		{Query: "Update warehouse_gifts set quantity = quantity - ? where id = ?",
			Args: []interface{}{
				quantity, id,
			}},
		{Query: "INSERT INTO warehouse_gifts_log (pid,user_id,kind,change_data,old_data,new_data,notes) VALUES (?,?,?,?,?,?,?)",
			Args: []interface{}{
				id, session.Values["id"], 1, quantity, old_data, new_data, notes,
			}},
	}
	RowsAffected, err := database.ExecuteTransaction_with_affectedRows(sqls)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "出库失败",
			"err":  err.Error(),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "出库成功",
		"id":   id,
	})
	if RowsAffected > 0 {
		common.Add_log(fmt.Sprintf("库存赠品%d出库%f，备注：%s，影响行数：%d", id, quantity, notes, RowsAffected), r)
	}
}

// 原料_出入库日志列表 78
func Warehouse_gifts_log_list(w http.ResponseWriter, r *http.Request) {
	api_id := 78
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}

	type Log struct {
		ID          int     `db:"id"`
		Pid         int     `db:"pid"`
		User_id     int     `db:"user_id"`
		Kind        int     `db:"kind"`
		Old_data    float64 `db:"old_data"`
		Change_data float64 `db:"change_data"`
		New_data    float64 `db:"new_data"`
		Notes       string  `db:"notes"`
		Create_time string  `db:"create_time"`
	}

	// Initialize empty logs slice to ensure we return [] instead of null
	logs := []Log{}

	limit, err := strconv.Atoi(r.FormValue("limit"))
	if err != nil || limit <= 0 {
		limit = 5
	}
	page, err := strconv.Atoi(r.FormValue("page"))
	if err != nil || page <= 0 {
		page = 1
	}
	var params []interface{}
	var attSql string
	kind := r.FormValue("kind")
	if kind != "" {
		attSql = " and kind = ?"
		params = append(params, kind)
	}
	var count int
	countSql := "SELECT COUNT(id) FROM warehouse_gifts_log WHERE 1" + attSql
	err = database.GetOne(countSql, &count, params...)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "读取数据总数失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(countSql, params...),
		})
		return
	}
	offset := (page - 1) * limit
	params = append(params, limit, offset)
	sql := "SELECT id,pid,user_id,kind,old_data,change_data,new_data,notes,create_time FROM warehouse_gifts_log WHERE 1" + attSql + " ORDER BY id DESC limit ? OFFSET ?"
	err = database.GetAll(sql, &logs, params...)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "读取数据失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, params...),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":  200,
		"msg":   "读取成功",
		"data":  logs,
		"count": count,
		"sql":   common.DebugSql(sql, params...),
	})
}

// 原料_出入库日志详情 74
func Warehouse_gifts_log_detail(w http.ResponseWriter, r *http.Request) {
	api_id := 74
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	id, err := common.CheckInt(r.FormValue("id"))
	if err != nil || id < 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "日志ID不能为空",
		})
		return
	}
	type Log struct {
		UserID     int     `db:"user_id"`
		UserName   string  `db:"name"`
		Kind       int     `db:"kind"`
		OldData    float64 `db:"old_data"`
		ChangeData float64 `db:"change_data"`
		NewData    float64 `db:"new_data"`
		Notes      string  `db:"notes"`
		CreateTime string  `db:"create_time"`
	}
	sql := "SELECT a.user_id,b.name,a.kind,a.old_data,a.change_data,a.new_data,a.notes,a.create_time FROM warehouse_gifts_log as a left join rbac_user as b on a.user_id = b.id WHERE a.id = ?"
	var log Log
	err = database.GetOne(sql, &log, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "读取数据失败",
			// "sql":  common.DebugSql(sql),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "读取成功",
		"data": log,
	})
}

// 开方时，选择成品药时的接口
func Warehouse_finisheddrug_list_choose_drug(w http.ResponseWriter, r *http.Request) {
	api_id := config.NormalPerm
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	// 分页参数
	limit, err := strconv.Atoi(r.FormValue("limit"))
	if err != nil || limit < 1 {
		limit = 10
	}
	page, err := strconv.Atoi(r.FormValue("page"))
	if err != nil || page < 1 {
		page = 1
	}
	var params []any
	var attSql string
	attSql = ""
	key := r.FormValue("key")
	if key != "" {
		if strings.ToUpper(string(key[0])) == "C" {
			key = key[1:]
		}
		attSql += " and pre_id = ?"
		params = append(params, key)
	}
	attSql += " and status = 1 and kind in(1,2) and new_pre_id = 0 "
	// 查询总数
	sql := "SELECT COUNT(id) as count FROM warehouse_finisheddrug where 1 " + attSql
	var count int
	err = database.GetOne(sql, &count, params...)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据总数失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, params...),
		})
		return
	}
	type Warehouse_finisheddrug struct {
		ID          int     `db:"id"`
		Kind        int     `db:"kind"`
		Ord_id      int     `db:"ord_id"`
		Pre_id      int     `db:"pre_id"`
		New_pre_id  int     `db:"new_pre_id"`
		Quantity    float64 `db:"quantity"`
		Unit        int     `db:"unit"`
		Notes       string  `db:"notes"`
		Create_time string  `db:"create_time"`
	}
	offset := (page - 1) * limit
	params = append(params, limit, offset)
	// 查询数据
	sql = "SELECT id,kind,ord_id,pre_id,new_pre_id,quantity,unit,name as notes,create_time FROM warehouse_finisheddrug where 1 " + attSql + " ORDER BY id DESC LIMIT ? OFFSET ?"
	var warehouse_finisheddrug []Warehouse_finisheddrug
	err = database.GetAll(sql, &warehouse_finisheddrug, params...)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, params...),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":  200,
		"msg":   "ok",
		"data":  warehouse_finisheddrug,
		"count": count,
		"sql":   common.DebugSql(sql, params...),
	})
}

// 仓库管理，成品药列表 76
func Warehouse_finisheddrug_list(w http.ResponseWriter, r *http.Request) {
	api_id := 76
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	// 分页参数
	limit, err := strconv.Atoi(r.FormValue("limit"))
	if err != nil || limit < 1 {
		limit = 10
	}
	page, err := strconv.Atoi(r.FormValue("page"))
	if err != nil || page < 1 {
		page = 1
	}
	var params []any
	var attSql string
	attSql = ""
	key := r.FormValue("key")
	if key != "" {
		if strings.ToUpper(string(key[0])) == "C" {
			key = key[1:]
		}
		attSql += " and pre_id = ?"
		params = append(params, key)
	}
	kind := r.FormValue("kind")
	if kind != "" {
		if strings.Contains(kind, ",") {
			// Split the comma-separated kinds into a slice
			kindSlice := strings.Split(kind, ",")

			// Create the appropriate number of placeholders
			placeholders := make([]string, len(kindSlice))
			for i := range placeholders {
				placeholders[i] = "?"
			}

			// Join the placeholders with commas
			attSql += " and kind in (" + strings.Join(placeholders, ",") + ")"

			// Add each kind value to the params
			for _, k := range kindSlice {
				params = append(params, strings.TrimSpace(k))
			}
		} else {
			attSql += " and kind = ?"
			params = append(params, kind)
		}
	}
	attSql += " and status = 1"
	// 查询总数
	sql := "SELECT COUNT(id) as count FROM warehouse_finisheddrug where 1 " + attSql
	var count int
	err = database.GetOne(sql, &count, params...)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据总数失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, params...),
		})
		return
	}
	type Warehouse_finisheddrug struct {
		ID          int     `db:"id"`
		Kind        int     `db:"kind"`
		Ord_id      int     `db:"ord_id"`
		Pre_id      int     `db:"pre_id"`
		Quantity    float64 `db:"quantity"`
		Status      int     `db:"status"`
		Unit        int     `db:"unit"`
		Notes       string  `db:"notes"`
		Create_time string  `db:"create_time"`
	}
	offset := (page - 1) * limit
	params = append(params, limit, offset)
	// 查询数据
	sql = "SELECT id,kind,ord_id,pre_id,quantity,status,unit,name as notes,create_time FROM warehouse_finisheddrug where 1 " + attSql + " ORDER BY id DESC LIMIT ? OFFSET ?"
	var warehouse_finisheddrug []Warehouse_finisheddrug
	err = database.GetAll(sql, &warehouse_finisheddrug, params...)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, params...),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":  200,
		"msg":   "ok",
		"data":  warehouse_finisheddrug,
		"count": count,
		"sql":   common.DebugSql(sql, params...),
	})
}

// 库存成品药详情 77
func Warehouse_finisheddrug_detail(w http.ResponseWriter, r *http.Request) {
	api_id := config.NormalPerm
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	id, err := common.CheckInt(r.FormValue("id"))
	if id < 1 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "成品药id不能为空",
		})
		return
	}
	type Warehouse_finisheddrug struct {
		ID          int     `db:"id"`
		Kind        int     `db:"kind"`
		Ord_id      int     `db:"ord_id"`
		Pre_id      int     `db:"pre_id"`
		Quantity    float64 `db:"quantity"`
		Status      int     `db:"status"`
		Unit        int     `db:"unit"`
		Notes       string  `db:"notes"`
		Price       float64 `db:"price"`
		Create_time string  `db:"create_time"`
	}
	sql := "SELECT id,kind,ord_id,pre_id,quantity,status,unit,name as notes,price,create_time FROM warehouse_finisheddrug WHERE id = ?"
	var warehouse_finisheddrug Warehouse_finisheddrug
	err = database.GetRow(sql, &warehouse_finisheddrug, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败",
			"err":  err.Error(),
			// "sql":  common.DebugSql(sql, id),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "ok",
		"data": warehouse_finisheddrug,
	})
}

// 新增库存成品药 78
func Warehouse_finisheddrug_add(w http.ResponseWriter, r *http.Request) {
	api_id := 78
	session, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	name, err := common.CheckStr(r.FormValue("name"))
	if name == "" || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "成品药名称不能为空",
		})
		return
	}
	price, err := common.CheckFloat(r.FormValue("price"))
	if price < 0 || err != nil { // 价格不能为负数
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "成品药价格不能为空",
		})
		return
	}
	quantity, err := common.CheckInt(r.FormValue("quantity"))
	if quantity < 0 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "成品药数量不能为空",
		})
		return
	}
	notes, err := common.CheckStr(r.FormValue("notes"))
	if notes == "" || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "备注不能为空",
		})
		return
	}
	// 先查下有没有已经添加过相同的库存
	sql := "SELECT id FROM warehouse_finisheddrug WHERE name = ? and price = ? and quantity = ? limit 1"
	var hasid int
	err = database.GetOne(sql, &hasid, name, price, quantity)
	if err == nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "该成品药已经添加过库存，请勿重复添加",
		})
		return
	}
	sql = "INSERT INTO warehouse_finisheddrug (name,price,quantity) VALUES (?,?,?)"
	result, err := database.Query(sql, name, price, quantity)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "成品库存新增失败",
			"err":  err.Error(),
			// "sql":  common.DebugSql(sql, name, price, quantity),
		})
		return
	}
	new_id, _ := result.LastInsertId()
	sql = "insert into warehouse_finisheddrug_log (pid,user_id,kind,old_data,new_data,change_data,notes) values (?,?,?,?,?,?,?)"
	_, err = database.Query(sql, new_id, session.Values["id"], 1, 0, quantity, quantity, notes)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "成品库存新增日志记录失败",
			"err":  err.Error(),
			// "sql":  common.DebugSql(sql, new_id, session.Values["id"], 0, quantity, notes),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":   200,
		"msg":    "库存新增成功",
		"new_id": new_id,
	})
}

// 库存补货 79
func Warehouse_finisheddrug_in(w http.ResponseWriter, r *http.Request) {
	api_id := 79
	session, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	id, err := common.CheckInt(r.FormValue("id"))
	if id < 1 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "补货库存ID不能为空",
		})
		return
	}
	quantity, err := common.CheckFloat(r.FormValue("quantity"))
	if quantity < 0 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "补货数量不能为空",
		})
		return
	}
	notes, err := common.CheckStr(r.FormValue("notes"))
	if notes == "" || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "备注不能为空",
		})
		return
	}
	// 查询当前补货成品药还剩余的库存量
	sql := "SELECT quantity FROM warehouse_finisheddrug WHERE id = ? limit 1"
	var old_data float64
	err = database.GetOne(sql, &old_data, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "补货成品药查询失败",
			"err":  err.Error(),
			// "sql":  common.DebugSql(sql, id),
		})
		return
	}
	new_data := old_data + quantity
	// 开始补货，库存表与库存日志使用事务以保持数据一致性
	// 事务开始
	sqls := []database.SQLExec{
		{Query: "Update warehouse_finisheddrug set quantity = quantity + ? where id = ?",
			Args: []interface{}{
				quantity, id,
			}},
		{Query: "INSERT INTO warehouse_finisheddrug_log (pid,user_id,kind,change_data,old_data,new_data,notes) VALUES (?,?,?,?,?,?,?)",
			Args: []interface{}{
				id, session.Values["id"], 1, quantity, old_data, new_data, notes,
			}},
	}
	RowsAffected, err := database.ExecuteTransaction_with_affectedRows(sqls)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "成品入库失败",
			"err":  err.Error(),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "成品入库成功",
		"id":   id,
	})
	if RowsAffected > 0 {
		common.Add_log(fmt.Sprintf("补货成品药，成品ID：%d，数量：%f，备注：%s,影响行数：%d", id, quantity, notes, RowsAffected), r)
	}
}

// 库存出库 80
func Warehouse_finisheddrug_out(w http.ResponseWriter, r *http.Request) {
	api_id := 80
	session, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	id, err := common.CheckInt(r.FormValue("id"))
	if id < 1 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "出库库存ID不能为空",
		})
		return
	}
	quantity, err := common.CheckFloat(r.FormValue("quantity"))
	if quantity < 0 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "出库数量不能为空",
		})
		return
	}
	notes, err := common.CheckStr(r.FormValue("notes"))
	if notes == "" || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "备注不能为空",
		})
		return
	}
	// 查询当前补货成品药还剩余的库存量
	sql := "SELECT quantity FROM warehouse_finisheddrug WHERE id = ? limit 1"
	var old_data float64
	err = database.GetOne(sql, &old_data, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "出库成品药查询失败",
			"err":  err.Error(),
			// "sql":  common.DebugSql(sql, id),
		})
		return
	}
	new_data := old_data - quantity
	if new_data < 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "出库数量不能大于库存数量",
		})
		return
	}
	// 开始补货，库存表与库存日志使用事务以保持数据一致性
	// 事务开始
	sqls := []database.SQLExec{
		{Query: "Update warehouse_finisheddrug set quantity = quantity - ? where id = ?",
			Args: []interface{}{
				quantity, id,
			}},
		{Query: "INSERT INTO warehouse_finisheddrug_log (pid,user_id,kind,change_data,old_data,new_data,notes) VALUES (?,?,?,?,?,?,?)",
			Args: []interface{}{
				id, session.Values["id"], 0, quantity, old_data, new_data, notes,
			}},
	}
	RowsAffected, err := database.ExecuteTransaction_with_affectedRows(sqls)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "出库失败",
			"err":  err.Error(),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "出库成功",
		"id":   id,
	})
	if RowsAffected > 0 {
		common.Add_log(fmt.Sprintf("出库成品药，成品ID：%d，数量：%f，备注：%s,影响行数：%d", id, quantity, notes, RowsAffected), r)
	}
}

// 成品药_出入库日志列表 81
func Warehouse_finisheddrug_log_list(w http.ResponseWriter, r *http.Request) {
	api_id := 81
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}

	limit, err := strconv.Atoi(r.FormValue("limit"))
	if err != nil || limit <= 0 {
		limit = 5
	}
	page, err := strconv.Atoi(r.FormValue("page"))
	if err != nil || page <= 0 {
		page = 1
	}

	type Log struct {
		ID          int     `db:"id"`
		Pid         int     `db:"pid"`
		Pre_id      int     `db:"pre_id"`
		Ord_id      int     `db:"ord_id"`
		User_id     int     `db:"user_id"`
		Kind        int     `db:"kind"`
		Old_data    float64 `db:"old_data"`
		Change_data float64 `db:"change_data"`
		New_data    float64 `db:"new_data"`
		Notes       string  `db:"notes"`
		Create_time string  `db:"create_time"`
	}

	logs := []Log{}
	params := []interface{}{}
	attSql := ""

	kind := r.FormValue("kind")
	if kind != "" {
		attSql = " and kind = ?"
		params = append(params, kind)
	}

	var count int
	countSql := "SELECT COUNT(id) FROM warehouse_finisheddrug_log WHERE 1" + attSql
	err = database.GetOne(countSql, &count, params...)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "读取数据总数失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(countSql, params...),
		})
		return
	}

	offset := (page - 1) * limit
	params = append(params, limit, offset)
	sql := "SELECT id,pid,ord_id,pre_id,user_id,kind,old_data,change_data,new_data,notes,create_time FROM warehouse_finisheddrug_log WHERE 1" + attSql + " ORDER BY id DESC limit ? OFFSET ?"
	err = database.GetAll(sql, &logs, params...)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "读取数据失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, params...),
		})
		return
	}

	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":  200,
		"msg":   "读取成功",
		"data":  logs,
		"count": count,
		"sql":   common.DebugSql(sql, params...),
	})
}

// 成品药_出入库日志详情 82
func Warehouse_finisheddrug_log_detail(w http.ResponseWriter, r *http.Request) {
	api_id := 82
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	id, err := common.CheckInt(r.FormValue("id"))
	if err != nil || id < 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "日志ID不能为空",
		})
		return
	}
	type Log struct {
		UserID     int     `db:"user_id"`
		UserName   string  `db:"name"`
		Kind       int     `db:"kind"`
		OldData    float64 `db:"old_data"`
		ChangeData float64 `db:"change_data"`
		NewData    float64 `db:"new_data"`
		Notes      string  `db:"notes"`
		CreateTime string  `db:"create_time"`
	}
	sql := "SELECT a.user_id,b.name,a.kind,a.old_data,a.change_data,a.new_data,a.notes,a.create_time FROM warehouse_finisheddrug_log as a left join rbac_user as b on a.user_id = b.id WHERE a.id = ?"
	var log Log
	err = database.GetOne(sql, &log, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "读取数据失败",
			// "sql":  common.DebugSql(sql),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "读取成功",
		"data": log,
	})
}

// 生成处方一码通 83
func Prescription_qrcode(w http.ResponseWriter, r *http.Request) {
	api_id := 83
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	id, err := common.CheckInt(r.FormValue("id"))
	if err != nil || id < 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "用户ID不能为空",
		})
		return
	}
	// 查询用户状态
	sql := "SELECT status FROM patient_profile WHERE id = (select pat_pro_id from prescription where id = ? limit 1) limit 1"
	var status int
	err = database.GetOne(sql, &status, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "读取用户状态失败",
			// "sql":  common.DebugSql(sql, id),
		})
		return
	}
	if status != 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "用户状态已禁用，无法生成处方一码通",
		})
		return
	}
	// 生成一码通
	codestr := "prescription|" + strconv.Itoa(id)
	codestr = common.Str2Base64(codestr)
	base64data, err := common.QRCode_Create(codestr, w)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "生成一码通失败",
			"err":  err.Error(),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "生成一码通成功",
		"url":  base64data,
	})
}

// 生成用户一码通 83
func Patient_profile_qrcode(w http.ResponseWriter, r *http.Request) {
	api_id := 83
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	id, err := common.CheckInt(r.FormValue("id"))
	if err != nil || id < 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "用户ID不能为空",
		})
		return
	}
	// 查询用户状态
	sql := "SELECT status FROM patient_profile WHERE id = ? limit 1"
	var status int
	err = database.GetOne(sql, &status, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "读取用户状态失败",
			// "sql":  common.DebugSql(sql),
		})
		return
	}
	if status != 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "该用户一码通已被禁用",
		})
		return
	}
	// 生成一码通
	codestr := "patient_profile|" + strconv.Itoa(id)
	codestr = common.Str2Base64(codestr)
	base64data, err := common.QRCode_Create(codestr, w)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "生成一码通失败",
			"err":  err.Error(),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "生成一码通成功",
		"url":  base64data,
	})
}

// 设置用户一码通状态 84
func Patient_profile_qrcode_status_change(w http.ResponseWriter, r *http.Request) {
	api_id := 84
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	id, err := common.CheckInt(r.FormValue("id"))
	if err != nil || id < 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "用户ID不能为空",
		})
		return
	}
	status, err := common.CheckInt(r.FormValue("status"))
	if err != nil || status < 0 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "接收到的状态值异常",
		})
		return
	}
	// 设置用户状态
	sql := "UPDATE patient_profile SET status = ? WHERE id = ?"
	result, err := database.Query(sql, status, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "设置用户状态失败",
			"err":  err.Error(),
			// "sql":  common.DebugSql(sql, status, id),
		})
		return
	}
	RowsAffected, _ := result.RowsAffected()
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":         200,
		"msg":          "设置用户状态成功",
		"RowsAffected": RowsAffected,
	})
	if RowsAffected > 0 {
		common.Add_log(fmt.Sprintf("设置用户%d的一码通状态为%d", id, status), r)
	}
}

// 患者档案-相关患者 86
func Patient_profile_links(w http.ResponseWriter, r *http.Request) {
	api_id := 86
	session, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	id, err := common.CheckInt(r.FormValue("id"))
	if err != nil || id < 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "患者ID不能为空",
		})
		return
	}

	type PatientBasicInfo struct {
		Pid   int    `db:"pid"`
		Name  string `db:"name"`
		Phone string `db:"phone"`
	}

	var patient_profile PatientBasicInfo
	sql := "SELECT pid, name, phone FROM patient_profile WHERE id = ? LIMIT 1"
	err = database.GetRow(sql, &patient_profile, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "读取患者信息失败",
			"err":  err.Error(),
		})
		return
	}

	sql = "SELECT id FROM patient_account WHERE last_login_ip = (SELECT last_login_ip FROM patient_account WHERE id = ? and last_login_ip <> '0')"
	var similar_ids []int
	err = database.GetAll(sql, &similar_ids, patient_profile.Pid)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "读取相似用户ID失败",
			"err":  err.Error(),
		})
		return
	}

	//将 similar_ids 转换为适合 SQL 的格式
	var similar_ids_str string
	if len(similar_ids) > 0 {
		// 将 []int 转换为 "1,2,3" 的字符串
		ids := make([]string, len(similar_ids))
		for i, id := range similar_ids {
			ids[i] = fmt.Sprintf("%d", id)
		}
		similar_ids_str = strings.Join(ids, ",")
	} else {
		// 如果没有相似 ID，那就只列出他自己帐号内的数据
		similar_ids_str = fmt.Sprintf("%d", patient_profile.Pid)
	}

	type Patient_profile_links struct {
		ID     int    `db:"id"`
		Name   string `db:"name"`
		Phone  string `db:"phone"`
		Status int    `db:"status"`
	}

	sql = fmt.Sprintf(`
        SELECT id, name, phone, status
        FROM patient_profile
        WHERE pid IN (%s) OR name = ? OR phone = ?
        ORDER BY id DESC`, similar_ids_str)

	var Patient_profile_links_data []Patient_profile_links
	err = database.GetAll(sql, &Patient_profile_links_data, patient_profile.Name, patient_profile.Phone)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "关联用户查询失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, patient_profile.Name, patient_profile.Phone),
		})
		return
	}
	// 根据权限处理手机号显示方式 - GetAll
	if session.Values["role_ids"] != "1" {
		for i := range Patient_profile_links_data {
			phone := Patient_profile_links_data[i].Phone
			if len(phone) == 11 || len(phone) == 13 {
				Patient_profile_links_data[i].Phone = phone[:5] + "****" + phone[len(phone)-2:]
			} else {
				Patient_profile_links_data[i].Phone = Patient_profile_links_data[i].Phone + "手机号码格式错误"
				// fmt.Println(Patient_profile_links_data[i].Phone)
			}
		}
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "ok",
		"data": Patient_profile_links_data,
		"sql":  common.DebugSql(sql, patient_profile.Name, patient_profile.Phone),
	})
}

// 系统日志列表，带分页
func System_logs_list(w http.ResponseWriter, r *http.Request) {
	api_id := 99
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	var params []interface{}
	attSql := ""
	page, err := common.CheckInt(r.FormValue("page"))
	if err != nil || page < 1 {
		page = 1
	}
	limit, err := common.CheckInt(r.FormValue("limit"))
	if err != nil || limit < 1 {
		limit = 10
	}
	log_type := r.FormValue("type")
	if log_type != "" {
		attSql += " AND type = ?"
		params = append(params, log_type)
	}
	key := r.FormValue("key")
	if key != "" {
		attSql += " AND contents LIKE '%" + key + "%'"
	}
	// 总数
	var count int
	sql := "SELECT COUNT(id) FROM system_logs WHERE is_display = 1 " + attSql
	err = database.GetOne(sql, &count, params...)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "读取数据总数失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, params...),
		})
		return
	}

	// 查询系统日志列表
	type SystemLogsList struct {
		ID          int    `db:"id"`          // 日志ID
		User_id     int    `db:"user_id"`     // 用户ID
		Contents    string `db:"contents"`    // 内容
		Ip          string `db:"ip"`          // IP地址
		Type        int    `db:"type"`        // 日志类型
		Create_time string `db:"create_time"` // 创建时间
	}

	sql = "SELECT id, ifnull(user_id,0)user_id,type, contents, ip, create_time FROM system_logs where is_display = 1 " + attSql + " ORDER BY id DESC LIMIT ?, ?"
	params = append(params, (page-1)*limit, limit)
	var system_logs_list []SystemLogsList
	err = database.GetAll(sql, &system_logs_list, params...)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败",
			"err":  err.Error(),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":  200,
		"msg":   "ok",
		"count": count,
		"data":  system_logs_list,
	})
}

// 系统日志详情
func System_logs_detail(w http.ResponseWriter, r *http.Request) {
	api_id := 100
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	id, err := common.CheckInt(r.FormValue("id"))
	if err != nil || id < 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{"code": 500,
			"msg": "日志ID不能为空",
		})
		return
	}
	// 查询系统日志详情
	type SystemLogsDetail struct {
		ID          int    `db:"id"`          // 日志ID
		Type        int    `db:"type"`        // 日志类型
		User_id     int    `db:"user_id"`     // 用户ID
		Contents    string `db:"contents"`    // 内容
		Useragent   string `db:"useragent"`   //AU
		Ip          string `db:"ip"`          // IP地址
		Create_time string `db:"create_time"` // 创建时间
	}
	var system_logs_detail SystemLogsDetail
	sql := "SELECT id, user_id, contents,type, ip, create_time,useragent FROM system_logs WHERE id = ? and is_display = 1"
	err = database.GetRow(sql, &system_logs_detail, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败",
			"err":  err.Error(),
			// "sql":  common.DebugSql(sql, id),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "ok",
		"data": system_logs_detail,
	})
}

// 删除日志
func System_logs_del(w http.ResponseWriter, r *http.Request) {
	api_id := 101
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	id, err := common.CheckInt(r.FormValue("id"))
	if err != nil || id < 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "日志ID不能为空",
		})
		return
	}
	// 删除日志(为日后好查询问题，当前暂时不删除日志，只是标记删除)
	sql := "update system_logs set is_display = 0 where id = ?"
	result, err := database.Query(sql, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "删除日志失败",
			"err":  err.Error(),
			// "sql":  common.DebugSql(sql, id),
		})
		return
	}
	RowsAffected, _ := result.RowsAffected()
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":         200,
		"msg":          "删除日志成功",
		"RowsAffected": RowsAffected,
	})
}

// 通用文件上传接口
func Upload_normal_file(w http.ResponseWriter, r *http.Request) {
	api_id := 102 // 使用与图片上传相同的权限ID
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}

	if err := r.ParseMultipartForm(20 << 20); err != nil { // 20MB 限制
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": http.StatusBadRequest,
			"msg":  "解析表单数据失败: " + err.Error(),
		})
		return
	}

	// 获取上传的文件列表
	files := r.MultipartForm.File["file"]
	if len(files) == 0 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": http.StatusBadRequest,
			"msg":  "上传文件不能为空",
		})
		return
	}

	var uploadFolder = "/uploads/normal_files/"
	category := r.FormValue("category")
	match, _ := regexp.MatchString(`^[a-zA-Z\-\_]+$`, category)
	if category != "" && match {
		uploadFolder += category + "/"
	}

	// 确保目录存在
	fullPath := config.Dist_catagory + uploadFolder
	if err := os.MkdirAll(fullPath, 0755); err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": http.StatusInternalServerError,
			"msg":  "创建目录失败: " + err.Error(),
		})
		return
	}

	// 用来存储上传成功的文件信息
	var uploadedFiles []map[string]interface{}

	// 允许的文件类型
	allowedExtensions := map[string]bool{
		".mp3":  true,
		".wav":  true,
		".xls":  true,
		".xlsx": true,
		".zip":  true,
		".rar":  true,
	}

	for _, fileHeader := range files {
		fileData, err := fileHeader.Open()
		if err != nil {
			common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
				"code": http.StatusBadRequest,
				"msg":  "无法打开文件: " + err.Error(),
			})
			return
		}
		defer fileData.Close()

		// 获取文件扩展名
		fileExt := strings.ToLower(filepath.Ext(fileHeader.Filename))

		// 检查文件类型是否允许
		if !allowedExtensions[fileExt] {
			common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
				"code": http.StatusBadRequest,
				"msg":  "不支持的文件类型: " + fileExt,
			})
			return
		}

		// 读取文件数据
		data, err := io.ReadAll(fileData)
		if err != nil {
			common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
				"code": http.StatusBadRequest,
				"msg":  "无法读取文件: " + err.Error(),
			})
			return
		}

		// 生成文件名
		fileExtWithoutDot := strings.TrimPrefix(fileExt, ".")
		fileName := common.Generate_filename(fileExtWithoutDot)
		filePath := config.Dist_catagory + uploadFolder + fileName

		// 保存文件
		err = os.WriteFile(filePath, data, 0666)
		if err != nil {
			common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
				"code": http.StatusInternalServerError,
				"msg":  "无法保存文件: " + err.Error(),
			})
			return
		}

		fileSize := fileHeader.Size
		uploadedFiles = append(uploadedFiles, map[string]interface{}{
			"filename": fileName,
			"filepath": filePath[2:],
			"url":      filePath[2:],
			"size":     fileSize,
		})
	}

	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "上传成功",
		"data": uploadedFiles,
	})
}

// 通用文件删除接口
func Normal_file_del(w http.ResponseWriter, r *http.Request) {
	api_id := 103 // 使用与图片删除相同的权限ID
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}

	filename := r.FormValue("filename")
	if filename == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": http.StatusBadRequest,
			"msg":  "文件名不能为空",
		})
		return
	}

	var uploadFolder = "/uploads/normal_files/"
	category := r.FormValue("category")
	match, _ := regexp.MatchString(`^[a-zA-Z\-\_]+$`, category)
	if category != "" && match {
		uploadFolder += category + "/"
	}
	filepath := config.Dist_catagory + uploadFolder + filename

	// 检查文件是否存在
	if _, err := os.Stat(filepath); err == nil { // 文件存在
		// 尝试删除文件
		if err := os.Remove(filepath); err != nil {
			common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
				"code":     http.StatusInternalServerError,
				"msg":      "删除文件失败",
				"err":      err.Error(),
				"filepath": filepath[2:],
			})
			return
		}
	} else if os.IsNotExist(err) {
		// 文件不存在
		common.JSONResponse(w, http.StatusNotFound, map[string]interface{}{
			"code":     http.StatusNotFound,
			"msg":      "文件不存在",
			"filepath": filepath[2:],
		})
		return
	} else {
		// 其他错误
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code":     http.StatusInternalServerError,
			"msg":      "检查文件状态失败: ",
			"err":      err.Error(),
			"filepath": filepath[2:],
		})
		return
	}

	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":     200,
		"msg":      "删除成功",
		"filepath": filepath[2:],
	})
}

// 通用图片上传接口
func Upload_normal_pic(w http.ResponseWriter, r *http.Request) {
	api_id := 102
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}

	if err := r.ParseMultipartForm(10 << 20); err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": http.StatusBadRequest,
			"msg":  "解析表单数据失败: " + err.Error(),
		})
		return
	}

	// 获取上传的文件列表
	files := r.MultipartForm.File["file"]
	if len(files) == 0 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": http.StatusBadRequest,
			"msg":  "上传文件不能为空",
		})
		return
	}
	var uploadFolder = "/uploads/normal_pics/"
	category := r.FormValue("category")
	match, _ := regexp.MatchString(`^[a-zA-Z\-\_]+$`, category)
	if category != "" && match {
		uploadFolder += category + "/"
	}
	// fmt.Println(uploadFolder)
	// 用来存储上传成功的文件信息
	var uploadedFiles []map[string]interface{}

	for _, fileHeader := range files {
		fileData, err := fileHeader.Open()
		if err != nil {
			common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
				"code": http.StatusBadRequest,
				"msg":  "无法打开文件: " + err.Error(),
			})
			return
		}
		defer fileData.Close()

		isImage, data, err := common.Check_Is_Image(fileHeader)
		if err != nil || !isImage {
			common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
				"code": http.StatusBadRequest,
				"msg":  "无效的图片文件: " + err.Error(),
			})
			return
		}

		fileExt := strings.ToLower(filepath.Ext(fileHeader.Filename))
		var filePath string
		var fileName string

		if fileExt == ".svg" {
			// SVG 文件直接保存
			fileName = common.Generate_filename("svg")
			filePath = config.Dist_catagory + uploadFolder + fileName
			err := os.WriteFile(filePath, data, 0666)
			if err != nil {
				common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
					"code": http.StatusInternalServerError,
					"msg":  "无法保存 SVG 文件: " + err.Error(),
				})
				return
			}
		} else {
			// 解码图片
			imgConfig, _, err := image.DecodeConfig(bytes.NewReader(data))
			if err != nil {
				common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
					"code": http.StatusBadRequest,
					"msg":  "无法读取图片尺寸: " + err.Error(),
				})
				return
			}

			const maxWidth = 1000
			var img image.Image
			if imgConfig.Width > maxWidth {
				ratio := float64(maxWidth) / float64(imgConfig.Width)
				newHeight := uint(float64(imgConfig.Height) * ratio)
				img, _, err = image.Decode(bytes.NewReader(data))
				if err != nil {
					common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
						"code": http.StatusBadRequest,
						"msg":  "解码图片失败: " + err.Error(),
					})
					return
				}
				img = resize.Resize(maxWidth, newHeight, img, resize.Lanczos3)
			} else {
				img, _, err = image.Decode(bytes.NewReader(data))
				if err != nil {
					common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
						"code": http.StatusBadRequest,
						"msg":  "解码图片失败: " + err.Error(),
					})
					return
				}
			}

			// 根据文件类型保存图片
			fileExt = strings.TrimPrefix(fileExt, ".")
			if fileExt == "" {
				fileExt = "jpg"
			}
			fileName = common.Generate_filename(fileExt)
			filePath = config.Dist_catagory + uploadFolder + fileName

			outFile, err := os.OpenFile(filePath, os.O_WRONLY|os.O_CREATE, 0666)
			if err != nil {
				common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
					"code": http.StatusInternalServerError,
					"msg":  "无法保存文件: " + err.Error(),
				})
				return
			}
			defer outFile.Close()

			// 保存为 PNG 或 JPEG
			switch fileExt {
			case ".png":
				if err := png.Encode(outFile, img); err != nil {
					common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
						"code": http.StatusInternalServerError,
						"msg":  "保存 PNG 图片失败: " + err.Error(),
					})
					return
				}
			default:
				if err := jpeg.Encode(outFile, img, nil); err != nil {
					common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
						"code": http.StatusInternalServerError,
						"msg":  "保存 JPEG 图片失败: " + err.Error(),
					})
					return
				}
			}
		}

		fileSize := fileHeader.Size // 获取文件的大小，单位为字节
		uploadedFiles = append(uploadedFiles, map[string]interface{}{
			"filename": fileName,
			"filepath": filePath[2:],
			"size":     fileSize, // 将文件大小添加到返回数据中
		})
	}

	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "上传成功",
		"data": uploadedFiles,
	})
}

// 通用删除图片接口
func Normal_pic_del(w http.ResponseWriter, r *http.Request) {
	api_id := 103
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}

	filename := r.FormValue("filename")
	if filename == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": http.StatusBadRequest,
			"msg":  "文件名不能为空",
		})
		return
	}

	var uploadFolder = "/uploads/normal_pics/"
	category := r.FormValue("category")
	match, _ := regexp.MatchString(`^[a-zA-Z\-\_]+$`, category)
	if category != "" && match {
		uploadFolder += category + "/"
	}
	filepath := config.Dist_catagory + uploadFolder + filename
	// 检查文件是否存在
	if _, err := os.Stat(filepath); err == nil { // 文件存在
		// 尝试删除文件
		if err := os.Remove(filepath); err != nil {
			common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
				"code":     http.StatusInternalServerError,
				"msg":      "删除文件失败",
				"err":      err.Error(),
				"filepath": filepath[2:],
			})
			return
		}
	} else if os.IsNotExist(err) {
		// 文件不存在
		common.JSONResponse(w, http.StatusNotFound, map[string]interface{}{
			"code":     http.StatusNotFound,
			"msg":      "文件不存在",
			"filepath": filepath[2:],
		})
		return
	} else {
		// 其他错误
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code":     http.StatusInternalServerError,
			"msg":      "检查文件状态失败: ",
			"err":      err.Error(),
			"filepath": filepath[2:],
		})
		return
	}

	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":     200,
		"msg":      "删除成功",
		"filepath": filepath[2:],
	})
}

// 读取文件夹下所有的图片
func Normal_pic_list(w http.ResponseWriter, r *http.Request) {
	api_id := 104
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	var uploadFolder = "/uploads/normal_pics/"
	category := r.FormValue("category")
	match, _ := regexp.MatchString(`^[a-zA-Z\-\_]+$`, category)
	if category != "" && match {
		uploadFolder += category + "/"
	}
	folderpath := config.Dist_catagory + uploadFolder
	files, err := os.ReadDir(folderpath)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": http.StatusInternalServerError,
			"msg":  "读取文件夹失败: " + err.Error(),
		})
		return
	}
	fileList := make([]map[string]interface{}, 0) // 存储所有文件的列表
	for _, file := range files {
		if !file.IsDir() {
			fileInfo, err := file.Info() // 调用 Info() 方法获取 os.FileInfo
			if err != nil {
				continue
			}
			fileList = append(fileList, map[string]interface{}{
				"filename": file.Name(),
				"size":     fileInfo.Size(), // 文件大小
			})
		}
	}
	// 返回所有文件列表
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "ok",
		"data": fileList,
	})
}

// 修改NAS配置
func Nas_config_edit(w http.ResponseWriter, r *http.Request) {
	api_id := 163
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	// {uploadUrl: 'https://www.abc.com/upload', username: 'user2029192', password: '12fjf29j38mk98'}
	uploadUrl := r.FormValue("uploadUrl")
	username := r.FormValue("username")
	password := r.FormValue("password")
	if uploadUrl == "" || username == "" || password == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": http.StatusBadRequest,
			"msg":  "参数不能为空",
		})
		return
	}
	nas_config := fmt.Sprintf("%s,%s,%s", uploadUrl, username, password)
	nas_config = strings.TrimSpace(nas_config)
	if len(strings.Split(nas_config, ",")) < 2 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": http.StatusBadRequest,
			"msg":  "配置参数不完整",
		})
		return
	}
	sql := "UPDATE configs SET value_data = ? WHERE attribute = 'nas_config'"
	result, err := database.Query(sql, nas_config)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据更新失败",
			"err":  err.Error(),
		})
		return
	}
	RowsAffected, _ := result.RowsAffected()
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "数据更新成功",
		// "sql":          common.DebugSql(sql, nas_config),
		"RowsAffected": RowsAffected,
	})
}

// 读取自动录制开关
func Sys_config_rtc_auto_record_detail(w http.ResponseWriter, r *http.Request) {
	api_id := config.NormalPerm
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	sql := "select value_data from configs where attribute = 'rtc_auto_record'"
	var value_data string
	err := database.GetOne(sql, &value_data)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败",
			"err":  err.Error(),
		})
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "ok",
		"data": value_data,
	})
}

// 读取系统配置
func Sys_config_detail(w http.ResponseWriter, r *http.Request) {
	api_id := 177
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	type Sys_config struct {
		ID         int    `json:"id"`
		Attribute  string `json:"attribute"`
		Value_data string `json:"value_data"`
	}
	var sys_config []Sys_config
	sql := "select id,attribute,value_data from configs"
	err := database.GetAll(sql, &sys_config)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败",
			"err":  err.Error(),
			// "sql":  common.DebugSql(sql),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "ok",
		"data": sys_config,
		// "sql":  common.DebugSql(sql),
	})
}

// 保存音视频自动录制状态设置
func Sys_config_rtc_setting_edit(w http.ResponseWriter, r *http.Request) {
	api_id := 177 //与读取系统配置同权限
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	isOpen := r.FormValue("isOpen")
	if isOpen == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": http.StatusBadRequest,
			"msg":  "自动录制参数不能为空",
		})
		return
	}
	isUploadCloud := r.FormValue("isUploadCloud")
	if isUploadCloud == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": http.StatusBadRequest,
			"msg":  "上传云端参数不能为空",
		})
		return
	}
	sql1 := "UPDATE configs SET value_data = ? WHERE attribute = 'rtc_auto_record'"
	sql2 := "UPDATE configs SET value_data = ? WHERE attribute = 'rtc_upload_cloud'"

	// 使用事务确保两个配置项的一致性更新
	sqls := []database.SQLExec{
		{Query: sql1, Args: []interface{}{isOpen}},
		{Query: sql2, Args: []interface{}{isUploadCloud}},
	}

	rowsAffected, err := database.ExecuteTransaction_with_affectedRows(sqls)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "更新配置失败",
			"err":  err.Error(),
		})
		return
	}

	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":         200,
		"msg":          "配置更新成功",
		"rowsAffected": rowsAffected,
	})
}

// 患者端小程序_幻灯片列表 104
func Weichat_patient_slides_list(w http.ResponseWriter, r *http.Request) {
	api_id := 104
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	var pics string
	sql := "SELECT value_data FROM configs WHERE attribute = 'weichat_patient_slides'"
	err := database.GetOne(sql, &pics)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败",
			"err":  err.Error(),
			// "sql":  common.DebugSql(sql),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "ok",
		"data": pics,
	})
}

// 患者端小程序_幻灯片修改 105
func Weichat_patient_slides_edit(w http.ResponseWriter, r *http.Request) {
	api_id := 105
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	pics := r.FormValue("pics")
	if pics == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": http.StatusBadRequest,
			"msg":  "幻灯片不能为空",
		})
		return
	}
	pics = strings.TrimSpace(pics)
	if len(strings.Split(pics, ",")) < 2 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": http.StatusBadRequest,
			"msg":  "幻灯片至少需要2张图片",
		})
		return
	}
	sql := "UPDATE configs SET value_data = ? WHERE attribute = 'weichat_patient_slides'"
	result, err := database.Query(sql, pics)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据更新失败",
			"err":  err.Error(),
			// "sql":  common.DebugSql(sql, pics),
		})
		return
	}
	RowsAffected, _ := result.RowsAffected()
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":         200,
		"msg":          "数据更新成功",
		"RowsAffected": RowsAffected,
	})
}

// 权限表rbac_perm，直接将资源写在了该权限表内，以方便权限直接控制。
// 所以该表也可以理解为携带了资源信息的权限表。
func Res_current(w http.ResponseWriter, r *http.Request) {
	api_id := config.NormalPerm
	session, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	user_id := session.Values["id"].(int)
	perm_ids := session.Values["perm_ids"].(string)
	// 校验 perm_ids 格式
	match, _ := regexp.MatchString(`^[\d,]+$`, perm_ids)
	if !match {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "权限ID格式错误",
		})
		return
	}

	type Res struct {
		ID        int    `json:"id"`
		PID       int    `json:"pid"`
		Name      string `json:"name"`
		URL       string `json:"url"`
		Api_path  string `json:"api_path"`
		Menu_icon string `json:"menu_icon"`
		Built_in  int    `json:"built_in"`
		Sort      int    `json:"sort"`
		Type      int    `json:"type"`
		Status    int    `json:"status"`
	}

	sql := ""
	if user_id == 1 { // 超级管理员显示所有
		sql = "SELECT id, pid, name, url, api_path, icon AS menu_icon, built_in, sort, type, status FROM rbac_perm ORDER BY sort DESC"
	} else {
		// 查询用户权限及其父菜单
		sql = `
        SELECT id, pid, name, url, api_path, icon AS menu_icon, built_in, sort, type, status
        FROM rbac_perm
        WHERE id IN (
            SELECT id FROM (
                SELECT id FROM rbac_perm WHERE id IN (%s)
                UNION
                SELECT DISTINCT pid FROM rbac_perm WHERE id IN (%s) and type = 0
            ) AS temp_ids
        )
        ORDER BY sort DESC`
		sql = fmt.Sprintf(sql, perm_ids, perm_ids)
	}

	var res []Res
	err := database.GetAll(sql, &res)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "读取菜单失败",
		})
		return
	}

	// 清除所有Redis缓存
	err = common.Redis_Clear_All(w, r)
	if err != nil {
		fmt.Println("清除Redis缓存失败:", err)
	}
	disable_dep_data := r.FormValue("disable_dep_data") //是否无需输出部门数据
	// fmt.Println("disable_dep_data:", disable_dep_data)
	var departments any
	if disable_dep_data == "" {
		type Department struct {
			Id      int    `db:"id"`
			Pid     int    `db:"pid"`
			Name    string `db:"name"`
			Details string `db:"details"`
			Sort    int    `db:"sort"`
		}
		sql = "select id,pid,name,sort,details from department order by sort desc"
		var depts []Department
		_ = database.GetAll(sql, &depts)
		departments = depts
	} else {
		departments = nil // nil，JSON中序列化为null
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":        200,
		"msg":         "ok",
		"data":        res,
		"departments": departments,
	})
}

// 刷新当前用户的最新权限
func Res_current_refresh(w http.ResponseWriter, r *http.Request) {
	api_id := config.NormalPerm
	session, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	user_id := session.Values["id"].(int)
	if user_id == 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]any{
			"code": 400,
			"msg":  "超级管理员拥有所有权限",
		})
		return
	}
	fmt.Println("旧权限:", session.Values["perm_ids"].(string))
	sql := "select perm_id from rbac_role_perm where role_id in(select role_id from rbac_user_roles where user_id = ?)"

	// 赋值权限给SESSION
	var perm_id_list []int
	err := database.GetAll(sql, &perm_id_list, user_id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]any{
			"code": 500,
			"msg":  "读取权限失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, user_id),
		})
		return
	}
	permIdsStr := make([]string, len(perm_id_list))
	for i, id := range perm_id_list {
		permIdsStr[i] = fmt.Sprint(id)
	}
	perm_ids := strings.Join(permIdsStr, ",")
	session.Values["perm_ids"] = perm_ids
	session.Save(r, w)
	fmt.Println("新权限:", session.Values["perm_ids"].(string))

	// 将新权限返回给前端
	type Res struct {
		ID        int    `json:"id"`
		PID       int    `json:"pid"`
		Name      string `json:"name"`
		URL       string `json:"url"`
		Api_path  string `json:"api_path"`
		Menu_icon string `json:"menu_icon"`
		Built_in  int    `json:"built_in"`
		Sort      int    `json:"sort"`
		Type      int    `json:"type"`
		Status    int    `json:"status"`
	}
	sql = `
	SELECT id, pid, name, url, api_path, icon AS menu_icon, built_in, sort, type, status
	FROM rbac_perm
	WHERE id IN (
		SELECT id FROM (
			SELECT id FROM rbac_perm WHERE id IN (%s)
			UNION
			SELECT DISTINCT pid FROM rbac_perm WHERE id IN (%s) and type = 0
		) AS temp_ids
	)
	ORDER BY sort DESC`
	sql = fmt.Sprintf(sql, perm_ids, perm_ids)
	var res []Res
	err = database.GetAll(sql, &res)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "读取菜单失败",
		})
		return
	}

	// 清除所有Redis缓存
	err = common.Redis_Clear_All(w, r)
	if err != nil {
		fmt.Println("清除Redis缓存失败:", err)
	}

	// 当前用户如果是销售数据管理员时，获取最新销售管理者的权限 - 其它角色无效
	sql = "select dep_ids from salemaster_department where user_id = ?"
	var dep_ids string
	_ = database.GetOne(sql, &dep_ids, user_id)
	if session.Values["role_ids"] == "2" {
		session.Values["dep_ids"] = dep_ids
		session.Save(r, w)
		fmt.Println("销售数据管理员新的部门IDS:", session.Values["dep_ids"].(string))
	}
	// 获取所有部门信息数据，并将数据放在user.Departments中
	type Department struct {
		Id      int    `db:"id"`
		Pid     int    `db:"pid"`
		Name    string `db:"name"`
		Details string `db:"details"`
		Sort    int    `db:"sort"`
	}
	sql = "select id,pid,name,sort,details from department order by sort desc"
	var departments []Department
	_ = database.GetAll(sql, &departments)

	common.JSONResponse(w, http.StatusOK, map[string]any{
		"code":        200,
		"msg":         "ok",
		"data":        res,
		"dep_ids":     dep_ids,
		"departments": departments,
		// "sql":  common.DebugSql(sql, perm_ids),
	})
}
