# 增删改查代码生成指南

## 1. 任务概述
- **目标**：基于现有模板生成完整的增删改查功能模块（前端+后端）
- **技术栈**：Golang后端 + LayUI前端
- **项目路径**：E:\works\go\

## 2. 核心规范（最重要）

### 2.1 命名规范
| 组件 | 格式 | 示例（模块名：article） |
|------|------|------------------------|
| 后端函数 | {Module}_list/add/edit/del/detail | Article_list() |
| API路由 | /admin/{module}/list/add/edit/del/detail | /admin/article/list |
| 前端页面 | {module}_list/add/edit/detail.html | article_list.html |

### 2.2 数据交互规范
- **成功响应**：`code: 200`
- **失败响应**：`code: 500`
- **API字段**：首字母大写（如：Title, Create_time）
- **表单字段**：必须与后端API返回字段完全匹配（包括大小写）

## 3. 实现步骤

### 3.1 分析数据表结构
- 表名通常与模块名相同
- 识别必填字段、搜索字段
- 特殊字段处理（如：sort, pid）

### 3.2 后端实现
1. 在`internal/app/admin/admin_extra_2.go`中实现5个核心函数
2. 在`internal/routes/admin.go`中添加路由配置
3. 权限控制模板：
   ```go
   func {module}_list(w http.ResponseWriter, r *http.Request) {
       api_id := config.NormalPerm
       _, isLogin := common.Check_Perm(w, r, api_id)
       if !isLogin {
           return
       }
       // 业务逻辑
   }
   ```
4. 获取用户ID：
   ```go
   func {module}_add(w http.ResponseWriter, r *http.Request) {
       api_id := config.NormalPerm
       session, isLogin := common.Check_Perm(w, r, api_id)
       if !isLogin {
           return
       }
       user_id := session.Values["id"] // 注意是session.Values["id"]，不是session.Values["user_id"]
       // 业务逻辑
   }
   ```

### 3.3 前端实现
1. 创建4个页面：list/add/edit/detail.html
2. 页面头部必须保持一致（仅修改title）
3. 表单验证使用`lay-verify="required"`
4. AJAX请求模板：
   ```javascript
   $.ajax({
       url: serverUrl + '/admin/{module}/{action}',
       type: 'POST',
       data: data.field,
       success: function(res) {
           if (res.code == 200) {
               // 成功处理
           } else {
               layer.msg(res.msg, { icon: 2, time: 1000 });
           }
       },
       error: function(err) {
           layer.msg(err.responseJSON ? err.responseJSON.msg : '请求失败', { icon: 2, time: 1000 });
       }
   });
   ```

## 4. 参考文件
- **后端参考**：`internal/app/admin/normal_template.go`中的Article_category_*相关函数
- **前端参考**：`interface/admin/article_category_*.html`

## 5. 特殊功能实现

### 5.1 搜索功能
- 默认参数名：`key`
- 实现方式：WHERE field1 LIKE ? OR field2 LIKE ?

### 5.2 父级关联（Pid字段）
- 自动从对应list接口获取父级数据
- 在add/edit页面中渲染为下拉选择框

### 5.3 排序字段（sort）
- add页面不显示sort输入框
- edit页面允许修改sort值
- 后端自动处理新增记录的sort值（最大值+10）

## 6. 常见问题与解决方案

### 6.1 详情页面显示样式
详情页面必须严格按照参考模板的样式实现。特别注意：
- **不要使用input标签显示只读内容**，而应使用div标签
- 正确示例：
  ```html
  <div class="detail-text" id="description">内容</div>
  ```
- 错误示例：
  ```html
  <input type="text" name="Title" readonly class="layui-input">
  ```
- 思维链：
  1. 查看参考模板article_category_detail.html
  2. 发现详情页使用div.detail-text显示内容
  3. 在新模块的详情页中也使用相同的结构
  4. 这样可以保持UI风格一致，并提供更好的阅读体验

### 6.2 编辑页面ID处理
编辑页面提交数据时必须正确处理ID参数：
- 从URL获取的ID必须合并到提交的表单数据中
- 正确示例：
  ```javascript
  form.on('submit(formDemo)', function(data) {
      // 确保ID被包含在提交数据中
      var id = request.get('id');
      if (id) {
          data.field.Id = id; // 确保字段名与后端期望的一致（首字母大写）
      }
      
      $.ajax({
          url: serverUrl + '/admin/module/edit',
          type: 'POST',
          data: data.field,
          // 其他代码
      });
      return false;
  });
  ```
- 思维链：
  1. 编辑页面通过URL参数获取ID
  2. 表单提交时需要将此ID包含在数据中
  3. 如果不包含ID，后端会返回"ID不能为空"错误
  4. 确保ID字段名与后端期望的格式一致（通常是首字母大写的"Id"）

## 7. 前端开发补充规范

### 7.1 jQuery使用规范
- jQuery的`$`符号必须在`layui.use()`内部使用
- 正确示例：
  ```javascript
  layui.use(['form', 'layer'], function(){
      var $ = layui.$;  // 在这里声明$
      // 这里可以安全使用$
  });
  ```

### 7.2 URL参数获取
- 必须使用系统封装的request对象获取URL参数
- 正确示例：`var id = request.get('id');`
- 错误示例：~~`var id = getQueryString('id');`~~

### 7.3 表单字段命名规则
- 所有表单字段的name属性必须与后端API返回的字段名完全匹配（包括大小写）
- 示例：如果API返回字段为"Title"，则表单字段应写为：
  ```html
  <input type="text" name="Title" class="layui-input">
  ```

## 8. 数据表结构信息
执行代码生成任务前，请提供以下信息：

### 8.1 数据库表名
- 默认使用模块名作为表名（除非特别说明）
- 例如：模块名为book，则表名为book

### 8.2 字段信息
提供类似如下格式的字段信息：
```
字段名      数据类型    字段说明
id          int         ID
title       varchar     标题
create_time timestamp   创建时间
```

### 8.3 搜索配置
指定：
1. 搜索关键词参数名（默认为'key'）
2. 需要进行模糊匹配的字段（单个或多个）

## 9. 前端模板严格遵循规范

### 9.1 头部模板规范
必须严格按照参考模板格式实现，各前端页面的头部标签，仅允许修改title内容：
```html
<head>
    <meta charset="utf-8">
    <title>{修改这里的标题}</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
</head>
```

### 9.2 列表页父ID筛选规范
- 列表页可能需要支持父ID（Pid）筛选
- 该功能默认不启用，除非明确指定："列表页有父ID传值，其参数名为pid"
- 实现方式：
  ```javascript
  // 前端
  var pid = request.get('pid');
  
  // 后端
  pid := r.FormValue("pid")
  if pid != "" {
     attSql = " and pid = ? "
     params = append(params, pid)
  }
  ``` 