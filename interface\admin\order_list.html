<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 订单管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
    <script src="/dist/js/xm-select.js"></script>
    <style>
        .pay_review_status {
            width: 60px;
        }

        .get_pre_status_by_orderid {
            /* text-align: left; */
            line-height: 25px;
            font-size:12px;
            /* cursor: pointer; */
        }

        .layui-btn+.layui-btn {
            margin: 0;
        }

        xm-select {
            border: 0 !important;
        }

        xm-select>.xm-body {
            min-width: 200px !important;
            padding: 10px !important;
        }

        xm-select>.xm-body .xm-toolbar {
            padding: 0;
        }
    </style>
</head>

<body>
    <div class="layui-layout layui-layout-admin">
        <!-- 头部 -->
        <div class="layui-header">
            <div class="layui-logo">幸年堂</div>
            <ul class="layui-nav layui-layout-right">
                <li class="layui-nav-item" style="background-color:transparent!important;padding-right:10px">
                    <a href="javascript:;" style="display:flex;align-items:center;color:#fff"><img
                            src="/dist/images/user.png" class="layui-nav-img"><span class="user_name"></span></a>
                    <dl class="layui-nav-child pop_menu">
                        <dd><i class="iconfont">&#xe62f;</i><a href="change_password.html#7">用户资料</a></dd>
                        <dd><i class="iconfont">&#xe601;</i><a href="javascript:void(0)" class="refresh_cache">刷新缓存</a>
                        </dd>
                        <dd><i class="iconfont">&#xe634;</i><a href="login_out.html">退出登录</a></dd>
                    </dl>
                </li>
            </ul>
        </div>
        <!-- 导航 -->
        <ul class="layui-nav layui-nav-tree layui-nav-side" id="menuContainer" lay-bar="disabled" lay-accordion>
            <div style="margin: 50px 0;color: #bbb;text-align: center;">菜单加载中...</div>
        </ul>
        <!-- 主体内容 -->
        <div class="layui-body">
            <div class="body_child">
                <div class="layui-panel">
                    <div class="layui-card-header">
                        <div class="layui-row" style="padding-top:10px;">
                            <div class="layui-col-md11 layui-col-sm10">订单管理</div>
                            <div class="layui-col-md1 layui-col-sm2 perm_check_btn" res_id="42">
                                <button type="submit" class="layui-btn layui-btn-primary layui-border-red create_btn"
                                    lay-submit=""><i class="layui-icon">&#xe654;</i> 新增</button>
                            </div>
                        </div>
                    </div>
                    <div class="layui-padding-3" style="padding-top: 0 !important;min-height: 800px;">



                        <div id="top_data_search">
                            <div class="layui-form" style="margin: 20px 0 0 0;">
                                <div class="layui-row">
                                    <!-- 科室+医生组合 -->
                                    <div class="layui-col-md4">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label">科室</label>
                                            <div class="layui-input-block" style="display: flex; gap: 10px;">
                                                <select name="doc_dep_id" id="doc_dep_id" lay-filter="doc_dep_id"
                                                    style="width: 48%;"></select>
                                                <select name="doc_id" id="doc_id" style="width: 48%;"></select>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 售前/售后部门+人员组合 -->
                                    <div class="layui-col-md4">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label">销售</label>
                                            <div class="layui-input-block" style="display: flex;gap: 5px;">
                                                <div id="asst_dep_id" style="width:66%"></div>
                                                <select name="asst_id" id="asst_id"></select>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 患者搜索 -->
                                    <div class="layui-col-md3">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label">患者</label>
                                            <div class="layui-input-block">
                                                <input type="text" name="wd" id="dropdown_input"
                                                    lay-filter="searchFilter" placeholder="请输入电话或姓名" autocomplete="off"
                                                    class="layui-input" lay-affix="clear">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 状态筛选 -->
                                <div class="layui-row">
                                    <div class="layui-col-md4">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label">状态</label>
                                            <div class="layui-input-block">
                                                <select name="Order_Status" id="Order_Status"
                                                    lay-filter="Order_Status"></select>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- 筛选按钮 -->
                                    <div class="layui-col-md4">
                                        <div class="layui-form-item">
                                            <div class="layui-input-block">
                                                <button class="layui-btn" lay-submit lay-filter="search"
                                                    style="min-width: 100px;">筛选</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>








                        <table id="mytable" lay-filter="mytable" class="layui-table" lay-skin="row"></table>
                    </div>
                </div>
                <br>
                <br>
            </div>
        </div>
    </div>


    <script type="text/html" id="TPL-bar">
            <button class="layui-btn layui-btn-xs perm_check_btn" lay-event="edit" res_id="43">编辑</button>
            <button class="layui-btn layui-btn-xs perm_check_btn" lay-event="show" res_id="56">详情</button>
            <button class="layui-btn layui-btn-xs perm_check_btn pay_review_status {{# if(d.Pay_review_status > 0){ }}layui-btn-disabled{{# } }}" lay-event="verify_cr_pic" res_id="158" {{# if(d.Pay_review_status > 0){ }}disabled{{# } }}>
                {{# if(d.Pay_review_status > 0){ }}✓订金{{# } else { }}审核订金{{# } }}
            </button>
            <button class="layui-btn layui-btn-xs perm_check_btn pay_review_status {{# if(d.Pay_review_status > 2){ }}layui-btn-disabled{{# } }}" lay-event="verify_pay_pic" res_id="159" {{# if(d.Pay_review_status > 2){ }}disabled{{# } }}>
                {{# if(d.Pay_review_status > 2){ }}✓尾款{{# } else { }}审核尾款{{# } }}
            </button>
    </script>

    <script>
        layui.use(['element', 'layer', 'util'], function () {
            var element = layui.element;
            var form = layui.form;
            var layer = layui.layer;
            var util = layui.util;
            var laydate = layui.laydate;
            var pat_pro_id = "";
            var table = layui.table;
            var dropdown = layui.dropdown;
            var $ = layui.$;
            // 表头自定义元素工具事件 --- 2.8.8+
            table.on('colTool(mytable)', function (obj) {
                var event = obj.event;
                if (event === 'dep_tips') {
                    layer.alert('如果员工后续换岗了，订单隶属部门信息不会随之改变，还是当时下单时的部门。', {
                        title: '部门说明',
                        shadeClose: true,
                        btn: ['知道了']
                    });
                }
            });
            // 渲染日期
            laydate.render({
                elem: '#ID-laydate-demo'
            });
            // 页面顶部插入缓存的用户数据
            let local_userinfo = localStorage.getItem('local_userinfo');
            let user_name = "";
            if (local_userinfo) {
                local_userinfo = JSON.parse(local_userinfo);
                user_name = local_userinfo.Name + " ( " + local_userinfo.Roles + " )";
            } else {
                user_name = "未登录";
            }
            $('.user_name').text(user_name);

            render_menu($);
            $('.refresh_cache').click(function () {
                refresh_cache($);
            });
            //引入TABLE模块
            layer.load(2);
            table.render({
                elem: '#mytable'
                , even: true
                , url: serverUrl + "/admin/order/list"
                , method: 'post'
                , response: {
                    statusName: 'code'
                    , statusCode: 200
                    , msgName: 'msg'
                    , countName: 'count'
                    , dataName: 'data'
                }
                , cols: [[
                    {
                        field: 'ID', title: '订单ID', align: 'center', width: 100, templet: function (d) {
                            return "D" + d.ID;
                        }
                    }
                    , {// 原型图中有：金额状态、物流状态，为何不用订单状态概括呢？
                        field: 'Status', title: '订单状态', align: 'center', width: 100, templet: function (d) {
                            const item = order_status_list[d.Status];
                            return `
                                <div style="display: flex; flex-direction: column; align-items: center;">
                                    <i class="iconfont" style="color: ${item.color}; font-size: 20px;">${item.icon}</i>
                                    <span>订单${item.name}</span>
                                </div>
                            `;
                        }
                    }
                    , {
                        title: '病历IDS', align: 'center', width: 90, templet: function (d) {
                            return "<a href='prescription_list.html?record_ids=" + d.Record_ids + "#/admin/order_list.html' class='btn_arg_pm' data-id='" + d.Record_ids + "'><b>" + d.Record_ids.split(',').length + "</b>病历</a>";
                        }
                    }
                    , {
                        title: '处方进度', align: 'center', minWidth: 210, templet: function (d) {
                            // 先返回一个带loading的占位内容
                            return "<div class='get_pre_status_by_orderid' data-record-ids='" + d.Record_ids + "'><i class='layui-icon layui-icon-loading-1 layui-anim layui-anim-rotate layui-anim-loop'></i></div>";
                        }
                    }
                    , {
                        field: 'Pat_pro_id', title: '患者', align: 'center', width: 90, templet: function (d) {
                            return '<div class="pat_pro_id" data-id="' + d.Pat_pro_id + '">-</div>';
                        }
                    }
                    , {
                        field: 'Doc_id', title: '医生', align: 'center', width: 90, templet: function (d) {
                            return '<div class="user_need_ajax" data-id="' + d.Doc_id + '">-</div>';
                        }
                    }
                    , {//<i class="layui-icon layui-icon-tips layui-font-14" lay-event="dep_tips" title="提示" style="margin-left: 3px;"></i>
                        field: 'Department_id', title: '医生部门', align: 'center', width: 103, templet: function (d) {
                            return '<div class="department_need_ajax" data-id="' + d.Doc_dep_id + '">-</div>';
                        }
                    }
                    , {
                        field: 'Asst_id', title: '医助', align: 'center', width: 90, templet: function (d) {
                            return '<div class="user_need_ajax" data-id="' + d.Asst_id + '">-</div>';
                        }
                    }
                    , {//<i class="layui-icon layui-icon-tips layui-font-14" lay-event="dep_tips" title="提示" style="margin-left: 3px;"></i>
                        field: 'Department_id', title: '售前部门', align: 'center', width: 103, templet: function (d) {
                            return '<div class="department_need_ajax" data-id="' + d.Asst_dep_id + '">-</div>';
                        }
                    }
                    , {
                        title: '订金/总价', align: 'center', width: 100, templet: function (d) {
                            let pre_pay = d.Pre_pay;
                            let total_money = d.Total_money;
                            let percent = pre_pay / total_money * 100;
                            return `
                                <div>`+ pre_pay + ` / ` + total_money + `</div>
                                <div class="layui-progress"><div class="layui-progress-bar" lay-percent="` + percent + `%"></div></div>
                            `;
                        }
                    }
                    , {
                        field: 'Pay_review_status', title: '金额状态', align: 'center', width: 100, templet: function (d) {
                            return Pay_review_status[d.Pay_review_status];
                        }
                    }
                    , {
                        title: '物流状态', align: 'center', width: 100, templet: function (d) {
                            return d.ID;
                        }
                    }
                    , {
                        field: 'Create_time', title: '创建时间', align: 'center', templet: function (d) {
                            return d.Create_time.replace('T', ' ').replace('Z', '');
                        }
                    }
                    , { title: '操作', align: 'center', width: 200, toolbar: '#TPL-bar' }
                ]]
                , done: function (res, curr, count) {
                    // 解决进度条组件在TABLE中不显示的BUG
                    element.render("progress");
                    //检查菜单权限
                    render_button($);

                    // 从本地存储获取部门/科室数据
                    let department_data = localStorage.getItem('local_departments') ? JSON.parse(localStorage.getItem('local_departments')) : [];
                    if (department_data.length > 0) {
                        $('.department_need_ajax').each(function () {
                            let id = $(this).data('id');
                            let department = id2department(id, department_data, 0);
                            $(this).text(department);
                        });
                    } else {
                        layer.msg('本地部门数据不存在，请先刷新缓存', { icon: 2, time: 2000 });
                    }

                    //高效-医助、医生等职能部分用户遍历并ID合并
                    let user_arr = [];
                    $('.user_need_ajax').each(function () {
                        let id = $(this).data('id');
                        user_arr.push(id);
                    });
                    user_arr = [...new Set(user_arr)];//去重
                    layer.load(2);
                    $.ajax({
                        url: '/admin/user/list_low',
                        data: {
                            user_id_list: user_arr.join(','),
                        },
                        type: 'post',
                        success: function (res) {
                            layer.closeAll('loading');
                            if (res.code === 200) {
                                let data = res.data;
                                for (let i = 0; i < data.length; i++) {
                                    let id = data[i].ID;
                                    let name = data[i].Name;
                                    $('.user_need_ajax[data-id="' + id + '"]').text(name);
                                }
                            } else {
                                layer.msg(res.msg, { icon: 2, time: 1000 });
                            }
                        }, error: function (res) {
                            layer.closeAll('loading');
                            layer.msg(res.responseJSON.msg, { icon: 2, time: 1000 });
                        }
                    });
                    //高效-患者ID2姓名
                    let pat_pro_id_arr = [];
                    $('.pat_pro_id').each(function () {
                        let id = $(this).data('id');
                        pat_pro_id_arr.push(id);
                    });
                    pat_pro_id_arr = [...new Set(pat_pro_id_arr)];//去重
                    layer.load(2);
                    $.ajax({
                        url: '/admin/patient_profile/patient_profile_ids2name',
                        data: {
                            ids: pat_pro_id_arr.join(','),
                        },
                        type: 'post',
                        success: function (res) {
                            layer.closeAll('loading');
                            if (res.code === 200) {
                                let data = res.data;
                                for (let i = 0; i < data.length; i++) {
                                    let id = data[i].ID;
                                    let name = data[i].Name;
                                    $('.pat_pro_id[data-id="' + id + '"]').html(`<a href="/admin/patient_profile_show.html?id=${id}#/admin/patient_profile_list.html" target=_blank>${name}</a>`);
                                }
                            }
                        }, error: function (res) {
                            layer.closeAll('loading');
                            layer.msg(res.responseJSON.msg, { icon: 2, time: 1000 });
                        }
                    });

                    // 获取所有订单的开方进度
                    $('.get_pre_status_by_orderid').each(function () {
                        let $link = $(this);
                        let record_ids = $link.data('record-ids');

                        $.ajax({
                            url: '/admin/order/get_pre_status_by_record_ids',
                            data: { record_ids: record_ids },
                            type: 'post',
                            success: function (res) {
                                if (res.code === 200) {
                                    let result = '';
                                    // 检查是否是单个病历
                                    let recordIdArray = typeof record_ids === 'string' ? record_ids.split(',') : [record_ids];

                                    if (!Array.isArray(recordIdArray)) {
                                        layer.msg('记录ID数据格式错误', { icon: 2, time: 1000 });
                                        return;
                                    }

                                    if (!res.data || res.data.length === 0) {
                                        result = '未开方';
                                    } else {
                                        // 如果只有一个病历，直接显示其状态，不加序号
                                        if (recordIdArray.length === 1) {
                                            result = res.data[0] ? '<a class="btn_arg_pm" href="prescription_show.html?id=' + res.data[0].ID + '#/admin/order_list.html">C' + res.data[0].ID + '</a><br>' + Pre_Status[res.data[0].Status] : '未开方';
                                        } else {
                                            // 多个病历时，添加序号
                                            let statusMap = {};
                                            res.data.forEach(item => {
                                                statusMap[item.Record_id] = '<a class="btn_arg_pm" href="prescription_show.html?id=' + item.ID + '#/admin/order_list.html">C' + item.ID + '</a><br>' + Pre_Status[item.Status];
                                            });
                                            let statuses = recordIdArray.map((ID) => statusMap[ID] || '未开方');

                                            result = statuses.join('<br>');
                                        }
                                    }

                                    $link.html(result);
                                }
                            },
                            error: function () {
                                $link.html('<span style="color: red;">加载失败</span>');
                            }
                        });
                    });
                },
                error: function (res) {
                    $('.layui-table-main').html('<div class="layui-none">' + res.responseJSON.msg + '</div>');
                },
                page: true,
                limit: 12,
            })
            //监听工具条
            table.on('tool(mytable)', function (obj) {
                var data = obj.data;
                if (obj.event === 'edit') {
                    window.location.href = 'order_edit.html?id=' + data.ID + '#/admin/order_list.html';
                } else if (obj.event === 'show') {
                    window.location.href = 'order_show.html?id=' + data.ID + '#/admin/order_list.html';
                } else if (obj.event === 'verify_cr_pic') {
                    if (confirm('您确定要审核该订金为已支付吗？')) {
                        $.ajax({
                            url: '/admin/order/pay_review_pre',
                            data: {
                                id: data.ID,
                            },
                            type: 'post',
                            success: function (res) {
                                if (res.code === 200) {
                                    layer.msg(res.msg, { icon: 1, time: 1000 });
                                    table.reload('mytable');
                                } else {
                                    layer.msg(res.msg, { icon: 2, time: 1000 });
                                }
                            }, error: function (res) {
                                layer.msg(res.responseJSON.msg, { icon: 2, time: 1000 });
                            }
                        })
                    }
                } else if (obj.event === 'verify_pay_pic') {
                    if (confirm('您确定要审核该尾款为已支付吗？')) {
                        $.ajax({
                            url: '/admin/order/pay_review_final',
                            data: {
                                id: data.ID,
                            },
                            type: 'post',
                            success: function (res) {
                                if (res.code === 200) {
                                    layer.msg(res.msg, { icon: 1, time: 1000 });
                                    table.reload('mytable');
                                } else {
                                    layer.msg(res.msg, { icon: 2, time: 1000 });
                                }
                            }, error: function (res) {
                                layer.msg(res.responseJSON.msg, { icon: 2, time: 1000 });
                            }
                        })
                    }
                }
            });
            form.on('input-affix(searchFilter)', function (data) {
                pat_pro_id = "";
            });
            form.on('submit(search)', function (data) {
                // 获取xm-select中选中的部门ID
                if (keshiAsstSelect) {
                    let selectedDepts = keshiAsstSelect.getValue();
                    console.log('筛选按钮 - 选中的部门:', selectedDepts);
                    // 如果有选中部门，添加到表单数据
                    if (selectedDepts.length > 0) {
                        // 提取ID值并合并为逗号分隔的字符串
                        let deptIds = selectedDepts.map(item => item.Id || item.id).join(',');
                        data.field.asst_dep_id = deptIds;
                        console.log('筛选按钮 - 添加到表单的部门IDs:', deptIds);
                    }
                } else {
                    console.warn('警告: keshiAsstSelect 未初始化');
                }

                // 处理患者搜索
                data.field.pat_pro_id = pat_pro_id;

                // 将Order_Status映射到status参数
                if (data.field.Order_Status !== '') {
                    data.field.status = data.field.Order_Status;
                }

                // 删除可能存在的多余字段
                if (data.field.select !== undefined) {
                    console.log('删除多余的select字段:', data.field.select);
                    delete data.field.select;
                }

                // 打印最终的筛选条件
                console.log('最终筛选条件:', data.field);

                // 重载表格
                table.reload('mytable', {
                    where: data.field,
                    page: {
                        curr: 1 //重新从第 1 页开始
                    }
                });
                return false;
            });
            //新增用户
            $('.create_btn').on('click', function () {
                window.location.href = 'order_add.html';
            });

            //渲染状态下拉框
            let order_status_html = '<option value="">全部状态</option>';
            for (let i = 0; i < Order_Status.length; i++) {
                order_status_html += '<option value="' + i + '">' + Order_Status[i] + '</option>';
            }
            $('#Order_Status').html(order_status_html);
            form.render('select');

            //从本地存储渲染部门下拉框
            let data = localStorage.getItem('local_departments') ? JSON.parse(localStorage.getItem('local_departments')) : [];
            if (data.length > 0) {
                // 渲染科室树形结构
                let treeData = format_to_treedata_department(data, global_default_store_id);
                treeData = renderDropdownItems(treeData);
                dropdown.render({
                    elem: '#Department',
                    id: 'DropdownID',
                    data: [],
                    content: '<div class="dropdown-menu">' + treeData + '</div>',
                    ready: function (elemPanel, elem) {
                        elemPanel.on('click', '.dropdown-item-leaf', function () {
                            $('#Department').val($(this).text());
                            dropdown.close('DropdownID');
                        });
                    }
                });
            } else {
                layer.msg('本地部门数据不存在，请先刷新缓存', { icon: 2, time: 2000 });
            }
            // 获取当前用户角色信息
            let currentUserIsSales = false;
            let currentUserIsDoctor = false;
            let currentUserDeptId = 0;
            let currentUserId = 0;
            let currentUserRoleIds = "";

            if (local_userinfo) {
                // 获取当前用户的角色ID、部门ID和用户ID
                currentUserRoleIds = local_userinfo.Role_ids || "";
                currentUserDeptId = local_userinfo.Department_id || 0;
                currentUserId = local_userinfo.Id || 0;

                // 判断是否为售前(3)或售后(9)用户
                if (currentUserRoleIds.split(',').includes(global_asst_role_id) || currentUserRoleIds.split(',').includes(global_after_asst_role_id)) {
                    currentUserIsSales = true;
                }

                // 判断是否为医生(4)用户
                if (currentUserRoleIds.split(',').includes(global_doctor_role_id)) {
                    currentUserIsDoctor = true;
                }
            }

            // 渲染科室下拉表，连带渲染科室下医生下拉
            layer.load(2);
            $.ajax({
                url: '/admin/department/list',
                data: {
                    name: '医疗部门',
                    store_id: global_default_store_id,
                },
                type: 'post',
                success: function (res) {
                    layer.closeAll('loading');
                    if (res.code === 200) {
                        let data = res.data;
                        let html = '<option value="">选择科室</option>';
                        for (let i = 0; i < data.length; i++) {
                            html += '<option value="' + data[i].id + '">' + data[i].name + '</option>';
                        }
                        $('#doc_dep_id').html(html);

                        // 如果当前用户是医生，设置默认选中和只读状态
                        if (currentUserIsDoctor && currentUserDeptId > 0) {
                            // 设置科室下拉框为只读
                            $('#doc_dep_id').val(currentUserDeptId);
                            $('#doc_dep_id').attr('disabled', 'disabled');
                            // 使用内联样式而不是添加类，避免影响布局
                            $('#doc_dep_id').css('background-color', '#f2f2f2');
                        }

                        form.render('select');

                        // 加载医生列表的函数
                        function loadDoctorsByDepartment(keshi_id) {
                            if (keshi_id) {
                                layer.load(2);
                                $.ajax({
                                    url: '/admin/user/list_low',
                                    data: {
                                        role_id: 4,
                                        department_id: keshi_id,
                                    },
                                    type: 'post',
                                    success: function (res) {
                                        layer.closeAll('loading');
                                        if (res.code === 200) {
                                            let data = res.data;
                                            //let html = '<option value="">选择医生</option>';
                                            let html = '';
                                            if (data && data.length > 0) {
                                                for (let i = 0; i < data.length; i++) {
                                                    html += '<option value="' + data[i].ID + '">' + data[i].Name + '</option>';
                                                }

                                                // 如果当前用户是医生，并且在当前科室中，设置默认选中和只读状态
                                                if (currentUserIsDoctor && currentUserDeptId == keshi_id && currentUserId > 0) {
                                                    $('#doc_id').html(html);
                                                    $('#doc_id').val(currentUserId);
                                                    $('#doc_id').attr('disabled', 'disabled');
                                                    // 使用内联样式而不是添加类，避免影响布局
                                                    $('#doc_id').css('background-color', '#f2f2f2');
                                                    form.render('select');
                                                } else {
                                                    $('#doc_id').html(html);
                                                    form.render('select');
                                                }
                                            } else {
                                                layer.msg('该科室暂无医生', { icon: 2, time: 1000 });
                                                html = '<option value="">该科室暂无医生</option>';
                                                $('#doc_id').html(html);
                                                form.render('select');
                                            }
                                        } else {
                                            layer.msg(res.msg, { icon: 2, time: 1000 });
                                        }
                                    }, error: function (res) {
                                        layer.closeAll('loading');
                                        layer.msg(res.responseJSON.msg, { icon: 2, time: 1000 });
                                    }
                                });
                            } else {
                                $('#doc_id').html('<option value="">选择医生</option>');
                                form.render('select');
                            }
                        }

                        // 如果当前用户是医生，自动触发科室选择事件加载对应的医生列表
                        if (currentUserIsDoctor && currentUserDeptId > 0) {
                            loadDoctorsByDepartment(currentUserDeptId);
                        }

                        // 监听科室选择事件
                        form.on('select(doc_dep_id)', function (data) {
                            let keshi_id = data.value;
                            loadDoctorsByDepartment(keshi_id);
                        });
                    } else {
                        layer.msg(res.msg, { icon: 2, time: 1000 });
                    }
                }, error: function (res) {
                    layer.closeAll('loading');
                    layer.msg(res.responseJSON.msg, { icon: 2, time: 1000 });
                }
            });

            // 声明全局变量，用于存储xm-select实例
            var keshiAsstSelect;

            //渲染售前/售后部门下拉框，连带渲染部门下人员下拉框
            layer.load(2);
            // 创建一个映射，用于快速查找部门
            let local_departments = localStorage.getItem('local_departments') ? JSON.parse(localStorage.getItem('local_departments')) : [];
            let deptMap = {};
            local_departments.forEach(dept => {
                deptMap[dept.Id] = dept;
            });

            // 获取所有售前和售后部门的子部门
            let allSaleDepts = [];
            global_sale_department_ids.forEach(deptId => {
                local_departments.forEach(dept => {
                    if (dept.Pid === deptId) {
                        allSaleDepts.push(dept);
                    }
                });
            });

            // 再找出这些子部门的所有子部门
            let allChildDepts = [...allSaleDepts];
            allSaleDepts.forEach(dept => {
                function findChildren(parentId) {
                    local_departments.forEach(d => {
                        if (d.Pid === parentId && !allChildDepts.some(cd => cd.Id === d.Id)) {
                            allChildDepts.push(d);
                            findChildren(d.Id);
                        }
                    });
                }
                findChildren(dept.Id);
            });

            // 过滤部门数据 - 只保留用户有权限的部门及其父级部门
            let filteredDepartments = [];
            // 默认只展开售前、售后2个大层级节点
            let expandedKeys = global_sale_department_ids;
            // 判断当前用户是否为销售数据管理员
            let isDataMaster = local_userinfo && local_userinfo.Department_id === global_sale_data_master;
            let userDepIds = [];
            if (isDataMaster) {
                userDepIds = local_userinfo.Dep_ids.split(',').map(id => parseInt(id.trim(), 10)).filter(id => !isNaN(id));
            }
            if (isDataMaster && userDepIds.length > 0) {
                // 数据管理员时，由于所负载节点比较少，所以默认展开所有
                expandedKeys = true;
                // 收集用户可访问的部门及其所有父级部门的ID
                let relevantDeptIds = new Set();
                // 递归查找父级部门
                function collectParentDepts(deptId) {
                    const dept = deptMap[deptId];
                    if (dept) {
                        relevantDeptIds.add(dept.Id);
                        if (dept.Pid && dept.Pid !== 0) {
                            collectParentDepts(dept.Pid);
                        }
                    }
                }
                // 处理每个用户可访问的部门
                userDepIds.forEach(deptId => {
                    collectParentDepts(deptId);
                });
                // 只保留用户有权限的部门及其父级部门
                filteredDepartments = allChildDepts.filter(dept =>
                    relevantDeptIds.has(dept.Id)
                );
            } else {
                // 非数据管理员，显示所有部门及其父级部门
                filteredDepartments = allChildDepts;
            }

            // 按照排序值降序排列
            filteredDepartments.sort((a, b) => b.Sort - a.Sort);

            // 将部门数据转换成树形结构
            let departmentTree = convertToTree(filteredDepartments);

            // 初始化xm-select，使用全局变量
            // 判断当前用户是否为销售岗位
            if (currentUserIsSales && currentUserDeptId > 0) {
                console.log('当前用户是销售岗位，部门ID:', currentUserDeptId);
                // 使用id2department函数获取部门名称
                let deptName = id2department(currentUserDeptId, local_departments, 0);
                // 渲染为禁用状态的xm-select
                keshiAsstSelect = xmSelect.render({
                    el: '#asst_dep_id',
                    data: [{
                        Name: deptName,
                        Id: currentUserDeptId
                    }],
                    initValue: [currentUserDeptId],//就显示当前的销售岗位
                    model: {
                        label: {
                            type: 'text'
                        }
                    },
                    prop: {
                        name: 'Name',
                        value: 'Id'
                    },
                    disabled: true,
                });
                // 渲染人员下拉框 - 直接使用当前用户信息
                let html = '<option value="' + currentUserId + '">' + local_userinfo.Name + '</option>';
                $('#asst_id').html(html);
                $('#asst_id').attr('disabled', 'disabled');
                $('#asst_id').css('background-color', '#f2f2f2');
                form.render('select');

            } else {
                console.log('当前用户是非销售岗位，部门ID:', currentUserDeptId);
                // 非销售岗位用户，正常初始化xm-select
                keshiAsstSelect = xmSelect.render({
                    el: '#asst_dep_id',
                    theme: {
                        color: '#1677ff',
                    },
                    height: 'auto',
                    data: departmentTree,
                    model: {
                        label: {
                            type: 'text',
                        }
                    },
                    clickClose: false, // 多选模式下点击不关闭
                    filterable: true,
                    // 添加默认提示文字
                    tips: '请选择部门',
                    prop: {
                        name: 'Name',
                        value: 'Id',
                        children: 'children'
                    },
                    // 设置表单提交时的名称为空，避免自动提交
                    name: '',
                    toolbar: {
                        show: true,
                        list: ['ALL', 'CLEAR', {
                            icon: 'layui-icon layui-icon-chart',
                            name: '图表',
                            method: function (data) {
                                let selectedDepts = keshiAsstSelect.getValue();
                                if (selectedDepts.length === 0) {
                                    layer.msg("您还没选择部门");
                                    return;
                                }
                                let deptIds = selectedDepts.map(item => item.Id || item.id).join(',');
                                layer.open({
                                    title: false,
                                    closeBtn: 0,
                                    type: 2,
                                    offset: 'b',
                                    anim: 'slideUp',
                                    area: ['100%', '560px'],
                                    shade: 0.1,
                                    shadeClose: true,
                                    id: 'ID-demo-layer-direction-b',
                                    content: 'echarts_orders.html?deptids=' + deptIds
                                });
                            }
                        }]
                    },
                    tree: {
                        show: true,
                        strict: true, // 保持严格模式，确保父子节点联动
                        expandedKeys: expandedKeys,
                        // 启用级联选择，确保父子节点联动
                        cascade: true,
                        // 自动展开父节点
                        autoExpandParent: true
                    },
                    on: function (data) {
                        if (data.change && data.change.length > 0) {
                            // 使用setTimeout确保在DOM更新后获取最新的选中值
                            setTimeout(function () {
                                // 获取当前所有选中的部门ID
                                if (keshiAsstSelect) {
                                    let selectedDepts = keshiAsstSelect.getValue();

                                    // 如果有选中的部门，加载这些部门的用户
                                    if (selectedDepts.length > 0) {
                                        loadDepartmentUsersAll(selectedDepts);
                                    } else {
                                        // 清空用户列表
                                        $('#asst_id').html('<option value="">选择人员</option>');
                                        form.render('select');
                                    }
                                }
                            }, 0);
                        }
                    }
                });
            }

            // 转换扁平数据为树形结构
            function convertToTree(data) {
                let result = [];
                let map = {};

                // 创建所有节点的映射
                data.forEach(function (item) {
                    map[item.Id] = {
                        ...item,
                        children: []
                    };
                });

                // 确保所有必要的父节点都存在
                let addedParentIds = new Set(); // 用于跟踪已添加的父节点ID

                data.forEach(function (item) {
                    if (item.Pid !== 0 && !map[item.Pid] && !addedParentIds.has(item.Pid)) {
                        // 如果父节点不在映射中且尚未添加，尝试从原始数据中找到它
                        const parentDept = local_departments.find(d => d.Id === item.Pid);
                        if (parentDept) {
                            // 添加父节点到映射
                            map[parentDept.Id] = {
                                ...parentDept,
                                children: []
                            };
                            // 将父节点添加到数据数组
                            data.push(parentDept);
                            // 记录已添加的父节点ID
                            addedParentIds.add(parentDept.Id);
                        }
                    }
                });

                // 移除重复的部门
                let uniqueData = [];
                let idSet = new Set();
                data.forEach(function (item) {
                    if (!idSet.has(item.Id)) {
                        uniqueData.push(item);
                        idSet.add(item.Id);
                    }
                });
                data = uniqueData;

                // 构建树结构
                data.forEach(function (item) {
                    let node = map[item.Id];
                    if (item.Pid !== 0 && map[item.Pid]) {
                        // 将当前节点添加到父节点的children中
                        map[item.Pid].children.push(node);
                    } else {
                        // 顶级节点直接添加到结果数组
                        result.push(node);
                    }
                });

                // 按Sort字段排序
                function sortBySort(arr) {
                    arr.sort(function (a, b) {
                        return b.Sort - a.Sort; // 降序排列
                    });
                    arr.forEach(function (item) {
                        if (item.children && item.children.length > 0) {
                            sortBySort(item.children);
                        }
                    });
                    return arr;
                }

                return sortBySort(result);
            }

            // 加载多个部门的用户
            function loadDepartmentUsersAll(selectedDepts) {
                layer.load(2);

                // 获取所有选中部门的ID
                let deptIds = selectedDepts.map(item => item.Id || item.id);

                // 创建查询条件，适应多个部门
                let queryParams = {
                    department_ids: deptIds.join(',')
                };

                // 确定角色类型 - 检查是否包含售后部门
                let hasSaleAfter = false;
                for (let i = 0; i < selectedDepts.length; i++) {
                    let deptId = selectedDepts[i].Id || selectedDepts[i].id;
                    let dept = filteredDepartments.find(d => d.Id == deptId);
                    if (dept && dept.Name.includes('售后')) {
                        hasSaleAfter = true;
                        break;
                    }
                }

                $.ajax({
                    url: '/admin/user/list_low',
                    data: queryParams,
                    type: 'post',
                    success: function (res) {
                        layer.closeAll('loading');
                        if (res.code === 200) {
                            let data = res.data;
                            //let html = '<option value="">选择人员</option>';
                            let html = '';
                            if (data && data.length > 0) {
                                // 按姓名排序
                                data.sort((a, b) => a.Name.localeCompare(b.Name, 'zh'));

                                for (let i = 0; i < data.length; i++) {
                                    html += '<option value="' + data[i].ID + '">' + data[i].Name + '</option>';
                                }

                                $('#asst_id').html(html);
                                form.render('select');
                            } else {
                                html = '<option value="">所选部门下暂无人员</option>';
                                $('#asst_id').html(html);
                                form.render('select');
                            }
                        } else {
                            layer.msg(res.msg, { icon: 2, time: 1000 });
                        }
                    },
                    error: function (res) {
                        layer.closeAll('loading');
                        layer.msg(res.responseJSON.msg, { icon: 2, time: 1000 });
                    }
                });
            }


            //模糊搜索用户的下拉框组件
            var inst = dropdown.render({
                elem: '#dropdown_input',
                trigger: 'keyup click',
                data: [],
                style: 'min-width: 220px; box-shadow: 1px 1px 11px rgb(0 0 0 / 11%);',
                click: function (data) {
                    if (!isEmpty(data)) {
                        this.elem.val(data.name + ' / ' + data.phone);
                        pat_pro_id = data.id;
                    }
                }
            });
            $(inst.config.elem).on('input propertychange', function () {
                var elem = $(this);
                var value = elem.val().trim();
                if (value.length > 0) {
                    $.ajax({
                        url: "/admin/patient_profile/patient_profile_phone2id",
                        type: 'post',
                        dataType: 'json',
                        data: {
                            "wd": value,
                        },
                        success: function (response) {
                            let data = response.data;
                            if (data && data.length > 0) {
                                dropdown.reloadData(inst.config.id, {
                                    data: data,
                                    templet: function (d) {
                                        var exp = new RegExp(value, 'gi');
                                        return (d.name + ' / ' + d.phone).replace(exp, function (str) {
                                            return '<span style="color: red;">' + str + '</span>';
                                        });
                                    }
                                });
                            } else {
                                dropdown.reloadData(inst.config.id, {
                                    data: [],
                                });
                            }
                        },
                        error: function (err) {
                            dropdown.reloadData(inst.config.id, {
                                data: [],
                            });
                        }
                    });
                } else {
                    dropdown.reloadData(inst.config.id, {
                        data: [],
                    });
                }
            });
        });
    </script>
</body>

</html>