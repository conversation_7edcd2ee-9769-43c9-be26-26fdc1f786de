请根据以下要求生成一个前端页面的代码：

1. 创建一个名为“文章分类”的列表页，文件路径为`E:/works/go/interface/admin/article_category_list.html`
2. 页面应模仿`E:/works/go/interface/admin/warehouse_gifts_list.html`的样式和结构。
3. 页面将通过API `/admin/article_category/list` 获取数据。
4. 数据字段包括：
   - `id` (int): 文章分类的唯一标识符。
   - `title` (varchar): 文章分类名称。
   - `description` (varchar): 描述。
   - `sort` (smallint): 排序。
   - `create_time` (timestamp): 建立时间。
5. 确保页面样式与模仿页面一致。
6. 在渲染数据时，注意API返回的值首字母均大写。
7. 考虑实现分页、搜索和排序功能。
8. 提供完整的HTML、CSS和JavaScript代码示例，确保代码结构清晰，易于维护。