<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 角色列表</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
</head>

<body>
    <div class="layui-layout layui-layout-admin">
        <!-- 头部 -->
        <div class="layui-header">
            <div class="layui-logo">幸年堂</div>
            <ul class="layui-nav layui-layout-right">
                <li class="layui-nav-item" style="background-color:transparent!important;padding-right:10px">
                    <a href="javascript:;" style="display:flex;align-items:center;color:#fff"><img
                            src="/dist/images/user.png" class="layui-nav-img"><span class="user_name"></span></a>
                    <dl class="layui-nav-child pop_menu">
                        <dd><i class="iconfont">&#xe62f;</i><a href="change_password.html#7">用户资料</a></dd>
                        <dd><i class="iconfont">&#xe601;</i><a href="javascript:void(0)" class="refresh_cache">刷新缓存</a>
                        </dd>
                        <dd><i class="iconfont">&#xe634;</i><a href="login_out.html">退出登录</a></dd>
                    </dl>
                </li>
            </ul>
        </div>
        <!-- 导航 -->
        <ul class="layui-nav layui-nav-tree layui-nav-side" id="menuContainer" lay-bar="disabled" lay-accordion>
            <div style="margin: 50px 0;color: #bbb;text-align: center;">菜单加载中...</div>
        </ul>
        <!-- 主体内容 -->
        <div class="layui-body">
            <div class="body_child">
                <div class="layui-panel">
                    <div class="layui-card-header">
                        <div class="layui-row" style="padding-top:10px;">
                            <div class="layui-col-md11 layui-col-sm10">部门 (科室) 列表</div>
                            <div class="layui-col-md1 layui-col-sm2">
                                <button type="submit" class=" layui-btn layui-btn-primary layui-border-red create_btn"
                                    lay-submit="">
                                    <i class="layui-icon">&#xe654;</i> 新增
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="layui-padding-3" style="min-height: 800px;">
                        <table id="treeTable" lay-filter="treeTable" class="layui-table" lay-skin="row"></table>
                    </div>
                </div>
                <br>
                <br>
            </div>
        </div>
    </div>

    <script type="text/html" id="TPL-treeTable-demo-tools">
          <a class="layui-btn layui-btn-xs" lay-event="detail">编辑</a>
          <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="delete">删除</a>
    </script>

    <script>
        var checkPic = function (d) {
            let department_icon = new Image();
            let picurl = serverUrl + '/static/uploads/icons/department_' + d.Id + '.svg?v=' + new Date().getTime();
            department_icon.src = picurl;
            department_icon.onload = function () {
                return '<img src="' + picurl + '" style="width:50px;height:50px;">';
            }
            return '-';
        }
        layui.use(['element', 'layer', 'util'], function () {
            var element = layui.element;
            var layer = layui.layer;
            var util = layui.util;
            var treeTable = layui.treeTable;
            var dropdown = layui.dropdown;
            var $ = layui.$;
            // 页面顶部插入缓存的用户数据
            let local_userinfo = localStorage.getItem('local_userinfo');
            let user_name = "";
            if (local_userinfo) {
                local_userinfo = JSON.parse(local_userinfo);
                user_name = local_userinfo.Name + " ( " + local_userinfo.Roles + " )";
            } else {
                user_name = "未登录";
            }
            $('.user_name').text(user_name);
            render_menu($);
            $('.refresh_cache').click(function () {
                refresh_cache($);
            });
            // 全局变量，用于存储已经处理过的图标和员工列表
            var already_icon = [];
            var already_workers = [];

            var append_icon_workers = function ($, data) {
                $('.icon').each(function () {
                    let that = $(this);
                    let id = $(this).data('id');

                    // 检查是否已经处理过该ID的图标
                    if (in_array(id, already_icon)) {
                        return; // 如果已处理过，则跳过
                    }

                    // 将ID添加到已处理列表
                    already_icon.push(id);

                    let picurl = serverUrl + '/static/uploads/icons/department_' + id + '.svg?v=' + new Date().getTime();
                    let img = new Image();
                    img.src = picurl;
                    img.onload = function () {
                        that.html('<img src="' + picurl + '" style="width:36px;height:36px;">');
                    }
                    img.onerror = function () {
                        that.html('-');
                    }
                });

                $('.workers').each(function () {
                    let that = $(this);
                    let id = $(this).data('id');

                    // 检查是否已经处理过该ID的员工列表
                    if (in_array(id, already_workers)) {
                        return; // 如果已处理过，则跳过
                    }

                    // 将ID添加到已处理列表
                    already_workers.push(id);

                    let workers_url = serverUrl + '/admin/user/list_low';
                    $.ajax({
                        url: workers_url,
                        type: 'post',
                        data: {
                            department_id: id,
                        },
                        success: function (res) {
                            if (res.code == 200) {
                                let data = res.data;
                                let names = "";
                                if (data) {
                                    if (id === global_sale_data_master) {//如果是数据管理员，则加上弹出模态框功能
                                        names = data.map(item => `<span class="perm_item_rows_with_modal" data-id="${item.ID}" onclick="openUserModal(${item.ID})">${item.Name}</span>`).join('');
                                    } else {
                                        names = data.map(item => `<span class="perm_item_rows">${item.Name}</span>`).join('');
                                    }
                                    that.html(names);
                                } else {
                                    that.html('-');
                                }
                            }
                        }
                    });
                });
            }
            layer.load(2);
            $.ajax({
                url: '/normal/department_cache_get',
                type: 'post',
                dataType: 'json',
                success: function (res) {
                    layer.closeAll();
                    let data = res.data;
                    let formattedData = format_to_treedata_department(data);
                    let inst = treeTable.render({
                        elem: '#treeTable',
                        tree: {
                            view: {
                                showIcon: false,
                                expandAllDefault: false, // 默认关闭所有节点
                                indent: 26,
                            },
                            customName: {
                                name: "Name",
                            },
                            callback: {
                                onExpand: function () {
                                    append_icon_workers($, res.data);
                                }
                            }
                        },
                        data: formattedData,
                        cols: [[
                            { field: 'Id', title: 'ID', width: 80, align: 'center' },
                            { field: 'Sort', title: '排序', width: 120, align: 'center', sort: true },
                            {
                                field: 'icons', title: '图标<i class="layui-icon layui-icon-tips layui-font-14" lay-event="icon_tips" title="图标用途" style="margin-left: 5px;"></i>', width: 80, align: 'center', templet: function (d) {
                                    return '<div class="icon" data-id="' + d.Id + '">加载中...</div>';
                                }
                            },
                            { field: 'Name', title: '部门名称', width: 220 },
                            {
                                field: 'Workers', title: '员工', minWidth: 600, templet: function (d) {
                                    return '<div class="workers" data-id="' + d.Id + '">加载中...</div>';
                                }
                            },
                            {
                                field: 'Details', title: '简介', minWidth: 200, templet: function (d) {
                                    return d.Details == '0' ? '-' : '<div class="details-ellipsis" style="display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical; overflow: hidden; text-overflow: ellipsis;">' + d.Details + '</div>';
                                }
                            },
                            { fixed: "right", title: "操作", width: 180, align: "center", toolbar: "#TPL-treeTable-demo-tools" }
                        ]],
                        id: 'treeTable',
                        done: function (res, curr, count) {
                            // 为没有子元素的行添加背景色
                            $('tr[data-indent]').each(function () {
                                // 检查是否有子元素（通过查找下一个同级或更高级的元素）
                                let currentIndex = $(this).attr('data-index');
                                let currentIndent = parseInt($(this).attr('data-indent'));
                                let hasChildren = false;

                                // 查找下一个元素，如果下一个元素的缩进更大，说明当前元素有子元素
                                let nextTr = $(this).next('tr[data-indent]');
                                if (nextTr.length > 0) {
                                    let nextIndent = parseInt(nextTr.attr('data-indent'));
                                    if (nextIndent > currentIndent) {
                                        hasChildren = true;
                                    }
                                }

                                // 如果没有子元素，添加背景色
                                if (!hasChildren) {
                                    $(this).css('background-color', '#ededed');
                                }
                            });

                            // 直接遍历原始数据查找需要展开的节点
                            let findAndExpandNode = function (nodeId) {
                                // 遍历所有行查找匹配的ID
                                $('tr[data-index]').each(function () {
                                    let dataIndex = $(this).attr('data-index');
                                    let rowData = treeTable.getNodeDataByIndex('treeTable', dataIndex);

                                    if (rowData && rowData.Id === nodeId) {
                                        console.log('找到节点:', nodeId, '在索引:', dataIndex);
                                        // 展开该节点
                                        treeTable.expandNode('treeTable', {
                                            index: dataIndex,
                                            expandFlag: true,
                                            callbackFlag: append_icon_workers
                                        });
                                        return false; // 找到后停止遍历
                                    }
                                });
                            };

                            // 依次展开每个指定的节点
                            global_department_expandIds.forEach(function (nodeId) {
                                findAndExpandNode(nodeId);
                            });
                        }
                    });
                    treeTable.on('colTool(treeTable)', function (obj) {
                        var event = obj.event;
                        if (event === 'icon_tips') {
                            layer.alert('主要针对医生角色推荐时的排序场景，如小程序首页医生推荐。', {
                                title: '排序应用场景说明',
                                shadeClose: true,
                                btn: ['知道了']
                            });
                        }
                    });
                    treeTable.on('tool(treeTable)', function (obj) {
                        var layEvent = obj.event; // 获得 lay-event 对应的值
                        var trElem = obj.tr;
                        var tableId = obj.config.id;
                        let click_id = obj.data.Id;
                        if (layEvent === "detail") {
                            // 弹出IFRAME窗口模态框
                            layer.open({
                                type: 2,
                                title: '编辑科室',
                                area: ['750px', '100%'],
                                shadeClose: true,
                                content: 'department_edit.html?id=' + click_id
                            });
                        } else if (layEvent === "delete") {
                            layer.confirm("确定要删除该数据吗？", function (index) {
                                layer.close(index);
                                layer.load(2);
                                $.ajax({
                                    url: '/admin/department/del',
                                    type: 'post',
                                    dataType: 'json',
                                    data: { id: click_id },
                                    success: function (res) {
                                        // 刷新部门缓存
                                        $.post(serverUrl + '/normal/department_cache_set', function (res) {
                                            if (res.code == 200) {
                                                layer.closeAll();
                                                obj.del();
                                                treeTable.removeNode(tableId, trElem.attr('data-index'))
                                                layer.msg(res.msg);
                                            } else {
                                                layer.msg('刷新缓存失败', { icon: 2, time: 1000 });
                                            }
                                        });

                                    },
                                    error: function (data) {
                                        layer.closeAll();
                                        layer.msg(data.responseJSON.msg);
                                    }
                                });
                            });
                        }
                    });
                    window.treeTableInstance = inst;
                },

                error: function (data) {
                    layer.closeAll();
                    layer.msg(data.responseJSON.msg);
                }
            });


            $('.create_btn').click(function () {
                layer.open({
                    type: 2,
                    title: '新增部门 / 科室',
                    area: ['600px', '580px'],
                    shadeClose: true,
                    content: '/admin/department_add.html'
                });
            })


        });

        // 打开用户详情模态框
        function openUserModal(userId) {
            layer.open({
                type: 2,
                title: '销售数据管理绑定',
                area: ['500px', '900px'],
                shadeClose: true,
                content: 'salemaster_bind_department.html?id=' + userId
            });
        }
    </script>
</body>

</html>