/* 在线链接服务仅供平台体验和调试使用，平台不承诺服务的稳定性，企业客户需下载字体包自行发布使用并做好备份。 */
@font-face {
    font-family: 'iconfont';
    /* Project id 4798954 */
    src: url('//at.alicdn.com/t/c/font_4798954_4l5ljk90o6d.woff2?t=1747203467911') format('woff2'),
        url('//at.alicdn.com/t/c/font_4798954_4l5ljk90o6d.woff?t=1747203467911') format('woff'),
        url('//at.alicdn.com/t/c/font_4798954_4l5ljk90o6d.ttf?t=1747203467911') format('truetype');
}

.iconfont {
    font-family: 'iconfont' !important;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 20px;
}

.layui-btn-xs {
    min-width: 70px;
}

.layui-disabled {
    background-color: #eee;
}

.layui-nav-tree .iconfont {
    font-size: 14px;
    margin-right: 10px;
}

.layui-nav-tree dd .iconfont {
    margin-right: 5px;
}

/* 子菜单样式 */
.layui-nav .layui-nav-item .layui-nav-child a {
    text-align: left;
    padding-left: 45px;
}

body {
    min-width: 380px;
}

.layui-nav-side {
    top: 0;
    padding-top: 50px !important;
    overflow-y: scroll;
}

.layui-form-select .layui-select-group dd {
    padding-left: 30px;
}

.div_disable {
    opacity: .3;
    background-color: #999;
}

.myheader {
    padding: 10px 10px 0 10px;
    display: flex;
    justify-content: space-between;
    flex-direction: row-reverse;
    align-items: center;
}

.search {
    line-height: 38px;
    display: flex;
}

.search input {
    height: 40px;
    line-height: 40px;
    border: 1px solid #ccc;
    padding: 0 5px;
    min-width: 200px;
}

.back_btn {
    height: 30px;
    line-height: 30px;
    border: 1px solid #20d786;
    background-color: #20d786;
    color: white;
    cursor: pointer;
    border-left: 0;
    width: 100px;
    font-size: 14px;
    margin: 15px auto;
}

.backBtn {
    height: 42px;
    line-height: 40px;
    border: 1px solid #20d786;
    background-color: white;
    color: #20d786;
    cursor: pointer;
    min-width: 70px;
}

.layui-layout-admin .layui-logo {
    font-size: 22px;
    font-weight: 100;
    line-height: 52px;
}

#list {
    min-height: 644px;
    line-height: 45px;
}

#list .nav {
    background: linear-gradient(to right, #1677ff, #20d786);
    color: white;
}

#list .table {
    display: flex;
    justify-content: space-around;
    border-bottom: 1px solid #efefef;
    border-top: 0;
    text-align: center;
}

#list img {
    width: 25px;
    height: 25px;
    vertical-align: middle;
}

#list .table .id {
    width: 80px;
}

#list .table .money {
    width: 70px;
}

#list .table .name {
    width: 155px;
}

#list .table .phone {
    width: 130px;
}

#list .table .status {
    width: 70px;
}

#list .table .time {
    width: 160px;
}

#list .table .do {
    width: 100px;
}

#list .table .do a {
    margin: 0 5px;
}

#list .active {
    background-color: #efefef;
}

#list .hover {
    background-color: #f9f9f9;
}

#page {
    margin: 30px 0;
    display: flex;
    justify-content: center;
}

#page ul li {
    border: 1px solid #e2e2e2;
    min-width: 32px;
    height: 38px;
    line-height: 38px;
    font-size: 14px;
    text-align: center;
    margin: 2px 5px;
    display: inline-block;
    cursor: pointer;
    padding: 0 6px;
    box-shadow: 0 0 20px -8px #bbb;
}

#page .active {
    background: linear-gradient(to right, #1677ff, #20d786);
    color: white;
    border: 1px solid white;
}

.body_child {
    padding: 15px;
}

.menuMobile {
    position: fixed;
    top: 20px;
    right: 20px;
    width: 31px;
    height: 30px;
    line-height: 11px;
}

.icon-bar {
    width: 100%;
    height: 1px;
    line-height: 1px;
    display: inline-block;
    background-color: white;
    margin: 0px;
    padding: 0px;
    clear: both;
    transition: 300ms;
    position: absolute;
    z-index: 9;
}

.icon-bar:nth-last-child(2) {
    top: 10px;
}

.icon-bar:nth-last-child(1) {
    top: 20px;
}

.trans_right {
    transform: rotate(45deg);
    -webkit-transform: rotate(45deg);
    /* Safari 和 Chrome */
    ;
}

.trans_left {
    transform: rotate(-45deg);
    -webkit-transform: rotate(-45deg);
    /* Safari 和 Chrome */
    ;
}

.layui-form-item {
    margin-bottom: 25px;
}

.layui-body {
    background-color: #f5f7f9;
}

#empty {
    display: none;
}

#empty .child {
    text-align: center;
    margin: 70px 0 150px 0;
    width: 100%;
    font-size: 17px;
}

#empty img {
    max-width: 400px;
}

.flex-center {
    display: flex;
    justify-content: center;
    align-items: baseline;
}

.layui-upload-drag {
    /* padding: 116px; */
    margin: 20px 0;
}

.layui-upload-drag .layui-icon {
    color: #1677ff;
}
.warning_tips{
    background-color: rgb(255, 253, 150);
    padding:10px;
    border-radius: 10px;
    font-size:12px;
    margin:10px;
}
.upload_tips {
    padding: 20px 0;
    line-height: 25px;
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
}

.upload_result {
    display: none;
    background-color: #f5f5f5;
    padding: 20px;
}

.input_hasData {
    background-color: greenyellow;
}

.output_success {
    background-color: yellow;
    padding: 5px 10px;
    border: 1px dashed black;
    display: inline-block;
    color: black;
}

.clear_left_phone {
    font-size: 25px;
    cursor: pointer;
    border: 1px solid #ccc;
    padding: 0 10px;
    border-left: 0;
    display: none;
}

.api_normal {
    color: black;
    display: inline-block;
    padding: 5px 10px;
    margin-top: 5px;
}

.smalltext {
    font-size: 12px;
}

.userpoints {
    width: 120px;
}

#store_inputter_menu {
    display: none;
}

.secmenu {
    background: linear-gradient(to right, #1677ff, #20d786);
    color: white;
    height: 30px;
    line-height: 30px;
}

.layui-nav-tree .layui-nav-item .secmenu {
    height: 30px;
    line-height: 30px;
}

.layui-nav-tree .layui-nav-item .secmenu:hover {
    color: white;
}

.mysort {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.mysort i {
    width: 20px;
    height: 20px;
    line-height: 20px;
    padding: 0;
}

.perm_item_rows {
    margin: 3px;
    background-color: #e6f4ff;
    border-radius: 5px;
    color: #1677ff;
    padding: 2px 5px;
}

.perm_item_rows_with_modal {
    margin: 3px;
    background-color: #ffdb77;
    border-radius: 5px;
    color: #fff;
    padding: 2px 5px;
    cursor: pointer;
}

.perm_item_rows_with_modal:hover {
    background-color: #ffe6a0;
}

.layui-table-cell {
    height: auto;
    overflow: visible;
    text-overflow: inherit;
    white-space: normal;
    padding: 10px;
}

.layui-layer-dialog {
    min-width: 250px;
}

.red_star {
    color: red;
    margin-right: 5px;
}

.form_bottom_button {
    display: flex;
    margin: 0 auto;
    justify-content: space-evenly;
    width: 70%;
}

.dropdown-menu {
    min-width: 200px;
    max-height: 350px;
    overflow-y: auto;
    border: 1px solid #ccc;
    border-radius: 5px;
    background-color: #fff;
    box-shadow: 0 0 10px #ccc;
}

.dropdown-item-group {
    font-size: 16px;
    font-weight: bold;
    padding: 5px 10px;
    background-color: #f6f6f6;
}

.dropdown-item-subgroup {
    padding-left: 20px;
    font-style: italic;
    line-height: 30px;
    color: #999;
}

.dropdown-item-leaf {
    padding-left: 40px;
    line-height: 25px;
    cursor: pointer;
}

.dropdown-item-leaf:hover {
    background-color: #f8f8f8;
}

.pop_menu dd {
    display: flex;
    align-items: center;
    justify-content: center;
}

.pop_menu dd a {
    padding-left: 10px !important;
}

.layui-upload-list img {
    width: 120px;
    height: 120px;
    margin: 0 10px;
    border-radius: 10px;
    over-flow: hidden;
    display: inline-block;
    box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
}

/* dropdown下拉样式 */
.dropdown-item-1 {
    background-color: #f6f6f6;
    font-size: 16px;
    font-weight: bold;
    padding: 5px 10px;
}

.my-dropdown {
    min-width: 500px;
    box-shadow: 1px 1px 11px rgb(0 0 0 / 11%);
}

.my-dropdown .layui-menu-body-title {
    display: flex;
    justify-content: space-around;
}

.my-dropdown .layui-menu-body-title div {
    width: 25%;
    text-align: center;
}

.my-dropdown .layui-menu-body-title div:nth-child(2) {
    text-align: left;
}

#patient_info_after_choose {
    display: none;
}

/* 修改LAYUI - input,select,textarea 内置样式 */
.layui-disabled,
input[disabled],
textarea[disabled],
select[disabled] {
    background-color: #f9f9f9;
    color: #666 !important;
    border: 0;
}

/* 修改LAYUI - table 内置样式 */
.layui-table-header tr .layui-table td,
.layui-table th {
    font-size: 16px;
    font-weight: bold;
    background-color: #efefef;
    color: #666;
}

/* 修改LAYUI - table 内置样式，原样式3600行 */
.layui-table[lay-even] tbody tr:nth-child(even) {
    background-color: #efefef;
}

/* .layui-form-label {
    width: 120px;
}
.layui-input-block{
    margin-left: 150px;
} */

.del_pm {
    display: none;
    position: fixed;
    right: 0;
    top: 15%;
    color: #fff;
    z-index: **********;
    width: 130px;
    height: 160px;
    background-color: #ffb800;
    padding: 5px 10px;
    border-radius: 10px 0 0 10px;
    font-size: 12px;
    box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08),
        0 3px 6px -4px rgba(0, 0, 0, 0.12),
        0 9px 28px 8px rgba(0, 0, 0, 0.05);
    text-align: center;
    cursor: move;
    user-select: none;
    touch-action: none;

    /* 添加过渡效果 */
    transition: background-color 600ms ease;
}

/* 鼠标移上时 */
.del_pm:hover {
    background-color: #ffde00;
}

/* 鼠标按下时 */
.del_pm:active {
    background-color: #ff8400;
}

.del_pm div {
    margin: 10px 0 15px 0;
}

.del_pm i {
    font-size: 27px;
}

.del_pm a {
    font-size: 17px;
    margin: 10px 0;
    color: black;
    display: block;
    transition: 300ms;
}

.del_pm a:hover {
    color: #fff;
}

.btn_arg_pm {
    margin: 3px;
    background-color: rgba(255, 251, 0, 0.158);
    border-radius: 5px;
    color: #ff9900;
    padding: 2px 10px;
}

.green_font {
    color: #16b777;
}

.strong_font {
    font-weight: bold;
}

.sign_box {
    margin-top: 30px;
}

.sign_box .sign_pic img {
    width: 100px;
}

.upload_big_btn {
    width: 300px;
    height: 160px;
    line-height: 30px;
    text-align: center;
    border: 2px dashed #fff;
}

.upload_big_btn .btn_big_font {
    font-size: 26px;
}

.red_font {
    color: red;
}

.owing {
    height: 20px;
    border-bottom: 1px solid black;
    color: red;
    min-width: 100px;
    font-size: 20px;
    font-weight: bold;
    position: relative;

    &::before {
        content: '￥';
    }
}

/* 新赠通用用户信息调用页面后的CSS */
.info-content {
    flex: 1;
    padding: 8px 12px;
    background: #f8f8f8;
    border-radius: 4px;
    min-height: 24px;
}

#patient_data {
    margin-bottom: 50px;
}

.info-item {
    margin-bottom: 15px;
    display: flex;
    align-items: center;
}

.info-label {
    color: #666;
    padding-right: 15px;
    text-align: right;
    width: 100px;
    display: inline-block;
}

/* 防止拖动时选中文本 */
.no-select {
    user-select: none !important;
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
}

.layui-nav-tree .layui-nav-item a {
    height: 35px;
    line-height: 35px;
}

.layui-nav-tree .layui-nav-item {
    line-height: 35px;
}

/*detail页面数据区域样式*/
.detail-text {
    padding: 9px 15px;
    min-height: 20px;
    line-height: 20px;
    background-color: #f8f8f8;
    border-radius: 2px;
}

/*2025-02-06新的样式优化*/
/*1、去掉input最大500px*/
/*.layui-input-block input,
.layui-form-select {
    max-width: 500px;
    min-width: 100px;
}*/
/*2、优化layui样式*/
/* 新增的美化样式 */
.layui-card {
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, .05);
    background: #fff;
    border-radius: 4px;
}

.layui-card-body {
    padding: 20px 30px;
}

.layui-form-label {
    font-weight: 500;
    color: #333;
}

.layui-input,
.layui-textarea,
.layui-select {
    border-radius: 4px;
    border-color: #e2e2e2;
    transition: all 0.3s;
}

.layui-input:hover,
.layui-textarea:hover {
    border-color: #c2c2c2;
}

.layui-input:focus,
.layui-textarea:focus {
    border-color: #1E9FFF;
}

.upload-container {
    background: #f8f8f8;
    border: 1px dashed #e2e2e2;
    border-radius: 4px;
    padding: 20px;
    text-align: center;
    min-width: 320px;
}

.content-textarea {
    min-height: 200px !important;
    resize: vertical;
}

.image-preview-container {
    min-height: 160px;
    background: #f8f8f8;
    border: 1px dashed #1084c7;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.image-preview-container-normal {
    background-color: #f8f8f8;
    text-align: center;
    min-height: 200px;
}

.image-preview-container-normal img {
    margin: 17px 0 8px 0;
    width: 130px;
    height: 130px;
}

.image-preview-container-normal .delete-btn {
    background-color: white;
    width: 90px;
    padding: 2px;
    margin: 0 auto;
    cursor: pointer;
    transition: all 0.3s;
}

.image-preview-container-normal .delete-btn:hover {
    background-color: #ff5722;
    color: white;
}

.text-center {
    text-align: center;
    margin-top: 30px;
}

.margin-bottom-none {
    margin-bottom: 0;
}

.table_list_img {
    cursor: pointer;
    max-width: 60px;
    border-radius: 5px;
    box-shadow: 0 6px 10px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 10px 8px rgba(0, 0, 0, 0.05);
}

#top_data_search {
    width: 80%;
}

#asst_dep_id {
    border: 1px solid #e2e2e2;
    border-radius: 4px;
    width: 48%;
    min-width: 100px;
}

#asst_dep_id:focus,
#asst_dep_id:active {
    border-color: #4096ff !important;
    box-shadow: 0 0 0 3px rgba(22, 183, 119, .08);
}

.rtc_style {
    border-radius: 0 0 10px 10px;
    border: 0;
    background-color: rgba(0, 0, 0, .3);
    color: white;
    top: -10px;
    text-align: center;
    height: 50px;
    box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
}

.layer_msg_normal_style {
    border-radius: 0 0 10px 10px;
    border: 0;
    color: white;
    top: -10px;
    text-align: center;
    height: 50px;
    box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
    background: linear-gradient(to right, #1677ff, #076eff);
}