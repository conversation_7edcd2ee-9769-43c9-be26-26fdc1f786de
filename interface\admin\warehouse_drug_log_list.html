<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 出入库记录</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
</head>

<body>
    <div class="layui-layout layui-layout-admin">
        <!-- 头部 -->
        <div class="layui-header">
            <div class="layui-logo">幸年堂</div>
            <ul class="layui-nav layui-layout-right">
                <li class="layui-nav-item" style="background-color:transparent!important;padding-right:10px">
                    <a href="javascript:;" style="display:flex;align-items:center;color:#fff"><img
                            src="/dist/images/user.png" class="layui-nav-img"><span class="user_name"></span></a>
                    <dl class="layui-nav-child pop_menu">
                        <dd><i class="iconfont">&#xe62f;</i><a href="change_password.html#7">用户资料</a></dd>
                        <dd><i class="iconfont">&#xe601;</i><a href="javascript:void(0)" class="refresh_cache">刷新缓存</a>
                        </dd>
                        <dd><i class="iconfont">&#xe634;</i><a href="login_out.html">退出登录</a></dd>
                    </dl>
                </li>
            </ul>
        </div>
        <!-- 导航 -->
        <ul class="layui-nav layui-nav-tree layui-nav-side" id="menuContainer" lay-bar="disabled" lay-accordion>
            <div style="margin: 50px 0;color: #bbb;text-align: center;">菜单加载中...</div>
        </ul>
        <!-- 主体内容 -->
        <div class="layui-body">
            <div class="body_child">
                <div class="layui-panel">
                    <!-- 添加搜索模块 -->
                    <div class="layui-form" style="position: absolute;top:50px;right: 100px;">
                        <div class="layui-row">
                            <div class="layui-col-md9">
                                <input type="text" name="key" placeholder="请输入关键词" autocomplete="off"
                                    class="layui-input" lay-affix="clear">
                            </div>
                            <div class="layui-col-md3">
                                <div class="layui-input-inline" style="margin-left: 10px;">
                                    <button class="layui-btn" lay-submit lay-filter="search"
                                        style="width: 120px;">筛选</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- 搜索模块结束 -->

                    <div class="layui-card-header">
                        <div class="layui-row" style="padding-top:10px;">
                            <div class="layui-col-md11 layui-col-sm10" style="display: flex;">
                                <div>出入库记录列表</div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-padding-3">
                        <div id="data_search">
                            <div class="layui-tab layui-tab-brief" lay-filter="logType">
                                <ul class="layui-tab-title">
                                    <li class="layui-this">全部</li>
                                    <li>出库</li>
                                    <li>入库</li>
                                    <li>报损</li>
                                </ul>
                            </div>
                        </div>
                        <table id="mytable" lay-filter="mytable" class="layui-table" lay-skin="row"></table>
                    </div>
                </div>
                <br>
                <br>
            </div>
        </div>
    </div>

    <script>
        layui.use(['element', 'layer', 'util', 'table', 'form'], function () {
            var element = layui.element;
            var form = layui.form;
            var layer = layui.layer;
            var util = layui.util;
            var table = layui.table;
            var $ = layui.$;

            // 定义全局变量currentKind，用于记录当前选中的状态
            let currentKind = -1; // -1表示全部

            // 监听Tab切换事件
            element.on('tab(logType)', function (data) {
                // 根据选项卡索引设置currentKind
                switch (data.index) {
                    case 0: currentKind = -1; break; // 全部
                    case 1: currentKind = 0; break;  // 出库
                    case 2: currentKind = 1; break;  // 入库
                    case 3: currentKind = 2; break;  // 报损
                }

                // 获取搜索框的值
                let searchKey = $('input[name="key"]').val();

                // 重新加载表格数据
                layer.load(2);
                table.reload('mytable', {
                    where: {
                        kind: currentKind,
                        key: searchKey || undefined  // 如果searchKey有值就携带，没有就不传
                    }
                    , page: {
                        curr: 1
                    }
                });
            });

            // 页面顶部插入缓存的用户数据
            let local_userinfo = localStorage.getItem('local_userinfo');
            let user_name = "";
            if (local_userinfo) {
                local_userinfo = JSON.parse(local_userinfo);
                user_name = local_userinfo.Name + " ( " + local_userinfo.Roles + " )";
            } else {
                user_name = "未登录";
            }
            $('.user_name').text(user_name);
            render_menu($);
            $('.refresh_cache').click(function () {
                refresh_cache($);
            });

            // 获取供应商和用户映射数据
            let supMap = {};
            let userMap = {};
            let warehouseNameMap = {};  // 新增仓库名称映射

            // 获取供应商映射
            $.ajax({
                url: serverUrl + '/admin/supplier_drug/sup_id2name',
                type: 'POST',
                async: false,
                success: function (res) {
                    if (res.code === 200 && res.data) {
                        // 将供应商数组转换为ID到Name的映射
                        res.data.forEach(function (item) {
                            supMap[item.ID] = item.Name;
                        });
                    }
                }
            });

            // 获取用户映射
            $.ajax({
                url: serverUrl + '/admin/user/list_low',
                type: 'POST',
                async: false,
                success: function (res) {
                    if (res.code === 200 && res.data) {
                        res.data.forEach(function (item) {
                            userMap[item.ID] = item.Name;
                        });
                    }
                }
            });

            // 获取仓库名称的函数
            function getWarehouseNames(data) {
                // 获取所有不重复的仓库ID
                const warehouseIds = [...new Set(data.map(item => item.Pid))];

                // 依次请求每个仓库ID对应的名称
                const promises = warehouseIds.map(wid => {
                    return new Promise((resolve, reject) => {
                        $.ajax({
                            url: serverUrl + '/admin/tools/warehouse2name',
                            type: 'POST',
                            data: { wid: wid, type: 0 },
                            success: function (res) {
                                if (res.code === 200 && res.data) {
                                    warehouseNameMap[wid] = res.data.Name;  // 使用大写的Name
                                }
                                resolve();
                            },
                            error: function (err) {
                                console.error('获取仓库名称失败:', err);
                                resolve();
                            }
                        });
                    });
                });

                return Promise.all(promises);
            }

            //引入TABLE模块
            layer.load(2);
            table.render({
                elem: '#mytable'
                , even: true
                , url: serverUrl + "/admin/warehouse_drug_log/list"
                , method: 'post'
                , response: {
                    statusName: 'code'
                    , statusCode: 200
                    , msgName: 'msg'
                    , countName: 'count'
                    , dataName: 'data'
                }
                , cols: [[
                    { field: 'ID', title: '记录ID', align: 'center', width: 80 }
                    , {
                        field: 'Pid', title: '库存ID', minWidth:150, align: 'center', templet: function (d) {
                            const warehouseName = warehouseNameMap[d.Pid] || '加载中...';
                            return '<a href="javascript:;" lay-event="viewDetail" class="btn_arg_pm" data-pid="' + d.Pid + '">' + warehouseName + '</a>';
                        }
                    }
                    , {
                        field: 'Sup_id', title: '供应商', width: 150, align: 'center', templet: function (d) {
                            return supMap[d.Sup_id] || "出库事件";
                        }
                    }
                    , {
                        field: 'User_id', title: '操作者', width: 150, align: 'center', templet: function (d) {
                            return userMap[d.User_id] || d.User_id;
                        }
                    }
                    , {
                        field: 'Kind', title: '操作类型', width: 150, align: 'center', templet: function (d) {
                            let kind = '';
                            switch (d.Kind) {
                                case 0: kind = '出库'; break;
                                case 1: kind = '入库'; break;
                                case 2: kind = '报损'; break;
                                default: kind = '未知';
                            }
                            return kind;
                        }
                    }
                    , {
                        field: 'Batch', title: '药品批次', width: 120, templet: function (d) {
                            return d.Kind !== 1 ? '出库无批次' : d.Batch;
                        }
                    }
                    , { field: 'Old_data', title: '修改前数据', minWidth:100, align: 'center' }
                    , {
                        field: 'Change_data', title: '变更数量', minWidth:100, align: 'center', templet: function (d) {
                            return d.Change_data > 0 ? (d.Kind === 1 ? "+" + d.Change_data : "-" + d.Change_data) : "-" + d.Change_data;
                        }
                    }
                    , { field: 'New_data', title: '修改后数据', minWidth:100, align: 'center' }
                    , { field: 'Notes', title: '备注', minWidth: 200 }
                    , {
                        field: 'Create_time', title: '日志时间', align: 'center',
                        templet: function (d) {
                            return d.Create_time ? d.Create_time.replace('T', ' ').replace('Z', '') : '';
                        }
                    }
                ]]
                , page: true
                , limit: 12
                , text: {
                    none: '暂无相关数据' // 自定义空数据提示文本
                }
                , done: function (res) {
                    if (!res.data || res.data.length === 0) {
                        // 如果数据为空，关闭加载层
                        layer.closeAll('loading');
                        return;
                    }
                    getWarehouseNames(res.data).then(() => {
                        $('.btn_arg_pm').each(function () {
                            const $this = $(this);
                            const pid = $this.data('pid');
                            const warehouseName = warehouseNameMap[pid] || '未知';
                            $this.html(pid + " | " + warehouseName);
                        });
                        layer.closeAll('loading');
                    });
                }
            });

            // Add event listener for the Pid column click
            table.on('tool(mytable)', function (obj) {
                if (obj.event === 'viewDetail') {
                    layer.open({
                        type: 2,
                        title: '库存详情',
                        area: ['1000px', '600px'],
                        shadeClose: true,
                        content: 'warehouse_drug_detail.html?id=' + obj.data.Pid
                    });
                }
            });

            //监听搜索
            form.on('submit(search)', function (data) {
                layer.load(2);
                data.field.kind = currentKind; // 添加当前选中的类型
                table.reload('mytable', {
                    where: data.field
                    , page: {
                        curr: 1
                    }
                });
                return false;
            });
        });
    </script>
</body>

</html>