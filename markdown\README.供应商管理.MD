# 慕生堂在线诊室系统

## 项目说明
- 本项目是一个基于Golang和LAYUI的互联网医院系统，其涵盖患者、病历、线上诊室（TRTC）、订单、处方、中药调剂、药材库存管理、药材供应商管理、成品药管理、赠品管理、仓储订单管理、售后、统计、角色RBAC管理等几个大模块

## 当前MARKDOWN文档主要针对模块为：药材供应商管理

# 帮我实现供应商管理中的：前端和后端

# 步骤1：

## 后端
### 列表页参考文件及函数：
  - 参考文件：internal\app\admin\admin_extra.go
  - 参考函数：Prescription_list_verify，列表页函数请命名为supplier_drug_list；api_id是164
  - 搜索参数，只保留供应商名称搜索（name）
### 详情页、修改页、添加页面
  - 这3个页面的函数，请自行帮我写。其函数命名分别为：supplier_drug_detail、supplier_drug_edit、supplier_drug_add；api_id分别为：167、166、165

## 前端
- 前端文件目录：interface\admin\
- 列表页参考文件：prescription_list_verify.html，列表页文件请新建文件名为：supplier_drug_list.html
- 搜索参数，只保留供应商名称搜索（name）
- 详情页、修改页、添加页面，请自行帮我写，注意尽量使用LAYUI的规范，做到美观漂亮。
  页面命名分别为：supplier_drug_detail.html、supplier_drug_edit.html、supplier_drug_add.html

### MYSQL数据库：
- 表名：supplier_drug
- 字段：
  id	int	主键ID
  code	varchar	社会统一代码
  name	varchar	供应商名称
  address	varchar	供应商地址
  bank_name	varchar	开户银行
  bank_account	varchar	银行账号
  contact_name	varchar	联系人姓名
  contact_mobile	varchar	负责人手机
  contact_phone	varchar	联系人座机
  business_scope	varchar	经营范围
  status	tinyint	状态 1:启用 0:禁用
  created_time	datetime	创建时间
  update_time	datetime	更新时间

# 步骤2
## 修改supplier_drug_list.html页面
  - 增加删除按钮，对应接口：/admin/supplier_drug/del
    删除后返回的数据格式详见admin_extra.go中的Supplier_drug_del函数
    删除成功后，要刷新当前列表页面
## 编辑、详情，弹出的模态框有点小，适当加大。
## 编辑、详情，弹出的模态框里的内容排版要再重新美化下，使用LAYUI的栅格化，变成2列N行，另外详情页里的样式，请参照patient_records_show.html（用div，不要用input，会难看。本身就是详情展示，又不是编辑页面，用INPUT干嘛呢，虽然设置成只可读，但也感觉不好看）