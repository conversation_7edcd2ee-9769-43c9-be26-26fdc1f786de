<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 角色列表</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
</head>

<body class="layui-padding-5">
    <form class="layui-form" lay-filter="form_edit" action="" onsubmit="return false">


        <div class="layui-col-sm12 layui-col-md12 layui-form-item">
            <span class="red_star">*</span> 角色名称
        </div>

        <div class="layui-col-sm12 layui-col-md12 layui-form-item">
            <input type="text" name="role" required lay-verify="required" placeholder="请输入输入框内容" autocomplete="off"
                class="layui-input">
        </div>


        <div class="layui-input-block" style="text-align: right;">
            <button class="layui-btn" lay-submit lay-filter="formSubmitBtn">立即提交</button>
            <button type="reset" class="layui-btn layui-btn-primary">重置</button>
        </div>
    </form>

    <script>
        layui.use('form', function () {
            var form = layui.form;
            var $ = layui.jquery;
            var index = parent.layer.getFrameIndex(window.name);
            form.on('submit(formSubmitBtn)', function (data) {
                layer.load(2);
                $.ajax({
                    url: '/admin/roles/add',
                    type: 'post',
                    data: data.field,
                    success: function (res) {
                        layer.closeAll('loading');
                        if (res.code == 200) {
                            layer.msg(res.msg, { icon: 1, time: 1000 }, function () {
                                parent.layer.close(index);
                                parent.layui.table.reload('role_list');
                            });
                        } else {
                            layer.msg(res.msg, { icon: 2, time: 1000 });
                        }
                    },
                    error: function (err) {
                        layer.closeAll('loading');
                        layer.msg(err.responseJSON.msg);
                    }
                });
                return false;
            });
        });
    </script>




</body>

</html>