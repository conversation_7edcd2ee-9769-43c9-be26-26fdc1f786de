<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 角色列表</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
    <style>
        #floating-submit {
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>

<body>
    <div class="layui-layout layui-layout-admin">
        <!-- 头部 -->
        <div class="layui-header">
            <div class="layui-logo">幸年堂</div>
            <ul class="layui-nav layui-layout-right">
                <li class="layui-nav-item" style="background-color:transparent!important;padding-right:10px">
                    <a href="javascript:;" style="display:flex;align-items:center;color:#fff"><img
                            src="/dist/images/user.png" class="layui-nav-img"><span class="user_name"></span></a>
                    <dl class="layui-nav-child pop_menu">
                        <dd><i class="iconfont">&#xe62f;</i><a href="change_password.html#7">用户资料</a></dd>
                        <dd><i class="iconfont">&#xe601;</i><a href="javascript:void(0)" class="refresh_cache">刷新缓存</a>
                        </dd>
                        <dd><i class="iconfont">&#xe634;</i><a href="login_out.html">退出登录</a></dd>
                    </dl>
                </li>
            </ul>
        </div>
        <!-- 导航 -->
        <ul class="layui-nav layui-nav-tree layui-nav-side" id="menuContainer" lay-bar="disabled" lay-accordion>
            <div style="margin: 50px 0;color: #bbb;text-align: center;">菜单加载中...</div>
        </ul>


        <!-- 主体内容 -->
        <div class="layui-body">
            <div class="body_child">
                <div class="layui-panel">
                    <div class="layui-card-header">
                        <div class="layui-row" style="padding-top:10px;">
                            <div class="layui-col-md11 layui-col-sm10">① 选择病历</div>
                        </div>
                    </div>

                    <div class="layui-padding-5" style="padding-top: 0 !important;min-height: 800px;">

                        <div id="data_search" style="margin-top: 10px;">
                            <div class="layui-form" style="display: flex; align-items: center; gap: 10px;">
                                <input type="text" name="wd" id="dropdown_input" placeholder="请输入电话或姓名"
                                    autocomplete="off" class="layui-input" lay-affix="clear" style="width: 250px;">

                                <button class="layui-btn" lay-submit lay-filter="search"
                                    style="width: 100px;">筛选</button>
                            </div>
                        </div>


                        <table id="mytable" lay-filter="mytable" class="layui-table" lay-skin="row"
                            style="margin-top: 20px;"></table>
                    </div>
                </div>
                <br>
                <br>
            </div>
        </div>
    </div>

    <!-- 添加浮动提交按钮的容器 -->
    <div id="floating-submit"
        style="display: none; position: fixed; left: 50%; bottom: -100px; transform: translateX(-50%); transition: all 0.3s ease;">
        <button class="layui-btn layui-btn-lg" style="padding: 0 60px;">
            <i class="layui-icon layui-icon-right"></i> 下一步
        </button>
    </div>

    <script>
        layui.use(['element', 'layer', 'util'], function () {
            var element = layui.element;
            var form = layui.form;
            var layer = layui.layer;
            var util = layui.util;
            var laydate = layui.laydate;
            var table = layui.table;
            var dropdown = layui.dropdown;
            var pat_pro_id = 0;
            var $ = layui.$;
            // 渲染日期
            laydate.render({
                elem: '#ID-laydate-demo'
            });
            // 页面顶部插入缓存的用户数据
            let local_userinfo = localStorage.getItem('local_userinfo');
            let user_name = "";
            if (local_userinfo) {
                local_userinfo = JSON.parse(local_userinfo);
                user_name = local_userinfo.Name + " ( " + local_userinfo.Roles + " )";
            } else {
                user_name = "未登录";
            }
            $('.user_name').text(user_name);
            render_menu($);
            $('.refresh_cache').click(function () {
                refresh_cache($);
            });
            //引入TABLE模块
            layer.load(2);
            table.render({
                elem: '#mytable'
                , url: serverUrl + "/admin/patient_records/list"
                , method: 'post'
                , even: true
                , where: {
                    status: 2,
                }
                , response: {
                    statusName: 'code'
                    , statusCode: 200
                    , msgName: 'msg'
                    , countName: 'count'
                    , dataName: 'data'
                }
                , cols: [[
                    { type: 'checkbox', fixed: 'left' },
                    {
                        field: 'ID', title: '病历ID', align: 'center', templet: function (d) {
                            return "B" + d.ID;
                        }
                    },
                    {
                        title: '已绑订单', align: 'center', templet: function (d) {
                            let str = '';
                            switch (d.Status) {
                                case 0:
                                    str = '待建诊室';
                                    break;
                                case 1:
                                    str = '待问诊'
                                    break;
                                case 2:
                                    str = '<i class="iconfont green_font strong_font">&#xeba3;</i>';
                                    break;
                                case 3:
                                    str = '无效病历';
                                    break;
                                case 4:
                                    str = '待开方';
                                    break;
                                case 5:
                                    str = '<a href="/admin/order_show.html?id=' + d.Ord_id + '#/admin/order_add.html" class="btn_arg_pm">已下单</a>';
                                    break;
                                case 6:
                                    str = '病历完结'
                                    break;
                            }
                            return str;
                        }
                    },
                    { field: 'Pat_id', title: '帐号ID', align: 'center' },
                    { field: 'Pat_pro_id', title: '用户ID', align: 'center' },
                    {
                        field: 'Name', title: '姓名', align: 'center', templet: function (d) {
                            return '-';
                        }
                    },
                    {
                        field: 'Sex', title: '性别', align: 'center', templet: function (d) {
                            return '-';
                        }
                    },
                    {
                        field: 'Age', title: '年龄', align: 'center', templet: function (d) {
                            return '-';
                        }
                    },
                    {
                        title: '就诊类型', align: 'center', templet: function (d) {
                            return '-';
                        }
                    },
                    {
                        field: 'Scheduled_time', title: '就诊时间', align: 'center', templet: function (d) {
                            return '-';
                        }
                    },
                    { field: 'Department_id', title: '科室', align: 'center' },
                    { field: 'Doc_id', title: '医生', align: 'center' }
                ]]
                , done: function (res, curr, count) {
                    layer.closeAll('loading');
                    after_table_done_rander();
                    $('.pid_link').on('click', function () {
                        let pid = $(this).data('id');
                        layer.open({
                            type: 2,
                            title: '帐号详情',
                            shade: 0.2,
                            maxmin: true,
                            area: ['500px', '690px'],
                            shadeClose: true,
                            content: 'patient_account_detail.html?id=' + pid
                        });
                    });
                },
                error: function (data) {
                    layer.msg(data.responseJSON.msg);
                },
                page: true,
                limit: 33,
            })
            //监听工具条
            table.on('tool(mytable)', function (obj) {
                var data = obj.data;
                if (obj.event === 'create_rtc_room') {
                    let url = 'medical_online_add_room.html?record_id=' + data.ID + "&department_id=" + data.Department_id + '&pat_pro_id=' + data.Pat_pro_id + '&doc_id=' + data.Doc_id;
                    url += '#/admin/medical_online_add.html';
                    window.location.href = url;
                }
            });
            form.on('submit(search)', function (data) {
                // 重载表格
                if (data.field.wd.split(' / ').length > 1) {
                    data.field.pat_pro_id = pat_pro_id;
                }
                data.field.status = 2;
                table.reload('mytable', {
                    where: data.field,
                    page: {
                        curr: 1 //重新从第 1 页开始
                    }
                });
                return false;
            });
            //新增用户按钮
            $('.create_btn').on('click', function () {
                window.location.href = 'patient_records_add.html';
            });
            //模糊搜索用户的下拉框组件
            var inst = dropdown.render({
                elem: '#dropdown_input',
                trigger: 'keyup click',
                data: [],
                style: 'min-width: 220px; box-shadow: 1px 1px 11px rgb(0 0 0 / 11%);',
                click: function (data) {
                    if (!isEmpty(data)) {
                        this.elem.val(data.name + ' / ' + data.phone);
                        pat_pro_id = data.id;
                    }
                }
            });
            $(inst.config.elem).on('input propertychange', function () {
                var elem = $(this);
                var value = elem.val().trim();
                if (value.length > 0) {
                    $.ajax({
                        url: "/admin/patient_profile/patient_profile_phone2id",
                        type: 'post',
                        dataType: 'json',
                        data: {
                            "wd": value,
                        },
                        success: function (response) {
                            let data = response.data;
                            if (data && data.length > 0) {
                                dropdown.reloadData(inst.config.id, {
                                    data: data,
                                    templet: function (d) {
                                        var exp = new RegExp(value, 'gi');
                                        return (d.name + ' / ' + d.phone).replace(exp, function (str) {
                                            return '<span style="color: red;">' + str + '</span>';
                                        });
                                    }
                                });
                            } else {
                                dropdown.reloadData(inst.config.id, {
                                    data: [],
                                });
                            }
                        },
                        error: function (err) {
                            dropdown.reloadData(inst.config.id, {
                                data: [],
                            });
                        }
                    });
                } else {
                    dropdown.reloadData(inst.config.id, {
                        data: [],
                    });
                }
            });




            var after_table_done_rander = function () {
                render_button($);
                // 表格内AJAX填充，整行填充
                // 获取用户信息
                let patient_profile_array = [];
                $("tbody:eq(0)").find("tr").each(function (d) {
                    patient_profile_array.push($(this).find("td:eq(4)").text());
                });
                patient_profile_array = Array.from(new Set(patient_profile_array));
                for (let i = 0; i < patient_profile_array.length; i++) {
                    $.ajax({
                        url: serverUrl + "/admin/patient_profile/detail",
                        type: "post",
                        data: {
                            id: patient_profile_array[i],
                        },
                        success: function (res) {
                            if (res.code === 200) {
                                let data = res.data;
                                $("tbody:eq(0)").find("tr").each(function () {
                                    let trid = $(this).find("td:eq(4)").text();
                                    if (trid == data.ID) {
                                        $(this).find("td:eq(5)").html('<div class="layui-table-cell laytable-cell-1-0-3" align="center">' + data.Name + '</div>');
                                        $(this).find("td:eq(6)").html('<div class="layui-table-cell laytable-cell-1-0-3" align="center">' + (data.Sex == 1 ? '男' : '女') + '</div>');
                                        $(this).find("td:eq(7)").html('<div class="layui-table-cell laytable-cell-1-0-3" align="center">' + date2age(data.Born_date) + '岁</div>');
                                        $(this).find("td:eq(9)").html('<div class="layui-table-cell laytable-cell-1-0-3" align="center">' + data.Create_time.split('T')[0] + '</div>');
                                    }
                                })

                            } else {
                                // console.log(res.msg);
                                layer.msg(res.msg, { icon: 2, time: 1000 });
                            }
                        }
                    });
                }

                // 获取医生信息
                let doctor_array = [];
                $("tbody:eq(0)").find("tr").each(function (d) {
                    doctor_array.push($(this).find("td:eq(11)").text());
                });
                doctor_array = Array.from(new Set(doctor_array));
                layer.load(2);
                $.ajax({
                    url: '/admin/user/list_low',
                    data: {
                        user_id_list: doctor_array.join(','),
                    },
                    type: 'post',
                    success: function (res) {
                        layer.closeAll('loading');
                        if (res.code === 200) {
                            let data = res.data;
                            for (let i = 0; i < data.length; i++) {
                                let id = data[i].ID;
                                let name = data[i].Name;
                                $("tbody:eq(0)").find("tr").each(function () {
                                    let trid = $(this).find("td:eq(11)").text();
                                    if (trid == id) {
                                        $(this).find("td:eq(11)").html('<div class="layui-table-cell laytable-cell-1-0-3" align="center">' + (name ? name : '未知') + '</div>');
                                    }
                                });
                            }
                        } else {
                            layer.msg(res.msg, { icon: 2, time: 1000 });
                        }
                    }, error: function (res) {
                        layer.closeAll('loading');
                        layer.msg(res.responseJSON ? res.responseJSON.msg : '网络错误', { icon: 2, time: 1000 });
                        // 处理错误情况，为未能获取的医生ID设置默认显示
                        for (let i = 0; i < doctor_array.length; i++) {
                            $("tbody:eq(0)").find("tr").each(function () {
                                let trid = $(this).find("td:eq(11)").text();
                                if (trid == doctor_array[i]) {
                                    $(this).find("td:eq(11)").html('<div class="layui-table-cell laytable-cell-1-0-3" align="center">无该医生ID</div>');
                                }
                            });
                        }
                    }
                });

                // 获取科室
                let Department_array = [];
                $("tbody:eq(0)").find("tr").each(function (d) {
                    Department_array.push($(this).find("td:eq(10)").text());
                });
                Department_array = Array.from(new Set(Department_array));
                for (let i = 0; i < Department_array.length; i++) {
                    $.ajax({
                        url: serverUrl + "/normal/department_cache_get",
                        type: "post",
                        data: {
                            id: Department_array[i],
                        },
                        success: function (res) {
                            if (res.code === 200) {
                                let data = res.data;
                                $("tbody:eq(0)").find("tr").each(function () {
                                    let trid = $(this).find("td:eq(10)").text();
                                    if (trid == data.Id) {
                                        $(this).find("td:eq(10)").html('<div class="layui-table-cell laytable-cell-1-0-3" align="center">' + (data.Name ? data.Name : '未知') + '</div>');
                                    }
                                })
                            }
                        },
                        error: function (err) {
                            $("tbody:eq(0)").find("tr").each(function () {
                                let trid = $(this).find("td:eq(10)").text();
                                if (trid == Department_array[i]) {
                                    $(this).find("td:eq(10)").html('<div class="layui-table-cell laytable-cell-1-0-3" align="center">无该科室ID</div>');
                                }
                            });
                        }
                    });
                }

                // $("tbody:eq(0)").find("tr").each(function (d) {
                //     let that = $(this).find("td:eq(2)");
                //     let id = $(this).find("td:eq(1)").text();
                //     $.ajax({
                //         url: serverUrl + "/admin/order/exist_by_record",
                //         type: "post",
                //         data: {
                //             record_id: id,
                //         },
                //         success: function (res) {
                //             if (res.code === 200) {
                //                 let data = res.data;
                //                 if (data > 0) {
                //                     that.html('<div class="layui-table-cell laytable-cell-1-0-3" align="center"><a href="order_show.html?id=' + data + '#/admin/order_add.html" class="btn_arg_pm" style="font-weight: bold;">' + data + '</a></div>');
                //                     $(this).find("td:eq(0)").find("input").prop("disabled", true);
                //                 } else {
                //                     that.html('<div class="layui-table-cell laytable-cell-1-0-3" align="center"><b class="green_font">可绑</b></div>');
                //                 }
                //             } else {
                //                 console.log(res.msg);
                //                 layer.msg(res.msg, { icon: 2, time: 1000 });
                //             }
                //         },
                //         error: function (err) {
                //             layer.msg('网络错误，请稍后再试', { icon: 2, time: 1000 });
                //         }
                //         //重新渲染按钮
                //     });
                //     // render_button($);
                // });

                // 就诊类型 - 初次还是N次
                let Records_array = [];
                $("tbody:eq(0)").find("tr").each(function (d) {
                    Records_array.push($(this).find("td:eq(4)").text());
                });
                Records_array = Array.from(new Set(Records_array));
                for (let i = 0; i < Records_array.length; i++) {
                    $.ajax({
                        url: serverUrl + "/admin/patient_records/count",
                        type: "post",
                        data: {
                            id: Records_array[i],
                        },
                        success: function (res) {
                            layer.closeAll('loading');
                            if (res.code === 200) {
                                let data = res.data;
                                $("tbody:eq(0)").find("tr").each(function () {
                                    let trid = $(this).find("td:eq(4)").text();
                                    if (trid == Records_array[i]) {
                                        $(this).find("td:eq(8)").html('<div class="layui-table-cell laytable-cell-1-0-3" align="center">' + (data == 1 ? '初诊' : data + '次诊') + '</div>');
                                    }
                                })
                            }
                        },
                        error: function (err) {
                            layer.closeAll('loading');
                            $("tbody:eq(0)").find("tr").each(function () {
                                let trid = $(this).find("td:eq(3)").text();
                                if (trid == Records_array[i]) {
                                    $(this).find("td:eq(7)").html('<div class="layui-table-cell laytable-cell-1-0-3" align="center">' + (data == 1 ? '初诊' : data + '次诊') + '</div>');
                                }
                            })
                        }
                    });
                }
            }
            // 监听表格复选框选择
            table.on('checkbox(mytable)', function (obj) {
                var checkStatus = table.checkStatus('mytable');
                var selectedData = checkStatus.data;

                // 显示/隐藏浮动提交按钮
                var floatingSubmit = $('#floating-submit');
                if (selectedData.length > 0) {
                    if (floatingSubmit.css('display') === 'none') {
                        floatingSubmit.css('display', 'block');
                        setTimeout(function () {
                            floatingSubmit.css('bottom', '20px');
                        }, 50);
                    }
                } else {
                    floatingSubmit.css('bottom', '-100px');
                    setTimeout(function () {
                        floatingSubmit.css('display', 'none');
                    }, 300);
                }
            });
            // 点击下一步按钮的处理
            $('#floating-submit').on('click', function () {
                var checkStatus = table.checkStatus('mytable');
                var selectedData = checkStatus.data;
                if (selectedData.length === 0) {
                    layer.msg('请选择至少一条记录', { icon: 2 });
                    return;
                }

                // 收集选中的ID
                var selectedIds = selectedData.map(function (item) {
                    return item.ID;
                });

                var selectedAccountIds = selectedData.map(function (item) {
                    return item.Pat_pro_id;
                });
                // 使用 Set 来获取唯一值
                var uniqueIds = new Set(selectedAccountIds);
                if (uniqueIds.size > 1) {
                    layer.msg('请选择同一用户帐号下，同一患者的病历', { icon: 2 });
                    return;
                }

                // 跳转到下一个页面，并携带选中的ID
                window.location.href = 'order_add_2.html?record_ids=' + selectedIds.join(',') + '#/admin/order_add.html';
            });

        });
    </script>
</body>

</html>