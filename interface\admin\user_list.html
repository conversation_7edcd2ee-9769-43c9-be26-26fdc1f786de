<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 用户列表</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
    <style>
        .new_dropdown_style,
        .new_dropdown_style .layui-panel {
            box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
            min-width: 150px;
            padding: 10px;
        }

        .layui-card-header-userlist {
            position: relative;
            height: 42px;
            line-height: 42px;
            padding: 10px 20px;
            color: #333;
            border-radius: 2px 2px 0 0;
        }

        .layui-card-header-userlist input {
            box-shadow: 0 2px 0 rgba(5, 145, 255, 0.1);
        }
    </style>
</head>

<body>
    <div class="layui-layout layui-layout-admin">
        <!-- 头部 -->
        <div class="layui-header">
            <div class="layui-logo">幸年堂</div>
            <ul class="layui-nav layui-layout-right">
                <li class="layui-nav-item" style="background-color:transparent!important;padding-right:10px">
                    <a href="javascript:;" style="display:flex;align-items:center;color:#fff"><img
                            src="/dist/images/user.png" class="layui-nav-img"><span class="user_name"></span></a>
                    <dl class="layui-nav-child pop_menu">
                        <dd><i class="iconfont">&#xe62f;</i><a href="change_password.html#7">用户资料</a></dd>
                        <dd><i class="iconfont">&#xe601;</i><a href="javascript:void(0)" class="refresh_cache">刷新缓存</a>
                        </dd>
                        <dd><i class="iconfont">&#xe634;</i><a href="login_out.html">退出登录</a></dd>
                    </dl>
                </li>
            </ul>
        </div>
        <!-- 导航 -->
        <ul class="layui-nav layui-nav-tree layui-nav-side" id="menuContainer" lay-bar="disabled" lay-accordion>
            <div style="margin: 50px 0;color: #bbb;text-align: center;">菜单加载中...</div>
        </ul>
        <!-- 主体内容 -->
        <div class="layui-body">
            <div class="body_child">
                <div class="layui-panel">
                    <div class="layui-card-header-userlist">
                        <!-- 头部搜索和操作区 -->



                        <div class="layui-row layui-flex layui-flex-nowrap layui-flex-middle">

                            <!-- 部门下拉菜单 -->
                            <div class="layui-col layui-inline">
                                <button type="button" class="layui-btn layui-btn-primary department-dropdown"
                                    id="departmentSelector">
                                    <span class="department-dropdown-text">所有部门</span>
                                    <i class="layui-icon layui-icon-down"></i>
                                </button>
                            </div>

                            <!-- 搜索 -->
                            <div class="layui-col layui-inline" style="flex-grow: 1;margin: 0 20px;">
                                <div class="layui-input-inline">
                                    <input type="text" name="key" placeholder="输入用户姓名搜索" autocomplete="off"
                                        class="layui-input" style="width: 300px;">
                                    <button class="layui-btn" id="searchBtn"
                                        style="position: absolute; top: 0; right: 0; cursor: pointer;">
                                        <i class="layui-icon layui-icon-search"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- 清空搜索 -->
                            <div class="layui-col layui-inline" style="margin-right: 20px;">
                                <button type="button" class="layui-btn" id="clearSearch">
                                    <i class="layui-icon layui-icon-refresh"></i>
                                </button>
                            </div>

                            <!-- 新增用户 -->
                            <div class="layui-col layui-inline">
                                <button type="button" class="layui-btn layui-btn-normal create_btn">
                                    <i class="layui-icon layui-icon-add-1"></i> 新增用户
                                </button>
                            </div>
                        </div>




                    </div>
                    <div class="layui-padding-3" style="min-height: 800px;">
                        <table id="user_list" lay-filter="user_list"></table>
                    </div>
                </div>
                <br>
                <br>
            </div>
        </div>
    </div>

    <script type="text/html" id="user_list_bar">
    {{# if(d.ID !== 1){ }}
        <a class="layui-btn layui-btn-xs role_permission" lay-event="permission">角色</a>
        <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
        {{# if(d.Role_id.indexOf('3') !== -1 || d.Role_id.indexOf('9') !== -1 || d.Role_id.indexOf('11') !== -1){ }}
          {{# if(d.Openid !== '0'){ }}
          <a class="layui-btn layui-btn-xs layui-btn-primary layui-border-orange" lay-event="bind_wx">绑定微信</a>
          {{# } else { }}
          <a class="layui-btn layui-btn-xs" lay-event="bind_wx">绑定微信</a>
          {{# } }}
        {{# } }}
        <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
    {{# } else { }}
        <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    {{# } }}
</script>

    <script>
        layui.use(['element', 'layer', 'util', 'table', 'dropdown'], function () {
            var element = layui.element;
            var layer = layui.layer;
            var util = layui.util;
            var table = layui.table;
            var dropdown = layui.dropdown;
            var $ = layui.$;

            // 页面顶部插入缓存的用户数据
            let local_userinfo = localStorage.getItem('local_userinfo');
            let user_name = "";
            if (local_userinfo) {
                local_userinfo = JSON.parse(local_userinfo);
                user_name = local_userinfo.Name + " ( " + local_userinfo.Roles + " )";
            } else {
                user_name = "未登录";
            }
            $('.user_name').text(user_name);
            render_menu($);
            $('.refresh_cache').click(function () {
                refresh_cache($);
            });

            // 声明变量
            var tableIns;
            var departmentData = [];
            var currentDepartmentId = 0; // 当前选中的部门ID
            var currentDepartmentPath = "所有部门"; // 当前部门路径文字

            // 读取部门信息
            layer.load(2);

            // 部门数据处理函数
            function buildDepartmentMenu(departments, parentId) {
                var menuItems = [];

                // 筛选当前父级下的部门
                var filteredDepts = departments.filter(function (item) {
                    return item.Pid == parentId;
                }).sort(function (a, b) {
                    return a.Sort - b.Sort; // 按Sort字段排序
                });

                // 为每个部门构建菜单项
                filteredDepts.forEach(function (dept) {
                    // 查找该部门是否有子部门
                    var hasChildren = departments.some(function (item) {
                        return item.Pid == dept.Id;
                    });

                    var menuItem = {
                        title: dept.Name,
                        id: dept.Id
                    };

                    // 如果有子部门，递归构建子菜单
                    if (hasChildren) {
                        menuItem.child = buildDepartmentMenu(departments, dept.Id);
                    }

                    menuItems.push(menuItem);
                });

                return menuItems;
            }

            // 初始化部门下拉菜单
            function initDepartmentDropdown() {
                // 构建下拉菜单数据
                var menuData = buildDepartmentMenu(departmentData, 0);

                // 新增"所有部门"选项
                var allDeptOption = {
                    title: '所有部门',
                    id: 0
                };

                // 添加分隔线
                var separator = {
                    type: '-'
                };

                // 完整菜单数据
                var fullMenuData = [allDeptOption, separator].concat(menuData);

                // 渲染下拉菜单
                dropdown.render({
                    elem: '#departmentSelector',
                    data: fullMenuData,
                    click: function (obj) {
                        // 更新当前选中部门ID
                        currentDepartmentId = obj.id;

                        // 更新按钮文本
                        updateDepartmentButtonText(obj);

                        // 重新加载表格数据
                        reloadTable();
                    },
                    className: 'new_dropdown_style'
                });
            }

            // 更新部门选择按钮的文本
            function updateDepartmentButtonText(obj) {
                if (!obj) return;

                // 如果是顶级"所有部门"
                if (obj.id === 0) {
                    currentDepartmentPath = "所有部门";
                    $('.department-dropdown-text').text(currentDepartmentPath);
                    return;
                }

                // 构建部门路径
                var deptPath = [];
                findDepartmentPath(obj.id, deptPath);

                // 反转数组，从高级到低级
                deptPath.reverse();

                // 生成路径文本
                currentDepartmentPath = deptPath.join(' / ');

                // 更新按钮文本
                $('.department-dropdown-text').text(currentDepartmentPath);
            }

            // 查找部门路径
            function findDepartmentPath(deptId, path) {
                var dept = departmentData.find(function (item) {
                    return item.Id == deptId;
                });

                if (dept) {
                    path.push(dept.Name);

                    // 如果有父部门，继续向上查找
                    if (dept.Pid > 0) {
                        findDepartmentPath(dept.Pid, path);
                    }
                }
            }

            function reloadTable() {
                // 重新加载表格，附带部门ID参数
                tableIns.reload({
                    where: {
                        department_id: currentDepartmentId > 0 ? currentDepartmentId : '',
                        key: $('input[name="key"]').val()
                    },
                    page: {
                        curr: 1 // 重新从第1页开始
                    }
                });
            }

            // 加载部门数据和初始化表格
            $.ajax({
                url: '/normal/department_cache_get',
                type: 'post',
                success: function (res) {
                    layer.closeAll('loading');
                    departmentData = res.data;

                    // 初始化部门下拉菜单
                    initDepartmentDropdown();

                    // 初始化表格
                    tableIns = table.render({
                        elem: '#user_list',
                        method: 'post',
                        url: '/admin/user/list_withpage',
                        parseData: function (res) {
                            return {
                                "code": 0,
                                "msg": "success",
                                "data": res.data,
                                "count": res.count
                            };
                        },
                        page: true,
                        limit: 20,
                        cols: [[
                            { field: 'ID', width: 80, align: 'center', title: 'ID' },
                            {
                                field: 'Department_id', width: 380, title: '部门 / 科室', templet: function (d) {
                                    return id2department(d.Department_id, departmentData);
                                }
                            },
                            { field: 'Name', width: 130, title: '姓名' },
                            {
                                field: 'Status', width: 100, title: '状态', align: 'center', templet: function (d) {
                                    return d.Status === 1 ? '<i class="layui-icon layui-icon-ok"></i>' : '<i class="layui-icon layui-icon-close" style="color:red"></i>';
                                }
                            },
                            { field: 'Phone', width: 150, title: '用户名' },
                            { field: 'Sort', width: 100, title: '排序 <i class="layui-icon layui-icon-tips layui-font-14" lay-event="sort_tips" title="排序" style="margin-left: 5px;"></i>', align: 'center' },
                            {
                                field: 'Role',
                                minWidth: 150,
                                title: '角色',
                                templet: function (d) {
                                    return d.Role.split(',')
                                        .map(perm_item => `<span class="perm_item_rows">${perm_item}</span>`)
                                        .join('');
                                }
                            },
                            { title: '操作 <i class="layui-icon layui-icon-tips layui-font-14" lay-event="sort_tips_wx" title="关于绑定微信的说明" style="margin-left: 5px;"></i>', align: 'left', toolbar: '#user_list_bar', width: 350 }
                        ]],
                        done: function (res, curr, count) {
                            // 可选择添加更多操作
                        }
                    });
                },
                error: function (res) {
                    layer.closeAll('loading');
                    layer.msg(res.responseJSON.msg);
                },
            });

            // 表头自定义元素工具事件 --- 2.8.8+
            table.on('colTool(user_list)', function (obj) {
                var event = obj.event;
                if (event === 'sort_tips') {
                    layer.alert('主要针对医生角色推荐时的排序场景，如小程序首页医生推荐。', {
                        title: '排序应用场景说明',
                        shadeClose: true,
                        btn: ['知道了']
                    });
                } else if (event === 'sort_tips_wx') {
                    layer.alert('只有医助、售后、库管这三个角色有绑定微信的必要性。', {
                        title: '关于绑定微信的说明',
                        shadeClose: true,
                        btn: ['知道了']
                    });
                }
            });
            // 监听工具条事件（table 的事件监听方式）
            table.on('tool(user_list)', function (obj) {
                var data = obj.data; // 当前行的数据
                var event = obj.event; // 事件类型

                if (event === 'permission') {
                    // 弹出模态框
                    layer.open({
                        type: 2, // iframe 类型
                        title: '绑定角色 - ' + data.Name,
                        area: ['350px', '100%'], // 模态框宽高
                        shadeClose: true, // 点击遮罩关闭弹窗
                        offset: 'r',
                        anim: 'slideLeft',
                        content: '/admin/user_bind_roles.html?id=' + data.ID // 加载的页面
                    });
                } else if (event === 'edit') {
                    let open_url = '/admin/user_edit.html?id=' + data.ID
                    let area = ['600px', '900px'];
                    if (data.Role_id.indexOf(global_doctor_role_id) === 0) {//医生
                        open_url = '/admin/user_edit_doctor.html?id=' + data.ID
                    } else if (data.Role_id.indexOf(global_sale_pre_clerk_role_id) === 0) {//售前文员
                        area = ['600px', '750px'];
                        open_url = '/admin/user_edit_sale_clerk.html?dep_id='+ global_sale_department_pre_sale +'&id=' + data.ID
                    } else if (data.Role_id.indexOf(global_sale_after_clerk_role_id) === 0) {//售后文员
                        area = ['600px', '750px'];
                        open_url = '/admin/user_edit_sale_clerk.html?dep_id='+ global_sale_department_after_sale +'&id=' + data.ID
                    }else if(data.Role_id.indexOf(global_asst_role_id) === 0 || data.role_id.indexOf(global_after_asst_role_id) === 0){
                        // 医助 - 售前、售后
                        area = ['600px', '750px'];
                        open_url = '/admin/user_edit_asst.html?id=' + data.ID
                    }
                    layer.open({
                        type: 2,
                        title: '编辑用户 - ' + data.Name,
                        area: area,
                        shadeClose: true,
                        content: open_url
                    });
                } else if (
                    event === 'bind_wx'
                ) {
                    layer.open({
                        type: 2,
                        title: '绑定微信 - ' + data.Name,
                        area: ['600px', '600px'],
                        shadeClose: true,
                        content: '/admin/user_bind_wx.html?department_id=' + data.Department_id + '&user_id=' + data.ID,
                    });
                } else if (event === 'del') {
                    layer.confirm('确定删除该角色吗？', function (index) {
                        layer.load(2);
                        $.ajax({
                            url: '/admin/roles/del',
                            type: 'post',
                            data: {
                                id: data.ID
                            },
                            success: function (res) {
                                layer.closeAll('loading');
                                layer.msg(res.msg);
                                tableIns.reload();
                            },
                            error: function (res) {
                                layer.closeAll('loading');
                                layer.msg(res.responseJSON.msg);
                            },
                        });
                        layer.close(index);
                    });
                }
            });
            $('.create_btn').click(function () {
                layer.open({
                    type: 2,
                    title: '新增用户',
                    area: ['600px', '580px'],
                    shadeClose: true,
                    content: '/admin/user_add.html'
                });
            })

            // 绑定搜索按钮点击事件
            $('#searchBtn').click(function () {
                reloadTable();
            });

            // 绑定输入框回车事件
            $('input[name="key"]').keypress(function (e) {
                if (e.which === 13) { // 回车键的键值为13
                    $('#searchBtn').click();
                }
            });

            //清空筛选
            $('#clearSearch').click(function () {
                window.location.reload();
            });
        });
    </script>

</body>

</html>