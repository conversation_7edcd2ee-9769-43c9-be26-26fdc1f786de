<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 编辑文章分类</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
</head>

<body>
    <div class="layui-padding-3" style="margin:30px 50px 20px 0;">
        <form class="layui-form" lay-filter="form">
            <div class="layui-row layui-col-space15">
                <!-- 第一列 -->
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">名称</label>
                        <div class="layui-input-block">
                            <input type="text" name="Title" lay-verify="required" placeholder="请输入分类名称" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">描述</label>
                        <div class="layui-input-block">
                            <textarea name="Description" placeholder="请输入描述" class="layui-textarea"></textarea>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">排序</label>
                        <div class="layui-input-block">
                            <input type="number" name="Sort" placeholder="请输入排序数字" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button type="submit" class="layui-btn" lay-submit lay-filter="form">保存</button>
                    <button type="button" class="layui-btn layui-btn-primary" onclick="closeModalWindow()">关闭</button>
                </div>
            </div>
        </form>
    </div>

    <script>
        layui.use(['form'], function () {
            var form = layui.form;
            var $ = layui.$;

            // 获取URL中的ID参数
            var id = getUrlParam('id');

            // 如果有ID，则加载数据
            if (id) {
                $.ajax({
                    url: serverUrl + '/admin/article_category/detail',
                    type: 'POST',
                    data: { id: id },
                    success: function (res) {
                        if (res.code === 200) {
                            // 填充表单数据
                            form.val('form', res.data);
                        } else {
                            layer.msg(res.msg || '数据加载失败', { icon: 2 });
                        }
                    },
                    error: function (res) {
                        layer.msg(res.responseJSON.msg, { icon: 2, time: 1000 });
                    }
                });
            }

            // 监听表单提交
            form.on('submit(form)', function (data) {
                var formData = data.field;
                if (id) {
                    formData.id = id;
                }
                
                $.ajax({
                    url: serverUrl + '/admin/article_category/edit',
                    type: 'POST',
                    data: formData,
                    success: function (res) {
                        if (res.code === 200) {
                            parent.layui.table.reload('mytable');
                            layer.msg('保存成功', { icon: 1, time: 1000 }, function () {
                                closeModalWindow();
                            });
                        } else {
                            layer.msg(res.msg || '保存失败', { icon: 2 });
                        }
                    },
                    error: function (res) {
                        layer.msg(res.responseJSON.msg, { icon: 2, time: 1000 });
                    }
                });
                return false;
            });
        });

        function closeModalWindow() {
            var index = parent.layer.getFrameIndex(window.name);
            parent.layer.close(index);
        }

        function getUrlParam(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return decodeURI(r[2]);
            return null;
        }
    </script>
</body>

</html> 