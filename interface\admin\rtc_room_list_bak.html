<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 线上诊室列表</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
    <script src="/dist/js/xm-select.js"></script>
    <style>
        .xm-label,
        .auto-row {
            display: none;
        }

        xm-select {
            border: 0 !important;
        }
    </style>
</head>

<body>

    <div class="del_pm"></div>


    <div class="layui-layout layui-layout-admin">
        <!-- 头部 -->
        <div class="layui-header">
            <div class="layui-logo">幸年堂</div>
            <ul class="layui-nav layui-layout-right">
                <li class="layui-nav-item" style="background-color:transparent!important;padding-right:10px">
                    <a href="javascript:;" style="display:flex;align-items:center;color:#fff"><img
                            src="/dist/images/user.png" class="layui-nav-img"><span class="user_name"></span></a>
                    <dl class="layui-nav-child pop_menu">
                        <dd><i class="iconfont">&#xe62f;</i><a href="change_password.html#7">用户资料</a></dd>
                        <dd><i class="iconfont">&#xe601;</i><a href="javascript:void(0)" class="refresh_cache">刷新缓存</a>
                        </dd>
                        <dd><i class="iconfont">&#xe634;</i><a href="login_out.html">退出登录</a></dd>
                    </dl>
                </li>
            </ul>
        </div>
        <!-- 导航 -->
        <ul class="layui-nav layui-nav-tree layui-nav-side" id="menuContainer" lay-bar="disabled" lay-accordion>
            <div style="margin: 50px 0;color: #bbb;text-align: center;">菜单加载中...</div>
        </ul>
        <!-- 主体内容 -->
        <div class="layui-body">
            <div class="body_child">
                <div class="layui-panel">
                    <div class="layui-card-header">
                        <div class="layui-row" style="padding-top:10px;">
                            <div class="layui-col-md11 layui-col-sm10">线上诊室列表</div>
                            <div class="layui-col-md1 layui-col-sm2">
                                <button type="submit"
                                    class="layui-btn layui-btn-primary layui-border-red perm_check_btn" res_id="151"
                                    onclick="javascript:window.location.href='medical_online_add.html'">
                                    <i class="layui-icon">&#xe654;</i> 新增
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="layui-padding-3" style="padding-top: 0 !important;min-height: 800px;">
                        <!-- 通用顶部筛选 -->
                        <div id="top_data_search">
                            <div class="layui-form" style="margin: 20px 0 0 0;">
                                <div class="layui-row">
                                    <!-- 科室+医生组合 -->
                                    <div class="layui-col-md4">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label">科室</label>
                                            <div class="layui-input-block" style="display: flex; gap: 10px;">
                                                <select name="keshi" id="keshi" lay-filter="keshi"
                                                    style="width: 48%;"></select>
                                                <select name="doctor" id="doctor" style="width: 48%;"></select>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 售前/售后部门+人员组合 -->
                                    <div class="layui-col-md4">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label">销售</label>
                                            <div class="layui-input-block" style="display: flex; gap: 10px;">
                                                <select name="keshi_asst" id="keshi_asst" lay-filter="keshi_asst" style="width: 48%;"></select>
                                                <select name="asst" id="asst" style="width: 48%;"></select>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 患者搜索 -->
                                    <div class="layui-col-md3">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label">患者</label>
                                            <div class="layui-input-block">
                                                <input type="text" name="wd" id="dropdown_input"
                                                    lay-filter="searchFilter" placeholder="请输入电话或姓名" autocomplete="off"
                                                    class="layui-input" lay-affix="clear">
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 筛选按钮 -->
                                    <div class="layui-col-md1" style="text-align: center;">
                                        <div class="layui-form-item">
                                            <div class="layui-input-block">
                                                <button class="layui-btn" lay-submit lay-filter="search"
                                                    style="min-width: 100px;">筛选</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>




                        <table id="mytable" lay-filter="mytable" class="layui-table" lay-skin="row"></table>
                    </div>
                </div>
                <br>
                <br>
            </div>
        </div>
    </div>


    <script type="text/html" id="TPL-bar">
            <button class="layui-btn layui-btn-xs layui-bg-orange perm_check_btn btn-enter" lay-event="detail" res_id="154">进入</button>
            <button class="layui-btn layui-btn-xs perm_check_btn btn-cancel" lay-event="cancel" res_id="153">取消</button>
            <button class="layui-btn layui-btn-danger layui-btn-xs perm_check_btn" lay-event="delete" res_id="152">删除</button>
    </script>

    <script>
        layui.use(['element', 'layer', 'util'], function () {
            var element = layui.element;
            var form = layui.form;
            var layer = layui.layer;
            var util = layui.util;
            var laydate = layui.laydate;
            var table = layui.table;
            var dropdown = layui.dropdown;
            var $ = layui.$;
            var pat_pro_id = 0;
            var record_id = request.get('record_id');
            if (record_id) {
                record_id = Number(record_id);
                $('.del_pm').html(`
                    <i class="iconfont">&#xe650;</i>
                    <div>注：当前页面含其它页面传来的参数进行筛选</div>
                    <a href="rtc_room_list.html">解绑该参数</a>
                    <a href="javascript:history.back();">返回原页面</a>
                `);
                $('.del_pm').show();
            }
            // 渲染日期
            laydate.render({
                elem: '#ID-laydate-demo'
            });
            // 页面顶部插入缓存的用户数据
            let local_userinfo = localStorage.getItem('local_userinfo');
            let user_name = "";
            let currentUserIsSales = false;
            let currentUserIsDoctor = false;
            let currentUserDeptId = 0;
            let currentUserId = 0;
            let currentUserRoleIds = "";

            if (local_userinfo) {
                local_userinfo = JSON.parse(local_userinfo);
                user_name = local_userinfo.Name + " ( " + local_userinfo.Roles + " )";

                // 获取当前用户的角色ID、部门ID和用户ID
                currentUserRoleIds = local_userinfo.Role_ids || "";
                currentUserDeptId = local_userinfo.Department_id || 0;
                currentUserId = local_userinfo.Id || 0;

                // 判断是否为售前(3)或售后(9)用户
                if (currentUserRoleIds.split(',').includes(global_asst_role_id) || currentUserRoleIds.split(',').includes(global_after_asst_role_id)) {
                    currentUserIsSales = true;
                }

                // 判断是否为医生(4)用户
                if (currentUserRoleIds.split(',').includes(global_doctor_role_id)) {
                    currentUserIsDoctor = true;
                }
            } else {
                user_name = "未登录";
            }
            $('.user_name').text(user_name);

            render_menu($);
            $('.refresh_cache').click(function () {
                refresh_cache($);
            });
            //引入TABLE模块
            layer.load(2);
            table.render({
                elem: '#mytable'
                , even: true
                , url: serverUrl + "/admin/rtc_room/list"
                , where: {
                    record_id: record_id,
                }
                , method: 'post'
                , response: {
                    statusName: 'code'
                    , statusCode: 200
                    , msgName: 'msg'
                    , countName: 'count'
                    , dataName: 'data'
                }
                , cols: [[
                    {
                        field: 'Record_id', title: '病历ID', align: 'center', width: 100, templet: function (d) {
                            return "B" + d.Record_id;
                        }
                    },
                    { field: 'ID', title: '诊室ID', align: 'center', width: 100 },
                    {
                        field: 'Department_id', title: '科室', align: 'center', width: 120, templet: function (d) {
                            return '<div class="department_need_ajax" data-id="' + d.Department_id + '">-</div>';
                        }
                    },
                    {
                        field: 'Asst_id', title: '医助', align: 'center', width: 120, templet: function (d) {
                            return '<div class="user_need_ajax" data-id="' + d.Asst_id + '">-</div>';
                        }
                    },
                    {
                        field: 'Doc_id', title: '医生', align: 'center', width: 120, templet: function (d) {
                            return '<div class="user_need_ajax" data-id="' + d.Doc_id + '">-</div>';
                        }
                    },
                    {
                        field: 'Pat_pro_id', title: '患者', align: 'center', width: 120, templet: function (d) {
                            return '<div class="pat_pro_id" data-id="' + d.Pat_pro_id + '">-</div>';
                        }
                    },
                    {
                        field: 'Status', title: '状态', align: 'center', width: 120, templet: function (d) {
                            let status_text = "";
                            switch (d.Status) {
                                case 0:
                                    status_text = '<i class="iconfont green_font strong_font">&#xeba3;</i>';
                                    break;
                                case 1:
                                    status_text = "已诊";
                                    break;
                                case 2:
                                    status_text = "已取消";
                                    break;
                                case 3:
                                    status_text = "已过期";
                                    break;
                            }
                            return status_text;
                        }
                    },
                    {
                        field: 'Scheduled_time', title: '预约时间', templet: function (d) {
                            return d.Scheduled_time.replace("T", " ").replace("Z", "");
                        }
                    },
                    {
                        field: 'Create_time', title: '创建时间', templet: function (d) {
                            return d.Create_time.replace("T", " ").replace("Z", "");
                        }
                    },
                    {
                        field: 'Finish_time', title: '完成时间', templet: function (d) {
                            if (d.Finish_time) {
                                return formatTimeAgo(linuxTimestampToNormalTime(d.Finish_time));
                            } else {
                                return "-";
                            }
                        }
                    },
                    { title: '操作', align: 'center', toolbar: '#TPL-bar', width: 280 }
                ]]
                , done: function (res, curr, count) {
                    // console.log(res);
                    // console.log(curr);
                    // console.log(count);
                    //检查菜单权限
                    render_button($);
                    if (!res.data) {
                        // layer.msg('暂无数据', { icon: 2, time: 1000 });
                        return false;
                    }
                    // 根据问诊室状态隐藏工具栏"进入"和"取消"按钮
                    res.data.forEach(function (row, index) {
                        if (row.Status !== 0) {
                            const row = $(`tr[data-index="${index}"]`);
                            row.find('.btn-enter').attr('disabled', true);
                            row.find('.btn-enter').addClass('layui-btn-disabled');
                            row.find('.btn-cancel').attr('disabled', true);
                            row.find('.btn-cancel').addClass('layui-btn-disabled');
                        }
                    });


                    //高效-医助、医生等职能部分用户遍历并ID合并
                    let user_arr = [];
                    $('.user_need_ajax').each(function () {
                        let id = $(this).data('id');
                        user_arr.push(id);
                    });
                    user_arr = [...new Set(user_arr)];//去重
                    layer.load(2);
                    $.ajax({
                        url: '/admin/user/list_low',
                        data: {
                            user_id_list: user_arr.join(','),
                        },
                        type: 'post',
                        success: function (res) {
                            layer.closeAll('loading');
                            if (res.code === 200) {
                                let data = res.data;
                                for (let i = 0; i < data.length; i++) {
                                    let id = data[i].ID;
                                    let name = data[i].Name;
                                    $('.user_need_ajax[data-id="' + id + '"]').text(name);
                                }
                            } else {
                                layer.msg(res.msg, { icon: 2, time: 1000 });
                            }
                        }, error: function (res) {
                            layer.closeAll('loading');
                            layer.msg(res.responseJSON.msg, { icon: 2, time: 1000 });
                        }
                    });

                    // 获取部门/科室数据
                    $.ajax({
                        url: '/normal/department_cache_get',
                        type: 'post',
                        success: function (res) {
                            layer.closeAll('loading');
                            let department_data = res.data;
                            $('.department_need_ajax').each(function () {
                                let id = $(this).data('id');
                                let department = id2department(id, department_data, 0);
                                $(this).text(department);
                            });
                        },
                        error: function (res) {
                            layer.load(2);
                            layer.msg(res.responseJSON.msg);
                        },
                    });
                    //高效-患者ID2姓名
                    let pat_pro_id_arr = [];
                    $('.pat_pro_id').each(function () {
                        let id = $(this).data('id');
                        pat_pro_id_arr.push(id);
                    });
                    pat_pro_id_arr = [...new Set(pat_pro_id_arr)];//去重
                    layer.load(2);
                    $.ajax({
                        url: '/admin/patient_profile/patient_profile_ids2name',
                        data: {
                            ids: pat_pro_id_arr.join(','),
                        },
                        type: 'post',
                        success: function (res) {
                            layer.closeAll('loading');
                            if (res.code === 200) {
                                let data = res.data;
                                for (let i = 0; i < data.length; i++) {
                                    let id = data[i].ID;
                                    let name = data[i].Name;
                                    $('.pat_pro_id[data-id="' + id + '"]').text(name);
                                }
                            }
                        }, error: function (res) {
                            layer.closeAll('loading');
                            layer.msg(res.responseJSON.msg, { icon: 2, time: 1000 });
                        }
                    });






                },
                page: true,
                limit: 15,
            })
            //监听工具条
            table.on('tool(mytable)', function (obj) {
                var data = obj.data;
                if (obj.event === 'detail') {
                    window.location.href = 'rtc_room_detail.html?id=' + data.ID + '#/admin/rtc_room_list.html';
                } else if (obj.event === 'delete') {
                    layer.confirm('您确定要删除该诊室吗？', function (index) {
                        layer.load(2);
                        $.ajax({
                            url: serverUrl + "/admin/rtc_room/del",
                            type: "post",
                            data: {
                                id: data.ID
                            },
                            success: function (res) {
                                layer.closeAll('loading');
                                if (res.code == 200) {
                                    layer.msg(res.msg, { icon: 1, time: 1000 });
                                    obj.del();
                                } else {
                                    layer.msg(res.msg, { icon: 2, time: 1000 });
                                }
                            },
                            error: function (res) {
                                layer.closeAll('loading');
                                layer.msg(res.responseJSON.msg);
                            }
                        });
                        layer.close(index);
                    });
                } else if (obj.event === 'cancel') {
                    layer.confirm('您确定要取消该诊室吗？', function (index) {
                        if (data.Status == 1) {
                            layer.msg('该诊室已被预约，无法取消', { icon: 2, time: 1000 });
                            return false;
                        } else if (data.Status == 2) {
                            layer.msg('该诊室已被取消，无需再次取消', { icon: 2, time: 1000 });
                            return false;
                        }
                        layer.load(2);
                        $.ajax({
                            url: serverUrl + "/admin/rtc_room/set",
                            type: "post",
                            data: {
                                id: data.ID,
                                status: 2,
                            },
                            success: function (res) {
                                layer.closeAll('loading');
                                if (res.code == 200) {
                                    layer.msg(res.msg, { icon: 1, time: 1000 });
                                    table.reload('mytable');
                                } else {
                                    layer.msg(res.msg, { icon: 2, time: 1000 });
                                }
                            },
                            error: function (res) {
                                layer.closeAll('loading');
                                layer.msg(res.responseJSON.msg);
                            }
                        });
                        layer.close(index);
                    });
                }
            });
            form.on('input-affix(searchFilter)', function (data) {
                pat_pro_id = 0;
            });
            form.on('submit(search)', function (data) {
                // 重载表格
                if (data.field.wd.split(' / ').length > 1) {
                    data.field.pat_pro_id = pat_pro_id;
                }
                if (record_id) {
                    data.field.record_id = record_id;
                }
                table.reload('mytable', {
                    where: data.field,
                    page: {
                        curr: 1 //重新从第 1 页开始
                    }
                });
                return false;
            });
            //新增用户
            $('.create_btn').on('click', function () {
                window.location.href = 'medical_online_add.html';
            });

            // 渲染科室下拉表，连带渲染科室下医生下拉
            layer.load(2);
            $.ajax({
                url: '/admin/department/list',
                data: {
                    name: '医疗部门',
                    store_id: global_default_store_id,
                },
                type: 'post',
                success: function (res) {
                    layer.closeAll('loading');
                    if (res.code === 200) {
                        let data = res.data;
                        let html = '<option value="">选择科室</option>';
                        for (let i = 0; i < data.length; i++) {
                            html += '<option value="' + data[i].id + '">' + data[i].name + '</option>';
                        }
                        $('#keshi').html(html);

                        // 如果当前用户是医生，设置默认选中和只读状态
                        if (currentUserIsDoctor && currentUserDeptId > 0) {
                            // 设置科室下拉框为只读
                            $('#keshi').val(currentUserDeptId);
                            $('#keshi').attr('disabled', 'disabled');
                            // 使用内联样式而不是添加类，避免影响布局
                            $('#keshi').css('background-color', '#f2f2f2');
                        }

                        form.render('select');

                        // 加载医生列表的函数
                        function loadDoctorsByDepartment(keshi_id) {
                            if (keshi_id) {
                                layer.load(2);
                                $.ajax({
                                    url: '/admin/user/list_low',
                                    data: {
                                        role_id: 4,
                                        department_id: keshi_id,
                                    },
                                    type: 'post',
                                    success: function (res) {
                                        layer.closeAll('loading');
                                        if (res.code === 200) {
                                            let data = res.data;
                                            let html = '<option value="">选择医生</option>';
                                            if (data && data.length > 0) {
                                                for (let i = 0; i < data.length; i++) {
                                                    html += '<option value="' + data[i].ID + '">' + data[i].Name + '</option>';
                                                }

                                                // 如果当前用户是医生，并且在当前科室中，设置默认选中和只读状态
                                                if (currentUserIsDoctor && currentUserDeptId == keshi_id && currentUserId > 0) {
                                                    $('#doctor').html(html);
                                                    $('#doctor').val(currentUserId);
                                                    $('#doctor').attr('disabled', 'disabled');
                                                    // 使用内联样式而不是添加类，避免影响布局
                                                    $('#doctor').css('background-color', '#f2f2f2');
                                                    form.render('select');
                                                } else {
                                                    $('#doctor').html(html);
                                                    form.render('select');
                                                }
                                            } else {
                                                layer.msg('该科室暂无医生', { icon: 2, time: 1000 });
                                                html = '<option value="">该科室暂无医生</option>';
                                                $('#doctor').html(html);
                                                form.render('select');
                                            }
                                        } else {
                                            layer.msg(res.msg, { icon: 2, time: 1000 });
                                        }
                                    }, error: function (res) {
                                        layer.closeAll('loading');
                                        layer.msg(res.responseJSON.msg, { icon: 2, time: 1000 });
                                    }
                                });
                            } else {
                                $('#doctor').html('<option value="">选择医生</option>');
                                form.render('select');
                            }
                        }

                        // 如果当前用户是医生，自动触发科室选择事件加载对应的医生列表
                        if (currentUserIsDoctor && currentUserDeptId > 0) {
                            loadDoctorsByDepartment(currentUserDeptId);
                        }

                        // 监听科室选择事件
                        form.on('select(keshi)', function (data) {
                            let keshi_id = data.value;
                            loadDoctorsByDepartment(keshi_id);
                        });
                    } else {
                        layer.msg(res.msg, { icon: 2, time: 1000 });
                    }
                }, error: function (res) {
                    layer.closeAll('loading');
                    layer.msg(res.responseJSON.msg, { icon: 2, time: 1000 });
                }
            });
            //渲染售前+售后部门下拉框，连带渲染部门下医助（售前/售后人员）下拉框
            layer.load(2);
            // 使用department_cache_get接口获取所有部门数据
            $.ajax({
                url: '/normal/department_cache_get',
                type: 'post',
                success: function (res) {
                    layer.closeAll('loading');
                    if (res.code === 200) {
                        let allDepartments = res.data;
                        // 分别筛选出售前和售后部门
                        let preSalesDepts = [];
                        let afterSalesDepts = [];

                        for (let i = 0; i < allDepartments.length; i++) {
                            if (allDepartments[i].Name.includes('售前')) {
                                preSalesDepts.push(allDepartments[i]);
                            } else if (allDepartments[i].Name.includes('售后')) {
                                afterSalesDepts.push(allDepartments[i]);
                            }
                        }

                        // 按照排序值降序排列
                        preSalesDepts.sort((a, b) => b.Sort - a.Sort);
                        afterSalesDepts.sort((a, b) => b.Sort - a.Sort);

                        // 使用LAYUI的分组选择框功能渲染下拉选择框
                        let html = '<option value="">选择部门</option>';

                        // 售前部门分组
                        if (preSalesDepts.length > 0) {
                            html += '<optgroup label="售前部门">';
                            for (let i = 0; i < preSalesDepts.length; i++) {
                                html += '<option value="' + preSalesDepts[i].Id + '">' + preSalesDepts[i].Name + '</option>';
                            }
                            html += '</optgroup>';
                        }

                        // 售后部门分组
                        if (afterSalesDepts.length > 0) {
                            html += '<optgroup label="售后部门">';
                            for (let i = 0; i < afterSalesDepts.length; i++) {
                                html += '<option value="' + afterSalesDepts[i].Id + '">' + afterSalesDepts[i].Name + '</option>';
                            }
                            html += '</optgroup>';
                        }

                        // 合并部门数据用于后续查询
                        let salesDepartments = [...preSalesDepts, ...afterSalesDepts];
                        $('#keshi_asst').html(html);

                        // 如果当前用户是售前或售后，设置默认选中和只读状态
                        if (currentUserIsSales && currentUserDeptId > 0) {
                            // 设置部门下拉框为只读
                            $('#keshi_asst').val(currentUserDeptId);
                            $('#keshi_asst').attr('disabled', 'disabled');

                            // 使用内联样式而不是添加类，避免影响布局
                            $('#keshi_asst').css('background-color', '#f2f2f2');
                        }

                        form.render('select');

                        // 加载部门用户的函数
                        function loadDepartmentUsers(keshi_id, salesDepartments) {
                            layer.load(2);
                            // 根据部门ID获取对应的角色ID（售前=3，售后=9）
                            let role_id = 3; // 默认为售前
                            let selectedDept = salesDepartments.find(dept => dept.Id == keshi_id);
                            if (selectedDept && selectedDept.Name.includes('售后')) {
                                role_id = 9; // 售后角色ID
                            }

                            $.ajax({
                                url: '/admin/user/list_low',
                                data: {
                                    role_id: role_id,
                                    department_id: keshi_id,
                                },
                                type: 'post',
                                success: function (res) {
                                    layer.closeAll('loading');
                                    if (res.code === 200) {
                                        let data = res.data;
                                        let html = '<option value="">选择人员</option>';
                                        if (data && data.length > 0) {
                                            for (let i = 0; i < data.length; i++) {
                                                html += '<option value="' + data[i].ID + '">' + data[i].Name + '</option>';
                                            }

                                            // 如果当前用户是售前或售后，并且在当前部门中，设置默认选中和只读状态
                                            if (currentUserIsSales && currentUserDeptId == keshi_id && currentUserId > 0) {
                                                $('#asst').html(html);
                                                $('#asst').val(currentUserId);
                                                $('#asst').attr('disabled', 'disabled');
                                                // 使用内联样式而不是添加类，避免影响布局
                                                $('#asst').css('background-color', '#f2f2f2');
                                                form.render('select');
                                            } else {
                                                $('#asst').html(html);
                                                form.render('select');
                                            }
                                        } else {
                                            layer.msg('该部门下暂无人员', { icon: 2, time: 1000 });
                                            html = '<option value="">该部门下暂无人员</option>';
                                            $('#asst').html(html);
                                            form.render('select');
                                        }
                                    } else {
                                        layer.msg(res.msg, { icon: 2, time: 1000 });
                                    }
                                },
                                error: function (res) {
                                    layer.closeAll('loading');
                                    layer.msg(res.responseJSON.msg, { icon: 2, time: 1000 });
                                }
                            });
                        }

                        // 如果当前用户是售前或售后，自动触发部门选择事件加载对应的人员列表
                        if (currentUserIsSales && currentUserDeptId > 0) {
                            loadDepartmentUsers(currentUserDeptId, salesDepartments);
                        }

                        // 监听部门选择事件
                        form.on('select(keshi_asst)', function (data) {
                            let keshi_id = data.value;
                            if (keshi_id) {
                                loadDepartmentUsers(keshi_id, salesDepartments);
                            } else {
                                $('#asst').html('<option value="">选择人员</option>');
                                form.render('select');
                            }
                        });
                    } else {
                        layer.msg(res.msg, { icon: 2, time: 1000 });
                    }
                }, error: function (res) {
                    layer.closeAll('loading');
                    layer.msg(res.responseJSON.msg, { icon: 2, time: 1000 });
                }
            });


            //模糊搜索用户的下拉框组件
            var inst = dropdown.render({
                elem: '#dropdown_input',
                trigger: 'keyup click',
                data: [],
                style: 'min-width: 220px; box-shadow: 1px 1px 11px rgb(0 0 0 / 11%);',
                click: function (data) {
                    if (!isEmpty(data)) {
                        this.elem.val(data.name + ' / ' + data.phone);
                        pat_pro_id = data.id;
                    }
                }
            });
            $(inst.config.elem).on('input propertychange', function () {
                var elem = $(this);
                var value = elem.val().trim();
                if (value.length > 0) {
                    $.ajax({
                        url: "/admin/patient_profile/patient_profile_phone2id",
                        type: 'post',
                        dataType: 'json',
                        data: {
                            "wd": value,
                        },
                        success: function (response) {
                            let data = response.data;
                            if (data && data.length > 0) {
                                dropdown.reloadData(inst.config.id, {
                                    data: data,
                                    templet: function (d) {
                                        var exp = new RegExp(value, 'gi');
                                        return (d.name + ' / ' + d.phone).replace(exp, function (str) {
                                            return '<span style="color: red;">' + str + '</span>';
                                        });
                                    }
                                });
                            } else {
                                dropdown.reloadData(inst.config.id, {
                                    data: [],
                                });
                            }
                        },
                        error: function (err) {
                            dropdown.reloadData(inst.config.id, {
                                data: [],
                            });
                        }
                    });
                } else {
                    dropdown.reloadData(inst.config.id, {
                        data: [],
                    });
                }
            });
        });
    </script>
</body>

</html>