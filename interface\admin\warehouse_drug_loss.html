<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 药品报损</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
    <style>
        .drug-item {
            border: 1px solid #e6e6e6;
            padding: 15px;
            margin-bottom: 15px;
            border-radius: 4px;
        }

        .drug-info {
            margin-bottom: 10px;
            padding: 10px;
            background-color: #f8f8f8;
            border-radius: 4px;
        }

        .drug-inputs {
            display: flex;
            gap: 10px;
        }
    </style>
</head>

<body>
    <div class="layui-padding-3">
        <form class="layui-form" id="lossForm">
            <div id="drugList">
                <!-- Drug items will be inserted here -->
            </div>

            <!-- 底部按钮 -->
            <div class="layui-form-item perm_check_btn" style="text-align: center; margin-top: 20px;" res_id="66" >
                <button type="submit" class="layui-btn" lay-submit lay-filter="submitLoss">提交报损</button>
                <button type="button" class="layui-btn layui-btn-primary" onclick="closeModalWindow()">关闭</button>
            </div>
        </form>
    </div>

    <!-- 进度条容器 -->
    <div id="uploadProgress"
        style="display: none; position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 20px; border-radius: 4px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); width: 400px; z-index: 999;">
        <div style="margin-bottom: 10px;">正在处理药材报损...</div>
        <div class="layui-progress layui-progress-big" lay-filter="upload-progress" lay-showPercent="true">
            <div class="layui-progress-bar" lay-percent="0%"></div>
        </div>
    </div>

    <!-- 结果统计对话框模板 -->
    <script type="text/html" id="resultDialogTpl">
        <div style="padding: 20px;">
            <div style="margin-bottom: 15px; display: flex; padding: 20px; justify-content: space-around; align-items: center; font-size: 15px;">
                <p>总计处理：{{d.total}}条</p>
                <p>成功报损：<span style="color: #5FB878;">{{d.success}}条</span></p>
                <p>报损失败：<span style="color: #FF5722;">{{d.fail}}条</span></p>
            </div>
            <div style="text-align: center;">
                <button type="button" class="layui-btn" onclick="closeResultDialog()">确定</button>
            </div>
        </div>
    </script>

    <script>
        layui.use(['form', 'layer', 'element'], function () {
            var form = layui.form;
            var layer = layui.layer;
            var $ = layui.$;
            var element = layui.element;

            render_button($);

            // 接收父页面传递的数据
            window.setDrugData = function (drugData) {
                var html = '';
                drugData.forEach(function (drug, index) {
                    html += `
                        <div class="drug-item">
                            <div class="drug-info">
                                药材名：${drug.Drug_name} &nbsp;&nbsp; 批号：${drug.Code} &nbsp;&nbsp;  供应商：${drug.Last_supplier} &nbsp;&nbsp; 库存数量：${drug.Quantity}
                            </div>
                            <div class="drug-inputs">
                                <div class="layui-inline" style="width: 200px;">
                                    <input type="number" name="loss_quantity_${drug.ID}" lay-verify="required|number|checkQuantity" 
                                           class="layui-input" placeholder="请输入报损数量" data-max="${drug.Quantity}">
                                </div>
                                <div class="layui-inline" style="flex-grow: 1;">
                                    <input type="text" name="loss_reason_${drug.ID}" lay-verify="required" 
                                           class="layui-input" placeholder="请输入报损原因">
                                </div>
                            </div>
                        </div>
                    `;
                });
                $('#drugList').html(html);
                form.render();
            };

            // 自定义验证规则
            form.verify({
                checkQuantity: function (value, item) {
                    var max = $(item).data('max');
                    if (value <= 0) {
                        return '报损数量必须大于0';
                    }
                    if (value > max) {
                        return '报损数量不能大于库存数量';
                    }
                }
            });

            // 显示结果统计对话框
            function showResultDialog(successCount, failCount, totalCount) {
                var content = layui.laytpl($('#resultDialogTpl').html()).render({
                    total: totalCount,
                    success: successCount,
                    fail: failCount
                });

                layer.open({
                    type: 1,
                    title: '报损完成',
                    content: content,
                    area: ['500px', 'auto'],
                    closeBtn: 0,
                    shadeClose: false
                });
            }

            // 关闭结果对话框
            window.closeResultDialog = function () {
                layer.closeAll();
                var index = parent.layer.getFrameIndex(window.name);
                parent.layui.table.reload('mytable');
                parent.layer.close(index);
            };

            // 监听提交
            form.on('submit(submitLoss)', function (data) {
                var formData = data.field;
                var drugItems = [];

                // 解析表单数据
                Object.keys(formData).forEach(function (key) {
                    if (key.startsWith('loss_quantity_')) {
                        var id = key.replace('loss_quantity_', '');
                        drugItems.push({
                            id: parseInt(id),
                            quantity: parseFloat(formData[key]),
                            notes: formData['loss_reason_' + id]
                        });
                    }
                });

                if (drugItems.length === 0) {
                    layer.msg('请至少选择一个药材并填写完整信息', { icon: 2 });
                    return false;
                }

                layer.confirm('是否确认提交药材报损？', function (index) {
                    layer.close(index);

                    // 显示进度条
                    $('#uploadProgress').show();
                    element.progress('upload-progress', '0%');

                    // 初始化计数器
                    var successCount = 0;
                    var failCount = 0;
                    var totalCount = drugItems.length;
                    var currentIndex = 0;

                    // 串行处理每个药材
                    function processNextDrug() {
                        if (currentIndex >= totalCount) {
                            // 所有药材处理完成，显示结果对话框
                            $('#uploadProgress').hide();
                            showResultDialog(successCount, failCount, totalCount);
                            return;
                        }

                        var drug = drugItems[currentIndex];

                        // 更新进度条
                        var progress = Math.round((currentIndex + 1) / totalCount * 100);
                        element.progress('upload-progress', progress + '%');

                        $.ajax({
                            url: '/admin/warehouse_drug/out',
                            type: 'POST',
                            data: {
                                id: drug.id,
                                quantity: drug.quantity,
                                notes: drug.notes,
                                kind: 2
                            },
                            success: function (res) {
                                if (res.code === 200) {
                                    successCount++;
                                } else {
                                    failCount++;
                                    layer.msg(res.msg || '药材ID:' + drug.id + ' 报损失败', { icon: 2 });
                                }
                                currentIndex++;
                                processNextDrug();
                            },
                            error: function () {
                                failCount++;
                                layer.msg('药材ID:' + drug.id + ' 网络错误', { icon: 2 });
                                currentIndex++;
                                processNextDrug();
                            }
                        });
                    }

                    // 开始处理第一个药材
                    processNextDrug();
                });

                return false;
            });
        });

        function closeModalWindow() {
            var index = parent.layer.getFrameIndex(window.name);
            parent.layer.close(index);
        }
    </script>
</body>

</html>