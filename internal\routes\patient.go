package routes

import (
	handlers "mstproject/internal/app/patient"
	"net/http"
)

// RegisterRoutes 注册所有的路由
func PatientRoutes(mux *http.ServeMux) {
	// mux.HandleFunc("/patient/login", handlers.Login)       // - web test，已作废
	// mux.HandleFunc("/patient/loginout", handlers.LoginOut) // - web test，已作废
	mux.HandleFunc("/patient/order_undelivered", handlers.Order_undelivered)
	mux.HandleFunc("/patient/patient_records", handlers.Patient_records)
	mux.HandleFunc("/patient/patient_pre", handlers.Patient_pre)
	mux.HandleFunc("/patient/patient_record_detail", handlers.Patient_record_detail)
	mux.HandleFunc("/patient/get_wechat_info", handlers.Get_wechat_info)
	mux.HandleFunc("/patient/get_wechat_openid", handlers.Get_wechat_openid)
	mux.HandleFunc("/patient/get_user_role", handlers.Get_user_role)
	mux.HandleFunc("/patient/department_list", handlers.Department_list)
	mux.HandleFunc("/patient/department_detail", handlers.Department_detail)
	mux.HandleFunc("/patient/bind_user", handlers.Bind_user)
	mux.HandleFunc("/patient/doctor_list", handlers.Doctor_list)
	mux.HandleFunc("/patient/doctor_detail", handlers.Doctor_detail)
	mux.HandleFunc("/patient/order_list", handlers.Order_list)
	mux.HandleFunc("/patient/order_detail", handlers.Order_detail)
	// mux.HandleFunc("/patient/express_info", handlers.Express_info)
	mux.HandleFunc("/patient/patient_profile_list", handlers.Patient_profile_list)
	mux.HandleFunc("/patient/patient_profile_add", handlers.Patient_profile_add)
	mux.HandleFunc("/patient/patient_profile_edit", handlers.Patient_profile_edit)
	mux.HandleFunc("/patient/article_category", handlers.Article_category)
	mux.HandleFunc("/patient/article_list", handlers.Article_list)
	mux.HandleFunc("/patient/article_detail", handlers.Article_detail)
	mux.HandleFunc("/patient/patient_slides_list", handlers.Patient_slides_list)
	mux.HandleFunc("/patient/patient_profile/detail", handlers.Patient_profile_detail)
	mux.HandleFunc("/patient/patient_profile/avatarurl_upload", handlers.Patient_profile_avatarurl_upload)
	mux.HandleFunc("/patient/orderid2preids", handlers.Orderid2preids)
	mux.HandleFunc("/patient/rtc_room_list", handlers.Rtc_room_list)
	mux.HandleFunc("/patient/rtc_room_status_set", handlers.Rtc_room_status_set)
	mux.HandleFunc("/patient/patient_rtc_room_list", handlers.Patient_rtc_room_list)
	mux.HandleFunc("/patient/get_trtc_config", handlers.Rtc_room_get_trtc_config)
	mux.HandleFunc("/patient/patient_profile_id2name", handlers.Patient_profile_id2name)
	// WebSocket服务用于RTC房间的实时通知
	mux.HandleFunc("/patient/rtc_room_ws", handlers.RtcRoomWebsocket)
	// 库存ID2NAME
	mux.HandleFunc("/patient/tools/warehouse2productname_finisheddrug", handlers.Warehouse2productname_finisheddrug)
	// 通过用户一码通，查询该用处户的处方单
	mux.HandleFunc("/patient/find_prescription_by_user_bar_code", handlers.Find_prescription_by_user_bar_code)
	// 根据ID求处方信息
	mux.HandleFunc("/patient/find_prescription_by_id", handlers.Find_prescription_by_id)
	// 入库 - 刚调剂完的成品药入库
	mux.HandleFunc("/patient/warehouse_finisheddrug_add", handlers.Warehouse_finisheddrug_add)
	// 退货入库
	mux.HandleFunc("/patient/warehouse_returndrug_add", handlers.Warehouse_returndrug_add)
	// 根据ID查询可入库订单
	mux.HandleFunc("/patient/find_order_to_return", handlers.Find_order_to_return)
	// 异常处方入库
	// no.1 异常处方查询
	mux.HandleFunc("/patient/find_err_prescription_by_id", handlers.Find_err_prescription_by_id)
	// no.2 异常处方入库
	mux.HandleFunc("/patient/warehouse_err_prescription_add", handlers.Warehouse_err_prescription_add)
	// 物流列表 - 库管员
	mux.HandleFunc("/patient/express_list", handlers.Express_list)
	// 查询物流信息
	mux.HandleFunc("/patient/express_info", handlers.Express_info)
	// 物流登记
	mux.HandleFunc("/patient/express_add", handlers.Express_add)
	// 库管查看当前用户订单信息
	mux.HandleFunc("/patient/order_list_master", handlers.Order_list_master)

	// 成品药_出入库日志
	// mux.HandleFunc("/patient/warehouse_finisheddrug_log_detail", handlers.Warehouse_finisheddrug_log_detail)
	// mux.HandleFunc("/patient/warehouse_finisheddrug_log_list_error", handlers.Warehouse_finisheddrug_log_list_error)

	// 库管 - 下面4个图标，服务接口
	mux.HandleFunc("/patient/warehouse_gifts/list", handlers.Warehouse_gifts_list)
	mux.HandleFunc("/patient/warehouse_gifts_log/list", handlers.Warehouse_gifts_log_list)

	mux.HandleFunc("/patient/warehouse_finisheddrug/list", handlers.Warehouse_finisheddrug_list)
	mux.HandleFunc("/patient/warehouse_finisheddrug_log/list", handlers.Warehouse_finisheddrug_log_list)

	mux.HandleFunc("/patient/warehouse_drug/list", handlers.Warehouse_drug_list)
	mux.HandleFunc("/patient/warehouse_drug_log/list", handlers.Warehouse_drug_log_list)
	mux.HandleFunc("/patient/warehouse/order_list", handlers.Warehouse_order_list)
	// 小程序 - 登录后判断当前用户是否有音视频的数据
	mux.HandleFunc("/patient/is_has_rtcdata", handlers.Is_has_rtcdata)
	// 病历问诊告知单同意时间
	mux.HandleFunc("/patient/records_notice_sheet_agree_time", handlers.Records_notice_sheet_agree_time)
	mux.HandleFunc("/patient/records_notice_sheet_update", handlers.Records_notice_sheet_update)
	// 医助 - 患者病历接口
	mux.HandleFunc("/patient/asst_records_list", handlers.Asst_records_list)
	// 医助 - 患者病历详情
	mux.HandleFunc("/patient/asst_records_detail", handlers.Asst_records_detail)
	// 医助 - 确认问诊咨询单
	mux.HandleFunc("/patient/confirm_consultation", handlers.Confirm_consultation)
}
