function characteristics(){
	let data = [];
	const canvas = document.createElement("canvas");
	const ctx = canvas.getContext("2d");
	const txt = "Canvas Fingerprint";
	ctx.textBaseline = "top";
	ctx.font = "14px 'Arial'";
	ctx.textBaseline = "alphabetic";
	ctx.fillStyle = "#f60";
	ctx.fillRect(125, 1, 62, 20);
	ctx.fillStyle = "#069";
	ctx.fillText(txt, 2, 15);
	ctx.fillStyle = "rgba(102, 204, 0, 0.7)";
	ctx.fillText(txt, 4, 17);
	const canvasData = canvas.toDataURL();
	data.push(canvasData);
	data.push(navigator.platform);
	data.push(navigator.userAgent.substr(0,50));
	data.push(window.screen.width);
	data.push(window.screen.height);
	data.push(window.screen.colorDepth);
	data.push(Intl.DateTimeFormat().resolvedOptions().timeZone);
	data.push(navigator.language);
	data.push(navigator.plugins.length);
	data.push(navigator.hardwareConcurrency);
	data = data.join('|');
	return data;
}
function characteristics_less() {
    let data = [];
    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d");
    const txt = "Canvas Fingerprint";
    ctx.textBaseline = "top";
    ctx.font = "14px 'Arial'";
    ctx.textBaseline = "alphabetic";
    ctx.fillStyle = "#f60";
    ctx.fillRect(125, 1, 62, 20);
    ctx.fillStyle = "#069";
    ctx.fillText(txt, 2, 15);
    ctx.fillStyle = "rgba(102, 204, 0, 0.7)";
    ctx.fillText(txt, 4, 17);
    const canvasData = canvas.toDataURL();
    data.push(canvasData);
    data.push(navigator.platform);
    data.push(navigator.userAgent.substr(0,50));
    data.push(navigator.language);
    data.push(navigator.plugins.length);
    if(navigator.hardwareConcurrency)data.push(navigator.hardwareConcurrency);
    data = data.join('|');
    return data;
}

function isIE() {
    var userAgent = navigator.userAgent;
    
    if (userAgent.indexOf("MSIE") !== -1 || !!document.documentMode === true) {
        return true;
    } else {
        return false;
    }
}