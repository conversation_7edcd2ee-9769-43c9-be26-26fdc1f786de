# 慕生堂在线诊室系统

## 项目说明
- 本项目是一个基于Golang和LAYUI的互联网医院系统，其涵盖患者、病历、线上诊室（TRTC）、订单、处方、中药调剂、药材库存管理、药材供应商管理、成品药管理、赠品管理、仓储订单管理、售后、统计、角色RBAC管理等几个大模块

## 当前MARKDOWN文档主要针对模块为：药材管理

# 帮我实现药材管理中的：前端和后端

# 参考MARKDOWN文档：公共模板.MD

# 后端需要增加函数：
Drug_list -> api_id：33
Drug_detail -> app_id:57
Drug_add -> app_id:34
Drug_edit -> app_id:35
Drug_del -> app_id:36

# MYSQL数据库：
- 表名：drug
- 字段：
    id	int	ID
    pid	int	类别
    name	varchar	药名
    code	varchar	批次
    unit	varchar	单位
    property	varchar	性味 (如：寒、热、温、凉)
    origin	varchar	产地
    harvestTime	date	采收时间
    validity_days	smallint	默认效期（天）
    storage	varchar	储存条件
    dosage	varchar	常用剂量
    directions	varchar	用法
    effect	varchar	药效
    indication	varchar	适应症
    contraindication	varchar	禁忌症
    sideeffect	varchar	副作用
    interaction	varchar	药物相互作用
    price	decimal	参考价格（仓库里还有个价格，为时仓库设置的价格）
    create_time	timestamp	建档时间
