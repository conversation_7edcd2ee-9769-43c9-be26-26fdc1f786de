package admin

import (
	"bytes"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"mstproject/pkg/common"
	"mstproject/pkg/config"
	"mstproject/pkg/database"
	"net/http"
	"os"
	"strconv"
	"strings"
	"time"
)

// 获取当前患者的病历数量
func Patient_records_count(w http.ResponseWriter, r *http.Request) {
	api_id := config.NormalPerm
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	id := r.FormValue("id")
	if id == "" {
		w.Write([]byte("id can not be empty"))
		return
	}
	sql := "SELECT COUNT(id) FROM patient_records WHERE pat_pro_id = ?"
	var count int
	err := database.GetOne(sql, &count, id)
	if err != nil {
		w.Write([]byte("scan database error"))
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "ok",
		"data": count,
	})
}

// 根据赠品ID查询库存是否存在
func Warehouse_gift_check_by_id(w http.ResponseWriter, r *http.Request) {
	api_id := config.NormalPerm
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	id := r.FormValue("id")
	if id == "" {
		w.Write([]byte("id can not be empty"))
		return
	}
	sql := "select id from warehouse_gifts where gift_id = ?"
	var count int
	err := database.GetOne(sql, &count, id)
	var code int
	if err != nil {
		code = 500
	} else {
		code = 200
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": code,
		"msg":  "ok",
		"data": count,
		"sql":  common.DebugSql(sql, id),
	})
}

// 根据药材ID查询库存是否存在
func Warehouse_drug_check_by_id(w http.ResponseWriter, r *http.Request) {
	api_id := config.NormalPerm
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	id := r.FormValue("id")
	if id == "" {
		w.Write([]byte("id can not be empty"))
		return
	}
	sql := "select id from warehouse_drug where drug_id = ?"
	var count int
	err := database.GetOne(sql, &count, id)
	var code int
	if err != nil {
		code = 500
	} else {
		code = 200
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": code,
		"msg":  "ok",
		"data": count,
	})
}

// 根据取患者id求患者姓名
func Patient_profile_id2name(w http.ResponseWriter, r *http.Request) {
	api_id := config.NormalPerm
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	id := r.FormValue("id")
	if id == "" {
		w.Write([]byte("id can not be empty"))
		return
	}

	sql := "SELECT name FROM patient_profile WHERE id = ?"
	var name string
	err := database.GetOne(sql, &name, id)
	if err != nil {
		w.Write([]byte("scan database error"))
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "ok",
		"data": name,
	})
}

// 根据取患者ids求患者姓名
func Patient_profile_ids2name(w http.ResponseWriter, r *http.Request) {
	api_id := config.NormalPerm
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	ids := r.FormValue("ids")
	if ids == "" {
		w.Write([]byte("id can not be empty"))
		return
	}
	type Patient_profile struct {
		ID   int    `db:"id"`
		Name string `db:"name"`
	}
	sql := "SELECT id,name FROM patient_profile WHERE id in (" + ids + ")"
	var data []Patient_profile
	err := database.GetAll(sql, &data)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "查询患者信息失败",
			"err":  err.Error(),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"data": data,
	})
}

// 根据手机号获取患者id（目的是求ID，但也会带出姓名与电话）
func Patient_profile_phone2id(w http.ResponseWriter, r *http.Request) {
	api_id := config.NormalPerm
	session, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	wd := r.FormValue("wd")

	type Patient_profile struct {
		ID        int    `json:"id"`
		Pid       int    `json:"pid"`
		Name      string `json:"name"`
		Phone     string `json:"phone"`
		Sex       string `json:"sex"`
		Born_date string `json:"born_date"`
	}
	var sql string
	// 判断wd是否纯数字
	if common.IsNumeric(wd) {
		if len(wd) == 7 {
			sql = "SELECT id,pid,name,phone,sex,born_date FROM patient_profile WHERE status = 1 AND left(phone,5)= ? and right(phone,2) = ? order by id desc"
			var patient_profile []Patient_profile
			err := database.GetAll(sql, &patient_profile, wd[:5], wd[len(wd)-2:])
			if err != nil {
				common.JSONResponse(w, http.StatusInternalServerError, map[string]any{
					"code": 500,
					"msg":  "查询患者信息失败",
					"err":  err.Error(),
				})
				return
			}

			// 根据权限处理手机号显示方式
			if session.Values["role_ids"] != "1" {
				for i := range patient_profile {
					phone := patient_profile[i].Phone
					if len(phone) == 11 || len(phone) == 13 {
						patient_profile[i].Phone = phone[:5] + "****" + phone[len(phone)-2:]
					} else {
						patient_profile[i].Phone = "手机号码格式错误"
					}
				}
			}

			common.JSONResponse(w, http.StatusOK, map[string]interface{}{
				"code": 200,
				"msg":  "ok",
				"data": patient_profile,
				"sql":  common.DebugSql(sql, wd),
			})
		} else if len(wd) == 11 || len(wd) == 13 {
			sql = "SELECT id,pid,name,phone,sex,born_date FROM patient_profile WHERE status = 1 AND phone = ? order by id desc"
			var patient_profile []Patient_profile
			err := database.GetAll(sql, &patient_profile, wd)
			if err != nil {
				common.JSONResponse(w, http.StatusInternalServerError, map[string]any{
					"code": 500,
					"msg":  "查询患者信息失败",
					"err":  err.Error(),
				})
				return
			}

			// 根据权限处理手机号显示方式
			if session.Values["role_ids"] != "1" {
				for i := range patient_profile {
					phone := patient_profile[i].Phone
					if len(phone) == 11 || len(phone) == 13 {
						patient_profile[i].Phone = phone[:5] + "****" + phone[len(phone)-2:]
					} else {
						patient_profile[i].Phone = "手机号码格式错误"
					}
				}
			}

			common.JSONResponse(w, http.StatusOK, map[string]interface{}{
				"code": 200,
				"msg":  "ok",
				"data": patient_profile,
				"sql":  common.DebugSql(sql, wd),
			})
		}
	} else {
		sql = "SELECT id,pid,name,phone,sex,born_date FROM patient_profile WHERE status = 1 AND name = ? order by id desc"
		var patient_profile []Patient_profile
		err := database.GetAll(sql, &patient_profile, wd)
		if err != nil {
			common.JSONResponse(w, http.StatusInternalServerError, map[string]any{
				"code": 500,
				"msg":  "查询患者信息失败",
				"err":  err.Error(),
			})
			return
		}

		// 根据权限处理手机号显示方式
		if session.Values["role_ids"] != "1" {
			for i := range patient_profile {
				phone := patient_profile[i].Phone
				if len(phone) == 11 || len(phone) == 13 {
					patient_profile[i].Phone = phone[:5] + "****" + phone[len(phone)-2:]
				} else {
					patient_profile[i].Phone = "手机号码格式错误"
				}
			}
		}

		common.JSONResponse(w, http.StatusOK, map[string]interface{}{
			"code": 200,
			"msg":  "ok",
			"data": patient_profile,
			"sql":  common.DebugSql(sql, wd),
		})
	}
}

// 病历删除
func Patient_records_del(w http.ResponseWriter, r *http.Request) {
	api_id := 150
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	id, err := common.CheckInt(r.FormValue("id"))
	if id < 1 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "病历id不能为空",
		})
		return
	}
	sql := "DELETE FROM patient_records WHERE id = ?"
	result, err := database.Query(sql, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "删除失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, id),
		})
		return
	}
	RowsAffected, _ := result.RowsAffected()
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":         200,
		"msg":          "删除成功",
		"RowsAffected": RowsAffected,
	})
	if RowsAffected > 0 {
		common.Add_log(fmt.Sprintf("删除线上诊室，ID：%d", id), r)
	}
}

// 诊室详情，进入诊室
func Rtc_room_detail(w http.ResponseWriter, r *http.Request) {
	api_id := 154
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	id, err := common.CheckInt(r.FormValue("id"))
	if id < 1 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "房间id不能为空",
		})
		return
	}
	// 结构体
	type RtcRoom struct {
		ID             int    `db:"id"`             // 房间ID
		Department_id  int    `db:"department_id"`  // 科室ID
		Doc_id         int    `db:"doc_id"`         // 医生ID
		Asst_id        int    `db:"asst_id"`        // 医助ID
		Pat_id         int    `db:"pat_id"`         // 患者ID
		Record_id      int    `db:"record_id"`      // 病历ID
		Pat_pro_id     int    `db:"pat_pro_id"`     // 患者档案ID
		Status         int    `db:"status"`         // 房间状态
		Scheduled_time string `db:"scheduled_time"` // 预约时间
		Create_time    string `db:"create_time"`    // 创建时间
	}
	sql := "select id,department_id,doc_id,asst_id,pat_id,pat_pro_id,record_id,status,scheduled_time,create_time from rtc_room where id = ?"
	var rtc_room RtcRoom
	err = database.GetOne(sql, &rtc_room, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "读取数据失败",
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "读取成功",
		"data": rtc_room,
	})
}

// 设置线上诊室状态
func Rtc_room_status_set(w http.ResponseWriter, r *http.Request) {
	api_id := 153
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	id, err := common.CheckInt(r.FormValue("id"))
	if id < 1 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "房间id不能为空",
		})
		return
	}
	status, err := common.CheckInt(r.FormValue("status"))
	if status < 0 || status > 2 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "状态值错误",
		})
		return
	}

	var RowsAffected int64
	var tips string
	if status == 2 {
		// 当状态为2（关闭）时，同时更新病历状态为0（未完成），并使用事务确保数据一致性
		sqls := []database.SQLExec{
			{Query: "UPDATE rtc_room SET status = ? WHERE id = ?",
				Args: []interface{}{
					status, id,
				}},
			{Query: "UPDATE patient_records SET status = 0 WHERE id = (SELECT record_id FROM rtc_room WHERE id = ?) and status = 1",
				Args: []interface{}{
					id,
				}},
		}
		var err error
		RowsAffected, err = database.ExecuteTransaction_with_affectedRows(sqls)
		if err != nil {
			common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
				"code": 500,
				"msg":  "设置失败",
				"err":  err.Error(),
			})
			return
		}
		tips = "更新病历为【待建诊室】"
	} else {
		// 其他状态值只更新诊室状态
		sql := "UPDATE rtc_room SET status = ? WHERE id = ?"
		result, err := database.Query(sql, status, id)
		if err != nil {
			common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
				"code": 500,
				"msg":  "设置失败",
				"err":  err.Error(),
				"sql":  common.DebugSql(sql, status, id),
			})
			return
		}
		RowsAffected, _ = result.RowsAffected()
		tips = "更新状态为【" + config.Room_Status[status] + "】"
	}

	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":         200,
		"msg":          "设置成功",
		"RowsAffected": RowsAffected,
	})
	if RowsAffected > 0 {
		common.Add_log(fmt.Sprintf("设置线上诊室状态，ID：%d，%s", id, tips), r)
	}
}

// 线上诊室添加
func Rtc_room_add(w http.ResponseWriter, r *http.Request) {
	api_id := 151 //与添加线上诊室的api_id同权限
	session, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	department_id := r.FormValue("department_id")
	record_id := r.FormValue("record_id")
	pat_pro_id := r.FormValue("pat_pro_id")
	doc_id := r.FormValue("doc_id")
	asst_id := session.Values["id"]
	scheduled_time := r.FormValue("scheduled_time")
	if department_id == "" || record_id == "" || pat_pro_id == "" || doc_id == "" || asst_id == "" || scheduled_time == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "参数不能为空",
		})
		return
	}
	// 时间必须得大于当前时间30分钟
	// now := time.Now()
	// scheduled_time_t, err := time.ParseInLocation("2006-01-02 15:04:05", scheduled_time, time.Local)
	// if err != nil {
	// 	common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
	// 		"code": 500,
	// 		"msg":  "预约时间格式错误",
	// 	})
	// 	return
	// }
	// if scheduled_time_t.Before(now) || scheduled_time_t.Sub(now) < 30*time.Minute {
	// 	common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
	// 		"code": 500,
	// 		"msg":  "预约时间必须大于当前时间，且至少30分钟",
	// 	})
	// 	return
	// }
	sql := "SELECT count(id)count from rtc_room where pat_pro_id = ? and status = 0"
	var count int
	err := database.GetOne(sql, &count, pat_pro_id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "查询当前用户问诊数据时失败",
			"err":  err.Error(),
		})
		return
	}
	if count > 0 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "当前用户有未完成的问诊数据",
			"data": count,
		})
		return
	}
	// 获取用户帐号ID - 其实这个加不加无所大谓
	sql = "SELECT pat_id FROM patient_records WHERE id = ?"
	var pat_id int
	err = database.GetOne(sql, &pat_id, record_id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "查询失败",
			"err":  err.Error(),
		})
	}
	// 添加数据

	sql1 := "insert into rtc_room (department_id,record_id,pat_pro_id,doc_id,asst_id,scheduled_time,pat_id) values (?,?,?,?,?,?,?)"
	sql2 := "update patient_records set status = 1 where id = ?"

	sqls := []database.SQLExec{
		{Query: sql2,
			Args: []interface{}{
				record_id,
			}},
		{Query: sql1,
			Args: []interface{}{
				department_id, record_id, pat_pro_id, doc_id, asst_id, scheduled_time, pat_id,
			}},
	}
	RowsAffected, err := database.ExecuteTransaction_with_affectedRows(sqls)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据添加失败",
			"err":  err.Error(),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":         200,
		"msg":          "数据添加成功",
		"RowsAffected": RowsAffected,
	})
	if RowsAffected > 0 {
		common.Add_log(fmt.Sprintf("添加线上诊室，科室ID：%s，病历ID：%s，患者档案ID：%s，医生ID：%s，医助ID：%d，预约时间：%s", department_id, record_id, pat_pro_id, doc_id, asst_id, scheduled_time), r)
	}
}

// 删除线上诊室
func Rtc_room_del(w http.ResponseWriter, r *http.Request) {
	api_id := 152
	session, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	id, err := common.CheckInt(r.FormValue("id"))
	if id < 1 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]any{
			"code": 500,
			"msg":  "房间id不能为空",
		})
		return
	}
	// 先判断该用户对此问诊数据有没有操作权限，顺便查出来对应病历ID
	type RtcRoom struct {
		Record_id int `db:"record_id"` // 病历ID
		Asst_id   int `db:"asst_id"`   // 医助ID
	}
	sql := "select record_id,asst_id from rtc_room where id = ?"
	var rtcroom RtcRoom
	err = database.GetRow(sql, &rtcroom, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]any{
			"code": 500,
			"msg":  "未找到诊室数据",
			"err":  err.Error(),
		})
		return
	}
	if session.Values["role_ids"] != "1" && session.Values["id"] != rtcroom.Asst_id {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]any{
			"code": 500,
			"msg":  "您对该诊室无操作权限",
		})
		return
	}
	// 删除ROOM数据
	sql = "DELETE FROM rtc_room WHERE id = ?"
	result, err := database.Query(sql, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]any{
			"code": 500,
			"msg":  "删除失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, id),
		})
		return
	}
	//更新病历表
	sql = "select count(id) from rtc_room where record_id = ? and status = 0"
	var count int
	err = database.GetOne(sql, &count, rtcroom.Record_id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]any{
			"code": 500,
			"msg":  "删除诊室后，查询剩余诊室失败",
			"err":  err.Error(),
		})
		return
	}
	if count == 0 {
		sql = "update patient_records set status = 0 where id = ?"
		result, err := database.Query(sql, rtcroom.Record_id)
		if err != nil {
			common.JSONResponse(w, http.StatusInternalServerError, map[string]any{
				"code": 500,
				"msg":  "更新病历失败",
				"err":  err.Error(),
			})
			return
		}
		RowsAffected, _ := result.RowsAffected()
		common.JSONResponse(w, http.StatusOK, map[string]any{
			"code":         200,
			"msg":          "删除成功",
			"RowsAffected": RowsAffected,
		})
		if RowsAffected > 0 {
			common.Add_log(fmt.Sprintf("删除线上诊室，ID：%d，更新病历为【待建诊室】成功，ID：%d", id, rtcroom.Record_id), r)
		}
	} else {
		RowsAffected, _ := result.RowsAffected()
		common.JSONResponse(w, http.StatusOK, map[string]any{
			"code":         200,
			"msg":          "删除成功",
			"RowsAffected": RowsAffected,
		})
		if RowsAffected > 0 {
			common.Add_log(fmt.Sprintf("删除线上诊室成功，ID：%d，未更新病历状态，因为还有剩余诊室%d个", id, count), r)
		}
	}
}

// 判断指定病历是否已绑定订单 - 弃用
func Order_exist_by_record(w http.ResponseWriter, r *http.Request) {
	api_id := 42 //与Order_add同权限
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	record_id := r.FormValue("record_id")
	if record_id == "" {
		w.Write([]byte("record_id can not be empty"))
		return
	}
	sql := "SELECT ord_id from patient_records where id = ?"
	var ord_id int
	err := database.GetOne(sql, &ord_id, record_id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "查询失败",
			"err":  err.Error(),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "查询成功",
		"data": ord_id,
		// "sql":  common.DebugSql(sql, record_id),
	})
}

// 判断指定病历是否存在未完成的线上诊室
func Rtc_room_exist_by_record(w http.ResponseWriter, r *http.Request) {
	api_id := 151 //与添加线上诊室的api_id同权限
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	record_id := r.FormValue("record_id")
	if record_id == "" {
		w.Write([]byte("record_id can not be empty"))
		return
	}
	sql := "SELECT count(id)count from rtc_room where record_id = ? and `status` = 0"
	var count int
	err := database.GetOne(sql, &count, record_id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "查询失败",
			"err":  err.Error(),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "查询成功",
		"data": count,
	})
}

// 查看当前诊室排班情况
func Rtc_room_schedule(w http.ResponseWriter, r *http.Request) {
	api_id := 151 //与添加线上诊室的api_id同权限
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	doc_id := r.FormValue("doc_id")
	scheduled_time1 := r.FormValue("scheduled_time1")
	scheduled_time2 := r.FormValue("scheduled_time2")
	// 结构体
	type RtcRoom struct {
		ID             int    `db:"id"`             // 房间ID
		Pat_pro_id     int    `db:"pat_pro_id"`     // 患者档案ID
		Status         int    `db:"status"`         // 房间状态
		Name           string `db:"name"`           // 患者姓名
		Phone          string `db:"phone"`          // 患者电话
		Scheduled_time string `db:"scheduled_time"` // 预约时间
		Record_id      int    `db:"record_id"`      // 病历ID
		Asst_id        int    `db:"asst_id"`        // 医助ID
	}
	// 查询数据
	sql := "SELECT a.id,a.record_id,a.asst_id,a.pat_pro_id,a.status,a.scheduled_time,b.name,b.phone FROM rtc_room as a left join patient_profile as b on a.pat_pro_id = b.id WHERE a.doc_id = ? and a.scheduled_time between ?  AND ?"
	var rtc_rooms []RtcRoom
	err := database.GetAll(sql, &rtc_rooms, doc_id, scheduled_time1, scheduled_time2)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "查询失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, doc_id, scheduled_time1, scheduled_time2),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "查询成功",
		"data": rtc_rooms,
		// "sql":  common.DebugSql(sql, doc_id, scheduled_time1, scheduled_time2),
	})
}

// 根据病历ID获取用户收货地址 - 地址已转至订单表
// func Patient_profile_get_address_by_record_id(w http.ResponseWriter, r *http.Request) {
// 	api_id := 42 //与Order_add同权限
// 	_, isLogin := common.Check_Perm(w, r, api_id)
// 	if !isLogin {
// 		return
// 	}
// 	record_id := r.FormValue("record_id")
// 	if record_id == "" {
// 		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
// 			"code": 500,
// 			"msg":  "病历ID不能为空",
// 		})
// 	}
// 	// 1：根据病历ID获取用户主帐号（patient_account）的ID
// 	sql := "SELECT pat_id FROM patient_records WHERE id = ?"
// 	var pat_id int
// 	err := database.GetOne(sql, &pat_id, record_id)
// 	if err != nil {
// 		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
// 			"code": 500,
// 			"msg":  "查询失败",
// 			"err":  err.Error(),
// 		})
// 	}
// 	// 2：根据用户主帐号ID获取用户收货地址
// 	type Address struct {
// 		Id      int    `db:"id"`
// 		Address string `db:"address"`
// 	}
// 	sql = "SELECT id,address FROM patient_profile where pid = ? and relation = 0 limit 1"
// 	var address Address
// 	err = database.GetRow(sql, &address, pat_id)
// 	if err != nil {
// 		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
// 			"code": 500,
// 			"msg":  "查询失败",
// 			"err":  err.Error(),
// 		})
// 	}
// 	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
// 		"code": 200,
// 		"msg":  "查询成功",
// 		"data": address,
// 	})
// }

// 判断若干病历的医生ID、医助ID（售前）是否完全一致
func Order_exist_by_record_check(w http.ResponseWriter, r *http.Request) {
	api_id := 42 //与Order_add同权限
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	record_ids := r.FormValue("record_ids")
	if record_ids == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "病历ID不能为空",
		})
		return
	}
	type Record struct {
		Doc_id  int `db:"doc_id"`
		Asst_id int `db:"asst_id"`
	}
	sql := "SELECT doc_id,asst_id FROM patient_records WHERE id IN (" + record_ids + ")"
	var records []Record
	err := database.GetAll(sql, &records)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "查询失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql),
		})
		return
	}

	// 检查是否有记录
	if len(records) == 0 {
		common.JSONResponse(w, http.StatusOK, map[string]interface{}{
			"code": 200,
			"msg":  "查询成功",
			"data": false,
			"sql":  common.DebugSql(sql),
		})
		return
	}

	// 获取第一条记录的值作为参考
	firstDoc := records[0].Doc_id
	firstAsst := records[0].Asst_id

	// 检查所有记录是否都与第一条记录相同
	allMatch := true
	for _, record := range records {
		if record.Doc_id != firstDoc || record.Asst_id != firstAsst {
			allMatch = false
			break
		}
	}

	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "查询成功",
		"data": allMatch,
		"sql":  common.DebugSql(sql),
	})
}

// 订单相关的赠品数据，与订单查看同权
func Order_gift_data(w http.ResponseWriter, r *http.Request) {
	api_id := 56
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	id, err := common.CheckInt(r.FormValue("id"))
	if id < 1 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "订单ID不能为空",
		})
		return
	}
	type OrderGift struct {
		ID         int    `db:"id"`
		Ord_id     int    `db:"ord_id"`
		Wh_gift_id int    `db:"wh_gift_id"`
		Nums       int    `db:"nums"`
		Status     int    `db:"status"`
		Time       string `db:"time"`
		Gift_id    int    `db:"gift_id"`
		Price      string `db:"price"`
		Name       string `db:"name"`
	}
	sql := "SELECT a.id,a.ord_id,a.wh_gift_id,a.nums,a.status,a.time,b.gift_id,b.price,c.name from orders_gifts as a left join warehouse_gifts as b on a.wh_gift_id = b.id left join gifts as c on c.id = b.gift_id WHERE a.ord_id = ?"
	var order_gift []OrderGift
	err = database.GetAll(sql, &order_gift, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "查询失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, id),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "查询成功",
		"data": order_gift,
	})
}

// 订单相关的查用户的数据，与订单查看同权
func Patient_profile_get_patient_ids_by_record_ids(w http.ResponseWriter, r *http.Request) {
	// api_id := 56 //与订单展示同权限
	// 重点标记 - 用户基础信息的调用，先暂时将权限放大，记录一下，将来要将权限放小，哪些角色允许读取用户的详细信息需要等需求下来再议
	api_id := config.NormalPerm
	session, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	record_ids := r.FormValue("record_ids")
	if record_ids == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "病历ID不能为空",
		})
		return
	}
	type PatientProfile struct {
		ID              int    `db:"id"`
		Phone           string `db:"phone"`           // 手机号码
		Relation        string `db:"relation"`        // 与主患者的家庭关系
		Patient_from    string `db:"patient_from"`    // 患者来源
		Name            string `db:"name"`            // 患者姓名
		Sex             int    `db:"sex"`             // 性别：0女1男
		Weixin          string `db:"weixin"`          // 微信号码
		Born_date       string `db:"born_date"`       // 出生日期
		Level           string `db:"level"`           // 患者等级
		Ins_card_num    string `db:"ins_card_num"`    // 医保卡
		Idcard          string `db:"idcard"`          // 身份证号
		Height          string `db:"height"`          // 身高
		Weight          string `db:"weight"`          // 体重
		Address         string `db:"address"`         // 详细地址
		Medical_history string `db:"medical_history"` // 既往病史
		Allergies       string `db:"allergies"`       // 过敏史
		Chief_complaint string `db:"chief_complaint"` // 主诉
		Customer_notes  string `db:"customer_notes"`  // 患者备注
	}

	sql := "SELECT id, name, phone, born_date, address, relation, patient_from, sex, level, weixin, ins_card_num, idcard, height, weight, medical_history, allergies, chief_complaint, customer_notes FROM patient_profile WHERE id IN (SELECT pat_pro_id FROM patient_records WHERE id IN (" + record_ids + "))"
	var patient_profiles []PatientProfile
	err := database.GetAll(sql, &patient_profiles)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "查询失败",
			"err":  err.Error(),
		})
		return
	}
	if len(patient_profiles) == 0 {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "查询失败，请确认是否有关联数据被删除，如用户数据",
			"sql":  common.DebugSql(sql, record_ids),
		})
		return
	}
	// 根据权限处理手机号显示方式 - GetAll
	if session.Values["role_ids"] != "1" {
		for i := range patient_profiles {
			phone := patient_profiles[i].Phone
			if len(phone) == 11 || len(phone) == 13 {
				patient_profiles[i].Phone = phone[:5] + "****" + phone[len(phone)-2:]
			} else {
				patient_profiles[i].Phone = "手机号码格式错误"
			}
		}
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "查询成功",
		"data": patient_profiles,
	})
}

// 根据药品名模糊匹配药品ID与药品名（多行，GETALL）
func Drug_name2id(w http.ResponseWriter, r *http.Request) {
	api_id := config.NormalPerm
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	name := r.FormValue("key")
	if name == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "药品名称不能为空",
		})
		return
	}
	type Drug struct {
		ID   int    `db:"id"`
		Name string `db:"name"`
	}
	sql := "SELECT id,name FROM drug WHERE name LIKE ?"
	var drugs []Drug
	err := database.GetAll(sql, &drugs, "%"+name+"%")
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "查询失败",
			"err":  err.Error(),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"data": drugs,
	})
}

// 处方所携带药品接口
func Prescription_drug(w http.ResponseWriter, r *http.Request) {
	api_id := config.NormalPerm
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	pre_id := r.FormValue("pre_id")
	if pre_id == "" {
		w.Write([]byte("id can not be empty"))
		return
	}
	type PrescriptionDrug struct {
		ID          int    `db:"id"`
		Name        string `db:"name"`
		Pre_id      int    `db:"pre_id"`
		Drug_id     int    `db:"drug_id"`
		Wh_drug_id  int    `db:"wh_drug_id"`
		Nums        int    `db:"nums"`
		Quantity    int    `db:"quantity"`
		Create_time string `db:"create_time"`
		Price       string `db:"price"`
		Wh_quantity int    `db:"wh_quantity"`
	}
	sql := `

			SELECT
			a.id,
			a.pre_id,
			a.drug_id,
			a.wh_drug_id,
			a.nums,
			a.quantity,
			a.create_time,
			b.name,
			ifnull(c.price,0) AS price,
			ifnull(c.quantity,0) AS wh_quantity
			FROM
			prescription_drug AS a
			LEFT JOIN drug AS b ON a.drug_id = b.id
			LEFT JOIN warehouse_drug AS c ON c.id = a.wh_drug_id
			WHERE
			a.pre_id = ?

	`
	var prescription_drugs []PrescriptionDrug
	err := database.GetAll(sql, &prescription_drugs, pre_id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "查询失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, pre_id),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"data": prescription_drugs,
		"sql":  common.DebugSql(sql, pre_id),
	})
}

// 处方调剂-该流程涉及出库，库存改变
func Prescription_dispense_save(w http.ResponseWriter, r *http.Request) {
	api_id := 157
	session, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}

	// 1. 验证处方ID
	pre_id, err := common.CheckInt(r.FormValue("prescription_id"))
	if pre_id < 1 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "处方ID不能为空",
		})
		return
	}

	// 2. 解析药品数据
	type DrugData struct {
		ID         int    `json:"id"`
		Drug_id    int    `json:"drug_id"`
		Wh_drug_id int    `json:"wh_drug_id"`
		Nums       int    `json:"nums"`
		Process    string `json:"process"`
	}

	drugsJson := r.FormValue("drugs")
	if drugsJson == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "药品数据不能为空",
		})
		return
	}

	var drugs []DrugData
	if err := json.Unmarshal([]byte(drugsJson), &drugs); err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "药品数据格式错误",
			"err":  err.Error(),
		})
		return
	}

	// 3. 构建事务查询
	queries := make([]database.TxQuery, 0, len(drugs)*3) // 每个药品可能有3个操作：更新处方药品、更新库存、添加日志

	// 4. 验证并收集每个药品的数据
	for _, drug := range drugs {
		// 4.1 检查库存
		var currentStock float64
		if err := database.GetOne("SELECT quantity FROM warehouse_drug WHERE id = ? FOR UPDATE", &currentStock, drug.Wh_drug_id); err != nil {
			common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
				"code": 500,
				"msg":  fmt.Sprintf("查询药品ID %d 的库存失败", drug.Wh_drug_id),
				"err":  err.Error(),
			})
			return
		}

		if float64(drug.Nums) > currentStock {
			common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
				"code": 500,
				"msg":  fmt.Sprintf("药品ID %d 库存不足，当前库存: %.2f, 需要数量: %d", drug.Wh_drug_id, currentStock, drug.Nums),
			})
			return
		}

		// 4.2 更新处方药品信息
		queries = append(queries, database.TxQuery{
			Query: "UPDATE prescription_drug SET nums = ?, process_type = ? WHERE id = ? AND pre_id = ?",
			Args:  []interface{}{drug.Nums, drug.Process, drug.ID, pre_id},
		})

		// 4.3 更新库存
		queries = append(queries, database.TxQuery{
			Query: "UPDATE warehouse_drug SET quantity = quantity - ? WHERE id = ?",
			Args:  []interface{}{drug.Nums, drug.Wh_drug_id},
		})

		// 4.4 添加库存日志
		newStock := currentStock - float64(drug.Nums)
		queries = append(queries, database.TxQuery{
			Query: "INSERT INTO warehouse_drug_log(pid,user_id,kind,change_data,old_data,new_data,notes) VALUES(?,?,?,?,?,?,?)",
			Args: []interface{}{
				drug.Wh_drug_id,
				session.Values["id"],
				0, // 0表示出库
				drug.Nums,
				currentStock,
				newStock,
				"处方调剂出库",
			},
		})
	}

	// 5. 更新处方状态为已调剂
	queries = append(queries, database.TxQuery{
		Query: "UPDATE prescription SET status = 4 WHERE id = ?",
		Args:  []interface{}{pre_id},
	})

	// 6. 更新病历状态为已完结 - 制成药后，病历使命完成，剩下的属于处方、订单；药入库后，处方使命完结，流程交给订单
	queries = append(queries, database.TxQuery{
		Query: "UPDATE patient_records SET status = 6 WHERE id = (select record_id from prescription where id = ?)",
		Args:  []interface{}{pre_id},
	})

	// 6. 执行事务
	_, affectedRows, err := database.ExecuteTxWithResult(queries)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "处方调剂失败",
			"err":  err.Error(),
		})
		return
	}

	// 7. 返回成功响应
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "处方调剂成功",
	})

	// 8. 记录日志
	common.Add_log(fmt.Sprintf("处方调剂成功，处方ID：%d，影响行数：%d", pre_id, affectedRows), r)
}

// 被驳回的处方申请复审
func Prescription_re_verify(w http.ResponseWriter, r *http.Request) {
	api_id := 161
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}

	// 1. 验证处方ID
	id, err := common.CheckInt(r.FormValue("id"))
	if id < 1 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "处方ID不能为空",
		})
		return
	}

	// 2. 验证该处方是不是被驳回的处方
	sql := "select id from prescription where id = ? and status = 2"
	var hasid int
	err = database.GetOne(sql, &hasid, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "查询失败，该处方可能不存在或不属于被驳回状态",
			"err":  err.Error(),
		})
		return
	}
	// 3.验证该处方有没有异常入库
	sql = "select id from warehouse_finisheddrug where pre_id = ?"
	var has_id int
	err = database.GetOne(sql, &has_id, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "该驳回处方，未在库管环节成品入库",
			"err":  err.Error(),
		})
		return
	}
	// 4. 更新处方状态
	sql = `

		UPDATE prescription set
		verify_1 = 0,
		verify_1_user_id = 0,
		verify_1_desc = 0,
		verify_2 = 0,
		verify_2_user_id = 0,
		verify_2_desc = 0,
		verify_3 = 0,
		verify_3_user_id = 0,
		verify_3_desc = 0,
		verify_4 = 0,
		verify_4_user_id = 0,
		verify_4_desc = 0,
		status = 1
		where id = ?

	`
	result, err := database.Query(sql, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "复审失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, id),
		})
		return
	}
	RowsAffected, _ := result.RowsAffected()
	if RowsAffected == 0 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "处方不存在或未更新",
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "复审申请成功，请等待审核",
	})
}

// 处方审核
func Prescription_verify(w http.ResponseWriter, r *http.Request) {
	api_id := 156
	session, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}

	// 1. 验证处方ID
	id, err := common.CheckInt(r.FormValue("id"))
	if id < 1 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "处方ID不能为空",
		})
		return
	}

	// 2. 验证审核类型
	verify_type, err := common.CheckInt(r.FormValue("verify_type"))
	if err != nil || verify_type < 0 || verify_type > 3 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "审核类型错误",
		})
		return
	}

	// 2.1 状态验证拦截
	if verify_type > 1 { //至少是调剂审核
		// 查询当前处方状态，是否大于3，即：至少是忆调剂
		sql := "SELECT status FROM prescription WHERE id = ? FOR UPDATE"
		var status int
		err := database.GetOne(sql, &status, id)
		if err != nil {
			common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
				"code": 500,
				"msg":  "查询处方状态失败",
				"err":  err.Error(),
			})
			return
		}
		if verify_type == 2 { //调配审核，只有调剂后才可以调配审核
			if status != 4 {
				common.JSONResponse(w, http.StatusBadRequest, map[string]any{
					"code": 500,
					"msg":  "当前操作的期望状态为【已调剂】，状态【" + config.Pre_status[status] + "】不允许执行该操作",
				})
				return
			}
		}
		if verify_type == 3 { //发药审核，审核完才能出库发药，所以审核前的状态是"已入成品库"
			if status != 5 {
				common.JSONResponse(w, http.StatusBadRequest, map[string]any{
					"code": 500,
					"msg":  "当前操作的期望状态为【已入成品库】，状态【" + config.Pre_status[status] + "】不允许执行该操作",
				})
				return
			}
		}
	}

	// 3. 验证审核状态
	verify_status, err := common.CheckInt(r.FormValue("verify_status"))
	if err != nil || (verify_status != 1 && verify_status != 2) {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "审核状态错误",
		})
		return
	}

	// 4. 定义审核字段数组
	verify_fields := []string{"verify_1", "verify_2", "verify_3", "verify_4"}
	verify_field := verify_fields[verify_type]
	verify_desc_field := verify_fields[verify_type] + "_desc"
	verify_user_id_field := verify_fields[verify_type] + "_user_id"

	// 构建更新SQL语句
	var updateFields []string
	var args []any

	// 添加审核状态字段
	updateFields = append(updateFields, fmt.Sprintf("%s = ?", verify_field))
	if verify_status == 1 {
		args = append(args, verify_status)
	} else {
		args = append(args, 2) // 审核未通过标记为2
	}

	// 添加审核人ID字段
	updateFields = append(updateFields, fmt.Sprintf("%s = ?", verify_user_id_field))
	args = append(args, session.Values["id"])

	// 获取当前时间
	currentTime := time.Now().Format("2006-01-02 15:04")

	// 添加审核描述字段
	updateFields = append(updateFields, fmt.Sprintf("%s = ?", verify_desc_field))
	if verify_status == 1 {
		// 审核通过
		args = append(args, fmt.Sprintf("审核通过 - %s", currentTime))
	} else {
		// 审核不通过
		verify_desc := r.FormValue("verify_desc")
		if verify_desc == "" {
			common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
				"code": 500,
				"msg":  "审核未通过原因不能为空",
			})
			return
		}
		args = append(args, fmt.Sprintf("%s - %s", verify_desc, currentTime))
	}

	// 添加整体状态更新逻辑
	if verify_type <= 1 && verify_status == 1 {
		// 当verify_type是0或1，且审核通过时，需要检查另一个verify状态
		updateFields = append(updateFields, `status = CASE
            WHEN verify_1 = 2 OR verify_2 = 2 OR verify_3 = 2 OR verify_4 = 2 THEN 2
            WHEN (? = 0 AND verify_2 = 1) OR (? = 1 AND verify_1 = 1) THEN 3
            ELSE status
        END`)
		args = append(args, verify_type, verify_type)
	} else {
		updateFields = append(updateFields, `status = CASE
            WHEN verify_1 = 2 OR verify_2 = 2 OR verify_3 = 2 OR verify_4 = 2 THEN 2
            ELSE status
        END`)
	}

	// 添加处方ID到参数列表
	args = append(args, id)

	// 构建并执行最终的SQL语句
	sql := fmt.Sprintf("UPDATE prescription SET %s WHERE id = ?", strings.Join(updateFields, ", "))
	result, err := database.Query(sql, args...)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "更新审核状态失败",
			"err":  err.Error(),
		})
		return
	}

	RowsAffected, _ := result.RowsAffected()
	if RowsAffected == 0 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "处方不存在或未更新",
		})
		return
	}

	// 添加审核日志
	if RowsAffected > 0 {
		var logMsg string
		if verify_status == 1 {
			logMsg = fmt.Sprintf("处方审核通过，审核类型：%s，处方ID：%d，审核类型：%s", config.Four_role_verify_name[verify_type], id, config.Foru_role_verify_status[verify_status])
		} else {
			verify_desc := r.FormValue("verify_desc")
			logMsg = fmt.Sprintf("处方审核驳回，审核类型：%s，处方ID：%d，审核类型：%s，驳回原因：%s", config.Four_role_verify_name[verify_type], id, config.Foru_role_verify_status[verify_status], verify_desc)
		}
		common.Add_log(logMsg, r)
		// 新增更新订单状态逻辑 - 当更新类型为发药审核时，检测处方所在订单的所有处方的4个审核状态，如果全是已通过，则更新订单为：可发
		if verify_type == 3 {
			// 1. 先求出订单ID：ord_id
			sql := "SELECT ord_id FROM prescription WHERE id = ?"
			var ord_id int
			err = database.GetOne(sql, &ord_id, id)
			if err != nil {
				common.JSONResponse(w, http.StatusInternalServerError, map[string]any{"code": 500, "msg": "查询订单ID失败", "err": err.Error()})
				return
			}

			// 2. 求出该订单对应处方的所有状态合辑
			sql = "SELECT status FROM prescription WHERE ord_id = ?"
			var prescriptionStatuses []int
			err = database.GetAll(sql, &prescriptionStatuses, ord_id)
			if err != nil {
				common.JSONResponse(w, http.StatusInternalServerError, map[string]any{"code": 500, "msg": "查询处方状态失败", "err": err.Error()})
				return
			}

			// 3. 遍历该处方状态集合，如果有不是5的，则终止程序
			for _, status := range prescriptionStatuses {
				if status != 5 {
					common.JSONResponse(w, http.StatusBadRequest, map[string]any{"code": 500, "msg": "四签章已满，非全通过"})
					return
				}
			}

			// 4. 查找该订单下有几个病历
			sql = "SELECT COUNT(id) as count FROM patient_records WHERE ord_id = ?"
			var recordCount int
			err = database.GetOne(sql, &recordCount, ord_id)
			if err != nil {
				common.JSONResponse(w, http.StatusInternalServerError, map[string]any{"code": 500, "msg": "查询病历数量失败", "err": err.Error()})
				return
			}

			// 对比"病历数量"与上面的"处方状态集合"的数量，如果相等，则执行下面代码
			if recordCount == len(prescriptionStatuses) {
				sql = "UPDATE orders SET status = 1 WHERE id = ?"
				result, err := database.Query(sql, ord_id)
				if err != nil {
					common.JSONResponse(w, http.StatusInternalServerError, map[string]any{"code": 500, "msg": "更新订单状态失败", "err": err.Error()})
					return
				}
				// 记录日志：查得当前处方隶属的订单全部为已入成品库，更改处方状态为：可发货
				RowsAffected, _ := result.RowsAffected()
				common.Add_log(fmt.Sprintf("查得当前处方 %d 隶属的订单 %d 旗下所有处方均为已入成品库，故更改当前订单状态为：可发货，影响行数 %d", id, ord_id, RowsAffected), r)
			}
		}
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "审核状态更新成功",
	})
}

// 处方审核列表 155
func Prescription_list_verify(w http.ResponseWriter, r *http.Request) {
	api_id := 155
	// 处方字段如下：id doc_id asst_id pat_id diagnosis tx_plan status create_time
	session, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	// 带分页的查询
	attSql := ""
	if session.Values["role_ids"] == "3" {
		attSql += " AND asst_id =  " + strconv.Itoa(session.Values["id"].(int))
	} else if session.Values["role_ids"] == "4" {
		attSql += " AND doc_id =  " + strconv.Itoa(session.Values["id"].(int))
	} else if session.Values["role_ids"] == "9" {
		attSql += " AND asst_id = " + strconv.Itoa(session.Values["id"].(int))
	}
	doc_id, _ := common.CheckInt(r.FormValue("doc_id"))
	if doc_id > 0 {
		attSql += " AND doc_id = " + strconv.Itoa(doc_id)
	}
	asst_id, _ := common.CheckInt(r.FormValue("asst_id"))
	if asst_id > 0 {
		attSql += " AND asst_id = " + strconv.Itoa(asst_id)
	}
	pat_id, _ := common.CheckInt(r.FormValue("pat_id"))
	if pat_id > 0 {
		attSql += " AND pat_id = " + strconv.Itoa(pat_id)
	}
	pat_pro_id, _ := common.CheckInt(r.FormValue("pat_pro_id"))
	if pat_pro_id > 0 {
		attSql += " AND pat_pro_id = " + strconv.Itoa(pat_pro_id)
	}
	diagnosis, _ := common.CheckStr(r.FormValue("diagnosis"))
	if diagnosis != "" {
		attSql += " AND diagnosis LIKE '%" + diagnosis + "%'"
	}
	tx_plan, _ := common.CheckStr(r.FormValue("tx_plan"))
	if tx_plan != "" {
		attSql += " AND tx_plan LIKE '%" + tx_plan + "%'"
	}
	status := r.FormValue("status")
	if status != "" {
		if strings.Contains(status, ",") {
			attSql += " AND status IN (" + status + ")"
		} else {
			attSql += " AND status = " + status
		}
	}
	// 分页参数
	limit, err := strconv.Atoi(r.FormValue("limit"))
	if err != nil || limit < 10 {
		limit = 10
	}
	page, err := strconv.Atoi(r.FormValue("page"))
	if err != nil || page < 1 {
		page = 1
	}
	offset := (page - 1) * limit
	// 总数
	var count int
	sql := "SELECT COUNT(id) FROM prescription WHERE 1 " + attSql
	err = database.GetOne(sql, &count)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "读取数据总数失败",
		})
		return
	}
	// 数据查询
	type Prescription struct { // 处方
		ID          int    `db:"id"`
		Doc_id      int    `db:"doc_id"`
		Asst_id     int    `db:"asst_id"`
		Pat_id      int    `db:"pat_id"`
		Pat_pro_id  int    `db:"pat_pro_id"`
		Diagnosis   string `db:"diagnosis"`
		Tx_plan     string `db:"tx_plan"`
		Status      int    `db:"status"`
		Verify_1    int    `db:"verify_1"`
		Verify_2    int    `db:"verify_2"`
		Verify_3    int    `db:"verify_3"`
		Verify_4    int    `db:"verify_4"`
		Create_time string `db:"create_time"`
	}
	sql = "SELECT id,doc_id,asst_id,pat_id,pat_pro_id,diagnosis,tx_plan,status,verify_1,verify_2,verify_3,verify_4,create_time FROM prescription WHERE 1 " + attSql + " ORDER BY id DESC limit ? OFFSET ?"
	var prescription []Prescription
	err = database.GetAll(sql, &prescription, limit, offset)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败",
			"err":  err.Error(),
			// "sql":  common.DebugSql(sql, limit, offset),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":  200,
		"msg":   "ok",
		"data":  prescription,
		"count": count,
		"sql":   common.DebugSql(sql, limit, offset),
	})
}

// 订单订金（预付款）审核
func Order_pay_review_pre(w http.ResponseWriter, r *http.Request) {
	api_id := 158
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	id, err := common.CheckInt(r.FormValue("id"))
	if err != nil || id < 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "ID不能为空",
		})
		return
	}
	sql := "UPDATE orders SET pay_review_status = 1 WHERE id = ?"
	result, err := database.Query(sql, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "订金审核数据更新失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, id),
		})
		return
	}
	RowsAffected, _ := result.RowsAffected()
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":         200,
		"msg":          "订金审核成功：" + strconv.Itoa(int(RowsAffected)) + "条",
		"RowsAffected": RowsAffected,
		"sql":          common.DebugSql(sql, id),
	})
	if RowsAffected > 0 {
		common.Add_log(fmt.Sprintf("订单审核成功，订单ID为%d", id), r)
	}
}

// 订单尾款更新
func Order_pay_update_final(w http.ResponseWriter, r *http.Request) {
	api_id := 178
	session, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	id, err := common.CheckInt(r.FormValue("id"))
	if err != nil || id < 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]any{
			"code": 500,
			"msg":  "ID不能为空",
		})
		return
	}
	// 金额类型 1全额 2非全额
	final_is_full := r.FormValue("final_is_full")
	if final_is_full == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]any{
			"code": 500,
			"msg":  "请选择金额类型",
		})
		return
	}
	// 金额
	final_money, err := common.CheckFloat(r.FormValue("final_money"))
	if err != nil || final_money < 0 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]any{
			"code": 500,
			"msg":  "尾款金额不能为空",
		})
		return
	}
	// 收款截图
	final_pay_pic := r.FormValue("final_pay_pic")
	if final_pay_pic == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]any{
			"code": 500,
			"msg":  "请上传收款截图",
		})
		return
	}
	// 备注
	remark := r.FormValue("remark")
	// 	订单状态：已收；钱状态：已付订金
	type Order struct {
		ID               int     `db:"id"`
		Total_money      float64 `db:"total_money"`
		Pre_pay          float64 `db:"pre_pay"`
		Final_pay        float64 `db:"final_pay"`
		Final_pay_remark string  `db:"final_pay_remark"`
	}
	sql := "SELECT id,total_money,pre_pay,final_pay,final_pay_remark FROM orders WHERE id = ? and status = 3 and pay_review_status = 2"
	var order Order
	err = database.GetRow(sql, &order, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]any{
			"code": 500,
			"msg":  "当前订单状态不符合更新尾款的条件",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, id),
		})
		return
	}
	// 看当前订单的尾款数据
	if order.Final_pay > 0 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "更新异常，当前订单已有尾款数据",
		})
		return
	}
	// 计算尾款
	final_money_db := order.Total_money - order.Pre_pay
	if final_money_db <= 0 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "尾款金额数据不正确",
		})
		return
	}
	// 操作逻辑
	if final_is_full == "1" {
		// 全额支付
		if final_money_db != final_money {
			common.JSONResponse(w, http.StatusBadRequest, map[string]any{
				"code": 500,
				"msg":  "全额支付场景下，尾款数额必须等于实际应付尾款额度",
			})
			return
		}
		remark = "全额支付尾款"
		sql := "UPDATE orders SET final_pay = ?,final_pay_remark = ?,  final_pay_pic = ?, pay_review_status = 3,final_is_full=1 WHERE id = ?"
		result, err := database.Query(sql, final_money, order.Final_pay_remark+"###"+remark+"|"+strconv.Itoa(session.Values["id"].(int))+"|"+common.GetTime(), final_pay_pic, id)
		if err != nil {
			common.JSONResponse(w, http.StatusBadRequest, map[string]any{
				"code": 500,
				"msg":  "更新尾款失败，请稍候再试",
				"sql":  common.DebugSql(sql, final_money, final_pay_pic, id),
			})
			return
		}
		RowsAffected, _ := result.RowsAffected()
		common.JSONResponse(w, http.StatusOK, map[string]interface{}{
			"code":         200,
			"msg":          "尾款更新成功：" + strconv.Itoa(int(RowsAffected)) + "条",
			"RowsAffected": RowsAffected,
			"sql":          common.DebugSql(sql, final_money, final_pay_pic, id),
		})
		if RowsAffected > 0 {
			common.Add_log(fmt.Sprintf("尾款更新成功（全额），订单D%d", id), r)
		}
	} else if final_is_full == "2" {
		// 非全额支付
		if final_money >= final_money_db {
			common.JSONResponse(w, http.StatusBadRequest, map[string]any{
				"code": 500,
				"msg":  "非全额支付场景下，尾款数额不能大于等于实际应付尾款额度",
			})
			return
		}
		sql := "UPDATE orders SET final_pay = ?, final_pay_remark = ?, final_pay_pic = ?, pay_review_status = 3,final_is_full=2 WHERE id = ?"
		result, err := database.Query(sql, final_money, order.Final_pay_remark+"###"+remark+"|"+strconv.Itoa(session.Values["id"].(int))+"|"+common.GetTime()+"|"+strconv.FormatFloat(final_money, 'f', 2, 64), final_pay_pic, id)
		if err != nil {
			common.JSONResponse(w, http.StatusBadRequest, map[string]any{
				"code": 500,
				"msg":  "更新总额失败，请稍候再试",
				"sql":  common.DebugSql(sql, final_money, remark, final_pay_pic, id),
			})
			return
		}
		RowsAffected, _ := result.RowsAffected()
		common.JSONResponse(w, http.StatusOK, map[string]interface{}{
			"code":         200,
			"msg":          "总额更新成功：" + strconv.Itoa(int(RowsAffected)) + "条",
			"RowsAffected": RowsAffected,
			"sql":          common.DebugSql(sql, final_money, remark, final_pay_pic, id),
		})
		if RowsAffected > 0 {
			common.Add_log(fmt.Sprintf("尾款更新成功（非全额），明细：应付尾款%.2f，实付尾款%.2f，详见订单D%d的备注与截图", final_money_db, final_money, id), r)
		}
	}
}

// 尾款明细
func Order_pay_detail(w http.ResponseWriter, r *http.Request) {
	api_id := 159 //在审核尾款时所显示出来的数据，所以权限同尾款审核权限
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	id, err := common.CheckInt(r.FormValue("id"))
	if err != nil || id < 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]any{
			"code": 500,
			"msg":  "ID不能为空",
		})
		return
	}
	type Order struct {
		ID                int     `db:"id"`
		Final_pay_pic     string  `db:"final_pay_pic"`
		Total_money       float64 `db:"total_money"`
		Pre_pay           float64 `db:"pre_pay"`
		Pay_review_status int     `db:"pay_review_status"`
		Final_pay_remark  string  `db:"final_pay_remark"`
		Final_pay         float64 `db:"final_pay"`
		Final_is_full     int     `db:"final_is_full"`
	}
	sql := "select id,final_pay_pic,total_money,pre_pay,final_pay_remark,final_pay,pay_review_status,final_is_full from orders where id = ?"
	var order Order
	err = database.GetRow(sql, &order, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]any{
			"code": 500,
			"msg":  "没有查到满足条件的数据",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, id),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]any{
		"code": 200,
		"msg":  "ok",
		"data": order,
	})
}

// 订单尾款审核
func Order_pay_review_final(w http.ResponseWriter, r *http.Request) {
	// 尾款审核状态：0订金待审 1已付订金 2尾款待付 3尾款待审 4已付尾款
	api_id := 159
	session, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	id, err := common.CheckInt(r.FormValue("id"))
	if err != nil || id < 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]any{
			"code": 500,
			"msg":  "ID不能为空",
		})
		return
	}
	// 判断当前订单的金额状态是否为2，不为2的话证明不应该审核尾款，需要阻止
	type Patient_account struct {
		Pay_review_status int `db:"pay_review_status"`
		Is_transfer       int `db:"is_transfer"`
	}
	var patient_account Patient_account
	sql := `
		SELECT
			a.pay_review_status,
			b.is_transfer
		FROM
			orders AS a
			LEFT JOIN patient_account AS b ON a.pat_id = b.id
		WHERE
			a.id = ?
    `
	err = database.GetRow(sql, &patient_account, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "读取数据失败",
		})
		return
	}
	if patient_account.Pay_review_status != 3 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "当前订单金额审核状态不是【尾款待审】，不应审核尾款",
		})
		return
	}
	pay_review_status_get := r.FormValue("pay_review_status")
	if pay_review_status_get != "4" && pay_review_status_get != "2" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]any{
			"code": 500,
			"msg":  "审核状态参数错误",
		})
		return
	}
	review_remark := r.FormValue("review_remark")
	var att_sql string
	if pay_review_status_get == "2" {
		// 审核未过
		if review_remark == "" {
			common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
				"code": 500,
				"msg":  "审核未过时，需要填写审核未过的原因。",
			})
			return
		}
		review_remark = "###" + review_remark + "|" + strconv.Itoa(session.Values["id"].(int)) + "|" + common.GetTime()
		att_sql = " ,final_pay = 0 " //审核未过，重置尾款要求重新填写
	} else if pay_review_status_get == "4" {

		// 审核通过，在此还不能核销该订单，因为还涉及到转移至售后人员
		var review_remark_passed string
		if review_remark == "" {
			review_remark_passed = "尾款审核通过"
		} else {
			review_remark_passed = "尾款审核通过 - " + review_remark
		}
		review_remark = "###" + review_remark_passed + "|" + strconv.Itoa(session.Values["id"].(int)) + "|" + common.GetTime()
		if patient_account.Is_transfer == 1 {
			att_sql = " ,status = 8,finish_time=now() " //当该订单对应患者帐号已转过售后，无需再转售后，审核通过，核销订单
		} else {
			att_sql = " ,status = 6 " //当该订单未绑定医助ID（兼容以前未将医助ID写入用户帐号表时的场景），或者绑定的医助ID是售前时
		}
	} else {
		fmt.Println("尾款审核参数出错:", pay_review_status_get)
		return
	}
	sql = "UPDATE orders SET pay_review_status = ?, final_pay_remark = CONCAT(final_pay_remark, ?) " + att_sql + " WHERE id = ?"
	result, err := database.Query(sql, pay_review_status_get, review_remark, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]any{
			"code": 500,
			"msg":  "尾款审核数据更新失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, pay_review_status_get, review_remark, id),
		})
		return
	}
	RowsAffected, _ := result.RowsAffected()
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":         200,
		"msg":          "尾款审核成功：" + strconv.Itoa(int(RowsAffected)) + "条",
		"RowsAffected": RowsAffected,
	})
	if RowsAffected > 0 {
		common.Add_log(fmt.Sprintf("尾款审核成功，订单ID为%d", id), r)
	}
}

// 根据处方IDS查询其对应的各处方状态,如果查不到对应处方,证明处方没开具,返回状态0
func Get_pre_status_by_record_ids(w http.ResponseWriter, r *http.Request) {
	api_id := config.NormalPerm
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	record_ids, err := common.CheckStr(r.FormValue("record_ids"))
	if err != nil || record_ids == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "病历IDS不能为空",
		})
		return
	}
	type Prescription struct {
		ID        int `db:"id"`
		Record_id int `db:"record_id"`
		Status    int `db:"status"`
	}
	var prescription []Prescription
	sql := "SELECT id,record_id,status FROM prescription WHERE record_id IN (" + record_ids + ")"
	err = database.GetAll(sql, &prescription)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败",
			"err":  err.Error(),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "ok",
		"data": prescription,
	})
}

// 处方（驳回）列表 155
func Prescription_list_reject(w http.ResponseWriter, r *http.Request) {
	api_id := 155
	// 处方字段如下：id doc_id asst_id pat_id diagnosis tx_plan status create_time
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	// 带分页的查询
	attSql := ""
	doc_id, _ := common.CheckInt(r.FormValue("doc_id"))
	if doc_id > 0 {
		attSql += " AND doc_id = " + strconv.Itoa(doc_id)
	}
	asst_id, _ := common.CheckInt(r.FormValue("asst_id"))
	if asst_id > 0 {
		attSql += " AND asst_id = " + strconv.Itoa(asst_id)
	}
	pat_pro_id, _ := common.CheckInt(r.FormValue("pat_pro_id"))
	if pat_pro_id > 0 {
		attSql += " AND pat_pro_id = " + strconv.Itoa(pat_pro_id)
	}
	// 分页参数
	limit, err := strconv.Atoi(r.FormValue("limit"))
	if err != nil || limit < 10 {
		limit = 10
	}
	page, err := strconv.Atoi(r.FormValue("page"))
	if err != nil || page < 1 {
		page = 1
	}
	offset := (page - 1) * limit
	// 总数
	var count int
	sql := "SELECT COUNT(id) FROM prescription WHERE status = 2 " + attSql
	err = database.GetOne(sql, &count)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "读取数据总数失败",
		})
		return
	}
	// 数据查询

	type Prescription struct {
		ID            int    `db:"id"`
		Doc_id        int    `db:"doc_id"`
		Asst_id       int    `db:"asst_id"`
		Pat_pro_id    int    `db:"pat_pro_id"`
		Verify_1      int    `db:"verify_1"`
		Verify_2      int    `db:"verify_2"`
		Verify_3      int    `db:"verify_3"`
		Verify_4      int    `db:"verify_4"`
		Verify_1_desc string `db:"verify_1_desc"`
		Verify_2_desc string `db:"verify_2_desc"`
		Verify_3_desc string `db:"verify_3_desc"`
		Verify_4_desc string `db:"verify_4_desc"`
	}

	sql = `SELECT
			id,
			doc_id,
			asst_id,
			pat_pro_id,
			verify_1,
			verify_1_desc,
			verify_2,
			verify_2_desc,
			verify_3,
			verify_3_desc,
			verify_4,
			verify_4_desc
		   FROM prescription
		   WHERE status = 2 ` + attSql + `
		   ORDER BY id DESC
		   LIMIT ? OFFSET ?`

	var prescription []Prescription
	err = database.GetAll(sql, &prescription, limit, offset)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败",
			"err":  err.Error(),
			// "sql":  common.DebugSql(sql, limit, offset),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":  200,
		"msg":   "ok",
		"data":  prescription,
		"count": count,
		"sql":   common.DebugSql(sql, limit, offset),
	})
}

// 解绑微信
func Unbind_wx(w http.ResponseWriter, r *http.Request) {
	api_id := config.NormalPerm
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	user_id, err := common.CheckInt(r.FormValue("user_id"))
	if err != nil || user_id < 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "用户ID不能为空",
		})
		return
	}
	sql := "update rbac_user set openid = '0' where id = ?"
	result, err := database.Query(sql, user_id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "解绑微信失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, user_id),
		})
		return
	}
	RowsAffected, _ := result.RowsAffected()
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":         200,
		"msg":          "解绑微信成功：" + strconv.Itoa(int(RowsAffected)) + "条",
		"RowsAffected": RowsAffected,
	})
	if RowsAffected > 0 {
		common.Add_log(fmt.Sprintf("解绑微信成功，用户ID为%d", user_id), r)
	}
}

// 判断部门ID+用户ID是否绑定了小程序
func Check_bind_wx(w http.ResponseWriter, r *http.Request) {
	api_id := config.NormalPerm
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	user_id := r.FormValue("user_id")
	if user_id == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "参数错误",
		})
		return
	}
	sql := "select openid from rbac_user where id = ?"
	var openid string
	err := database.GetOne(sql, &openid, user_id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "查询用户信息失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, user_id),
		})
		return
	}
	if len(openid) > 10 {
		common.JSONResponse(w, http.StatusOK, map[string]interface{}{
			"code": 200,
			"msg":  "该用户已绑定小程序",
			"data": map[string]interface{}{
				"openid": openid,
			},
		})
	} else {
		common.JSONResponse(w, http.StatusOK, map[string]interface{}{
			"code": 200,
			"msg":  "该用户未绑定小程序",
			"data": map[string]interface{}{
				"openid": "",
			},
		})
	}
}

// 获取绑定微信的小程序码
func Get_wx_acode(w http.ResponseWriter, r *http.Request) {
	api_id := config.NormalPerm
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	user_id := r.FormValue("user_id")
	department_id := r.FormValue("department_id")
	if user_id == "" || department_id == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "参数错误",
		})
		return
	}

	// 获取access_token
	accessToken, err := GetAccessToken()
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "获取access_token失败",
			"err":  err.Error(),
		})
		return
	}

	// 准备请求数据
	requestData := map[string]any{
		"page":        "pages/bind_employees/bind",
		"scene":       fmt.Sprintf("%s,%s", department_id, user_id),
		"env_version": "trial",
		"width":       300,
		"check_path":  false,
	}

	jsonData, err := json.Marshal(requestData)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "JSON编码失败",
			"err":  err.Error(),
		})
		return
	}

	// 发送请求到微信接口
	url := fmt.Sprintf("https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=%s", accessToken)
	resp, err := http.Post(url, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "请求微信接口失败",
			"err":  err.Error(),
		})
		return
	}
	defer resp.Body.Close()

	// 读取响应内容
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "读取响应失败",
			"err":  err.Error(),
		})
		return
	}

	// 检查是否返回错误信息
	var errorResponse struct {
		Errcode int    `json:"errcode"`
		Errmsg  string `json:"errmsg"`
	}
	if json.Unmarshal(body, &errorResponse) == nil && errorResponse.Errcode != 0 {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "生成小程序码失败，请确认小程序APPKEY和APPSECRET是否正确",
			"err":  errorResponse.Errmsg,
		})
		return
	}

	// 保存图片到本地
	filename := department_id + "_" + user_id + ".jpg"
	filepath := config.Dist_catagory + "/uploads/wx_qr_code/" + filename
	err = os.WriteFile(filepath, body, 0666)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "保存小程序码失败",
			"err":  err.Error(),
		})
		return
	}

	// 返回成功信息
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "生成小程序码成功",
		"data": map[string]any{
			"filename": filename,
			"filepath": config.Dist_catagory + "/uploads/wx_qr_code/" + filename,
		},
	})
}

func GetAccessToken() (string, error) {
	appid := config.WX_appid
	appsecret := config.WX_appsecret

	url := fmt.Sprintf("https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s", appid, appsecret)
	resp, err := http.Get(url)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	var result struct {
		AccessToken string `json:"access_token"`
		ExpiresIn   int    `json:"expires_in"`
		ErrCode     int    `json:"errcode"`
		ErrMsg      string `json:"errmsg"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return "", err
	}

	if result.ErrCode != 0 {
		return "", fmt.Errorf("get access token failed: %s", result.ErrMsg)
	}

	return result.AccessToken, nil
}

// 供应商列表
func Supplier_drug_list(w http.ResponseWriter, r *http.Request) {
	api_id := 164
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}

	// 分页参数
	limit, err := strconv.Atoi(r.FormValue("limit"))
	if err != nil || limit < 1 {
		limit = 10
	}
	page, err := strconv.Atoi(r.FormValue("page"))
	if err != nil || page < 1 {
		page = 1
	}
	offset := (page - 1) * limit

	// 搜索参数
	var attSql string
	var params []interface{}

	// 供应商名称搜索
	name := r.FormValue("name")
	if name != "" {
		attSql += " AND name LIKE ?"
		params = append(params, "%"+name+"%")
	}

	// 总数
	var count int
	sql := "SELECT COUNT(id) FROM supplier_drug WHERE 1 " + attSql
	err = database.GetOne(sql, &count, params...)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "读取数据总数失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, params...),
		})
		return
	}

	// 数据
	type SupplierDrug struct {
		ID             int    `db:"id"`
		Code           string `db:"code"`
		Name           string `db:"name"`
		Address        string `db:"address"`
		Bank_name      string `db:"bank_name"`
		Bank_account   string `db:"bank_account"`
		Contact_name   string `db:"contact_name"`
		Contact_mobile string `db:"contact_mobile"`
		Contact_phone  string `db:"contact_phone"`
		Business_scope string `db:"business_scope"`
		Status         int    `db:"status"`
		Update_time    string `db:"update_time"`
		Create_time    string `db:"create_time"`
	}

	var data []SupplierDrug
	sql = "SELECT id,code,name,address,bank_name,bank_account,contact_name,contact_mobile,contact_phone,business_scope,status,create_time,update_time FROM supplier_drug WHERE 1 " + attSql + " ORDER BY id DESC LIMIT ?,?"
	params = append(params, offset, limit)
	err = database.GetAll(sql, &data, params...)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "读取数据失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, params...),
		})
		return
	}

	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":  200,
		"msg":   "ok",
		"count": count,
		"data":  data,
	})
}

// 供应商详情
func Supplier_drug_detail(w http.ResponseWriter, r *http.Request) {
	api_id := 167
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}

	id := r.FormValue("id")
	if id == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "id不能为空",
		})
		return
	}

	type SupplierDrug struct {
		ID             int    `db:"id"`
		Code           string `db:"code"`
		Name           string `db:"name"`
		Address        string `db:"address"`
		Bank_name      string `db:"bank_name"`
		Bank_account   string `db:"bank_account"`
		Contact_name   string `db:"contact_name"`
		Contact_mobile string `db:"contact_mobile"`
		Contact_phone  string `db:"contact_phone"`
		Business_scope string `db:"business_scope"`
		Status         int    `db:"status"`
		Qualification  string `db:"qualification"`
		Create_time    string `db:"create_time"`
		Update_time    string `db:"update_time"`
	}

	var data SupplierDrug
	sql := "SELECT id,code,name,address,bank_name,bank_account,contact_name,contact_mobile,contact_phone,business_scope,qualification,status,create_time,update_time FROM supplier_drug WHERE id = ?"
	err := database.GetOne(sql, &data, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "读取数据失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, id),
		})
		return
	}

	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "ok",
		"data": data,
	})
}

// 供应商删除，与供应商编辑同权
func Supplier_drug_del(w http.ResponseWriter, r *http.Request) {
	api_id := 166
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	id, err := common.CheckInt(r.FormValue("id"))
	if id < 1 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "病历id不能为空",
		})
		return
	}
	sql := "DELETE FROM supplier_drug WHERE id = ?"
	result, err := database.Query(sql, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "删除失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, id),
		})
		return
	}
	RowsAffected, _ := result.RowsAffected()
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":         200,
		"msg":          "删除成功",
		"RowsAffected": RowsAffected,
	})
	if RowsAffected > 0 {
		common.Add_log(fmt.Sprintf("删除供应商，ID：%d", id), r)
	}
}

// 供应商编辑
func Supplier_drug_edit(w http.ResponseWriter, r *http.Request) {
	// 1. 权限检查
	api_id := 166
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}

	// 2. 获取参数
	id := r.FormValue("id")
	name := r.FormValue("name")
	code := r.FormValue("code")
	address := r.FormValue("address")
	bank_name := r.FormValue("bank_name")
	bank_account := r.FormValue("bank_account")
	contact_name := r.FormValue("contact_name")
	contact_mobile := r.FormValue("contact_mobile")
	contact_phone := r.FormValue("contact_phone")
	business_scope := r.FormValue("business_scope")
	status := r.FormValue("status")
	qualification := r.FormValue("qualification")

	// 3. 参数验证
	if id == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "ID不能为空",
		})
		return
	}

	if name == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "供应商名称不能为空",
		})
		return
	}

	// 4. 更新数据
	sql := "UPDATE supplier_drug SET name=?,code=?,address=?,bank_name=?,bank_account=?,contact_name=?,contact_mobile=?,contact_phone=?,business_scope=?,status=?,qualification=?,update_time=NOW() WHERE id=?"
	result, err := database.Query(sql, name, code, address, bank_name, bank_account, contact_name, contact_mobile, contact_phone, business_scope, status, qualification, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "修改失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, name, code, address, bank_name, bank_account, contact_name, contact_mobile, contact_phone, business_scope, status, qualification, id),
		})
		return
	}

	// 5. 返回结果
	rowsAffected, _ := result.RowsAffected()
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":          200,
		"msg":           "修改成功",
		"rows_affected": rowsAffected,
	})
}

// 供应商添加
func Supplier_drug_add(w http.ResponseWriter, r *http.Request) {
	// 1. 权限检查
	api_id := 165
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}

	// 2. 获取参数
	name := r.FormValue("name")
	code := r.FormValue("code")
	address := r.FormValue("address")
	bank_name := r.FormValue("bank_name")
	bank_account := r.FormValue("bank_account")
	contact_name := r.FormValue("contact_name")
	contact_mobile := r.FormValue("contact_mobile")
	contact_phone := r.FormValue("contact_phone")
	business_scope := r.FormValue("business_scope")
	status := r.FormValue("status")
	qualification := r.FormValue("qualification")

	// 3. 参数验证
	if name == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "供应商名称不能为空",
		})
		return
	}

	// 4. 插入数据
	sql := "INSERT INTO supplier_drug (name,code,address,bank_name,bank_account,contact_name,contact_mobile,contact_phone,business_scope,status,qualification,create_time,update_time) VALUES (?,?,?,?,?,?,?,?,?,?,?,NOW(),NOW())"
	result, err := database.Query(sql, name, code, address, bank_name, bank_account, contact_name, contact_mobile, contact_phone, business_scope, status, qualification)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "添加失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, name, code, address, bank_name, bank_account, contact_name, contact_mobile, contact_phone, business_scope, status, qualification),
		})
		return
	}

	// 5. 返回结果
	rowsAffected, _ := result.RowsAffected()
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":          200,
		"msg":           "添加成功",
		"rows_affected": rowsAffected,
	})
}

// Drug list
func Drug_list(w http.ResponseWriter, r *http.Request) {
	api_id := 164 // Using same permission as supplier drug for now
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}

	// 分页参数
	limit, err := strconv.Atoi(r.FormValue("limit"))
	if err != nil || limit < 1 {
		limit = 10
	}
	page, err := strconv.Atoi(r.FormValue("page"))
	if err != nil || page < 1 {
		page = 1
	}
	offset := (page - 1) * limit

	// 搜索参数
	var attSql string
	var params []interface{}

	pid, err := strconv.Atoi(r.FormValue("pid"))
	if err != nil && pid > 0 {
		attSql += " AND pid = ?"
		params = append(params, pid)
	}

	// 药名搜索
	key := r.FormValue("key")
	if key != "" {
		key = strings.TrimSpace(key)
		attSql += " AND name LIKE ?"
		params = append(params, "%"+key+"%")
	}

	// 总数
	var count int
	sql := "SELECT COUNT(id) FROM drug WHERE 1 " + attSql
	err = database.GetOne(sql, &count, params...)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "读取数据总数失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, params...),
		})
		return
	}

	// 数据
	type Drug struct {
		ID               int     `db:"id"`
		Name             string  `db:"name"`
		Batch            string  `db:"batch"`
		Code             string  `db:"code"`
		Spec             string  `db:"spec"`
		Unit             string  `db:"unit"`
		Property         string  `db:"property"`
		Origin           string  `db:"origin"`
		HarvestTime      string  `db:"harvestTime"`
		Validity_Days    int     `db:"validity_days"`
		Storage          string  `db:"storage"`
		Dosage           string  `db:"dosage"`
		Directions       string  `db:"directions"`
		Effect           string  `db:"effect"`
		Indication       string  `db:"indication"`
		Contraindication string  `db:"contraindication"`
		Sideeffect       string  `db:"sideeffect"`
		Interaction      string  `db:"interaction"`
		Price            float64 `db:"price"`
		Update_user_id   int     `db:"update_user_id"`
		Update_time      string  `db:"update_time"`
		Create_time      string  `db:"create_time"`
	}

	var data []Drug
	sql = "SELECT id,name,batch,spec,code,unit,property,origin,harvestTime,validity_days,storage,dosage,directions,effect,indication,contraindication,sideeffect,interaction,price,update_user_id,update_time,create_time FROM drug WHERE 1 " + attSql + " ORDER BY id DESC LIMIT ?,?"
	params = append(params, offset, limit)
	err = database.GetAll(sql, &data, params...)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "读取数据失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, params...),
		})
		return
	}

	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":  200,
		"msg":   "ok",
		"count": count,
		"data":  data,
		"sql":   common.DebugSql(sql, params...),
	})
}

// Drug detail
func Drug_detail(w http.ResponseWriter, r *http.Request) {
	api_id := 167 // Using same permission as supplier drug for now
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}

	id := r.FormValue("id")
	if id == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "id不能为空",
		})
		return
	}

	type Drug struct {
		ID               int     `db:"id"`
		Pid              int     `db:"pid"`
		Name             string  `db:"name"`
		Batch            string  `db:"batch"`
		Code             string  `db:"code"`
		Spec             string  `db:"spec"`
		Unit             string  `db:"unit"`
		Property         string  `db:"property"`
		Origin           string  `db:"origin"`
		HarvestTime      string  `db:"harvestTime"`
		Validity_Days    int     `db:"validity_days"`
		Storage          string  `db:"storage"`
		Dosage           string  `db:"dosage"`
		Directions       string  `db:"directions"`
		Effect           string  `db:"effect"`
		Indication       string  `db:"indication"`
		Contraindication string  `db:"contraindication"`
		Sideeffect       string  `db:"sideeffect"`
		Interaction      string  `db:"interaction"`
		Price            float64 `db:"price"`
		Update_user_id   int     `db:"update_user_id"`
		Update_time      string  `db:"update_time"`
		Create_time      string  `db:"create_time"`
	}

	var data Drug
	sql := "SELECT id,pid,name,batch,spec,code,unit,property,origin,harvestTime,validity_days,storage,dosage,directions,effect,indication,contraindication,sideeffect,interaction,price,update_user_id,update_time,create_time FROM drug WHERE id = ?"
	err := database.GetOne(sql, &data, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "读取数据失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, id),
		})
		return
	}

	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "ok",
		"data": data,
	})
}

// Drug delete
func Drug_del(w http.ResponseWriter, r *http.Request) {
	api_id := 166 // Using same permission as supplier drug for now
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}

	id, err := common.CheckInt(r.FormValue("id"))
	if id < 1 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "药材id不能为空",
		})
		return
	}

	sql := "DELETE FROM drug WHERE id = ?"
	result, err := database.Query(sql, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "删除失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, id),
		})
		return
	}

	RowsAffected, _ := result.RowsAffected()
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":         200,
		"msg":          "删除成功",
		"RowsAffected": RowsAffected,
	})
	if RowsAffected > 0 {
		common.Add_log(fmt.Sprintf("删除药材，ID：%d", id), r)
	}
}

// Drug edit
func Drug_edit(w http.ResponseWriter, r *http.Request) {
	api_id := 166 // Using same permission as supplier drug for now
	session, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}

	// Get parameters
	id := r.FormValue("id")
	pid := r.FormValue("pid")
	name := r.FormValue("name")
	batch := r.FormValue("batch")
	code := r.FormValue("code")
	unit := r.FormValue("unit")
	spec := r.FormValue("spec")
	property := r.FormValue("property")
	origin := r.FormValue("origin")
	harvestTime := r.FormValue("harvestTime")
	validityDays := r.FormValue("validity_days")
	storage := r.FormValue("storage")
	dosage := r.FormValue("dosage")
	directions := r.FormValue("directions")
	effect := r.FormValue("effect")
	indication := r.FormValue("indication")
	contraindication := r.FormValue("contraindication")
	sideeffect := r.FormValue("sideeffect")
	interaction := r.FormValue("interaction")
	price := r.FormValue("price")

	// Validate parameters
	if id == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "ID不能为空",
		})
		return
	}
	if pid == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "分类不能为空",
		})
		return
	}
	if name == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "药名不能为空",
		})
		return
	}
	if batch == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "批次不能为空",
		})
		return
	}
	if code == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "批号不能为空",
		})
		return
	}
	if unit == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "单位不能为空",
		})
		return
	}
	if spec == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "规格不能为空",
		})
		return
	}
	if property == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "性味不能为空",
		})
		return
	}
	if origin == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "产地不能为空",
		})
		return
	}
	if harvestTime == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "采收时间不能为空",
		})
		return
	}
	if validityDays == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "默认效期不能为空",
		})
		return
	}
	if storage == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "储存条件不能为空",
		})
		return
	}
	if dosage == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "常用剂量不能为空",
		})
		return
	}
	if directions == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "用法不能为空",
		})
		return
	}
	if effect == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "药效不能为空",
		})
		return
	}
	if indication == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "适应症不能为空",
		})
		return
	}
	if contraindication == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "禁忌症不能为空",
		})
		return
	}
	if sideeffect == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "副作用不能为空",
		})
		return
	}
	if interaction == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "药物相互作用不能为空",
		})
		return
	}
	if price == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "参考价格不能为空",
		})
		return
	}

	// Update data
	sql := `UPDATE drug SET
		pid=?,
		name=?,
		batch=?,
		code=?,
		unit=?,
		spec=?,
		property=?,
		origin=?,
		harvestTime=?,
		validity_days=?,
		storage=?,
		dosage=?,
		directions=?,
		effect=?,
		indication=?,
		contraindication=?,
		sideeffect=?,
		interaction=?,
		price=?,
		update_user_id=?,
		update_time=NOW()
		WHERE id=?`

	result, err := database.Query(sql,
		pid,
		name, batch, code, unit, spec, property, origin, harvestTime,
		validityDays, storage, dosage, directions, effect, indication,
		contraindication, sideeffect, interaction, price,
		session.Values["id"], // update_user_id
		id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "修改失败",
			"err":  err.Error(),
			"sql": common.DebugSql(sql, pid, name, batch, code, unit, spec, property, origin, harvestTime,
				validityDays, storage, dosage, directions, effect, indication,
				contraindication, sideeffect, interaction, price,
				session.Values["id"], id),
		})
		return
	}

	rowsAffected, _ := result.RowsAffected()
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":          200,
		"msg":           "修改成功",
		"rows_affected": rowsAffected,
	})
}

// Drug add
func Drug_add(w http.ResponseWriter, r *http.Request) {
	api_id := 165 // Using same permission as supplier drug for now
	session, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}

	// Get parameters
	pid := r.FormValue("pid")
	name := r.FormValue("name")
	batch := r.FormValue("batch")
	code := r.FormValue("code")
	spec := r.FormValue("spec")
	unit := r.FormValue("unit")
	property := r.FormValue("property")
	origin := r.FormValue("origin")
	harvestTime := r.FormValue("harvestTime")
	validityDays := r.FormValue("validity_days")
	storage := r.FormValue("storage")
	dosage := r.FormValue("dosage")
	directions := r.FormValue("directions")
	effect := r.FormValue("effect")
	indication := r.FormValue("indication")
	contraindication := r.FormValue("contraindication")
	sideeffect := r.FormValue("sideeffect")
	interaction := r.FormValue("interaction")
	price := r.FormValue("price")

	// Validate parameters

	if pid == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "分类不能为空",
		})
		return
	}
	if name == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "药名不能为空",
		})
		return
	}
	name_py := common.Name2pinyin(name)
	if batch == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "批次不能为空",
		})
		return
	}
	if code == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "批号不能为空",
		})
		return
	}
	if unit == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "单位不能为空",
		})
		return
	}
	if spec == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "规格不能为空",
		})
		return
	}
	if property == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "性味不能为空",
		})
		return
	}
	if origin == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "产地不能为空",
		})
		return
	}
	if harvestTime == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "采收时间不能为空",
		})
		return
	}
	if validityDays == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "默认效期不能为空",
		})
		return
	}
	if storage == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "储存条件不能为空",
		})
		return
	}
	if dosage == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "常用剂量不能为空",
		})
		return
	}
	if directions == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "用法不能为空",
		})
		return
	}
	if effect == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "药效不能为空",
		})
		return
	}
	if indication == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "适应症不能为空",
		})
		return
	}
	if contraindication == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "禁忌症不能为空",
		})
		return
	}
	if sideeffect == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "副作用不能为空",
		})
		return
	}
	if interaction == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "药物相互作用不能为空",
		})
		return
	}
	if price == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "参考价格不能为空",
		})
		return
	}

	// Insert data
	sql := "INSERT INTO drug (pid,name,name_py,batch,code,spec,unit,property,origin,harvestTime,validity_days,storage,dosage,directions,effect,indication,contraindication,sideeffect,interaction,price,update_user_id) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)"
	result, err := database.Query(sql, pid, name, name_py, batch, code, spec, unit, property, origin, harvestTime, validityDays, storage, dosage, directions, effect, indication, contraindication, sideeffect, interaction, price, session.Values["id"])
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "添加失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, pid, name, batch, code, spec, unit, property, origin, harvestTime, validityDays, storage, dosage, directions, effect, indication, contraindication, sideeffect, interaction, price, session.Values["id"]),
		})
		return
	}

	rowsAffected, _ := result.RowsAffected()
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":          200,
		"msg":           "添加成功",
		"rows_affected": rowsAffected,
	})
}

// 药材库存补货 65
func Warehouse_drug_in(w http.ResponseWriter, r *http.Request) {
	api_id := 65
	session, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	// 供应商ID
	sup_id, err := common.CheckInt(r.FormValue("supplier_id"))
	if sup_id < 1 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "供应商ID不能为空",
		})
		return
	}
	//有效期
	validityDaysInt, err := strconv.Atoi(r.FormValue("validity_days"))
	if err != nil || r.FormValue("validity_days") == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "有效期不能为空或格式错误",
		})
		return
	}
	exp_date := time.Now().AddDate(0, 0, validityDaysInt).Format("2006-01-02")
	// 最新的供应商名称
	last_supplier := r.FormValue("supplier_name")
	if last_supplier == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "供应商名称不能为空",
		})
		return
	}
	// 批次编号
	batch := r.FormValue("batch")
	if batch == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "批次编号不得为空",
		})
		return
	}
	unit := r.FormValue("unit")
	if unit == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "药材单位（如mg,kg）不得为空",
		})
		return
	}
	// 核算价 - 用于核算成本的价格，即进货价嘛
	cost_price, err := common.CheckInt(r.FormValue("price"))
	if cost_price <= 0 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "进货价格式出错",
		})
		return
	}
	// 入库数量
	quantity, err := common.CheckFloat(r.FormValue("quantity"))
	if err != nil || quantity < 0 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "补货数量必须是有效的数字",
		})
		return
	}
	// 药材ID
	drug_id, err := common.CheckInt(r.FormValue("drug_id"))
	if drug_id < 1 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "药材ID格式错误",
		})
		return
	}
	//根据药材ID查询有没有对应的库存ID，如果有，就标识本次操作类型为补货，否则新增
	sql := "select id,quantity from warehouse_drug where drug_id = ? FOR UPDATE"
	type WarehouseDrug struct {
		ID       int     `db:"id"`
		Quantity float64 `db:"quantity"`
	}
	var warehouseDrug WarehouseDrug
	err = database.GetRow(sql, &warehouseDrug, drug_id)

	var sqls []database.SQLExec
	var new_data float64
	var rowsAffected int64
	if err != nil {
		// 新增库存场景
		new_data = quantity
		sqls = []database.SQLExec{
			{Query: `INSERT INTO warehouse_drug
				(drug_id, quantity, last_supplier, cost_price,exp_date)
				VALUES (?, ?, ?, ?,?)`,
				Args: []interface{}{
					drug_id, quantity, last_supplier, cost_price, exp_date,
				}},
			{Query: `INSERT INTO warehouse_drug_log
				(pid, user_id, kind, change_data, old_data, new_data, sup_id, batch,exp_date,notes)
				VALUES (LAST_INSERT_ID(), ?, ?, ?, ?, ?, ?, ?,?,?)`,
				Args: []interface{}{
					session.Values["id"], 1, quantity, 0, new_data,
					sup_id, batch, exp_date, "新增库存",
				}},
		}

		// 执行事务
		rowsAffected, err = database.ExecuteTransaction_with_affectedRows(sqls)
		if err != nil {
			common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
				"code": 500,
				"msg":  "库存操作失败",
				"err":  err.Error(),
			})
			return
		}

		// 后面使用 rowsAffected 变量
		if rowsAffected > 0 {
			// ... 日志记录逻辑 ...
		}
	} else {
		// 补货场景
		new_data = warehouseDrug.Quantity + quantity
		sqls = []database.SQLExec{
			{Query: `UPDATE warehouse_drug
				SET quantity = quantity + ?,
					last_supplier = ?,
					cost_price = ?
				WHERE id = ?`,
				Args: []interface{}{
					quantity, last_supplier, cost_price, warehouseDrug.ID,
				}},
			{Query: `INSERT INTO warehouse_drug_log
				(pid, user_id, kind, change_data, old_data, new_data, sup_id, batch,exp_date,notes)
				VALUES (?, ?, ?, ?, ?, ?, ?, ?,?,?)`,
				Args: []interface{}{
					warehouseDrug.ID, session.Values["id"], 1, quantity, warehouseDrug.Quantity, new_data, sup_id, batch, exp_date, "补货",
				}},
		}

		// 执行事务
		rowsAffected, err = database.ExecuteTransaction_with_affectedRows(sqls)
		if err != nil {
			common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
				"code": 500,
				"msg":  "库存操作失败",
				"err":  err.Error(),
			})
			return
		}
	}

	// 返回成功响应
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "库存操作成功",
	})

	// 添加系统日志
	if rowsAffected > 0 {
		var logMsg string
		if warehouseDrug.ID == 0 { // 新增场景
			logMsg = fmt.Sprintf("采购入库 - 类型：新增库存；药材ID：%d，数量：%.2f（%s），供应商%s(ID：%d)，批次：%s，效期：%s；更详尽见【出入库明细】",
				drug_id, quantity, unit, last_supplier, sup_id, batch, exp_date)
		} else { // 补货场景
			logMsg = fmt.Sprintf("采购入库 - 类型：库存补货；药材ID：%d，补货数量：%.2f（%s），供应商%s(ID：%d)，批次：%s，效期：%s；更详尽见【出入库明细】",
				drug_id, quantity, unit, last_supplier, sup_id, batch, exp_date)
		}
		common.Add_log(logMsg, r)
	}
}

// 库存出库 66
func Warehouse_drug_out(w http.ResponseWriter, r *http.Request) {
	api_id := 66
	session, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	id, err := common.CheckInt(r.FormValue("id"))
	if id < 1 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "出库库存ID不能为空",
		})
		return
	}
	quantity, err := common.CheckFloat(r.FormValue("quantity"))
	if err != nil || quantity < 0 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "减库存量必须是有效数字",
		})
		return
	}
	notes := r.FormValue("notes")
	if notes == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "出库备注不能为空",
		})
		return
	}
	// kind,在数据库中的值： 0-正常出库，1-入库 2-报损出库
	kind := r.FormValue("kind")
	var kind_name string
	if kind == "" {
		kind = "0"
		kind_name = "正常出库"
	} else {
		kind_name = "报损出库"
	}
	// 查询当前补货商品还剩余的库存量
	sql := "SELECT quantity FROM warehouse_drug WHERE id = ? limit 1"
	var old_data float64
	err = database.GetOne(sql, &old_data, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "出库商品查询失败",
			"err":  err.Error(),
			// "sql":  common.DebugSql(sql, id),
		})
		return
	}
	new_data := old_data - quantity
	if new_data < 0 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "出库数量不能大于库存数量",
		})
		return
	}
	// 开始补货，库存表与库存日志使用事务以保持数据一致性
	// 事务开始
	sqls := []database.SQLExec{
		{Query: "Update warehouse_drug set quantity = quantity - ? where id = ?",
			Args: []interface{}{
				quantity, id,
			}},
		{Query: "INSERT INTO warehouse_drug_log (pid,user_id,kind,change_data,old_data,new_data,notes) VALUES (?,?,?,?,?,?,?)",
			Args: []interface{}{
				id, session.Values["id"], kind, quantity, old_data, new_data, notes,
			}},
	}
	RowsAffected, err := database.ExecuteTransaction_with_affectedRows(sqls)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "出库失败",
			"err":  err.Error(),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "出库成功",
		"id":   id,
	})
	if RowsAffected > 0 {
		common.Add_log(fmt.Sprintf("%s - 原料ID：%d，出库数量：%f，影响行数：%d", kind_name, id, quantity, RowsAffected), r)
	}
}

// 获取用户绑定微信的小程序码
func Get_wx_bind_user_acode(w http.ResponseWriter, r *http.Request) {
	api_id := config.NormalPerm
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	user_id := r.FormValue("user_id")
	if user_id == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "用户ID不得为空",
		})
		return
	}

	// 获取access_token
	accessToken, err := GetAccessToken()
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "获取access_token失败",
			"err":  err.Error(),
		})
		return
	}

	// 准备请求数据
	requestData := map[string]any{
		"page":        "pages/bind_users/bind",
		"scene":       user_id,
		"env_version": "trial",
		"width":       300,
		"check_path":  false,
	}

	jsonData, err := json.Marshal(requestData)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "JSON编码失败",
			"err":  err.Error(),
		})
		return
	}

	// 发送请求到微信接口
	url := fmt.Sprintf("https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=%s", accessToken)
	resp, err := http.Post(url, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "请求微信接口失败",
			"err":  err.Error(),
		})
		return
	}
	defer resp.Body.Close()

	// 读取响应内容
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "读取响应失败",
			"err":  err.Error(),
		})
		return
	}

	// 检查是否返回错误信息
	var errorResponse struct {
		Errcode int    `json:"errcode"`
		Errmsg  string `json:"errmsg"`
	}
	if json.Unmarshal(body, &errorResponse) == nil && errorResponse.Errcode != 0 {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "生成小程序码失败，请确认小程序APPKEY和APPSECRET是否正确",
			"err":  errorResponse.Errmsg,
		})
		return
	}

	// 转换为base64
	base64Data := base64.StdEncoding.EncodeToString(body)
	base64Img := "data:image/png;base64," + base64Data

	// 返回成功信息和base64编码的图片
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "生成小程序码成功",
		"data": map[string]any{
			"base64Img": base64Img,
		},
	})
}
