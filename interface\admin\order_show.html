<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 订单详情</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
    <style>
        body {
            background-color: #f5f7fa;
        }

        /* 错误提示样式 */
        .error-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(5px);
            z-index: 9999;
            display: flex;
            justify-content: center;
            align-items: center;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .error-overlay.show {
            opacity: 1;
        }

        .error-card {
            width: 400px;
            text-align: center;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.95);
            transform: translateY(20px);
            transition: all 0.3s ease;
        }

        .error-overlay.show .error-card {
            transform: translateY(0);
        }

        /* 区块标题样式 */
        .line_font {
            font-size: 17px;
            font-weight: bold;
            margin: 10px 0 20px 0;
            border-bottom: 1px solid rgba(0,0,0,0.05);
            padding: 10px;
            color: #333;
            position: relative;
        }

        .line_font:after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #1E9FFF, #5FB878);
            border-radius: 3px;
        }

        /* 信息标签和内容样式 */
        .info-label {
            color: #666;
            padding-right: 15px;
            text-align: right;
            width: 120px;
            display: inline-block;
            font-size: 14px;
        }

        .info-content {
            color: #333;
            font-weight: 500;
            position: relative;
            transition: all 0.3s;
        }

        .info-item {
            margin: 15px 0;
            line-height: 24px;
            transition: all 0.3s;
        }

        .info-item:hover {
            transform: translateX(5px);
        }

        /* 信息区块样式 */
        .info-section {
            background: rgba(255, 255, 255, 0.9);
            padding: 25px;
            border-radius: 8px;
            margin-bottom: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .info-section:hover {
            box-shadow: 0 8px 25px rgba(0,0,0,0.08);
            transform: translateY(-3px);
        }

        /* 图片预览样式 */
        .image-preview-item {
            width: 150px;
            height: 150px;
            position: relative;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s;
            margin: 10px 0;
        }

        .image-preview-item:hover {
            transform: scale(1.05);
            box-shadow: 0 8px 20px rgba(0,0,0,0.15);
        }

        .image-preview-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 12px;
            cursor: pointer;
            border: none;
            transition: all 0.3s;
        }

        /* 状态标签样式 */
        .status-tag {
            display: inline-block;
            padding: 4px 10px;
            border-radius: 4px;
            font-size: 12px;
            margin-left: 10px;
            transition: all 0.3s;
        }

        .status-tag:hover {
            transform: translateY(-2px);
        }

        .status-pending {
            background-color: rgba(230, 162, 60, 0.1);
            color: #e6a23c;
        }

        .status-shipped {
            background-color: rgba(64, 158, 255, 0.1);
            color: #409eff;
        }

        .status-completed {
            background-color: rgba(103, 194, 58, 0.1);
            color: #67c23a;
        }

        .status-returned {
            background-color: rgba(245, 108, 108, 0.1);
            color: #f56c6c;
        }

        .status-abnormal {
            background-color: rgba(144, 147, 153, 0.8);
            color: #fff;
        }

        /* 表格样式 */
        .layui-table {
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.03);
        }

        .layui-table thead tr {
            background: linear-gradient(to right, #f8f8f8, #ffffff);
        }

        .layui-table tbody tr:hover {
            background-color: rgba(30, 159, 255, 0.05);
        }

        /* 按钮样式 */
        .layui-btn {
            border-radius: 6px;
            transition: all 0.3s;
        }

        .layui-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .layui-btn-primary {
            background: linear-gradient(to right, #f5f5f5, #ffffff);
            border-color: #e6e6e6;
        }

        .layui-btn-primary:hover {
            background: linear-gradient(to right, #ffffff, #f5f5f5);
            box-shadow: 0 3px 8px rgba(0,0,0,0.08);
            border-color: #d9d9d9;
        }

        /* 页面内容区域样式 */
        .body_child {
            padding: 20px;
        }

        .layui-panel {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 20px rgba(0,0,0,0.05);
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
        }

        /* 链接样式 */
        .layui-table-link {
            color: #1E9FFF;
            text-decoration: none;
            transition: all 0.3s;
            position: relative;
        }

        .layui-table-link:hover {
            color: #5FB878;
        }

        .layui-table-link:after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 0;
            height: 1px;
            background: linear-gradient(90deg, #1E9FFF, #5FB878);
            transition: all 0.3s;
        }

        .layui-table-link:hover:after {
            width: 100%;
        }
    </style>
</head>

<body>
    <div class="layui-layout layui-layout-admin">
        <!-- 头部 -->
        <div class="layui-header">
            <div class="layui-logo">幸年堂</div>
            <ul class="layui-nav layui-layout-right">
                <li class="layui-nav-item" style="background-color:transparent!important;padding-right:10px">
                    <a href="javascript:;" style="display:flex;align-items:center;color:#fff">
                        <img src="/dist/images/user.png" class="layui-nav-img"><span class="user_name"></span>
                    </a>
                    <dl class="layui-nav-child pop_menu">
                        <dd><i class="iconfont">&#xe62f;</i><a href="change_password.html#7">用户资料</a></dd>
                        <dd><i class="iconfont">&#xe601;</i><a href="javascript:void(0)" class="refresh_cache">刷新缓存</a>
                        </dd>
                        <dd><i class="iconfont">&#xe634;</i><a href="login_out.html">退出登录</a></dd>
                    </dl>
                </li>
            </ul>
        </div>
        <!-- 导航 -->
        <ul class="layui-nav layui-nav-tree layui-nav-side" id="menuContainer" lay-bar="disabled" lay-accordion>
            <div style="margin: 50px 0;color: #bbb;text-align: center;">菜单加载中...</div>
        </ul>

        <!-- 主体内容 -->
        <div class="layui-body">
            <div class="body_child">
                <div class="layui-panel">
                    <div class="layui-card-header" style="border-bottom: 1px solid rgba(0,0,0,0.05); padding: 15px 20px;">
                        <div class="layui-row" style="padding-top:5px;">
                            <div class="layui-col-md8">
                                <span style="font-size: 18px; font-weight: 500; color: #333; position: relative; padding-left: 12px;">
                                    <span style="position: absolute; left: 0; top: 0; bottom: 0; width: 4px; background: linear-gradient(to bottom, #1E9FFF, #5FB878); border-radius: 2px;"></span>
                                    订单详情
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="layui-padding-3" style="min-height: 800px;">



                        <div class="info-section">
                            <!-- 填充患者基础信息，接口/admin/patient_profile/detail -->
                            <div class="line_font">患者基础信息</div>
                            <div id="patient_data" class="layui-row">
                                <i
                                    class='layui-icon layui-icon-loading-1 layui-anim layui-anim-rotate layui-anim-loop'></i>
                            </div>
                        </div>


                        <!-- 订单信息部分 -->
                        <div class="info-section">
                            <div class="line_font">订单信息</div>
                            <div class="layui-row">
                                <div class="layui-col-md4">
                                    <div class="info-item">
                                        <span class="info-label">订单ID：</span>
                                        <span class="info-content" id="orderId">-</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">医助：</span>
                                        <span class="info-content" id="asst_name">-</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">医生：</span>
                                        <span class="info-content" id="doctor_name">-</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">创建时间：</span>
                                        <span class="info-content" id="createTime">-</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">订单状态：</span>
                                        <span class="info-content" id="status">-</span>
                                    </div>
                                </div>
                                <div class="layui-col-md4">
                                    <div class="info-item">
                                        <span class="info-label">订单总额：</span>
                                        <span class="info-content" id="totalMoney">-</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">已付定金：</span>
                                        <span class="info-content" id="prePay">-</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">待收款项：</span>
                                        <span class="info-content" id="finalPay">-</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">实收尾款：</span>
                                        <span class="info-content" id="final_pay">-</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">全额尾款：</span>
                                        <span class="info-content" id="Final_is_full">-</span>
                                    </div>

                                </div>
                                <div class="layui-col-md4">
                                    <div class="info-item">
                                        <span class="info-label">物流公司：</span>
                                        <span class="info-content" id="express">-</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">物流编号：</span>
                                        <span class="info-content" id="trackingNum">-</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">核销时间：</span>
                                        <span class="info-content" id="finishTime">-</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">物流信息：</span>
                                        <span class="info-content" id="express_info_by_ord_id">-</span>
                                    </div>
                                </div>
                            </div>

                            <!-- 凭证图片部分 -->
                            <div class="line_font" style="margin-top: 30px;">凭证图片</div>
                            <div class="layui-row">
                                <div class="layui-col-md4">
                                    <div class="info-item" style="display: flex; flex-direction: column; align-items: center;">
                                        <div style="width: 100%; text-align: center; margin-bottom: 10px; color: #666; font-weight: 500;">代收款凭证</div>
                                        <div id="crPicContainer" style="display: flex; justify-content: center;"></div>
                                    </div>
                                </div>
                                <div class="layui-col-md4">
                                    <div class="info-item" style="display: flex; flex-direction: column; align-items: center;">
                                        <div style="width: 100%; text-align: center; margin-bottom: 10px; color: #666; font-weight: 500;">预付款收款截图</div>
                                        <div id="payPicContainer" style="display: flex; justify-content: center;"></div>
                                    </div>
                                </div>
                                <div class="layui-col-md4">
                                    <div class="info-item" style="display: flex; flex-direction: column; align-items: center;">
                                        <div style="width: 100%; text-align: center; margin-bottom: 10px; color: #666; font-weight: 500;">尾款收款截图</div>
                                        <div id="Final_pay_pic" style="display: flex; justify-content: center;"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 赠品数据 -->
                        <div class="info-section">
                            <div class="line_font">赠品数据</div>
                            <table class="layui-table">
                                <thead>
                                    <tr>
                                        <th>库存ID</th>
                                        <th>赠品ID</th>
                                        <th>赠品名称</th>
                                        <th>数量</th>
                                        <th>单价</th>
                                        <th>总价</th>
                                    </tr>
                                </thead>
                                <tbody id="gift_data"></tbody>
                            </table>
                        </div>

                        <!-- 底部按钮 -->
                        <div style="text-align: center; margin-top: 30px; padding-bottom: 20px;">
                            <button type="button" class="layui-btn layui-btn-primary" style="font-size: 15px;"
                                onclick="history.go(-1)">
                                <i class="layui-icon layui-icon-left" style="margin-right: 5px;"></i>返回
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        layui.use(['element', 'layer', 'util'], function () {
            var element = layui.element;
            var layer = layui.layer;
            var $ = layui.$;
            var Record_ids = '';
            var Tracking_num = 0;
            var express_type = 0;
            var pat_pro_id = 0;

            // 获取订单ID
            var orderId = request.get('id');
            if (!orderId) {
                layer.msg('参数错误', { icon: 2, time: 1000 });
                return false;
            }
            // 页面顶部插入缓存的用户数据
            let local_userinfo = localStorage.getItem('local_userinfo');
            let user_name = "";
            if (local_userinfo) {
                local_userinfo = JSON.parse(local_userinfo);
                user_name = local_userinfo.Name + " ( " + local_userinfo.Roles + " )";
            } else {
                user_name = "未登录";
            }
            $('.user_name').text(user_name);

            render_menu($);
            $('.refresh_cache').click(function () {
                refresh_cache($);
            });

            // 加载订单详情数据
            layer.load(2);
            $.ajax({
                url: serverUrl + "/admin/order/detail",
                type: "post",
                async: false,
                data: { id: orderId },
                success: function (res) {
                    layer.closeAll('loading');
                    let data = res.data;
                    Record_ids = data.Record_ids;
                    $('#orderId').text(data.ID);
                    $('#totalMoney').text(data.Total_money);
                    $('#prePay').text(data.Pre_pay);
                    $('#finalPay').text((data.Total_money - data.Pre_pay - data.Final_pay));
                    $('#final_pay').text(data.Final_pay);
                    $('#Final_is_full').text(data.Final_is_full === 1 ? '是' : '否');
                    $('#createTime').text(new Date(data.Create_time).toLocaleString());
                    // $('#status').text(data.Status === 0 ? '未完成' : '已完成');
                    $('#status').text(Order_Status[data.Status]);
                    Tracking_num = data.Tracking_num;
                    express_type = data.Express;
                    pat_pro_id = data.Pat_pro_id;
                    $('#express').text(KD100_Express_Info[express_type].name); // 假设'0'表示无物流公司
                    $('#trackingNum').text(Tracking_num === '0' ? '-' : Tracking_num); // 假设'0'表示无物流编号
                    $('#finishTime').text(Utc2time(data.Finish_time));
                    $('#crPicContainer').html(data.Cr_pic.split('\n').map(function (src) {
                        return '<img src="/static/uploads/normal_pics/photo_cr_pic/' + src + '" alt="代收款凭证" class="image-preview-item">';
                    }).join(''));
                    $('#payPicContainer').html(data.Pay_pic.split('\n').map(function (src) {
                        return '<img src="/static/uploads/normal_pics/photo_pay_pic/' + src + '" alt="收款凭证" class="image-preview-item">';
                    }).join(''));
                    $('#Final_pay_pic').html("<img src='/static/uploads/normal_pics/order/" + data.Final_pay_pic + "' alt='尾款收款截图' class='image-preview-item'>");
                    // 加载医助、医生姓名
                    let user_id_list = data.Asst_id + ',' + data.Doc_id;
                    $.ajax({
                        url: '/admin/user/list_low',
                        data: {
                            user_id_list: user_id_list,
                        },
                        type: 'post',
                        success: function (res) {
                            if (res.code == 200) {
                                const asstName = res.data.find(data => data.Role_id === "3").Name;
                                const doctorName = res.data.find(data => data.Role_id === "4").Name;
                                $('#asst_name').text(asstName);
                                $('#doctor_name').text(doctorName);
                            }
                        }
                    });
                },
                error: function (xhr) {
                    layer.closeAll('loading');

                    // 获取错误信息
                    let errorMsg = xhr.responseJSON ? xhr.responseJSON.msg : '请求失败，请稍后重试';

                    // 创建模糊覆盖层和错误信息显示
                    let blurOverlay = `
                        <div class="error-overlay">
                            <div class="error-card layui-card">
                                <div class="layui-card-body">
                                    <p style="font-size: 16px; padding: 20px 10px; color: #333;">${errorMsg}</p>
                                    <div style="padding: 10px 0 20px 0;">
                                        <button class="layui-btn layui-btn-normal return-btn" style="padding: 0 30px;">返回</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                    $('body').append(blurOverlay);

                    // 添加动画效果
                    setTimeout(function() {
                        $('.error-overlay').addClass('show');
                    }, 10);

                    // 添加返回按钮点击事件
                    $('.return-btn').click(function () {
                        $('.error-overlay').removeClass('show');
                        setTimeout(function() {
                            window.history.go(-1);
                            $('.error-overlay').remove();
                        }, 300);
                    });
                }
            });
            // 获取赠品数据
            layer.load(2);
            $.ajax({
                url: serverUrl + "/admin/order/gift_data",
                type: "post",
                data: { id: orderId },
                success: function (res) {
                    layer.closeAll('loading');
                    let gift_data = res.data;
                    let html = '';
                    if (gift_data && gift_data.length > 0) {
                        for (let i = 0; i < gift_data.length; i++) {
                            html += '<tr><td>' + gift_data[i].ID + '</td><td>' + gift_data[i].Wh_gift_id + '</td><td>' + gift_data[i].Name + '</td><td>' + gift_data[i].Nums + '</td><td>' + gift_data[i].Price + '</td><td>' + gift_data[i].Nums * gift_data[i].Price + '</td></tr>';
                        }
                    } else {
                        html = '<div style="padding:30px 0;">当前订单未选择赠品</div>';
                    }
                    $('#gift_data').html(html);
                },
                error: function (xhr) {
                    layer.closeAll('loading');

                    // 获取错误信息
                    let errorMsg = xhr.responseJSON ? xhr.responseJSON.msg : '请求失败，请稍后重试';

                    // 创建模糊覆盖层和错误信息显示
                    let blurOverlay = `
                        <div class="error-overlay">
                            <div class="error-card layui-card">
                                <div class="layui-card-body">
                                    <p style="font-size: 16px; padding: 20px 10px; color: #333;">${errorMsg}</p>
                                    <div style="padding: 10px 0 20px 0;">
                                        <button class="layui-btn layui-btn-normal return-btn" style="padding: 0 30px;">返回</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                    $('body').append(blurOverlay);

                    // 添加动画效果
                    setTimeout(function() {
                        $('.error-overlay').addClass('show');
                    }, 10);

                    // 添加返回按钮点击事件
                    $('.return-btn').click(function () {
                        $('.error-overlay').removeClass('show');
                        setTimeout(function() {
                            window.history.go(-1);
                            $('.error-overlay').remove();
                        }, 300);
                    });
                }
            });

            //根据处方ID求出来的病历ID来填充患者基础信息
            var render_patient_info = function (record_ids) {
                layer.load(2);
                $.ajax({
                    url: serverUrl + "/admin/patient_profile/get_patient_ids_by_record_ids",
                    type: "post",
                    data: { record_ids: record_ids },
                    success: function (res) {
                        layer.closeAll('loading');
                        let data = res.data;
                        let html = '';
                        if (data.length > 1) {
                            layer.msg("如订单出现多个患者信息，则属于历史遗留，后续已对患者规则进行了限制：1个订单，只允许1个帐号下的1个患者", {
                                icon: 0,
                                time: 10000
                            });
                        }
                        for (let i = 0; i < data.length; i++) {
                            html += `
                                        <!-- 第1大行 -->
                                        <div class="layui-col-xs3">
                                            <div class="info-item">
                                                <span class="info-label">手机号码：</span>
                                                <span class="info-content">${data[i].Phone || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">患者姓名：</span>
                                                <span class="info-content">${data[i].Name || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">患者性别：</span>
                                                <span class="info-content">${data[i].Sex == 0 ? '女' : '男' || '-'}</span>
                                            </div>
                                        </div>

                                        <div class="layui-col-xs3">
                                            <div class="info-item">
                                                <span class="info-label">关系：</span>
                                                <span class="info-content">${FamilyRelation[data[i].Relation] || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">身高：</span>
                                                <span class="info-content">${data[i].Height || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">体重：</span>
                                                <span class="info-content">${data[i].Weight || '-'}</span>
                                            </div>
                                        </div>

                                        <div class="layui-col-xs3">
                                            <div class="info-item">
                                                <span class="info-label">身份证号：</span>
                                                <span class="info-content">${data[i].Idcard || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">年龄：</span>
                                                <span class="info-content">${date2age(data[i].Born_date)}岁</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">医保卡：</span>
                                                <span class="info-content">${data[i].Ins_card_num || '-'}</span>
                                            </div>
                                        </div>

                                        <div class="layui-col-xs3">
                                            <div class="info-item">
                                                <span class="info-label">患者来源：</span>
                                                <span class="info-content">${Patient_From[data[i].Patient_from] || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">患者等级：</span>
                                                <span class="info-content">${data[i].Level + '级' || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">微信号：</span>
                                                <span class="info-content">${data[i].Weixin || '-'}</span>
                                            </div>
                                        </div>


                                        <div class="layui-col-xs6">
                                            <div class="info-item">
                                                <span class="info-label">患者备注：</span>
                                                <span class="info-content">${data[i].Customer_notes || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">既往病史：</span>
                                                <span class="info-content">${data[i].Medical_history || '-'}</span>
                                            </div>
                                        </div>
                                        <div class="layui-col-xs6">
                                            <div class="info-item">
                                                <span class="info-label">主诉：</span>
                                                <span class="info-content">${data[i].Chief_complaint || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">过敏史：</span>
                                                <span class="info-content">${data[i].Allergies || '-'}</span>
                                            </div>
                                        </div>
                                        <div class="layui-col-xs12">
                                            <div class="info-item">
                                                <span class="info-label">详细地址：</span>
                                                <span class="info-content">${removeAllPipe(data[i].Address) || '-'}</span>
                                            </div>
                                        </div>
                            `;
                        }
                        $('#patient_data').html(html);
                    },
                    error: function (xhr) {
                        layer.closeAll('loading');

                        // 获取错误信息
                        let errorMsg = xhr.responseJSON ? xhr.responseJSON.msg : '获取患者信息失败，请稍后重试';

                        // 创建模糊覆盖层和错误信息显示
                        let blurOverlay = `
                            <div class="error-overlay">
                                <div class="error-card layui-card">
                                    <div class="layui-card-body">
                                        <p style="font-size: 16px; padding: 20px 10px; color: #333;">${errorMsg}</p>
                                        <div style="padding: 10px 0 20px 0;">
                                            <button class="layui-btn layui-btn-normal return-btn" style="padding: 0 30px;">返回</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;
                        $('body').append(blurOverlay);

                        // 添加动画效果
                        setTimeout(function() {
                            $('.error-overlay').addClass('show');
                        }, 10);

                        // 添加返回按钮点击事件
                        $('.return-btn').click(function () {
                            $('.error-overlay').removeClass('show');
                            setTimeout(function() {
                                window.history.go(-1);
                                $('.error-overlay').remove();
                            }, 300);
                        });
                    }
                });
            }
            render_patient_info(Record_ids);
            let ordId = $('#orderId').text();
            if (Tracking_num) {
                $.ajax({
                    url: '/admin/express_info_by_ord_id',
                    data: {
                        id: orderId // 传递订单ID
                    },
                    type: 'post',
                    success: function (res) {
                        if (res.code === 200 && res.data) {
                            // 获取物流记录
                            let expressInfo = res.data;
                            let last_contents = '<a href="javascript:;" class="layui-table-link" id="express_info_link">' + expressInfo.Last_contents + '</a>' || '-';

                            // 更新物流信息
                            $('#express_info_by_ord_id').html(last_contents);

                            // 显示查看详情链接
                            $('#express_info_link').show().on('click', function () {
                                showExpressInfo(express_type, Tracking_num, ordId, pat_pro_id);
                            });
                        }
                    },
                    error: function (err) {
                        console.error('获取物流信息失败：', err);
                    }
                });
            }

            // 显示物流信息模态框
            function showExpressInfo(express_type, tracking_num, ord_id, pat_pro_id) {
                layer.open({
                    type: 2,
                    title: '物流信息查询',
                    area: ['600px', '900px'],
                    shadeClose: true,
                    content: 'express_info.html?express_type=' + express_type + '&tracking_num=' + tracking_num + '&ord_id=' + ord_id + '&pat_pro_id=' + pat_pro_id,
                    end: function () {
                        // 模态框关闭时重新加载物流信息
                        $.ajax({
                            url: '/admin/express_info_by_ord_id',
                            data: {
                                id: orderId
                            },
                            type: 'post',
                            success: function (res) {
                                if (res.code === 200 && res.data) {
                                    let expressInfo = res.data;
                                    let last_contents = expressInfo.Last_contents || '-';
                                    $('#express_info_by_ord_id').text(last_contents);
                                }
                            }
                        });
                    }
                });
            }

            $(document).on('click', '.image-preview-item', function () {
                var imgSrc = $(this).attr('src');

                // 创建预加载图片
                var preloadImg = new Image();
                preloadImg.src = imgSrc;

                // 显示加载动画
                var loadingIndex = layer.load(2, {shade: [0.2, '#000']});

                preloadImg.onload = function() {
                    // 图片加载完成后关闭加载动画
                    layer.close(loadingIndex);

                    // 显示图片查看器
                    layer.photos({
                        photos: {
                            title: '查看凭证图片',
                            data: [{
                                src: imgSrc
                            }]
                        },
                        anim: 5, // 图片切换动画类型，可选值：0-6
                        shade: 0.9, // 遮罩层透明度
                        closeBtn: 1, // 显示关闭按钮
                        footer: false, // 不显示底部栏
                        success: function(layero, index) {
                            // 添加自定义样式到图片查看器
                            $(layero).find('.layui-layer-phimg').css({
                                'border-radius': '8px',
                                'box-shadow': '0 10px 30px rgba(0,0,0,0.15)',
                                'transition': 'all 0.3s'
                            });

                            // 添加缩放动画
                            setTimeout(function() {
                                $(layero).find('.layui-layer-phimg img').css({
                                    'transform': 'scale(1)',
                                    'opacity': '1'
                                });
                            }, 50);
                        }
                    });
                };

                preloadImg.onerror = function() {
                    // 图片加载失败
                    layer.close(loadingIndex);
                    layer.msg('图片加载失败', {icon: 2});
                };
            });
        });
    </script>
</body>

</html>