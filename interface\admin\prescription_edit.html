<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 编辑处方</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
    <style>
        h3 {
            margin: 30px 0 20px 0;
            border-left: 3px solid #666;
            padding-left: 15px;
        }

        table,
        th {
            text-align: center !important;
        }

        .info-content {
            color: #333;
            font-weight: 500;
        }

        .text-area-content {
            background: #f9f9f9;
            padding: 10px;
            border-radius: 4px;
            min-height: 60px;
            white-space: pre-wrap;
        }

        .image-grid {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .image-grid img {
            width: 200px;
            height: 200px;
            object-fit: cover;
            cursor: pointer;
            border-radius: 4px;
            border: 1px solid #ddd;
        }

        .info-item {
            margin-bottom: 10px;
        }

        .info-label {
            font-weight: bold;
            display: inline-block;
            min-width: 100px;
        }
    </style>
</head>

<body>
    <div class="layui-layout layui-layout-admin">
        <!-- 头部 -->
        <div class="layui-header">
            <div class="layui-logo">幸年堂</div>
            <ul class="layui-nav layui-layout-right">
                <li class="layui-nav-item" style="background-color:transparent!important;padding-right:10px">
                    <a href="javascript:;" style="display:flex;align-items:center;color:#fff"><img
                            src="/dist/images/user.png" class="layui-nav-img"><span class="user_name"></span></a>
                    <dl class="layui-nav-child pop_menu">
                        <dd><i class="iconfont">&#xe62f;</i><a href="change_password.html#7">用户资料</a></dd>
                        <dd><i class="iconfont">&#xe601;</i><a href="javascript:void(0)" class="refresh_cache">刷新缓存</a>
                        </dd>
                        <dd><i class="iconfont">&#xe634;</i><a href="login_out.html">退出登录</a></dd>
                    </dl>
                </li>
            </ul>
        </div>
        <!-- 导航 -->
        <ul class="layui-nav layui-nav-tree layui-nav-side" id="menuContainer" lay-bar="disabled" lay-accordion>
            <div style="margin: 50px 0;color: #bbb;text-align: center;">菜单加载中...</div>
        </ul>

        <!-- 主体内容 -->
        <div class="layui-body">
            <div class="body_child">
                <div class="layui-panel">
                    <div class="layui-card-header">
                        <div class="layui-row" style="padding-top:10px;">
                            <div class="layui-col-md11 layui-col-sm10">编辑处方</div>
                        </div>
                    </div>

                    <div class="layui-padding-3" style="min-height: 600px;">
                        <form class="layui-form" lay-filter="form_edit" onsubmit="return false">
                            <input type="hidden" name="id" id="prescription_id">


                            <h3>患者基础信息</h3>
                            <!-- 填充患者基础信息，接口/admin/patient_profile/detail -->
                            <div class="layui-form">
                                <div id="patient_data" class="layui-row">
                                    <i
                                        class='layui-icon layui-icon-loading-1 layui-anim layui-anim-rotate layui-anim-loop'></i>
                                </div>
                            </div>

                            <h3>病历信息</h3>
                            <!-- 填充病历信息，接口/admin/patient_records/detail -->
                            <div class="layui-form">
                                <div id="record_data" class="layui-row">
                                    <i
                                        class='layui-icon layui-icon-loading-1 layui-anim layui-anim-rotate layui-anim-loop'></i>
                                </div>
                            </div>

                            <!-- 舌苔照显示部分 -->
                            <div class="layui-form-item">
                                <label class="layui-form-label">舌苔照</label>
                                <div class="layui-input-block">
                                    <div class="image-grid" id="photo_tongue_container"></div>
                                </div>
                            </div>

                            <!-- 检查单显示部分 -->
                            <div class="layui-form-item">
                                <label class="layui-form-label">检查单</label>
                                <div class="layui-input-block">
                                    <div class="image-grid" id="photo_sheet_container"></div>
                                </div>
                            </div>

                            <h3>诊断信息及医嘱</h3>
                            <div class="layui-form-item">
                                <div class="layui-row">
                                    <!-- 诊断信息 -->
                                    <div class="layui-col-md6 layui-col-sm12">
                                        <label class="layui-form-label">诊断信息</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="diagnosis" placeholder="请输入诊断信息"
                                                class="layui-input" autocomplete="off">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="layui-form-item">
                                <!-- 治疗计划 -->
                                <div class="layui-col-md6 layui-col-sm12">
                                    <label class="layui-form-label">治疗计划</label>
                                    <div class="layui-input-block">
                                        <input type="text" name="tx_plan" placeholder="请输入治疗计划" class="layui-input"
                                            autocomplete="off">
                                    </div>
                                </div>
                            </div>

                            <div class="layui-form-item">
                                <!-- 医嘱 -->
                                <div class="layui-col-md6 layui-col-sm12">
                                    <label class="layui-form-label">医嘱</label>
                                    <div class="layui-input-block">
                                        <input type="text" name="askfor" placeholder="请输入医嘱" class="layui-input"
                                            autocomplete="off">
                                    </div>
                                </div>
                            </div>

                            <h3>用药信息</h3>
                            <!-- 第一行：用药信息 -->
                            <div class="layui-form-item">
                                <div class="layui-inline">
                                    <label class="layui-form-label">共：</label>
                                    <div class="layui-input-inline">
                                        <input type="text" name="tx_day" lay-verify="required" class="layui-input"
                                            oninput="if(value>100)value=100">
                                    </div>
                                    <div class="layui-form-mid" style="margin-left: 10px;">天</div>
                                </div>
                                <div class="layui-inline">
                                    <label class="layui-form-label">药品类型：</label>
                                    <div class="layui-input-inline" style="width: 120px;">
                                        <select name="tx_type" lay-verify="required">
                                            <option value=""></option>
                                            <option value="膏滋">膏滋</option>
                                            <option value="丸剂">丸剂</option>
                                            <option value="汤剂">汤剂</option>
                                            <option value="散剂">散剂</option>
                                            <option value="无糖膏">无糖膏</option>
                                            <option value="水丸">水丸</option>
                                            <option value="内服粉剂">内服粉剂</option>
                                            <option value="外用粉剂">外用粉剂</option>
                                            <option value="中药材">中药材</option>
                                            <option value="清膏">清膏</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="layui-inline" style="margin-left: 30px;">
                                    <label class="layui-form-label">共：</label>
                                    <div class="layui-input-inline" style="width: 120px;">
                                        <input type="text" name="totalDoses" class="layui-input" autocomplete="off">
                                    </div>
                                    <div class="layui-form-mid">剂</div>
                                </div>
                            </div>

                            <!-- 第二行：使用方式和禁忌 -->
                            <div class="layui-form-item">
                                <div class="layui-inline">
                                    <label class="layui-form-label">每</label>
                                    <div class="layui-input-inline" style="width: 50px;">
                                        <input type="text" name="dosage_1" class="layui-input" autocomplete="off">
                                    </div>
                                    <div class="layui-form-mid">天</div>
                                    <div class="layui-input-inline" style="width: 50px;">
                                        <input type="text" name="dosage_2" class="layui-input" autocomplete="off">
                                    </div>
                                    <div class="layui-form-mid">次</div>
                                </div>

                                <div class="layui-inline">
                                    <label class="layui-form-label">每次</label>
                                    <div class="layui-input-inline" style="width: 50px;">
                                        <input type="text" name="dosage_3" class="layui-input" autocomplete="off">
                                    </div>
                                    <div class="layui-input-inline" style="width: 130px;">
                                        <input type="text" name="dosage_4" class="layui-input" placeholder="请输入单位"
                                            autocomplete="off">
                                    </div>
                                </div>

                                <div class="layui-inline">
                                    <label class="layui-form-label">禁忌：</label>
                                    <div class="layui-input-inline" style="width: 300px;">
                                        <input type="text" name="dosage_5" class="layui-input" autocomplete="off">
                                    </div>
                                </div>
                            </div>

                            <!-- 提交按钮 -->
                            <div class="layui-row" style="display: flex;justify-content: center;margin-top: 50px;">
                                <button class="layui-btn" lay-submit lay-filter="formSubmitBtn"
                                    style="margin-right: 50px;" id="confirm_btn">确认修改</button>
                                <button type="reset" class="layui-btn layui-btn-primary"
                                    onclick="history.go(-1)">取消</button>
                            </div>
                        </form>
                    </div>







                </div>
            </div>
            <div class="body_child">
                <div class="layui-panel">
                    <div class="layui-card-header">
                        <div class="layui-row">
                            <div class="layui-col-md11 layui-col-sm10">处方药品信息</div>
                        </div>
                    </div>
                    <div class="layui-padding-5" style="padding-top: 0 !important;min-height: 300px;">
                        <!-- 为了让程序更加清晰赠品数据额外拉出来 -->
                        <div style="display: flex;align-items: center;justify-content: space-between;">
                            <h3>药品列表</h3>
                        </div>
                        <table class="layui-table">
                            <thead>
                                <tr>
                                    <th>库存ID</th>
                                    <th>药品ID</th>
                                    <th>药品名称</th>
                                    <th>单价</th>
                                    <th>库存量</th>
                                    <th>数量</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="prescription_drug_table_body">
                                <!-- 表格内容将在这里动态插入 -->
                            </tbody>
                        </table>
                        <div class="layui-row" style="display: flex;justify-content: center;margin-top:50px;">
                            <button class="layui-btn layui-btn-primary layui-border-green"
                                id="add_meds_btn">药品数据管理</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        layui.use(['element', 'layer', 'util', 'form'], function () {
            var element = layui.element;
            var layer = layui.layer;
            var util = layui.util;
            var form = layui.form;
            var $ = layui.$;

            // 页面顶部插入缓存的用户数据
            let local_userinfo = localStorage.getItem('local_userinfo');
            let user_name = "";
            if (local_userinfo) {
                local_userinfo = JSON.parse(local_userinfo);
                user_name = local_userinfo.Name + " ( " + local_userinfo.Roles + " )";
            } else {
                user_name = "未登录";
            }
            $('.user_name').text(user_name);
            render_menu($);

            $('.refresh_cache').click(function () {
                refresh_cache($);
            });

            // 获取处方ID
            var prescription_id = request.get('id');
            if (!prescription_id) {
                layer.msg('处方ID不能为空', { icon: 2 });
                return;
            }

            //根据处方ID求出来的病历ID来填充患者基础信息
            var render_patient_info = function (record_ids) {
                layer.load(2);
                $.ajax({
                    url: serverUrl + "/admin/patient_profile/get_patient_ids_by_record_ids",
                    type: "post",
                    data: { record_ids: record_ids },
                    success: function (res) {
                        layer.closeAll('loading');
                        let data = res.data;
                        let html = '';
                        for (let i = 0; i < data.length; i++) {
                            html += `
                                        <!-- 第1大行 -->
                                        <div class="layui-col-xs3">
                                            <div class="info-item">
                                                <span class="info-label">手机号码：</span>
                                                <span class="info-content">${data[i].Phone || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">患者姓名：</span>
                                                <span class="info-content">${data[i].Name || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">患者性别：</span>
                                                <span class="info-content">${data[i].Sex == 0 ? '女' : '男' || '-'}</span>
                                            </div>
                                        </div>

                                        <div class="layui-col-xs3">
                                            <div class="info-item">
                                                <span class="info-label">关系：</span>
                                                <span class="info-content">${FamilyRelation[data[i].Relation] || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">身高：</span>
                                                <span class="info-content">${data[i].Height || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">体重：</span>
                                                <span class="info-content">${data[i].Weight || '-'}</span>
                                            </div>
                                        </div>

                                        <div class="layui-col-xs3">
                                            <div class="info-item">
                                                <span class="info-label">身份证号：</span>
                                                <span class="info-content">${data[i].Idcard || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">年龄：</span>
                                                <span class="info-content">${date2age(data[i].Born_date)}岁</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">医保卡：</span>
                                                <span class="info-content">${data[i].Ins_card_num || '-'}</span>
                                            </div>
                                        </div>

                                        <div class="layui-col-xs3">
                                            <div class="info-item">
                                                <span class="info-label">患者来源：</span>
                                                <span class="info-content">${Patient_From[data[i].Patient_from] || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">患者等级：</span>
                                                <span class="info-content">${data[i].Level + '级' || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">微信号：</span>
                                                <span class="info-content">${data[i].Weixin || '-'}</span>
                                            </div>
                                        </div>


                                        <div class="layui-col-xs6">
                                            <div class="info-item">
                                                <span class="info-label">患者备注：</span>
                                                <span class="info-content">${data[i].Customer_notes || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">既往病史：</span>
                                                <span class="info-content">${data[i].Medical_history || '-'}</span>
                                            </div>
                                        </div>
                                        <div class="layui-col-xs6">
                                            <div class="info-item">
                                                <span class="info-label">主诉：</span>
                                                <span class="info-content">${data[i].Chief_complaint || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">过敏史：</span>
                                                <span class="info-content">${data[i].Allergies || '-'}</span>
                                            </div>
                                        </div>
                                        <div class="layui-col-xs12">
                                            <div class="info-item">
                                                <span class="info-label">详细地址：</span>
                                                <span class="info-content">${data[i].Address.replace("|", " ") || '-'}</span>
                                            </div>
                                        </div>
                            `;
                        }
                        $('#patient_data').html(html);
                    },
                    error: function () {
                        layer.msg('获取患者信息失败', { icon: 2 });
                    }
                });
            }

            // 加载处方详情
            $.ajax({
                url: '/admin/prescription/detail',
                type: 'POST',
                data: { id: prescription_id },
                success: function (res) {
                    if (res.code === 200) {
                        var data = res.data;

                        // 设置表单值
                        form.val('form_edit', {
                            'id': data.ID,
                            'diagnosis': data.Diagnosis,
                            'tx_plan': data.Tx_plan,
                            'askfor': data.Askfor,
                            'tx_day': data.Tx_day,
                            'tx_type': data.Tx_type,
                            'totalDoses': data.TotalDoses
                        });

                        // 处理用法用量
                        if (data.Dosage) {
                            var dosage = data.Dosage.split('|');
                            form.val('form_edit', {
                                'dosage_1': dosage[0],
                                'dosage_2': dosage[1],
                                'dosage_3': dosage[2],
                                'dosage_4': dosage[3],
                                'dosage_5': dosage[4]
                            });
                        }

                        // 如果处方状态不是1也不是2，则禁止修改处方
                        if (data.Status != 1 && data.Status != 2) {
                            $('#confirm_btn,#add_meds_btn').attr('disabled', true).addClass('layui-btn-disabled').text('当前状态【' + Pre_Status[data.Status] + '】不允许操作')
                        }

                        // 如果Verify_1大于0，则移除确认修改、取消、药品数据管理按钮，并设置药品列表中的删除按钮为不可操作状态
                        if (data.Verify_1 > 0) {
                            // 移除按钮
                            $('#confirm_btn, button[type="reset"], #add_meds_btn').remove();

                            // 保存Verify_1状态供其他函数使用
                            window.prescriptionVerified = true;
                        } else {
                            window.prescriptionVerified = false;
                        }

                        // 填充患者基础信息
                        render_patient_info(data.Record_id);

                        // 获取病历信息
                        var render_record_info = function (record_id) {
                            layer.load(2);
                            $.ajax({
                                url: serverUrl + "/admin/patient_records/detail",
                                type: "post",
                                data: { id: record_id },
                                success: function (res) {
                                    layer.closeAll('loading');
                                    if (res.code == 200) {
                                        let data = res.data;
                                        let html = '';

                                        // 构建病历信息HTML
                                        html += `
                                            <!-- 第1行：患者现状 -->
                                            <div class="layui-col-md4">
                                                <div class="info-item">
                                                    <span class="info-label">患者主诉：</span>
                                                    <span class="info-content">${data.Chief_complaint || '-'}</span>
                                                </div>
                                                <div class="info-item">
                                                    <span class="info-label">复诊主诉：</span>
                                                    <span class="info-content">${data.Re_chief_complaint || '-'}</span>
                                                </div>
                                                <div class="info-item">
                                                    <span class="info-label">现病史：</span>
                                                    <span class="info-content">${data.History_of_present_illness || '-'}</span>
                                                </div>
                                                <div class="info-item">
                                                    <span class="info-label">现需治疗：</span>
                                                    <span class="info-content">${data.Now_needs || '-'}</span>
                                                </div>
                                                <div class="info-item">
                                                    <span class="info-label">大小便情况：</span>
                                                    <span class="info-content">${data.Urination || '-'}</span>
                                                </div>
                                                <div class="info-item">
                                                    <span class="info-label">有无三高：</span>
                                                    <span class="info-content">${data.Triad || '-'}</span>
                                                </div>
                                            </div>

                                            <!-- 第2行：病史记录 -->
                                            <div class="layui-col-md4">
                                                <div class="info-item">
                                                    <span class="info-label">既往病史：</span>
                                                    <span class="info-content">${data.Past_medical_history || '-'}</span>
                                                </div>
                                                <div class="info-item">
                                                    <span class="info-label">个人史：</span>
                                                    <span class="info-content">${data.Personal_history || '-'}</span>
                                                </div>
                                                <div class="info-item">
                                                    <span class="info-label">家族史：</span>
                                                    <span class="info-content">${data.Family_history || '-'}</span>
                                                </div>
                                                <div class="info-item">
                                                    <span class="info-label">过敏史：</span>
                                                    <span class="info-content">${data.Allergy_history || '-'}</span>
                                                </div>
                                                <div class="info-item">
                                                    <span class="info-label">用药史：</span>
                                                    <span class="info-content">${data.Past_medication_history || '-'}</span>
                                                </div>
                                                <div class="info-item">
                                                    <span class="info-label">上次用药时间：</span>
                                                    <span class="info-content">${data.Last_medication_time ? data.Last_medication_time.replace('T', ' ').replace('Z', '') : '-'}</span>
                                                </div>
                                                <div class="info-item">
                                                    <span class="info-label">上次用药情况：</span>
                                                    <span class="info-content">${data.Last_medical || '-'}</span>
                                                </div>
                                            </div>

                                            <!-- 第3行：诊疗信息 -->
                                            <div class="layui-col-md4">
                                                <div class="info-item">
                                                    <span class="info-label">诊断信息：</span>
                                                    <span class="info-content">${data.Diagnosis_information || '-'}</span>
                                                </div>
                                                <div class="info-item">
                                                    <span class="info-label">治疗方案：</span>
                                                    <span class="info-content">${data.Treatment_plan || '-'}</span>
                                                </div>
                                                <div class="info-item">
                                                    <span class="info-label">患者状态：</span>
                                                    <span class="info-content">${Record_Status[data.Status] || '-'}</span>
                                                </div>
                                                <div class="info-item">
                                                    <span class="info-label">核销时间：</span>
                                                    <span class="info-content">${data.Discharge_time == '0001-01-01T00:00:00Z' ? "未核销" : "已核销"}</span>
                                                </div>
                                                <div class="info-item">
                                                    <span class="info-label">创建时间：</span>
                                                    <span class="info-content">${data.Create_time ? data.Create_time.replace('T', ' ').replace('Z', '') : '-'}</span>
                                                </div>
                                                <div class="info-item">
                                                    <span class="info-label">对应处方：</span>
                                                    <span class="info-content">${data.Pre_id == 0 ? "未开处方" : data.Pre_id}</span>
                                                </div>
                                                <div class="info-item">
                                                    <span class="info-label">意向剂型：</span>
                                                    <span class="info-content">${data.Tx_day || '-'}天，${data.Tx_type || '-'}</span>
                                                </div>
                                            </div>
                                        `;
                                        $('#record_data').html(html);

                                        // 显示舌苔照图片
                                        if (data.Photo_tongue) {
                                            let tongue_photos = data.Photo_tongue.split('\n');
                                            let container = $('#photo_tongue_container');
                                            container.empty();
                                            tongue_photos.forEach(photo => {
                                                if (photo.trim()) {
                                                    let pic = '/static/uploads/normal_pics/photo_tongue/' + photo.trim();
                                                    container.append(`<img src="${pic}" alt="舌苔照" onclick="viewImage('${pic}')">`);
                                                }
                                            });
                                        }

                                        // 显示检查单图片
                                        if (data.Photo_sheet) {
                                            let sheet_photos = data.Photo_sheet.split('\n');
                                            let container = $('#photo_sheet_container');
                                            container.empty();
                                            sheet_photos.forEach(photo => {
                                                if (photo.trim()) {
                                                    let pic = '/static/uploads/normal_pics/photo_sheet/' + photo.trim();
                                                    container.append(`<img src="${pic}" alt="检查单" onclick="viewImage('${pic}')">`);
                                                }
                                            });
                                        }

                                        // 自动填充诊断信息和治疗计划（仅当表单为空时）
                                        if (data.Diagnosis_information && !$('input[name="diagnosis"]').val()) {
                                            $('input[name="diagnosis"]').val(data.Diagnosis_information);
                                        }
                                        if (data.Treatment_plan && !$('input[name="tx_plan"]').val()) {
                                            $('input[name="tx_plan"]').val(data.Treatment_plan);
                                        }

                                        // 自动填充意向剂型信息（仅当未选择时）
                                        if (data.Tx_day && !$('select[name="tx_day"]').val()) {
                                            $('select[name="tx_day"]').val(data.Tx_day);
                                            form.render('select');
                                        }
                                        if (data.Tx_type && !$('select[name="tx_type"]').val()) {
                                            $('select[name="tx_type"]').val(data.Tx_type);
                                            form.render('select');
                                        }
                                    } else {
                                        layer.msg(res.msg || '获取病历信息失败', { icon: 2 });
                                    }
                                },
                                error: function () {
                                    layer.closeAll('loading');
                                    layer.msg('获取病历信息失败', { icon: 2 });
                                }
                            });
                        };
                        render_record_info(data.Record_id);

                        // 重新渲染表单
                        form.render();

                        // 加载药品信息
                        loadPrescriptionDrugs(prescription_id);
                    } else {
                        layer.msg(res.msg || '加载失败', { icon: 2 });
                    }
                },
                error: function () {
                    layer.msg('服务器错误', { icon: 2 });
                }
            });

            // 添加药品按钮点击事件
            $('#add_meds_btn').on('click', function () {
                // 先获取当前处方的药品数据
                $.ajax({
                    url: '/admin/prescription/drug',
                    type: 'POST',
                    data: { pre_id: prescription_id },
                    success: function (res) {
                        if (res.code === 200) {
                            // 格式化药品数据
                            if(res.data){
                                var existingDrugs = res.data.map(function (item) {
                                return {
                                    id: item.ID,
                                    wh_drug_id: item.Wh_drug_id,
                                    drug_id: item.Drug_id,
                                    quantity: item.Quantity,
                                    name: item.Name,
                                    price: item.Price
                                    };
                                });
                            }

                            // 设置全局变量，供iframe使用
                            window.drugModalData = {
                                prescriptionId: prescription_id,
                                existingDrugs: existingDrugs
                            };

                            // 打开模态框
                            layer.open({
                                type: 2,
                                title: '管理处方药品',
                                area: ['1200px', '800px'],
                                shadeClose: true,
                                content: '/admin/warehouse_drug_edit_toolspage.html'
                            });
                        } else {
                            layer.msg('获取药品数据失败：' + res.msg, { icon: 2 });
                        }
                    }
                });
            });

            // 加载处方药品信息
            function loadPrescriptionDrugs(prescription_id) {
                $.ajax({
                    url: '/admin/prescription/drug',
                    type: 'POST',
                    data: { pre_id: prescription_id },
                    success: function (res) {
                        if (res.code === 200) {
                            var tableBody = $('#prescription_drug_table_body');
                            tableBody.empty();
                            if(res.data){
                                res.data.forEach(function (item) {
                                    // 根据处方审核状态决定删除按钮的样式和功能
                                    let deleteBtn = '';
                                    if (window.prescriptionVerified) {
                                        // 已审核处方，删除按钮不可用
                                        deleteBtn = `<button type="button" class="layui-btn layui-btn-xs layui-btn-disabled"
                                                    disabled title="已审核的处方不可修改" style="cursor:not-allowed">删除</button>`;
                                    } else {
                                        // 未审核处方，删除按钮可用
                                        deleteBtn = `<button type="button" class="layui-btn layui-btn-xs delete-row">删除</button>`;
                                    }

                                    var row = `
                                        <tr>
                                            <td data-id="${item.ID || 0}">${item.Wh_drug_id || '-'}</td>
                                            <td>${item.Drug_id || '-'}</td>
                                            <td>${item.Name || '-'}</td>
                                            <td>${item.Price || 0}</td>
                                            <td>${item.Wh_quantity || 0}</td>
                                            <td>${item.Quantity || 0}</td>
                                            <td>${deleteBtn}</td>
                                        </tr>
                                    `;
                                    tableBody.append(row);
                                });
                            }
                        } else {
                            layer.msg(res.msg || '加载药品信息失败', { icon: 2 });
                        }
                    },
                    error: function () {
                        layer.msg('服务器错误', { icon: 2 });
                    }
                });
            }

            // 绑定删除按钮事件
            function bindDeleteEvent() {
                $('#prescription_drug_table_body').on('click', '.delete-row', function () {
                    var $this = $(this);
                    layer.confirm('您确定要删除该药材吗？确认后会直接生效，无需二次保存。', {
                        btn: ['确定','取消'], // 按钮文本
                        title: '操作确认', // 标题
                        anim: 1, // 动画效果
                        icon: 3 // 问号图标
                    }, function(index){
                        // 用户点击确定后的回调
                        layer.close(index); // 关闭确认框
                        $.ajax({
                            url: '/admin/prescription/delete_drug',
                            type: 'POST',
                            data: {
                                pre_id: prescription_id,
                                pre_drug_id: $this.closest('tr').find('td:eq(0)').attr('data-id'),
                            },
                            success: function (res) {
                                if (res.code === 200) {
                                    loadPrescriptionDrugs(prescription_id);
                                    // $this.closest('tr').remove();
                                    // form.render();
                                } else {
                                    layer.msg(res.msg || '删除失败', { icon: 2 });
                                }
                            },
                            error: function (res) {
                                layer.closeAll('loading');
                                layer.msg(res.responseJSON.msg, { icon: 2, time: 1000 });
                            }
                        });
                    });
                });
            }

            // 初始绑定删除事件
            bindDeleteEvent();

            // 监听数量输入变化
            $(document).on('input', '.single-dose', function () {
                var value = $(this).val();

                // 限制最小值
                if (value < 0.1) {
                    $(this).val(0.1);
                }

                // 限制小数点后一位
                if (value.toString().split('.')[1]?.length > 1) {
                    $(this).val(parseFloat(value).toFixed(1));
                }
            });

            // 表单提交
            form.on('submit(formSubmitBtn)', function (data) {
                let field = data.field;
                field.id = prescription_id;  // 添加处方ID

                // 收集药品数据
                let drugs = [];
                $('#prescription_drug_table_body tr').each(function () {
                    let drug = {
                        wh_drug_id: $(this).find('td:eq(0)').text(),  // 库存ID
                        drug_id: $(this).find('td:eq(1)').text(),  // 药品ID
                        single_dose: parseFloat($(this).find('td:eq(5)').text())  // 数量
                    };

                    // 检查库存是否充足
                    // let stock = parseFloat($(this).find('td:eq(4)').text());
                    // if (stock < 1) {
                    //     layer.msg('药品库存ID：' + drug.wh_drug_id + ' 库存不足，请检查', { icon: 2 });
                    //     return false;
                    // }

                    drugs.push(drug);
                });

                // 检查是否选择了药品
                if (drugs.length === 0) {
                    layer.msg('请至少选择一个药品', { icon: 2 });
                    return false;
                }

                // 检查必填字段
                if (!field.diagnosis) {
                    layer.msg('请输入诊断信息', { icon: 2 });
                    return false;
                }
                if (!field.tx_type) {
                    layer.msg('请选择药品类型', { icon: 2 });
                    return false;
                }
                if (!field.tx_day) {
                    layer.msg('请选择疗程天数', { icon: 2 });
                    return false;
                }

                // 收集用法用量数据
                let dosage = [];
                for (let key in field) {
                    if (key.startsWith('dosage_')) {
                        dosage.push(field[key] || '');
                    }
                }
                field.dosage = dosage.join('|');
                field.drugs = drugs;

                // 发送请求
                layer.load(2);
                $.ajax({
                    url: '/admin/prescription/edit',
                    type: 'POST',
                    data: field,
                    success: function (res) {
                        layer.closeAll('loading');
                        if (res.code === 200) {
                            layer.confirm('处方修改成功，是否返回列表页？', {
                                icon: 1,
                                title: '提示'
                            }, function (index) {
                                layer.close(index);
                                window.location.href = 'prescription_list.html';
                            });
                        } else {
                            layer.msg(res.msg || '修改失败', { icon: 2 });
                        }
                    },
                    error: function (xhr) {
                        layer.closeAll('loading');
                        layer.msg(xhr.responseJSON?.msg || '提交失败', { icon: 2 });
                    }
                });

                return false;
            });
        });
    </script>

    <script>
        // 图片查看函数
        function viewImage(src) {
            layui.use(['layer'], function () {
                var layer = layui.layer;
                layer.photos({
                    photos: {
                        title: "查看图片",
                        start: 0,
                        data: [{ src: src }]
                    },
                    footer: false
                });
            });
        }
    </script>
</body>

</html>