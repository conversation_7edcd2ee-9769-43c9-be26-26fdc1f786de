package admin

import (
	"fmt"
	"mstproject/pkg/common"
	"mstproject/pkg/database"
	"net/http"
	"strconv"
)

// 文章分类列表：
func Article_category_list(w http.ResponseWriter, r *http.Request) {
	api_id := 90
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}

	// 分页参数
	limit, err := strconv.Atoi(r.FormValue("limit"))
	if err != nil || limit < 1 {
		limit = 10
	}
	page, err := strconv.Atoi(r.FormValue("page"))
	if err != nil || page < 1 {
		page = 1
	}
	offset := (page - 1) * limit

	// 搜索参数
	var attSql string
	var params []interface{}
	key := r.FormValue("key")
	if key != "" {
		attSql = " WHERE (title LIKE ? OR description LIKE ?)"
		params = append(params, "%"+key+"%", "%"+key+"%")
	}

	// 查询总数
	var count int
	countSql := "SELECT COUNT(id) FROM article_category" + attSql
	err = database.GetOne(countSql, &count, params...)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "读取数据总数失败",
			"err":  err.Error(),
		})
		return
	}

	// 查询分类列表
	type ArticleCategory struct {
		ID          int    `db:"id"`          // 分类ID
		Title       string `db:"title"`       // 分类名称
		Sort        int    `db:"sort"`        // 排序
		Description string `db:"description"` // 描述
		Create_time string `db:"create_time"` // 创建时间
	}

	sql := "SELECT id, title, description, sort, create_time FROM article_category" + attSql + " ORDER BY sort desc LIMIT ?, ?"
	params = append(params, offset, limit)
	var article_category []ArticleCategory
	err = database.GetAll(sql, &article_category, params...)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败",
			"err":  err.Error(),
		})
		return
	}

	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":  200,
		"msg":   "ok",
		"data":  article_category,
		"count": count,
	})
}

// 添加文章分类
func Article_category_add(w http.ResponseWriter, r *http.Request) {
	api_id := 91
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	title, err := common.CheckStr(r.FormValue("title"))
	if err != nil || title == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "分类名称不能为空",
		})
		return
	}
	description := r.FormValue("description")
	if description == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "分类描述不能为空",
		})
		return
	}
	// 防止重复
	sql := "SELECT id FROM article_category WHERE title = ? limit 1"
	var id int
	err = database.GetOne(sql, &id, title)
	if err == nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "分类名称已存在",
		})
		return
	}

	// 获取最大排序值
	var maxSort int
	sql = "SELECT COALESCE(MAX(sort), 0) FROM article_category"
	err = database.GetOne(sql, &maxSort)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "获取排序值失败",
			"err":  err.Error(),
		})
		return
	}

	// 添加分类
	sql = "INSERT INTO article_category (title, description, sort) VALUES (?, ?, ?)"
	result, err := database.Query(sql, title, description, maxSort+10)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "添加分类失败",
			"err":  err.Error(),
			// "sql":  common.DebugSql(sql, title),
		})
		return
	}
	new_id, _ := result.LastInsertId()
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "添加分类成功",
		"id":   new_id,
	})
}

// 修改文章分类
func Article_category_edit(w http.ResponseWriter, r *http.Request) {
	api_id := 92
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	id, err := common.CheckInt(r.FormValue("id"))
	if err != nil || id < 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "分类ID不能为空",
		})
		return
	}
	title, err := common.CheckStr(r.FormValue("Title"))
	if err != nil || title == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "分类名称不能为空",
		})
		return
	}
	description := r.FormValue("Description")
	if description == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "分类描述不能为空",
		})
		return
	}
	sort, err := common.CheckInt(r.FormValue("Sort"))
	if err != nil || sort < 0 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "排序不能为空",
		})
		return
	}
	// 修改分类
	sql := "UPDATE article_category SET title = ?, sort = ?, description = ? WHERE id = ?"
	result, err := database.Query(sql, title, sort, description, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "修改分类失败",
			"err":  err.Error(),
			// "sql":  common.DebugSql(sql, title, sort, id, description),
		})
		return
	}
	RowsAffected, _ := result.RowsAffected()
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":         200,
		"msg":          "修改分类成功",
		"RowsAffected": RowsAffected,
	})
	if RowsAffected > 0 {
		common.Add_log(fmt.Sprintf("修改文章分类，ID：%d，名称：%s，排序：%d", id, title, sort), r)
	}
}

// 删除文章分类
func Article_category_del(w http.ResponseWriter, r *http.Request) {
	api_id := 93
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	id, err := common.CheckInt(r.FormValue("id"))
	if err != nil || id < 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "分类ID不能为空",
		})
		return
	}
	// 删除分类，同时删除分类下的文章，使用事务操作
	sqls := []database.SQLExec{
		{Query: "DELETE FROM article_category WHERE id = ?",
			Args: []interface{}{
				id,
			}},
		{Query: "DELETE FROM article WHERE pid = ?",
			Args: []interface{}{
				id,
			}},
	}
	RowsAffected, err := database.ExecuteTransaction_with_affectedRows(sqls)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "文章分类及其下所有文章删除失败",
			"err":  err.Error(),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  fmt.Sprintf("文章分类及其下所有文章删除成功，影响行数: %d", RowsAffected),
	})
	if RowsAffected > 0 {
		common.Add_log(fmt.Sprintf("删除文章分类，ID：%d，影响行数：%d", id, RowsAffected), r)
	}
}

// 文章详情
func Article_category_detail(w http.ResponseWriter, r *http.Request) {
	api_id := 92 //与删除同权
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	id, err := common.CheckInt(r.FormValue("id"))
	if err != nil || id < 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "文章ID不能为空",
		})
		return
	}
	// 查询文章详情
	type ArticleDetail struct {
		ID          int    `db:"id"`          // 文章ID
		Title       string `db:"title"`       // 标题
		Description string `db:"description"` //描述
		Sort        int    `db:"sort"`        // 排序
		Create_time string `db:"create_time"` // 创建时间
	}
	sql := "SELECT id,title,description,sort,create_time FROM article_category WHERE id = ?"
	var article_detail ArticleDetail
	err = database.GetOne(sql, &article_detail, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败",
			"err":  err.Error(),
			// "sql":  common.DebugSql(sql, id),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "ok",
		"data": article_detail,
		// "sql":  common.DebugSql(sql, id),
	})
}
