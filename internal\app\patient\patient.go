package patient

import (
	"bytes"
	"encoding/json"
	"fmt"
	"image"
	"image/jpeg"
	"mstproject/pkg/common"
	"mstproject/pkg/config"
	"mstproject/pkg/database"
	"net/http"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/nfnt/resize"
)

// 取消当前聊天室
func Rtc_room_status_set(w http.ResponseWriter, r *http.Request) {
	// TODO: 测试阶段该环节暂未JWT鉴权
	asst_id, err := common.CheckInt(r.FormValue("asst_id"))
	if err != nil || asst_id < 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "参数错误",
			"err":  "asst_id不能为空",
		})
		return
	}
	id, err := common.CheckInt(r.FormValue("id"))
	if err != nil || id < 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "参数错误",
			"err":  "id不能为空",
		})
		return
	}
	sql := "UPDATE rtc_room SET status = 2 WHERE id = ?"
	result, err := database.Query(sql, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "设置失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, id),
		})
		return
	}
	RowsAffected, _ := result.RowsAffected()
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":         200,
		"msg":          "设置成功",
		"RowsAffected": RowsAffected,
	})
	if RowsAffected > 0 {
		useragent := r.FormValue("useragent")
		common.Add_log_wxapp(fmt.Sprintf("取消线上诊室，ID：%d", id), asst_id, r, useragent)
	}
}

// 等待视频问诊的病历列表 - 医助使用接口
func Rtc_room_list(w http.ResponseWriter, r *http.Request) {
	// TODO: 测试阶段该环节暂未JWT鉴权
	type RtcRoom struct {
		ID             int    `db:"id"`             // 房间ID
		Department_id  int    `db:"department_id"`  // 科室ID
		Doc_id         int    `db:"doc_id"`         // 医生ID
		Asst_id        int    `db:"asst_id"`        // 医助ID
		Pat_id         int    `db:"pat_id"`         // 患者帐号ID
		Pat_pro_id     int    `db:"pat_pro_id"`     // 患者ID
		Status         int    `db:"status"`         // 房间状态
		Scheduled_time string `db:"scheduled_time"` // 预约时间
		Name           string `db:"name"`           // 患者姓名
		Record_id      int    `db:"record_id"`      // 病历ID
	}
	asst_id, err := common.CheckInt(r.FormValue("asst_id"))
	if err != nil || asst_id < 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "参数错误",
			"err":  "asst_id不能为空",
		})
		return
	}

	sql := `
    
        SELECT a.id,a.department_id,a.doc_id,a.asst_id,a.record_id,a.pat_pro_id,a.pat_id,a.status,a.scheduled_time,b.name
        FROM rtc_room as a left join patient_profile as b on a.pat_pro_id = b.id
        WHERE a.status = 0 and a.asst_id = ?
    
    `

	// 获取参数
	pageStr := r.FormValue("page")
	limitStr := r.FormValue("limit")
	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit < 1 {
		limit = 10
	}
	offset := (page - 1) * limit
	//存储参数的切片
	params := []interface{}{asst_id}
	// 添加排序和分页条件
	sql += " ORDER BY a.id DESC LIMIT ? OFFSET ?"
	params = append(params, limit, offset)

	var rtcRoom []RtcRoom
	err = database.GetAll(sql, &rtcRoom, params...)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败",
			"err":  err.Error(),
			// "sql":  common.DebugSql(sql, patientid, limit, offset),
		})
		return
	}

	// 如果请求头中不包含WebSocket字段，表示这是普通HTTP请求，返回正常的JSON响应
	// WebSocket连接仅用于通知，而不用于直接数据获取
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "ok",
		"data": rtcRoom,
		"sql":  common.DebugSql(sql, params...),
	})
}

// 获取患者处方信息
func Patient_pre(w http.ResponseWriter, r *http.Request) {
	// jwt鉴权
	authHeader := r.Header.Get("Authorization")
	tokenString := strings.TrimPrefix(authHeader, "Bearer ")
	claims, err := common.ValidateJWTToken(tokenString)
	if err != nil {
		// token过期提示：Token is expired
		common.JSONResponse(w, http.StatusUnauthorized, map[string]interface{}{
			"code":  500,
			"msg":   "鉴权失败 - token",
			"error": err.Error(),
		})
		return
	}
	patientid := claims["patientid"].(float64)
	if patientid == 0 {
		common.JSONResponse(w, http.StatusUnauthorized, map[string]interface{}{
			"code":  500,
			"msg":   "鉴权失败 - patientid",
			"error": "Invalid token: patientid is missing",
		})
		return
	}
	type Prescription struct {
		ID               int    `db:"id"`               // 处方ID
		Dosage           string `db:"dosage"`           // 用法用量
		TotalDoses       int    `db:"totalDoses"`       // 疗程-剂量
		Tx_type          string `db:"tx_type"`          // 疗程-剂型：膏滋，丸剂，汤剂，散剂
		Tx_day           string `db:"tx_day"`           // 疗程-天数
		Askfor           string `db:"askfor"`           // 医嘱
		Doc_id           int    `db:"doc_id"`           // 医生ID
		Asst_id          int    `db:"asst_id"`          // 医助ID
		Verify_1_user_id int    `db:"verify_1_user_id"` // 审核人ID - 医生
		Verify_2_user_id int    `db:"verify_2_user_id"` // 审核人ID - 药师
		Verify_3_user_id int    `db:"verify_3_user_id"` // 审核人ID - 调配
		Verify_4_user_id int    `db:"verify_4_user_id"` // 审核人ID - 发药
		Create_time      string `db:"create_time"`      // 创建时间
	}
	// 处方ID
	pre_id, err := common.CheckInt(r.FormValue("pre_id"))
	if err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "参数错误",
			"err":  err.Error(),
		})
		return
	}
	// 处方信息
	sql := `select id,totalDoses,tx_type,tx_day,askfor,dosage,doc_id,asst_id,verify_1_user_id,verify_2_user_id,verify_3_user_id,verify_4_user_id,create_time from prescription where id = ? and pat_id = ?`
	var prescription Prescription
	err = database.GetOne(sql, &prescription, pre_id, patientid)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]any{
			"code": 500,
			"msg":  "没有查寻到对应处方",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, pre_id, patientid),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "ok",
		"data": prescription,
	})
}

// 获取患者病历信息
func Patient_records(w http.ResponseWriter, r *http.Request) {
	// 患者病历列表
	// jwt鉴权
	authHeader := r.Header.Get("Authorization")
	tokenString := strings.TrimPrefix(authHeader, "Bearer ")
	claims, err := common.ValidateJWTToken(tokenString)
	if err != nil {
		// token过期提示：Token is expired
		common.JSONResponse(w, http.StatusUnauthorized, map[string]interface{}{
			"code":  500,
			"msg":   "鉴权失败 - token",
			"error": err.Error(),
		})
		return
	}
	patientid := claims["patientid"].(float64)
	if patientid == 0 {
		common.JSONResponse(w, http.StatusUnauthorized, map[string]interface{}{
			"code":  500,
			"msg":   "鉴权失败 - patientid",
			"error": "Invalid token: patientid is missing",
		})
		return
	}

	// SQL语句与pat_id无关联，所以鉴定家庭成员ID是否在JWT鉴权的patientid中
	pat_pro_id, _ := strconv.Atoi(r.FormValue("pat_pro_id"))
	err = common.Is_in_family(w, int(patientid), pat_pro_id)
	if err != nil {
		common.JSONResponse(w, http.StatusForbidden, map[string]interface{}{
			"code":  403,
			"msg":   "鉴权失败 - 家庭成员ID不在JWT鉴权的patientid中",
			"error": err.Error(),
		})
		return
	}

	sql := `
    
        SELECT a.id, a.pat_id,a.pat_pro_id, a.doc_id, a.asst_id, a.chief_complaint, 
        a.history_of_present_illness, a.past_medical_history, a.personal_history, 
        a.family_history, a.allergy_history, a.diagnosis_information, a.treatment_plan, 
        a.discharge_time, a.status, a.create_time, a.scheduled_time, 
        a.department_id,
		a.pre_id,
        c.name doctor_name
        FROM patient_records AS a 
        LEFT JOIN rbac_user AS c ON c.id = a.doc_id 
        WHERE a.pat_pro_id = ?
    
    `

	// 获取参数
	pageStr := r.FormValue("page")
	limitStr := r.FormValue("limit")
	statusStr := r.FormValue("status")
	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit < 1 {
		limit = 10
	}
	offset := (page - 1) * limit
	//存储参数的切片
	params := []interface{}{pat_pro_id}
	status, err := strconv.Atoi(statusStr)
	if err == nil && status > -1 {
		sql += " AND a.status = ?"
		params = append(params, status)
	}
	// 添加排序和分页条件
	sql += " ORDER BY a.id DESC LIMIT ? OFFSET ?"
	params = append(params, limit, offset)

	// 定义结果结构体
	type Patient_records struct {
		ID                         int    `db:"id"`                         // 主键
		Pat_id                     int    `db:"pat_id"`                     // 用户帐号ID
		Pre_id                     int    `db:"pre_id"`                     // 对应处方ID
		Pat_pro_id                 int    `db:"pat_pro_id"`                 // 用户信息ID
		Doc_id                     int    `db:"doc_id"`                     // 医生ID
		Asst_id                    int    `db:"asst_id"`                    // 医助ID
		Chief_complaint            string `db:"chief_complaint"`            // 主诉
		History_of_present_illness string `db:"history_of_present_illness"` // 既往史
		Past_medical_history       string `db:"past_medical_history"`       // 过往病史
		Personal_history           string `db:"personal_history"`           // 个人史
		Family_history             string `db:"family_history"`             // 家族史
		Allergy_history            string `db:"allergy_history"`            // 过敏史
		Diagnosis_information      string `db:"diagnosis_information"`      // 诊断信息
		Treatment_plan             string `db:"treatment_plan"`             // 治疗方案
		Discharge_time             string `db:"discharge_time"`             // 出院时间
		Status                     int    `db:"status"`                     // 病历状态
		Create_time                string `db:"create_time"`                // 创建时间
		Scheduled_time             string `db:"scheduled_time"`             // 预约时间
		Doctor_Name                string `db:"doctor_name"`                // 医生姓名
		Department_id              int    `db:"department_id"`              // 科室ID
	}
	var patient_records []Patient_records
	err = database.GetAll(sql, &patient_records, params...)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败",
			"err":  err.Error(),
			// "sql":  common.DebugSql(sql, patientid, limit, offset),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "ok",
		"data": patient_records,
		"sql":  common.DebugSql(sql, params...),
	})
}
func Patient_rtc_room_list(w http.ResponseWriter, r *http.Request) {
	// 患者视频问诊列表 - 小程序使用接口
	// jwt鉴权
	authHeader := r.Header.Get("Authorization")
	tokenString := strings.TrimPrefix(authHeader, "Bearer ")
	claims, err := common.ValidateJWTToken(tokenString)
	if err != nil {
		// token过期提示：Token is expired
		common.JSONResponse(w, http.StatusUnauthorized, map[string]interface{}{
			"code":  500,
			"msg":   "鉴权失败 - token",
			"error": err.Error(),
		})
		return
	}
	patientid := claims["patientid"].(float64)
	if patientid == 0 {
		common.JSONResponse(w, http.StatusUnauthorized, map[string]interface{}{
			"code":  500,
			"msg":   "鉴权失败 - patientid",
			"error": "Invalid token: patientid is missing",
		})
		return
	}
	// 病历ID
	record_id, err := common.CheckInt(r.FormValue("record_id"))
	if err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "参数错误",
			"err":  err.Error(),
		})
		return
	}
	// 状态
	status, err := common.CheckInt(r.FormValue("status"))
	if err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "参数错误",
			"err":  err.Error(),
		})
		return
	}
	// 用户ID
	pat_pro_id, err := common.CheckInt(r.FormValue("pat_pro_id"))
	if err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "参数错误",
			"err":  err.Error(),
		})
		return
	}
	type RtcRoom struct {
		ID             int    `db:"id"`             // 房间ID
		Scheduled_time string `db:"scheduled_time"` // 预约时间
	}
	sql := `select id,scheduled_time from rtc_room where status = ? and pat_pro_id = ? and record_id = ?`
	params := []interface{}{status, pat_pro_id, record_id}
	var rtcRoom []RtcRoom
	err = database.GetAll(sql, &rtcRoom, params...)

	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败",
			"err":  err.Error(),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "ok",
		"data": rtcRoom,
	})

}
func Patient_record_detail(w http.ResponseWriter, r *http.Request) {
	// 患者病历详情，鉴权时只判断患者帐号ID即可
	// jwt鉴权
	authHeader := r.Header.Get("Authorization")
	tokenString := strings.TrimPrefix(authHeader, "Bearer ")
	claims, err := common.ValidateJWTToken(tokenString)
	if err != nil {
		// token过期提示：Token is expired
		common.JSONResponse(w, http.StatusUnauthorized, map[string]interface{}{
			"code":  500,
			"msg":   "鉴权失败 - token",
			"error": err.Error(),
		})
		return
	}
	patientid := claims["patientid"].(float64)
	if patientid == 0 {
		common.JSONResponse(w, http.StatusUnauthorized, map[string]interface{}{
			"code":  500,
			"msg":   "鉴权失败 - patientid",
			"error": "Invalid token: patientid is missing",
		})
		return
	}
	// 获取参数
	id, err := strconv.Atoi(r.FormValue("id"))
	if err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "参数错误",
			"err":  err.Error(),
		})
		return
	}
	type Patient_records struct {
		ID                         int     `db:"id"`                         // 主键
		Pat_id                     int     `db:"pat_id"`                     // 用户帐号id
		Pat_pro_id                 int     `db:"pat_pro_id"`                 // 用户资料id
		Pre_id                     int     `db:"pre_id"`                     // 绑定的处方ID
		Doc_id                     int     `db:"doc_id"`                     // 医生ID
		Department_id              int     `db:"department_id"`              // 科室ID
		Asst_id                    int     `db:"asst_id"`                    // 医助ID
		Chief_complaint            string  `db:"chief_complaint"`            // 主诉
		History_of_present_illness string  `db:"history_of_present_illness"` // 现病史
		Past_medical_history       string  `db:"past_medical_history"`       // 既往病史
		Personal_history           string  `db:"personal_history"`           // 个人史
		Family_history             string  `db:"family_history"`             // 家族史
		Allergy_history            string  `db:"allergy_history"`            // 过敏史
		Diagnosis_information      string  `db:"diagnosis_information"`      // 诊断信息
		Treatment_plan             string  `db:"treatment_plan"`             // 治疗方案
		Discharge_time             string  `db:"discharge_time"`             // 核销时间
		Status                     int     `db:"status"`                     // 状态
		Triad                      string  `db:"triad"`                      // 三高
		Tx_day                     int     `db:"tx_day"`                     // 疗程-天数
		Tx_type                    string  `db:"tx_type"`                    // 疗程-剂型
		Re_chief_complaint         string  `db:"re_chief_complaint"`         // 复诊主诉
		Urination                  string  `db:"urination"`                  // 大小便情况
		Last_medical               string  `db:"last_medical"`               // 上次用药情况
		Now_needs                  string  `db:"now_needs"`                  // 现需治疗
		Photo_tongue               *string `db:"photo_tongue"`               // 舌苔照
		Photo_sheet                *string `db:"photo_sheet"`                // 检查单
		Scheduled_time             string  `db:"scheduled_time"`             // 预约时间
		Create_time                string  `db:"create_time"`                // 建档时间
		Past_medication_history    string  `db:"past_medication_history"`    // 过往用药记录
		Last_medication_time       string  `db:"last_medication_time"`       // 上次用药时间
	}
	var patient_records Patient_records
	sql := "SELECT id, pat_id, pat_pro_id, pre_id,past_medication_history,last_medication_time, doc_id, department_id, asst_id, chief_complaint, history_of_present_illness, past_medical_history, personal_history, family_history, allergy_history, diagnosis_information, treatment_plan, discharge_time, status, scheduled_time, create_time, triad, tx_day, tx_type, re_chief_complaint, urination, last_medical, now_needs, photo_tongue, photo_sheet FROM patient_records WHERE id = ? and pat_id = ?"
	err = database.GetRow(sql, &patient_records, id, patientid)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, id, patientid),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "ok",
		"data": patient_records,
		// "sql":  common.DebugSql(sql, id, patientid),
	})
}

func Order_detail(w http.ResponseWriter, r *http.Request) {
	// 订单详情，鉴权时只判断患者帐号ID即可
	// jwt鉴权
	authHeader := r.Header.Get("Authorization")
	tokenString := strings.TrimPrefix(authHeader, "Bearer ")
	claims, err := common.ValidateJWTToken(tokenString)
	if err != nil {
		// token过期提示：Token is expired
		common.JSONResponse(w, http.StatusUnauthorized, map[string]interface{}{
			"code":  500,
			"msg":   "鉴权失败 - token",
			"error": err.Error(),
		})
		return
	}
	patientid := claims["patientid"].(float64)
	if patientid == 0 {
		common.JSONResponse(w, http.StatusUnauthorized, map[string]interface{}{
			"code":  500,
			"msg":   "鉴权失败 - patientid",
			"error": "Invalid token: patientid is missing",
		})
		return
	}
	// 获取参数
	id, err := strconv.Atoi(r.FormValue("id"))
	if err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "参数错误",
			"err":  err.Error(),
		})
		return
	}
	// 根据订单ID查询处方信息的结构体
	type Prescription struct {
		ID          int    `db:"id"`
		Doc_id      int    `db:"doc_id"`
		Asst_id     int    `db:"asst_id"`
		Pat_id      int    `db:"pat_id"`
		Pat_pro_id  int    `db:"pat_pro_id"`
		Diagnosis   string `db:"diagnosis"`
		Tx_plan     string `db:"tx_plan"`
		Tx_type     string `db:"tx_type"`
		Tx_day      int    `db:"tx_day"`
		Dosage      string `db:"dosage"`
		Askfor      string `db:"askfor"`
		Status      int    `db:"status"`
		Create_time string `db:"create_time"`
	}
	//订单对应的赠品数据结构体
	type Gift struct {
		ID    int     `db:"id"`
		Name  string  `db:"name"`
		Price float64 `db:"price"`
		Nums  int     `db:"nums"`
	}
	// 定义订单结构体
	type Order struct {
		ID           int            `db:"id"`
		Express      string         `db:"express"`
		Tracking_num string         `db:"tracking_num"`
		Total_money  float64        `db:"total_money"`
		Pre_pay      float64        `db:"pre_pay"`
		Final_pay    float64        `db:"final_pay"`
		Status       int            `db:"status"`
		Address      string         `db:"address"`
		Finish_time  string         `db:"finish_time"`
		Create_time  string         `db:"create_time"`
		Prescription []Prescription // 处方为切片
		Gift         []Gift         // 赠品信息为切片
	}
	// 定义订单赠品数据结构体
	sql := "select a.id,b.name,b.price,a.nums from orders_gifts as a left join gifts as b on b.id = a.gift_id where a.ord_id = ?"
	var gift []Gift
	_ = database.GetAll(sql, &gift, id)
	// if err != nil {
	// 	common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
	// 		"code": 500,
	// 		"msg":  "查询订单关联的赠品数据失败",
	// 		"err":  err.Error(),
	// 	})
	// 	return
	// }
	// 查询订单数据
	sql = "SELECT id, express, tracking_num,address, total_money,finish_time, pre_pay, final_pay, status, create_time FROM orders WHERE id = ?"
	var order Order
	err = database.GetOne(sql, &order, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]any{
			"code": 500,
			"msg":  "查询订单数据失败",
			"err":  err.Error(),
		})
		return
	}
	// 查询处方数据
	sql = "SELECT id, doc_id, asst_id, pat_id,pat_pro_id,dosage,askfor, diagnosis, tx_plan,tx_type,tx_day, status, create_time FROM prescription WHERE ord_id = ?"
	var prescriptions []Prescription
	err = database.GetAll(sql, &prescriptions, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "查询订单关联的处方数据失败",
			"err":  err.Error(),
		})
		return
	}
	order.Prescription = prescriptions
	order.Gift = gift
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "ok",
		"data": order,
	})
}

// 用户自己查看自己的订单信息
func Order_list(w http.ResponseWriter, r *http.Request) {
	// 订单列表
	// jwt鉴权
	authHeader := r.Header.Get("Authorization")
	tokenString := strings.TrimPrefix(authHeader, "Bearer ")
	claims, err := common.ValidateJWTToken(tokenString)
	if err != nil {
		// token过期提示：Token is expired
		common.JSONResponse(w, http.StatusUnauthorized, map[string]interface{}{
			"code":  500,
			"msg":   "鉴权失败 - token",
			"error": err.Error(),
		})
		return
	}
	attSql := ""
	status := r.FormValue("status")
	if status != "" && status != "0" {
		if status == "1" {
			attSql = " and status < 3 "
		} else if status == "2" {
			attSql = " and status >= 3 "
		} else {
			common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
				"code": 400,
				"msg":  "参数错误",
				"err":  "status参数错误",
			})
			return
		}
	}
	patientid := claims["patientid"].(float64)
	if patientid == 0 {
		common.JSONResponse(w, http.StatusUnauthorized, map[string]interface{}{
			"code":  500,
			"msg":   "鉴权失败 - patientid",
			"error": "Invalid token: patientid is missing",
		})
		return
	}
	// SQL语句与pat_id无关联，所以鉴定家庭成员ID是否在JWT鉴权的patientid中
	pat_pro_id, _ := strconv.Atoi(r.FormValue("pat_pro_id"))
	err = common.Is_in_family(w, int(patientid), pat_pro_id)
	if err != nil {
		common.JSONResponse(w, http.StatusForbidden, map[string]interface{}{
			"code":  403,
			"msg":   "鉴权失败 - 家庭成员ID不在JWT鉴权的patientid中",
			"error": err.Error(),
		})
		return
	}
	// 获取limit和page参数，post发送来的
	limit, err := strconv.Atoi(r.FormValue("limit"))
	if err != nil {
		limit = 10
	}
	page, err := strconv.Atoi(r.FormValue("page"))
	if err != nil {
		page = 1
	}

	offset := (page - 1) * limit

	// 我的药品订单
	type Patient_orders struct {
		ID          int    `db:"id"`          // 主键
		Status      int    `db:"status"`      // 订单状态
		Finish_time string `db:"finish_time"` // 核销时间
		Create_time string `db:"create_time"` // 创建时间
	}

	// 修改SQL查询，添加LIMIT和OFFSET
	sql := "select id,status,finish_time,create_time from orders as a WHERE pat_pro_id = ? " + attSql + " ORDER BY id DESC limit ? offset ?"
	var patient_orders []Patient_orders
	err = database.GetAll(sql, &patient_orders, pat_pro_id, limit, offset)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, pat_pro_id, limit, offset),
		})
		return
	}

	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "ok",
		"data": patient_orders,
		"sql":  common.DebugSql(sql, pat_pro_id, limit, offset),
	})
}

// 物流插入
func Express_add(w http.ResponseWriter, r *http.Request) {
	// todo 需要鉴定是否是库管员
	user_id, err := common.CheckInt(r.FormValue("user_id"))
	if err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "缺少参数",
			"err":  "缺少user_id参数",
		})
		return
	}
	fmt.Println("小程序流程 - userid", user_id)
	ord_id, err := common.CheckInt(r.FormValue("ord_id")) // 订单ID
	if err != nil || ord_id < 0 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "缺少ord_id参数",
			"err":  err.Error(),
		})
		return
	}
	//判断该订单是否为可发货状态
	sql := "SELECT status FROM orders WHERE id = ? and status = 1"
	var status int
	err = database.GetOne(sql, &status, ord_id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "当前订单状态不为可发货状态",
			"err":  err.Error(),
		})
		return
	}
	express, err := common.CheckInt(r.FormValue("express")) // 快递公司索引ID
	if err != nil || express < 0 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "缺少express参数",
			"err":  err.Error(),
		})
		return
	}
	tracking_num := r.FormValue("tracking_num") // 快递单号
	if tracking_num == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "缺少tracking_num参数",
		})
		return
	}

	// 从请求中获取user_id参数
	user_id, err = common.CheckInt(r.FormValue("user_id"))
	if err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "缺少user_id参数",
			"err":  err.Error(),
		})
		return
	}

	// 1. 查询成品表warehouse_finisheddrug，获取需要更新的数据
	type WarehouseFinishedDrug struct {
		ID       int     `db:"id"`
		Pre_id   int     `db:"pre_id"`
		Quantity float64 `db:"quantity"`
		Ord_id   int     `db:"ord_id"`
	}

	var warehouseFinishedDrugData []WarehouseFinishedDrug
	sql = `
		SELECT id, pre_id, quantity, CASE WHEN new_ord_id > 0 THEN new_ord_id ELSE ord_id END AS ord_id 
		FROM warehouse_finisheddrug 
		WHERE ord_id = ?
	`
	err = database.GetAll(sql, &warehouseFinishedDrugData, ord_id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "查询成品库数据失败",
			"err":  err.Error(),
		})
		return
	}

	// 2. 准备事务SQL语句
	var sqls []database.SQLExec

	// 更新订单状态
	sqls = append(sqls, database.SQLExec{
		Query: "UPDATE orders SET express = ?, tracking_num = ?, status = 2 WHERE id = ?",
		Args:  []any{express, tracking_num, ord_id},
	})

	// 遍历成品库数据，更新状态和添加日志
	for _, item := range warehouseFinishedDrugData {
		// 更新成品库状态
		sqls = append(sqls, database.SQLExec{
			Query: "UPDATE warehouse_finisheddrug SET status = 2,name='已发成品药',update_time=NOW() WHERE id = ?",
			Args:  []any{item.ID},
		})

		// 添加出库日志
		sqls = append(sqls, database.SQLExec{
			Query: `INSERT INTO warehouse_finisheddrug_log 
					(pid, ord_id, pre_id, user_id, kind, change_data, old_data, new_data, notes) 
					VALUES (?, ?, ?, ?, 0, ?, ?, 0, ?)`,
			Args: []interface{}{
				item.ID,
				item.Ord_id,
				item.Pre_id,
				user_id,
				-item.Quantity, // 负数表示出库
				item.Quantity,  // 原有库存
				"快递发出-出库",
			},
		})
	}

	// 3. 执行事务
	err = database.ExecuteTransaction(sqls)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "发货处理失败",
			"err":  err.Error(),
		})
		return
	}

	// 4. 记录操作日志
	common.Add_log_worker_in_wx(fmt.Sprintf("为订单 %d 添加快递信息并发货，快递公司：%s，快递单号：%s", ord_id, config.KD100_Express_Info[express][1], tracking_num), user_id, r, r.Header.Get("User-Agent"))

	// 5. 返回成功响应
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "发货成功",
	})
}

// 库管查看用户的订单信息
func Order_list_master(w http.ResponseWriter, r *http.Request) {
	// todo 需要鉴定是否是库管员
	user_id, err := common.CheckInt(r.FormValue("user_id"))
	if err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "缺少参数",
			"err":  "缺少user_id参数",
		})
		return
	}
	fmt.Println("小程序流程 - userid", user_id)
	pat_pro_id, err := common.CheckInt(r.FormValue("pat_pro_id"))
	if err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "缺少pat_pro_id参数",
			"err":  err.Error(),
		})
		return
	}
	// 查寻该用户的所有订单信息
	type Patient_orders struct {
		ID          int    `db:"id"`          // 主键
		Status      int    `db:"status"`      // 订单状态
		Finish_time string `db:"finish_time"` // 核销时间
		Create_time string `db:"create_time"` // 创建时间
	}
	sql := "select id,status,finish_time,create_time from orders as a WHERE pat_pro_id=? and status = 1 ORDER BY id DESC"
	var patient_orders []Patient_orders
	err = database.GetAll(sql, &patient_orders, pat_pro_id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败",
			"err":  err.Error(),
		})
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "ok",
		"data": patient_orders,
	})
}

// 物流列表
func Express_list(w http.ResponseWriter, r *http.Request) {
	pat_pro_id, err := common.CheckInt(r.FormValue("pat_pro_id"))
	if err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "缺少pat_pro_id参数",
			"err":  err.Error(),
		})
		return
	}
	// todo 需要鉴定是否是库管员
	user_id, err := common.CheckInt(r.FormValue("user_id"))
	if err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "缺少参数",
			"err":  "缺少user_id参数",
		})
		return
	}
	fmt.Println("小程序流程 - userid", user_id)
	type Express_list struct {
		ID           int    `db:"id"`           // 主键
		Status       int    `db:"status"`       // 状态
		Name         string `db:"name"`         // 患者姓名
		Phone        string `db:"phone"`        // 患者手机号
		Address      string `db:"address"`      // 患者地址
		Create_time  string `db:"create_time"`  // 创建时间
		Express      int    `db:"express"`      // 快递公司
		Tracking_num string `db:"tracking_num"` // 物流单号
	}
	sql := `
	
		SELECT
		a.id,
		a.status,
		b.name,
		b.phone,
		a.address,
		a.create_time,
		a.express,
		a.tracking_num
		FROM
		orders AS a
		LEFT JOIN patient_profile AS b ON a.pat_pro_id = b.id
		WHERE
		a.pat_pro_id = ?
		ORDER BY
		CASE WHEN a.STATUS = 1 THEN 1 ELSE 0 END DESC,
		a.id DESC
		LIMIT 60;
	
	`
	var express_list []Express_list
	err = database.GetAll(sql, &express_list, pat_pro_id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败",
			"err":  err.Error(),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "ok",
		"data": express_list,
		"sql":  common.DebugSql(sql, pat_pro_id),
	})
}

// 查询物流信息，web与小程序共用
func Express_info(w http.ResponseWriter, r *http.Request) {
	// todo 需要鉴定是否是库管员
	express_type := r.URL.Query().Get("express_type")
	nu := r.URL.Query().Get("tracking_num")
	ord_id := r.URL.Query().Get("ord_id")
	pat_pro_id := r.URL.Query().Get("pat_pro_id")

	if express_type == "" || nu == "" || ord_id == "" || pat_pro_id == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "缺少参数",
		})
		return
	}

	express_type_int, _ := strconv.Atoi(express_type)
	ord_id_int, _ := strconv.Atoi(ord_id)
	pat_pro_id_int, _ := strconv.Atoi(pat_pro_id)

	// 调用抽离的查询物流信息函数
	result, err := common.QueryExpressInfo(express_type_int, nu, ord_id_int, pat_pro_id_int, false, r)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败",
			"err":  err.Error(),
		})
		return
	}

	common.JSONResponse(w, http.StatusOK, map[string]any{
		"code": 200,
		"msg":  "ok",
		"data": result,
	})
}

func Patient_profile_detail(w http.ResponseWriter, r *http.Request) {
	// 患者个人信息详情，鉴权时只判断患者帐号ID即可
	// jwt鉴权
	authHeader := r.Header.Get("Authorization")
	tokenString := strings.TrimPrefix(authHeader, "Bearer ")
	claims, err := common.ValidateJWTToken(tokenString)
	if err != nil {
		// token过期提示：Token is expired
		common.JSONResponse(w, http.StatusUnauthorized, map[string]interface{}{
			"code":  500,
			"msg":   "鉴权失败 - token",
			"error": err.Error(),
		})
		return
	}
	patientid := claims["patientid"].(float64)
	if patientid == 0 {
		common.JSONResponse(w, http.StatusUnauthorized, map[string]interface{}{
			"code":  500,
			"msg":   "鉴权失败 - patientid",
			"error": "Invalid token: patientid is missing",
		})
		return
	}
	id := r.FormValue("id")
	if id == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "缺少参数",
			"err":  "缺少id参数",
		})
		return
	}
	// 我的个人信息
	type Patient_profile struct {
		ID              int       `db:"id"`              // 主键
		Pid             int       `db:"pid"`             // 患者帐号id
		Asst_id         int       `db:"asst_id"`         // 销售id
		Doc_id          int       `db:"doc_id"`          // 医生id
		Support_id      int       `db:"support_id"`      // 支援人员id
		Relation        int       `db:"relation"`        // 与患者关系
		Phone           string    `db:"phone"`           // 手机号
		Name            string    `db:"name"`            // 姓名
		Sex             string    `db:"sex"`             // 性别
		Born_date       time.Time `db:"born_date"`       // 出生日期
		Idcard          string    `db:"idcard"`          // 身份证号
		Weixin          string    `db:"weixin"`          // 微信号
		Ins_card_num    string    `db:"ins_card_num"`    // 医保卡号
		Ins_type        string    `db:"ins_type"`        // 医保类型
		Height          float64   `db:"height"`          // 身高
		Weight          float64   `db:"weight"`          // 体重
		Allergies       string    `db:"allergies"`       // 过敏史
		Medical_history string    `db:"medical_history"` // 既往病史
		Patient_Type    int       `db:"Patient_Type"`    // 患者类型
		Patient_From    int       `db:"Patient_From"`    // 信息来源
		Address         string    `db:"address"`         // 地址
		Ismarried       int       `db:"Ismarried"`       // 是否已婚
		Status          int       `db:"status"`          // 状态
		Customer_notes  string    `db:"customer_notes"`  // 客户备注
		Create_time     string    `db:"create_time"`     // 创建时间
	}
	sql := "SELECT id,pid,asst_id,doc_id,support_id,relation,phone,name,sex,born_date,idcard,weixin,ins_card_num,ins_type,height,weight,allergies,medical_history,Patient_Type,Patient_From,address,Ismarried,status,customer_notes,create_time FROM patient_profile WHERE id = ? and pid = ?"
	var patient_profile Patient_profile
	err = database.GetOne(sql, &patient_profile, id, patientid)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, id, patientid),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "ok",
		"data": patient_profile,
		"sql":  common.DebugSql(sql, id, patientid),
	})
}

func Patient_profile_list(w http.ResponseWriter, r *http.Request) {
	// 患者列表，不用鉴定单个患者信息，该功能以以帐号权限为依据
	// jwt鉴权
	authHeader := r.Header.Get("Authorization")
	tokenString := strings.TrimPrefix(authHeader, "Bearer ")
	claims, err := common.ValidateJWTToken(tokenString)
	if err != nil {
		// token过期提示：Token is expired
		common.JSONResponse(w, http.StatusUnauthorized, map[string]interface{}{
			"code":  500,
			"msg":   "鉴权失败 - token",
			"error": err.Error(),
		})
		return
	}
	patientid := claims["patientid"].(float64)
	if patientid == 0 {
		common.JSONResponse(w, http.StatusUnauthorized, map[string]interface{}{
			"code":  500,
			"msg":   "鉴权失败 - patientid",
			"error": "Invalid token: patientid is missing",
		})
		return
	}
	//鉴权结束，用户ID：patientid，剩下程序开始
	// patientid := 1
	// 数据查询
	type Patient_profile struct {
		ID              int       `db:"id"`              // 主键
		Pid             int       `db:"pid"`             // 患者帐号id
		Asst_id         int       `db:"asst_id"`         // 销售id
		Doc_id          int       `db:"doc_id"`          // 医生id
		Support_id      int       `db:"support_id"`      // 支援人员id
		Relation        int       `db:"relation"`        // 与患者关系
		Phone           string    `db:"phone"`           // 手机号
		Name            string    `db:"name"`            // 姓名
		Sex             string    `db:"sex"`             // 性别
		Born_date       time.Time `db:"born_date"`       // 出生日期
		Idcard          string    `db:"idcard"`          // 身份证号
		Weixin          string    `db:"weixin"`          // 微信号
		Ins_card_num    string    `db:"ins_card_num"`    // 医保卡号
		Ins_type        string    `db:"ins_type"`        // 医保类型
		Height          float64   `db:"height"`          // 身高
		Weight          float64   `db:"weight"`          // 体重
		Allergies       string    `db:"allergies"`       // 过敏史
		Medical_history string    `db:"medical_history"` // 既往病史
		Patient_Type    int       `db:"Patient_Type"`    // 患者类型
		Patient_From    int       `db:"Patient_From"`    // 信息来源
		Address         string    `db:"address"`         // 地址
		Ismarried       int       `db:"Ismarried"`       // 是否已婚
		Status          int       `db:"status"`          // 状态
		Customer_notes  string    `db:"customer_notes"`  // 客户备注
		Create_time     string    `db:"create_time"`     // 创建时间
	}
	sql := "SELECT id,pid,asst_id,doc_id,support_id,relation,phone,name,sex,born_date,idcard,weixin,ins_card_num,ins_type,height,weight,allergies,medical_history,Patient_Type,Patient_From,address,Ismarried,status,customer_notes,create_time FROM patient_profile WHERE pid = ? ORDER BY id DESC"
	var patient_profile []Patient_profile
	err = database.GetAll(sql, &patient_profile, patientid)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败",
			"err":  err.Error(),
			// "sql":  common.DebugSql(sql, limit, offset),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "ok",
		"data": patient_profile,
		// "sql":   common.DebugSql(sql, limit, offset),
	})
}

// 患者添加
func Patient_profile_add(w http.ResponseWriter, r *http.Request) {
	// 患者列表，不用鉴定单个患者信息，该功能以以帐号权限为依据
	// jwt鉴权
	authHeader := r.Header.Get("Authorization")
	tokenString := strings.TrimPrefix(authHeader, "Bearer ")
	claims, err := common.ValidateJWTToken(tokenString)
	if err != nil {
		// token过期提示：Token is expired
		common.JSONResponse(w, http.StatusUnauthorized, map[string]interface{}{
			"code":  500,
			"msg":   "鉴权失败 - token",
			"error": err.Error(),
		})
		return
	}
	patientid := claims["patientid"].(float64)
	if patientid == 0 {
		common.JSONResponse(w, http.StatusUnauthorized, map[string]interface{}{
			"code":  500,
			"msg":   "鉴权失败 - patientid",
			"error": "Invalid token: patientid is missing",
		})
		return
	}
	// patientid := 1
	//鉴权结束，用户ID：patientid，剩下程序开始
	relation, err := common.CheckInt(r.FormValue("relation"))
	if relation < 0 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "与患者关系不能为空",
		})
		return
	}
	phone, err := common.CheckStr(r.FormValue("phone"))
	if phone == "" || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "手机号不能为空",
		})
		return
	}
	name, err := common.CheckStr(r.FormValue("name"))
	if name == "" || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "姓名不能为空",
		})
		return
	}
	sex, err := common.CheckInt(r.FormValue("sex"))
	if err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "性别不能为空",
			"err":  err.Error(),
		})
		return
	}
	address, err := common.CheckStr(r.FormValue("address"))
	if address == "" || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "地址不能为空",
			"err":  err.Error(),
		})
		return
	}
	isMarried, err := common.CheckInt(r.FormValue("ismarried"))
	if err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "是否已婚不能为空",
			"err":  err.Error(),
		})
		return
	}
	if isMarried > 0 {
		isMarried = 1
	} else {
		isMarried = 0
	}
	//医助ID，寻找该帐户旗下最后一个绑定的医助ID
	sql := "select asst_id from patient_profile where pid = ? order by id desc limit 1"
	var asst_id int
	err = database.GetOne(sql, &asst_id, patientid)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "获取医助ID失败",
			"err":  err.Error(),
			// "sql":  common.DebugSql(sql, patientid),
		})
		return
	}
	sql = "INSERT INTO patient_profile(pid,relation,phone,name,sex,address,Ismarried,asst_id) VALUES(?,?,?,?,?,?,?,?)"
	result, err := database.Query(sql, patientid, relation, phone, name, sex, address, isMarried, asst_id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "添加数据失败",
			"err":  err.Error(),
			// "sql":  common.DebugSql(sql, patientid, relation, phone, name, sex, address, isMarried),
		})
		return
	}
	new_id, _ := result.LastInsertId()
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":   200,
		"msg":    "添加成功",
		"new_id": new_id,
	})
}

// 患者修改 50
func Patient_profile_edit(w http.ResponseWriter, r *http.Request) {
	// 患者列表，不用鉴定单个患者信息，该功能以以帐号权限为依据
	// jwt鉴权
	authHeader := r.Header.Get("Authorization")
	tokenString := strings.TrimPrefix(authHeader, "Bearer ")
	claims, err := common.ValidateJWTToken(tokenString)
	if err != nil {
		// token过期提示：Token is expired
		common.JSONResponse(w, http.StatusUnauthorized, map[string]interface{}{
			"code":  500,
			"msg":   "鉴权失败 - token",
			"error": err.Error(),
		})
		return
	}
	patientid := claims["patientid"].(float64)
	if patientid == 0 {
		common.JSONResponse(w, http.StatusUnauthorized, map[string]interface{}{
			"code":  500,
			"msg":   "鉴权失败 - patientid",
			"error": "Invalid token: patientid is missing",
		})
		return
	}
	// patientid := 1
	//鉴权结束，用户ID：patientid，剩下程序开始
	id, err := common.CheckInt(r.FormValue("id"))
	if id < 1 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "患者id不能为空",
		})
		return
	}
	phone, err := common.CheckStr(r.FormValue("phone"))
	if phone == "" || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "手机号不能为空",
		})
		return
	}
	//检测有没有重复的电话号码
	sql := "SELECT id FROM patient_profile WHERE phone = ? and pid <> ?"
	var id_exist int
	err = database.GetOne(sql, &id_exist, phone, id, patientid)
	if err == nil && id_exist > 0 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "您的手机号不得与其它帐号下的号码重复（允许同帐号下手机号重复）",
		})
		return
	}
	name, err := common.CheckStr(r.FormValue("name"))
	if name == "" || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "姓名不能为空",
		})
		return
	}
	sex, err := common.CheckStr(r.FormValue("sex"))
	if sex == "" || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "性别不能为空",
		})
		return
	}
	idcard := r.FormValue("idcard")
	sql = "UPDATE patient_profile SET phone = ?, name = ?, sex = ?, idcard = ? WHERE id = ? and pid = ?"
	result, err := database.Query(sql, phone, name, sex, idcard, id, patientid)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "更新数据失败",
			"err":  err.Error(),
			// "sql":  common.DebugSql(sql, patientid, relation, phone, name, sex, address, isMarried, id),
		})
		return
	}
	RowsAffected, _ := result.RowsAffected()
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":         200,
		"msg":          "修改成功",
		"RowsAffected": RowsAffected,
	})
	if RowsAffected > 0 {
		useragent := r.FormValue("useragent")
		common.Add_log_wxapp(fmt.Sprintf("成功修改患者资料，ID为%d", id), id, r, useragent)
	}
}

// 获取文章分类
func Article_category(w http.ResponseWriter, r *http.Request) {
	type Article_category struct {
		Id          int    `db:"id"`          // 分类ID
		Title       string `db:"title"`       // 分类名称
		Description string `db:"description"` // 分类描述
		Sort        int    `db:"sort"`        // 排序
		Create_time string `db:"create_time"` // 创建时间
	}
	sql := "SELECT id,title,description,sort,create_time FROM article_category ORDER BY sort ASC"
	var article_category []Article_category
	err := database.GetAll(sql, &article_category)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败",
			"err":  err.Error(),
			// "sql":  common.DebugSql(sql),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "ok",
		"data": article_category,
		// "sql":   common.DebugSql(sql),
	})
}

// 获取文章列表
func Article_list(w http.ResponseWriter, r *http.Request) {
	pid, _ := common.CheckInt(r.FormValue("pid")) // 如果为空、出错、非正整数，都会是-1，所以接下来不用作err的判断
	limit, _ := common.CheckInt(r.FormValue("limit"))
	page, _ := common.CheckInt(r.FormValue("page")) // 获取页码参数
	// fmt.Println(r.FormValue("pid"), r.FormValue("limit"), r.FormValue("page"))

	if limit < 0 {
		limit = 10
	}
	if page < 1 {
		page = 1 // 页码至少为1
	}

	offset := (page - 1) * limit // 计算偏移量

	type Article_list struct {
		Id          int    `db:"id"`          // 文章ID
		Pid         int    `db:"pid"`         // 分类ID
		Title       string `db:"title"`       // 文章标题
		CoverPic    string `db:"coverPic"`    // 封面图片
		Contents    string `db:"contents"`    // 文章内容
		Visits      int    `db:"visits"`      // 浏览量
		Sort        int    `db:"sort"`        // 排序
		Create_time string `db:"create_time"` // 创建时间
	}

	sql := "SELECT id, pid, title, coverPic, contents, visits, sort, create_time FROM article WHERE 1"
	var args []interface{}

	if pid > 0 {
		sql += " AND pid = ?"
		args = append(args, pid)
	}
	args = append(args, limit, offset) // 将偏移量和限制添加到参数中
	sql += " ORDER BY sort DESC"
	sql += " LIMIT ? OFFSET ?"

	var article_list []Article_list
	err := database.GetAll(sql, &article_list, args...)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, pid, limit, offset),
		})
		return
	}

	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "ok",
		"data": article_list,
		"sql":  common.DebugSql(sql, pid, limit, offset),
	})
}

// 获取文章详情
func Article_detail(w http.ResponseWriter, r *http.Request) {
	id, err := common.CheckInt(r.FormValue("id"))
	if err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "文章ID不能为空",
		})
		return
	}
	type Article_detail struct {
		Id          int    `db:"id"`          // 文章ID
		Pid         int    `db:"pid"`         // 分类ID
		Title       string `db:"title"`       // 文章标题
		CoverPic    string `db:"coverPic"`    // 封面图片
		Contents    string `db:"contents"`    // 文章内容
		Visits      int    `db:"visits"`      // 浏览量
		Sort        int    `db:"sort"`        // 排序
		Create_time string `db:"create_time"` // 创建时间
	}
	sql := "SELECT id,pid,title,coverPic,contents,visits,sort,create_time FROM article WHERE id = ?"
	var article_detail Article_detail
	err = database.GetOne(sql, &article_detail, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败",
			"err":  err.Error(),
			// "sql":  common.DebugSql(sql, id),
		})
		return
	}
	// 文章浏览量+1
	sql = "UPDATE article SET visits = visits + 1 WHERE id = ?"
	_, err = database.Query(sql, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "更新数据失败",
			"err":  err.Error(),
			// "sql":  common.DebugSql(sql, id),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "ok",
		"data": article_detail,
	})
}

// 在途物流信息
func Order_undelivered(w http.ResponseWriter, r *http.Request) {
	// jwt鉴权
	authHeader := r.Header.Get("Authorization")
	tokenString := strings.TrimPrefix(authHeader, "Bearer ")
	claims, err := common.ValidateJWTToken(tokenString)
	if err != nil {
		// token过期提示：Token is expired
		common.JSONResponse(w, http.StatusUnauthorized, map[string]interface{}{
			"code":  500,
			"msg":   "鉴权失败 - token",
			"error": err.Error(),
		})
		return
	}
	patientid := claims["patientid"].(float64)
	if patientid == 0 {
		common.JSONResponse(w, http.StatusUnauthorized, map[string]any{
			"code":  500,
			"msg":   "鉴权失败 - patientid",
			"error": "Invalid token: patientid is missing",
		})
		return
	}

	type Order_undelivered struct {
		Id           int    `db:"id"`           // 订单ID
		Express      string `db:"express"`      // 快递公司
		Tracking_num string `db:"tracking_num"` // 快递单号
		Name         string `db:"name"`         // 收件人姓名
		Phone        string `db:"phone"`        // 收件人电话
		Address      string `db:"address"`      // 收件地址
		Create_time  string `db:"create_time"`  // 创建时间
	}
	sql := "select a.id,a.express,a.address,a.tracking_num,a.create_time,b.name,b.phone from orders as a left join patient_profile as b on b.id = a.pat_pro_id where a.pat_id = ? and a.status = 2 order by a.id desc"
	var order_undelivered []Order_undelivered
	err = database.GetAll(sql, &order_undelivered, patientid)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]any{
			"code": 500,
			"msg":  "数据查询失败",
			"err":  err.Error(),
			// "sql":  common.DebugSql(sql, uid),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "ok",
		"data": order_undelivered,
		// "sql":  common.DebugSql(sql, uid),
	})
}

// 小程序头像上传
func Patient_profile_avatarurl_upload(w http.ResponseWriter, r *http.Request) {
	token := r.FormValue("token")
	claims, err := common.ValidateJWTToken(token)
	if err != nil {
		common.JSONResponse(w, http.StatusUnauthorized, map[string]interface{}{
			"code":  500,
			"msg":   "鉴权失败 - token",
			"error": err.Error(),
		})
		return
	}
	patientid := claims["patientid"].(float64)
	if patientid == 0 {
		common.JSONResponse(w, http.StatusUnauthorized, map[string]interface{}{
			"code":  500,
			"msg":   "鉴权失败 - patientid",
			"error": "Invalid token: patientid is missing",
		})
		return
	}
	pat_pro_id, _ := strconv.Atoi(r.FormValue("pat_pro_id"))
	err = common.Is_in_family(w, int(patientid), pat_pro_id)
	if err != nil {
		common.JSONResponse(w, http.StatusForbidden, map[string]interface{}{
			"code":  403,
			"msg":   "鉴权失败 - 家庭成员ID不在JWT鉴权的patientid中",
			"error": err.Error(),
		})
		return
	}
	file, fileHeader, err := r.FormFile("file")
	if err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "上传文件失败",
			"err":  err.Error(),
		})
		return
	}
	defer file.Close()

	isImage, imgData, err := common.Check_Is_Image(fileHeader)
	if err != nil || !isImage {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": http.StatusBadRequest,
			"msg":  "无效的图片文件: " + err.Error(),
		})
		return
	}

	// 解码图片
	img, _, err := image.Decode(bytes.NewReader(imgData))
	if err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": http.StatusBadRequest,
			"msg":  "无法解码图片: " + err.Error(),
		})
		return
	}

	// 检查图片宽度并调整
	maxWidth := uint(300)
	bounds := img.Bounds()
	if uint(bounds.Dx()) > maxWidth { // 使用 Dx() 获取宽度
		// 根据比例调整宽度
		ratio := float64(maxWidth) / float64(bounds.Dx())
		newWidth := maxWidth
		newHeight := uint(float64(bounds.Dy()) * ratio) // 使用 Dy() 获取高度
		img = resize.Resize(newWidth, newHeight, img, resize.Lanczos3)
	}

	// 保存调整后的文件
	filepath := config.Dist_catagory + "/uploads/avatarurl/" + strconv.Itoa(pat_pro_id) + ".jpg"
	f, err := os.OpenFile(filepath, os.O_WRONLY|os.O_CREATE, 0666)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "保存文件失败",
			"err":  err.Error(),
		})
		return
	}
	defer f.Close()

	// 写入调整后的图片
	err = jpeg.Encode(f, img, nil)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "保存文件失败",
			"err":  err.Error(),
		})
		return
	}

	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "ok",
		"data": strings.TrimPrefix(filepath, ".."),
	})
}

// ORDER ID 换 处方ID
func Orderid2preids(w http.ResponseWriter, r *http.Request) {
	// jwt鉴权
	authHeader := r.Header.Get("Authorization")
	tokenString := strings.TrimPrefix(authHeader, "Bearer ")
	claims, err := common.ValidateJWTToken(tokenString)
	if err != nil {
		// token过期提示：Token is expired
		common.JSONResponse(w, http.StatusUnauthorized, map[string]interface{}{
			"code":  500,
			"msg":   "鉴权失败 - token",
			"error": err.Error(),
		})
		return
	}
	patientid := claims["patientid"].(float64)
	if patientid == 0 {
		common.JSONResponse(w, http.StatusUnauthorized, map[string]interface{}{
			"code":  500,
			"msg":   "鉴权失败 - patientid",
			"error": "Invalid token: patientid is missing",
		})
		return
	}

	order_id, err := common.CheckInt(r.FormValue("order_id"))
	if err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "订单ID不能为空",
		})
		return
	}

	type Order_id_to_prescription_id struct {
		Id int `db:"id"`
	}
	var order_id_to_prescription_ids []Order_id_to_prescription_id // 把这里改成切片
	sql := "SELECT id FROM prescription WHERE ord_id = ?"
	err = database.GetAll(sql, &order_id_to_prescription_ids, order_id) // 传入切片
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败",
			"err":  err.Error(),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "ok",
		"data": order_id_to_prescription_ids, // 返回切片
	})
}

// // 成品药_入库日志
// func Warehouse_finisheddrug_log_list_error(w http.ResponseWriter, r *http.Request) {
// 	// TODO: 测试阶段该环节暂未JWT鉴权 - 获取库管员ID
// 	user_id := r.FormValue("user_id")
// 	if user_id == "" {
// 		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
// 			"code": 500,
// 			"msg":  "库管员ID不得为空",
// 		})
// 		return
// 	}
// 	// 业务开始
// 	limit, err := strconv.Atoi(r.FormValue("limit"))
// 	if err != nil || limit <= 0 {
// 		limit = 5
// 	}
// 	page, err := strconv.Atoi(r.FormValue("page"))
// 	if err != nil || page <= 0 {
// 		page = 1
// 	}

// 	type Log struct {
// 		ID          int     `db:"id"`
// 		Pid         int     `db:"pid"`
// 		Ord_id      int     `db:"ord_id"`
// 		Pre_id      int     `db:"pre_id"`
// 		User_id     int     `db:"user_id"`
// 		Kind        int     `db:"kind"`
// 		Old_data    float64 `db:"old_data"`
// 		Change_data float64 `db:"change_data"`
// 		New_data    float64 `db:"new_data"`
// 		Notes       string  `db:"notes"`
// 		Create_time string  `db:"create_time"`
// 	}

// 	logs := []Log{}
// 	params := []interface{}{}
// 	attSql := ""

// 	kind := r.FormValue("kind")
// 	if kind != "" {
// 		attSql = " and kind = ?"
// 		params = append(params, kind)
// 	}

// 	var count int
// 	countSql := "SELECT COUNT(id) FROM warehouse_finisheddrug_log WHERE 1" + attSql
// 	err = database.GetOne(countSql, &count, params...)
// 	if err != nil {
// 		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
// 			"code": 500,
// 			"msg":  "读取数据总数失败",
// 			"err":  err.Error(),
// 			"sql":  common.DebugSql(countSql, params...),
// 		})
// 		return
// 	}

// 	offset := (page - 1) * limit
// 	params = append(params, limit, offset)
// 	sql := "SELECT id,pid,user_id,ord_id,pre_id,kind,old_data,change_data,new_data,notes,create_time FROM warehouse_finisheddrug_log WHERE 1" + attSql + " ORDER BY id DESC limit ? OFFSET ?"
// 	err = database.GetAll(sql, &logs, params...)
// 	if err != nil {
// 		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
// 			"code": 500,
// 			"msg":  "读取数据失败",
// 			"err":  err.Error(),
// 			"sql":  common.DebugSql(sql, params...),
// 		})
// 		return
// 	}

// 	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
// 		"code":  200,
// 		"msg":   "读取成功",
// 		"data":  logs,
// 		"count": count,
// 		"sql":   common.DebugSql(sql, params...),
// 	})
// }

// 成品药_出入库日志详情 82
func Warehouse_finisheddrug_log_detail(w http.ResponseWriter, r *http.Request) {
	// TODO: 测试阶段该环节暂未JWT鉴权 - 获取库管员ID
	user_id := r.FormValue("user_id")
	if user_id == "" {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "库管员ID不得为空",
		})
		return
	}
	// 业务开始
	id, err := common.CheckInt(r.FormValue("id"))
	if err != nil || id < 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "日志ID不能为空",
		})
		return
	}
	type Log struct {
		UserID     int     `db:"user_id"`
		UserName   string  `db:"name"`
		Kind       int     `db:"kind"`
		OldData    float64 `db:"old_data"`
		ChangeData float64 `db:"change_data"`
		NewData    float64 `db:"new_data"`
		Notes      string  `db:"notes"`
		CreateTime string  `db:"create_time"`
	}
	sql := "SELECT a.user_id,b.name,a.kind,a.old_data,a.change_data,a.new_data,a.notes,a.create_time FROM warehouse_finisheddrug_log as a left join rbac_user as b on a.user_id = b.id WHERE a.id = ?"
	var log Log
	err = database.GetOne(sql, &log, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "读取数据失败",
			// "sql":  common.DebugSql(sql),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "读取成功",
		"data": log,
	})
}

// 成品库存ID2商品名称 - 之所以与赠品、药材分开，是因为成品没有品种表，成品是自己制作的成品药
func Warehouse2productname_finisheddrug(w http.ResponseWriter, r *http.Request) {
	id, err := common.CheckInt(r.FormValue("wid"))
	if err != nil || id < 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "无效的ID",
		})
		return
	}
	var product struct {
		ID   int    `db:"id"`
		Name string `db:"name"`
	}
	sql := "SELECT id, name FROM warehouse_finisheddrug WHERE id = ?"
	err = database.GetRow(sql, &product, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败",
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "获取库存ID对应品名成功",
		"data": product,
	})
}

// 用户ID转姓名
func Patient_profile_id2name(w http.ResponseWriter, r *http.Request) {
	id := r.FormValue("id")
	if id == "" {
		w.Write([]byte("id can not be empty"))
		return
	}

	sql := "SELECT name FROM patient_profile WHERE id = ?"
	var name string
	err := database.GetOne(sql, &name, id)
	if err != nil {
		w.Write([]byte("scan database error"))
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "ok",
		"data": name,
	})
}

// 通过用户一码通，查询该用处户的处方单
func Find_prescription_by_user_bar_code(w http.ResponseWriter, r *http.Request) {
	pat_pro_id, err := common.CheckInt(r.FormValue("pat_pro_id"))
	if err != nil || pat_pro_id < 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "患者ID不能为空",
		})
		return
	}
	type Prescription struct {
		ID          int     `db:"id"`
		Ord_id      int     `db:"ord_id"`
		Doc_id      int     `db:"doc_id"`
		Pat_pro_id  int     `db:"pat_pro_id"`
		Tx_day      int     `db:"tx_day"`
		Tx_type     string  `db:"tx_type"`
		TotalDoses  float64 `db:"totalDoses"`
		Create_time string  `db:"create_time"` // 创建时间
	}
	sql := "select id,ord_id,doc_id,pat_pro_id,tx_day,tx_type,totalDoses,create_time from prescription where pat_pro_id = ? and status = 4 and verify_3 = 1 order by id desc"
	var prescription []Prescription
	err = database.GetAll(sql, &prescription, pat_pro_id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败",
			"sql":  common.DebugSql(sql, pat_pro_id),
			"err":  err.Error(),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "获取处方单成功",
		"data": prescription,
		"sql":  common.DebugSql(sql, pat_pro_id),
	})
}

// 根据ID查询处方信息
func Find_prescription_by_id(w http.ResponseWriter, r *http.Request) {
	id, err := common.CheckInt(r.FormValue("id"))
	if id < 0 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "处方单ID不能为空",
		})
		return
	}

	type Prescription struct {
		ID          int     `db:"id"`          // 处方ID
		Ord_id      int     `db:"ord_id"`      // 订单ID
		Record_id   int     `db:"record_id"`   // 病历ID
		Doc_id      int     `db:"doc_id"`      // 医生ID
		Asst_id     int     `db:"asst_id"`     // 医助ID
		Pat_pro_id  int     `db:"pat_pro_id"`  // 患者信息ID
		Status      int     `db:"status"`      // 处方状态
		Dosage      string  `db:"dosage"`      // 用法用量
		Askfor      string  `db:"askfor"`      // 医嘱
		Diagnosis   string  `db:"diagnosis"`   // 诊断结果
		Tx_day      int     `db:"tx_day"`      // 疗程-天数
		Tx_type     string  `db:"tx_type"`     // 疗程-剂型
		TotalDoses  float64 `db:"totalDoses"`  // 疗程-共多少剂
		Create_time string  `db:"create_time"` // 创建时间
		Doc_name    string  `db:"doc_name"`    // 医生姓名
		Asst_name   string  `db:"asst_name"`   // 医助姓名
		Pat_name    string  `db:"pat_name"`    // 患者姓名
	}

	sql := `
        SELECT
        a.id,
        a.ord_id,
        a.record_id,
        a.doc_id,
        a.asst_id,
        a.pat_pro_id,
        a.status,
        a.dosage,
        a.askfor,
        a.diagnosis,
        a.tx_day,
        a.tx_type,
        a.totalDoses,
        a.create_time,
        b.name as doc_name,
        c.name as asst_name,
        d.name as pat_name
        FROM
        prescription AS a 
        left join rbac_user as b on b.id = a.doc_id
        left join rbac_user as c on c.id = a.asst_id
        left join patient_profile as d on d.id = a.pat_pro_id
        WHERE
        a.id = ?
    `

	var prescription Prescription
	err = database.GetRow(sql, &prescription, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "查询处方数据失败",
			"err":  err.Error(),
		})
		return
	}

	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "ok",
		"data": prescription,
	})
}

// 入库
func Warehouse_finisheddrug_add(w http.ResponseWriter, r *http.Request) {
	user_id, err := common.CheckInt(r.FormValue("user_id"))
	if user_id < 0 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]any{
			"code": 500,
			"msg":  "对不起，库管ID不得为空",
		})
		return
	}
	pre_id, err := common.CheckInt(r.FormValue("pre_id"))
	if pre_id < 0 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]any{
			"code": 500,
			"msg":  "对应处方不得为空",
		})
		return
	}
	ord_id, err := common.CheckInt(r.FormValue("ord_id"))
	if ord_id < 0 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "订单ID不得为空",
		})
		return
	}
	quantity, err := common.CheckInt(r.FormValue("quantity"))
	if quantity < 0 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "成品药数量不能为空",
		})
		return
	}
	unit := r.FormValue("unit")
	if unit == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "成品药单位不得为空",
		})
		return
	}
	// 先查下有没有已经添加过相同的库存
	type Warehouse struct {
		ID       int     `db:"id"`
		Quantity float64 `db:"quantity"`
		Status   int     `db:"status"`
	}
	sql := "SELECT id,quantity FROM warehouse_finisheddrug WHERE pre_id = ? limit 1"
	var warehouse Warehouse
	err = database.GetRow(sql, &warehouse, pre_id)
	if err == nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "该成品药已经添加过库存，请勿重复添加",
		})
		return
	}

	// 构建事务查询
	queries := []database.TxQuery{
		// 1. 添加成品库存
		{
			Query: "INSERT INTO warehouse_finisheddrug (ord_id,pre_id,quantity,unit) VALUES (?,?,?,?)",
			Args:  []interface{}{ord_id, pre_id, quantity, unit},
		},
		// 2. 设置变量存储新增ID
		{
			Query: "SET @new_id = LAST_INSERT_ID()",
			Args:  []interface{}{},
		},
		// 3. 添加成品库存日志
		{
			Query: "INSERT INTO warehouse_finisheddrug_log (pid,user_id,kind,old_data,new_data,change_data,pre_id,ord_id) VALUES (@new_id,?,?,?,?,?,?,?)",
			Args:  []interface{}{user_id, 1, 0, quantity, quantity, pre_id, ord_id},
		},
		// 4. 更新处方状态为5 - 已入成品库
		{
			Query: "UPDATE prescription SET status = 5 WHERE id = ?",
			Args:  []interface{}{pre_id},
		},
	}

	// 执行事务
	new_id, affectedRows, err := database.ExecuteTxWithResult(queries)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "成品库存操作失败",
			"err":  err.Error(),
		})
		return
	}

	// 返回成功响应
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":   200,
		"msg":    "库存新增成功",
		"new_id": new_id,
	})

	// 添加日志记录
	useragent := r.FormValue("useragent")
	logContent := fmt.Sprintf("仓库-成品入库，ID：%d，数量：%d，影响行数：%d", new_id, quantity, affectedRows)
	common.Add_log_worker_in_wx(logContent, user_id, r, useragent)
}

// 根据ID查询可退货的订单
func Find_order_to_return(w http.ResponseWriter, r *http.Request) {
	id := r.FormValue("order_number")
	// 检查ID是否为空
	if id == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "订单ID不得为空",
		})
		return
	}
	// 如果ID第1个字符为D或d，则去掉第1个字符
	if id[0] == 'D' || id[0] == 'd' {
		id = id[1:]
	}
	// 判断ID是不是数字
	if _, err := strconv.Atoi(id); err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "订单ID不得为空",
		})
		return
	}
	// 1，求出订单信息
	type Order struct {
		ID            int    `db:"id"`
		Status        int    `db:"status"`
		Delivery_time string `db:"delivery_time"`
		Create_time   string `db:"create_time"`
		Pat_pro_name  string `db:"pat_pro_name"`
		Asst_name     string `db:"asst_name"`
	}
	sql := `
        SELECT
        a.id,
        b.NAME AS pat_pro_name,
        c.NAME AS asst_name,
        a.status,
        a.delivery_time,
        a.create_time 
        FROM
        orders AS a
        LEFT JOIN patient_profile AS b ON b.id = a.pat_pro_id
        LEFT JOIN rbac_user AS c ON c.id = a.asst_id 
        WHERE
        a.id = ?
		and a.status in(2,3)
        `
	// a.status in(2,3) - 待收货、已签收，2种状态下为可退货状态
	var order Order
	err := database.GetRow(sql, &order, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "该订单ID不存在或非已签收订单",
			"sql":  common.DebugSql(sql, id),
			"err":  err.Error(),
		})
		return
	}
	// 2，查出该订单之前发出的赠品信息
	type Gifts struct {
		ID           int    `db:"id"`
		Wh_gift_name string `db:"wh_gift_name"`
		Wh_gift_id   int    `db:"wh_gift_id"`
		Nums         int    `db:"nums"`
	}
	sql = `
        SELECT
        a.id,
        b.NAME AS wh_gift_name,
        a.wh_gift_id,
        a.nums 
        FROM
        orders_gifts AS a
        LEFT JOIN gifts AS b ON b.id = a.wh_gift_id 
        WHERE
        a.ord_id = ?
    `
	var gifts []Gifts
	err = database.GetAll(sql, &gifts, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "查询赠品数据失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, id),
		})
		return
	}
	// 3，查出该订单之前发出的药品信息
	type Finished_drugs struct {
		ID       int     `db:"id"`
		Pre_id   int     `db:"pre_id"`
		Quantity float64 `db:"quantity"`
		Unit     int     `db:"unit"`
	}
	sql = "select id,pre_id,quantity,unit from warehouse_finisheddrug where ord_id = ?"
	var finished_drugs []Finished_drugs
	err = database.GetAll(sql, &finished_drugs, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]any{
			"code": 500,
			"msg":  "查询药品数据失败",
			"err":  err.Error(),
		})
		return
	}
	//判断当前切片是否为空
	if len(finished_drugs) == 0 {
		common.JSONResponse(w, http.StatusOK, map[string]any{
			"code": 500,
			"msg":  "没在成品药仓库查到关于该订单的相关信息",
		})
		return
	}
	// 4，将上面的3处数据，一起输出客户端
	common.JSONResponse(w, http.StatusOK, map[string]any{
		"code": 200,
		"msg":  "ok",
		"data": map[string]any{
			"order":          order,
			"gifts":          gifts,
			"finished_drugs": finished_drugs,
		},
	})
}

// 退货入库
func Warehouse_returndrug_add(w http.ResponseWriter, r *http.Request) {
	var err error
	// 定义请求结构体
	type DrugItem struct {
		ID       int `json:"id"`
		Quantity int `json:"quantity"`
	}

	type ReturnDrugRequest struct {
		UserID int        `json:"user_id"`
		OrdID  int        `json:"ord_id"`
		Drugs  []DrugItem `json:"drugs"`
		Gifts  []DrugItem `json:"gifts"`
	}

	// 解析JSON请求体
	var req ReturnDrugRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]any{
			"code": 500,
			"msg":  "请求数据格式错误",
			"err":  err.Error(),
		})
		return
	}

	// 验证必要参数
	if req.UserID <= 0 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]any{
			"code": 500,
			"msg":  "对不起，库管ID不得为空",
		})
		return
	}

	if req.OrdID <= 0 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]any{
			"code": 500,
			"msg":  "订单ID不得为空",
		})
		return
	}

	if len(req.Drugs) == 0 && len(req.Gifts) == 0 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]any{
			"code": 500,
			"msg":  "药品和赠品数据不能同时为空",
		})
		return
	}

	// 构建事务查询
	queries := make([]database.TxQuery, 0)

	// 处理药品数据
	for _, drug := range req.Drugs {
		drugID := drug.ID
		quantity := drug.Quantity
		// var currentQuantity float64
		// sql := "SELECT quantity FROM warehouse_finisheddrug WHERE id = ?"
		// err = database.GetOne(sql, &currentQuantity, drugID)
		// if err != nil {
		//  common.JSONResponse(w, http.StatusInternalServerError, map[string]any{
		//      "code": 500,
		//      "msg":  "药品库存数据获取失败",
		//      "err":  err.Error(),
		//      "sql":  common.DebugSql(sql, drugID),
		//  })
		//  return
		// }
		// 成品药与赠品有区别，成品药，入库再出库，当前处方对应的成品药库存就是0
		// 将以前已经出库的成品药退回(不用在原库存基础上增加，因为当前处方对应的成品药在出库时，已经清空为0了)，并标记状态为已入库
		queries = append(queries, database.TxQuery{
			Query: "UPDATE warehouse_finisheddrug SET quantity = ?,status = 1,kind=1,update_time=NOW(),name='退回成品药' WHERE id = ?",
			Args:  []any{quantity, drugID},
		})

		// 添加库存日志
		queries = append(queries, database.TxQuery{
			Query: `INSERT INTO warehouse_finisheddrug_log 
                (pid, user_id, kind, change_data, old_data, new_data, ord_id, notes) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
			Args: []any{
				drugID,     // pid
				req.UserID, // user_id
				1,          // kind (入库)
				quantity,   // change_data
				0,          // old_data
				quantity,   // new_data
				req.OrdID,  // ord_id
				"退货入库",     // notes
			},
		})
	}

	// 处理赠品数据
	for _, gift := range req.Gifts {
		giftID := gift.ID
		quantity := gift.Quantity

		// 验证赠品库存数据
		var currentQuantity float64
		sql := "SELECT quantity FROM warehouse_gifts WHERE id = ?"
		err = database.GetOne(sql, &currentQuantity, giftID)
		if err != nil {
			common.JSONResponse(w, http.StatusInternalServerError, map[string]any{
				"code": 500,
				"msg":  "赠品库存数据获取失败",
				"err":  err.Error(),
				"sql":  common.DebugSql(sql, giftID),
			})
			return
		}

		// 更新库存
		queries = append(queries, database.TxQuery{
			Query: "UPDATE warehouse_gifts SET quantity = quantity + ? WHERE id = ?",
			Args:  []any{quantity, giftID},
		})

		// 添加库存日志
		queries = append(queries, database.TxQuery{
			Query: `INSERT INTO warehouse_gifts_log 
                (pid, user_id, kind, change_data, old_data, new_data, notes) 
                VALUES (?, ?, ?, ?, ?, ?, ?)`,
			Args: []any{
				giftID,                              // pid
				req.UserID,                          // user_id
				1,                                   // kind (入库)
				quantity,                            // change_data
				currentQuantity,                     // old_data
				currentQuantity + float64(quantity), // new_data
				"退货入库",                              // notes
			},
		})
	}

	// 更新订单状态为已退货
	queries = append(queries, database.TxQuery{
		Query: "UPDATE orders SET status = 4 WHERE id = ?",
		Args:  []any{req.OrdID},
	})
	// 执行事务
	_, affectedRows, err := database.ExecuteTxWithResult(queries)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]any{
			"code": 500,
			"msg":  "退货入库失败",
			"err":  err.Error(),
		})
		return
	}

	// 返回成功响应
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "退货入库成功",
	})

	// 添加系统日志
	useragent := r.FormValue("useragent")
	logContent := fmt.Sprintf("退货入库成功，订单ID：%d，影响行数：%d", req.OrdID, affectedRows)
	common.Add_log_worker_in_wx(logContent, req.UserID, r, useragent)
}

// 根据一码通（用户ID）查旗下所有订单，时间倒序排列
func Find_err_prescription_by_id(w http.ResponseWriter, r *http.Request) {
	pre_id := r.FormValue("pre_id")
	if pre_id == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "处方ID不得为空",
		})
	}
	if pre_id[0] == 'C' || pre_id[0] == 'c' {
		pre_id = pre_id[1:]
	}
	if _, err := strconv.Atoi(pre_id); err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]any{
			"code": 500,
			"msg":  "处方ID不得为空",
		})
		return
	}
	//先看异常入库里有没有该处方对应的成品
	sql := "select IFNULL(count(id),0) as id from warehouse_finisheddrug where pre_id = ? and kind = 2"
	var has_id int
	err := database.GetOne(sql, &has_id, pre_id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]any{
			"code": 500,
			"msg":  "成品库存检查失败",
			"err":  err.Error(),
		})
		return
	}
	if has_id > 0 {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]any{
			"code": 200,
			"msg":  "当前处方对应的异常成品已入库",
		})
		return
	}
	// 检查结束
	type Prescription struct {
		ID          int    `db:"id"`
		Name        string `db:"name"`
		Phone       string `db:"phone"`
		Diagnosis   string `db:"diagnosis"`
		TxPlan      string `db:"tx_plan"`
		TxDay       string `db:"tx_day"`
		TxType      string `db:"tx_type"`
		TotalDoses  int    `db:"totalDoses"`
		CreateTime  string `db:"create_time"`
		Verify3Desc string `db:"verify_3_desc"`
	}
	sql = `
        SELECT
        a.id,
        b.name,
        b.phone,
        a.diagnosis,
        a.tx_plan,
        a.tx_day,
        a.tx_type,
        a.totalDoses,
        a.create_time,
        a.verify_3_desc 
        FROM
        prescription AS a
        LEFT JOIN patient_profile AS b ON b.id = a.pat_pro_id 
        WHERE
        a.id = ? 
        AND a.STATUS = 2 
        AND a.verify_3 = 2
    `
	var prescription Prescription
	err = database.GetRow(sql, &prescription, pre_id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "没有查到符合条件的数据",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, pre_id),
		})
		return
	}
	// 求出当前处方对应的药品信息
	sql = "select a.id,a.nums,a.quantity,a.process_type,b.name from prescription_drug as a left join drug as b on b.id = a.drug_id where pre_id = ?"
	type Prescription_drug struct {
		ID       int     `db:"id"`
		Nums     int     `db:"nums"`
		Quantity float64 `db:"quantity"`
		Process  int     `db:"process_type"`
		Name     string  `db:"name"`
	}
	var prescription_drug []Prescription_drug
	err = database.GetAll(sql, &prescription_drug, pre_id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "查询药品信息失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, pre_id),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "查询订单成功",
		"data": map[string]interface{}{
			"prescription":      prescription,
			"prescription_drug": prescription_drug,
		},
	})
}

// 异常处方成品入库
func Warehouse_err_prescription_add(w http.ResponseWriter, r *http.Request) {
	// 定义请求结构体
	type RequestData struct {
		PreID     int     `json:"pre_id"`
		UserID    int     `json:"user_id"`
		Unit      int     `json:"unit"`
		Quantity  float64 `json:"quantity"`
		Useragent string  `json:"useragent"`
	}

	// 解析JSON请求体
	var reqData RequestData
	if err := json.NewDecoder(r.Body).Decode(&reqData); err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "请求数据格式错误",
			"err":  err.Error(),
		})
		return
	}

	// 验证处方ID
	if reqData.PreID <= 0 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "处方ID不能为空或小于等于0",
		})
		return
	}

	// 验证用户ID
	if reqData.UserID == 0 {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "库管员ID不得为空",
		})
		return
	}
	//先看异常入库里有没有该处方对应的成品
	sql := "select IFNULL(count(id),0) as id from warehouse_finisheddrug where pre_id = ? and kind = 2"
	var has_id int
	err := database.GetOne(sql, &has_id, reqData.PreID)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]any{
			"code": 500,
			"msg":  "成品库存检查失败",
			"err":  err.Error(),
		})
		return
	}
	if has_id > 0 {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]any{
			"code": 200,
			"msg":  "当前处方对应的异常成品已入库",
		})
		return
	}
	// 检查结束
	type Prescription struct {
		ID        int `db:"id"`
		Record_id int `json:"record_id"`
		Ord_id    int `db:"ord_id"`
	}
	sql = `
        SELECT
        id,
		record_id,
        ord_id
        FROM
        prescription
        WHERE
        id = ?
        AND STATUS = 2 
        AND verify_3 = 2
    `
	var prescription Prescription
	err = database.GetRow(sql, &prescription, reqData.PreID)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "查询处方失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, reqData.PreID),
		})
		return
	}

	// 设置默认值
	if reqData.Quantity == 0 {
		reqData.Quantity = 1
	}
	// 异常处方制的成品药入库

	// 构建事务查询
	queries := []database.TxQuery{
		// 1. 添加成品库存
		{
			Query: "insert into warehouse_finisheddrug (kind,ord_id,pre_id,quantity,status,unit,name) values (?,?,?,?,?,?,?)",
			Args:  []any{2, prescription.Ord_id, prescription.ID, reqData.Quantity, 1, reqData.Unit, "异常处方入库成品"},
		},
		// 2. 设置变量存储新增ID
		{
			Query: "SET @new_id = LAST_INSERT_ID()",
			Args:  []any{},
		},
		// 3. 添加成品库存日志
		{
			Query: "INSERT INTO warehouse_finisheddrug_log (pid,user_id,kind,old_data,new_data,change_data,pre_id,ord_id) VALUES (@new_id,?,?,?,?,?,?,?)",
			Args:  []any{reqData.UserID, 1, 0, reqData.Quantity, reqData.Quantity, prescription.ID, prescription.Ord_id},
		},
		// 4. 更新处方状态 - 异常处方成品入库后，处方改为7，作废
		{
			Query: "UPDATE prescription SET status = 7 WHERE id = ?",
			Args:  []any{prescription.ID},
		},
		// 5. 将该处方从病历中解绑，并将病历设置为待开方
		{
			Query: "UPDATE patient_records SET pre_id = 0,status=4 WHERE id = ?",
			Args:  []any{prescription.Record_id},
		},
	}

	// 执行事务
	new_id, affectedRows, err := database.ExecuteTxWithResult(queries)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "异常处方成品库存操作失败",
			"err":  err.Error(),
		})
		return
	}

	// 返回成功响应
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":   200,
		"msg":    "库存新增成功",
		"new_id": new_id,
	})

	// 添加日志记录
	logContent := fmt.Sprintf("仓库-成品（异常处方）入库，ID：%d，数量：%.2f，影响行数：%d", new_id, reqData.Quantity, affectedRows)
	common.Add_log_worker_in_wx(logContent, reqData.UserID, r, reqData.Useragent)
}

// 小程序个人中心，查看当前用户是否存在待视频问诊的数据
func Is_has_rtcdata(w http.ResponseWriter, r *http.Request) {
	// jwt鉴权
	authHeader := r.Header.Get("Authorization")
	tokenString := strings.TrimPrefix(authHeader, "Bearer ")
	claims, err := common.ValidateJWTToken(tokenString)
	if err != nil {
		// token过期提示：Token is expired
		common.JSONResponse(w, http.StatusUnauthorized, map[string]interface{}{
			"code":  500,
			"msg":   "鉴权失败 - token",
			"error": err.Error(),
		})
		return
	}
	patientid := claims["patientid"].(float64)
	if patientid == 0 {
		common.JSONResponse(w, http.StatusUnauthorized, map[string]interface{}{
			"code":  500,
			"msg":   "鉴权失败 - patientid",
			"error": "Invalid token: patientid is missing",
		})
		return
	}

	// SQL语句与pat_id无关联，所以鉴定家庭成员ID是否在JWT鉴权的patientid中
	pat_pro_id, _ := strconv.Atoi(r.FormValue("pat_pro_id"))
	err = common.Is_in_family(w, int(patientid), pat_pro_id)
	if err != nil {
		common.JSONResponse(w, http.StatusForbidden, map[string]interface{}{
			"code":  403,
			"msg":   "鉴权失败 - 家庭成员ID不在JWT鉴权的patientid中",
			"error": err.Error(),
		})
		return
	}
	// 查询是否有待视频问诊的数据
	type TrtcData struct {
		ID             int    `db:"id"`
		Name           string `db:"name"`
		Title          string `db:"title"`
		Record_id      int    `db:"record_id"`
		Scheduled_time string `db:"scheduled_time"`
	}
	sql := `
			SELECT
			a.id,
			b.name,
			b.professionalTitle title,
			a.scheduled_time,
			a.record_id
			FROM
			rtc_room AS a
			LEFT JOIN rbac_user AS b ON b.id = a.doc_id
			WHERE
			a.pat_pro_id = ?
			AND a.STATUS = 0
			AND DATE(a.scheduled_time) >= CURDATE()
	`
	var trtcData TrtcData
	err = database.GetRow(sql, &trtcData, pat_pro_id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "查询待视频问诊数据失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, pat_pro_id),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "当前用户存在待视频问诊数据",
		"data": trtcData,
		"sql":  common.DebugSql(sql, pat_pro_id),
	})
}
