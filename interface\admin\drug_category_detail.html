<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 药材分类详情</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
</head>

<body>
    <div class="layui-padding-3">
        <div class="layui-form-item">
            <label class="layui-form-label">分类名称</label>
            <div class="layui-input-block">
                <div class="detail-text" id="name"></div>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">分类描述</label>
            <div class="layui-input-block">
                <div class="detail-text" id="description"></div>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">状态</label>
            <div class="layui-input-block">
                <div class="detail-text" id="status"></div>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">创建时间</label>
            <div class="layui-input-block">
                <div class="detail-text" id="created_time"></div>
            </div>
        </div>

        <!-- 底部按钮 -->
        <div class="layui-form-item" style="text-align: center;">
            <button type="button" class="layui-btn layui-btn-primary" onclick="closeModalWindow()">关闭</button>
        </div>
    </div>

    <script>
        layui.use(['layer'], function () {
            var layer = layui.layer;
            var $ = layui.$;

            // 获取URL参数
            var id = request.get('id');
            if (!id) {
                layer.msg('缺少必要参数', { icon: 2 });
                return;
            }

            // 加载数据
            layer.load(2);
            $.ajax({
                url: serverUrl + '/admin/drug_category/detail',
                type: 'POST',
                data: { id: id },
                success: function (res) {
                    layer.closeAll('loading');
                    if (res.code === 200) {
                        var data = res.data;
                        $('#name').text(data.Name || '--');
                        $('#description').text(data.Description || '--');
                        $('#status').html(data.Status == 1 ?
                            '<span class="layui-badge layui-bg-green">启用</span>' :
                            '<span class="layui-badge layui-bg-gray">禁用</span>');
                        $('#created_time').text(data.Create_time.replace('T',' ').replace('Z','') || '--');
                    } else {
                        layer.msg(res.msg || '加载数据失败', { icon: 2 });
                    }
                },
                error: function (res) {
                    layer.closeAll('loading');
                    layer.msg(res.responseJSON.msg, { icon: 2, time: 1000 });
                }
            });
        });

        function closeModalWindow() {
            var index = parent.layer.getFrameIndex(window.name);
            parent.layer.close(index);
        }
    </script>
</body>

</html> 