package routes

import (
	handlers "mstproject/internal/app/normal"
	"net/http"
)

func NormalRoutes(mux *http.ServeMux) {
	mux.HandleFunc("/normal/createtoken", handlers.CreateToken)
	mux.HandleFunc("/normal/uploadWebInterface", handlers.UploadWebInterface)
	// 连了很多表的用户数据缓存
	mux.HandleFunc("/normal/user_more_cache_get", handlers.User_more_cache_get)
	mux.HandleFunc("/normal/user_more_cache_set", handlers.User_more_cache_set)
	// 常用单表缓存
	mux.HandleFunc("/normal/api_cache_set", handlers.Api_cache_set)
	mux.HandleFunc("/normal/perm_cache_set", handlers.Perm_cache_set)
	mux.HandleFunc("/normal/menu_cache_set", handlers.Menu_cache_set)
	mux.HandleFunc("/normal/role_cache_set", handlers.Role_cache_set)
	mux.HandleFunc("/normal/user_cache_set", handlers.User_cache_set)
	mux.HandleFunc("/normal/user_cache_get", handlers.User_cache_get)
	mux.HandleFunc("/normal/api_cache_get", handlers.Api_cache_get)
	mux.HandleFunc("/normal/perm_cache_get", handlers.Perm_cache_get)
	mux.HandleFunc("/normal/menu_cache_get", handlers.Menu_cache_get)
	mux.HandleFunc("/normal/role_cache_get", handlers.Role_cache_get)
	mux.HandleFunc("/normal/department_cache_get", handlers.Department_cache_get)
	mux.HandleFunc("/normal/department_cache_set", handlers.Department_cache_set)
	mux.HandleFunc("/normal/id2name_cache_get", handlers.Id2name_cache_get)
	mux.HandleFunc("/normal/set_rtc_room_status_to_expired", handlers.Set_rtc_room_status_to_expired)
	mux.HandleFunc("/normal/bind_wx", handlers.Bind_wx)
	mux.HandleFunc("/normal/bind_wx_patient", handlers.Bind_wx_patient)
	mux.HandleFunc("/normal/bind_wx_in_share_room", handlers.Bind_wx_in_share_room)

	// 自动更新订单最新物流信息 - 按照上次更新时间的倒序排列，每次只更新一条
	mux.HandleFunc("/normal/auto_update_order_express_info", handlers.Auto_update_order_express_info)
	// 获取订单状态
	mux.HandleFunc("/normal/get_order_status", handlers.Get_order_status)
}
