<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 病历修改</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/layui/layarea_lc.js"></script>
    <script src="/dist/js/main.js"></script>
    <style>
        .has_account {
            display: none;
        }

        .layui-input-block input,
        .layui-form-select {
            max-width: 100% !important;
        }

        .chooseUser {
            box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
        }

        .line_font {
            font-size: 17px;
            font-weight: bold;
            margin: 10px 0 20px 0;
            border-bottom: 1px solid #efefef;
            padding: 10px;
        }

        /* doctor-tips 相关样式 */
        .doctor-tips {
            position: absolute;
            top: -35px;
            left: 140px;
            background-color: #3595CC;
            color: #fff;
            padding: 5px 10px;
            border-radius: 3px;
            z-index: 10000;
            white-space: nowrap;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }

        /* doctor-tips 小三角形 */
        .doctor-tips::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 50%;
            margin-left: -5px;
            width: 0;
            height: 0;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 5px solid #3595CC;
        }

        /* 模糊效果相关样式 */
        .choose_doctor.disabled {
            position: relative;
            padding: 10px;
        }

        .choose_doctor.disabled .layui-input-block {
            filter: blur(1.5px);
            background: #eee;
            padding: 5px;
        }

        /* 图片预览容器样式 */
        .image-preview-container {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-top: 15px;
        }

        .image-preview-item {
            position: relative;
            width: 150px;
            height: 150px;
            background-color: #f5f5f5;
            border-radius: 4px;
            overflow: hidden;
        }

        .image-preview-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 4px;
            cursor: pointer;
        }

        .image-preview-item .delete-btn {
            position: absolute;
            bottom: 5px;
            left: 50%;
            transform: translateX(-50%);
            padding: 2px 10px;
            background-color: rgba(255, 255, 255, 0.9);
            border: 1px solid #ddd;
            border-radius: 3px;
            cursor: pointer;
        }

        .delete-btn:hover {
            background-color: #f56c6c;
            color: white;
        }
    </style>
</head>

<body>
    <div class="layui-layout layui-layout-admin">
        <!-- 头部 -->
        <div class="layui-header">
            <div class="layui-logo">幸年堂</div>
            <ul class="layui-nav layui-layout-right">
                <li class="layui-nav-item" style="background-color:transparent!important;padding-right:10px">
                    <a href="javascript:;" style="display:flex;align-items:center;color:#fff"><img
                            src="/dist/images/user.png" class="layui-nav-img"><span class="user_name"></span></a>
                    <dl class="layui-nav-child pop_menu">
                        <dd><i class="iconfont">&#xe62f;</i><a href="change_password.html#7">用户资料</a></dd>
                        <dd><i class="iconfont">&#xe601;</i><a href="javascript:void(0)" class="refresh_cache">刷新缓存</a>
                        </dd>
                        <dd><i class="iconfont">&#xe634;</i><a href="login_out.html">退出登录</a></dd>
                    </dl>
                </li>
            </ul>
        </div>
        <!-- 导航 -->
        <ul class="layui-nav layui-nav-tree layui-nav-side" id="menuContainer" lay-bar="disabled" lay-accordion>
            <div style="margin: 50px 0;color: #bbb;text-align: center;">菜单加载中...</div>
        </ul>
        <!-- 主体内容 -->
        <div class="layui-body">
            <div class="body_child">
                <div class="layui-panel">
                    <div class="layui-card-header">
                        <div class="layui-row" style="padding-top:10px;">
                            <div class="layui-col-md8">病历修改</div>
                        </div>
                    </div>
                    <div class="layui-padding-4" style="min-height: 800px;">

                        <form class="layui-form" lay-filter="form_edit" onsubmit="return false">










                            <div class="line_font">患者基础信息</div>
                            <div class="layui-row">
                                <div class="layui-col-xs4">
                                    <div class="info-item">
                                        <span class="info-label">手机号码：</span>
                                        <span class="info-content" id="phone">-</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">关系：</span>
                                        <span class="info-content" id="relation">-</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">患者来源：</span>
                                        <span class="info-content" id="patient_from">-</span>
                                    </div>
                                </div>
                                <div class="layui-col-xs4">
                                    <div class="info-item">
                                        <span class="info-label">患者姓名：</span>
                                        <span class="info-content" id="name">-</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">性别：</span>
                                        <span class="info-content" id="sex">-</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">微信号：</span>
                                        <span class="info-content" id="weixin">-</span>
                                    </div>
                                </div>
                                <div class="layui-col-xs4">
                                    <div class="info-item">
                                        <span class="info-label">出生日期：</span>
                                        <span class="info-content" id="born_date">-</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">主治医生：</span>
                                        <span class="info-content" id="doctor_with_account">-</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">患者等级：</span>
                                        <span class="info-content" id="patient_level">-</span>
                                    </div>
                                </div>
                                <div class="layui-col-xs4">
                                    <div class="info-item">
                                        <span class="info-label">医保卡：</span>
                                        <span class="info-content" id="ins_card_num">-</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">身高：</span>
                                        <span class="info-content" id="height">-</span>
                                    </div>
                                </div>
                                <div class="layui-col-xs4">
                                    <div class="info-item">
                                        <span class="info-label">身份证号：</span>
                                        <span class="info-content" id="idcard">-</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">体重：</span>
                                        <span class="info-content" id="weight">-</span>
                                    </div>
                                </div>
                                <div class="layui-col-xs4">
                                    <div class="info-item">
                                        <span class="info-label">参保类型：</span>
                                        <span class="info-content" id="ins_type">-</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">婚姻状况：</span>
                                        <span class="info-content" id="isMarried">-</span>
                                    </div>
                                </div>
                            </div>
                            <div class="line_font">病历修改</div>

                            <div class="layui-row">
                                <div class="layui-col-md6">
                                    <div class="layui-row">
                                        <div class="layui-col-md7">
                                            <div class="layui-form-item choose_doctor">
                                                <label class="layui-form-label">医生与科室</label>
                                                <div class="layui-input-block">
                                                    <div class="layui-col-md5" style="margin-right: 10px;">
                                                        <select name="department_id" id="keshi"
                                                            lay-filter="keshi"></select>
                                                    </div>
                                                    <div class="layui-col-md5"><select name="doc_id"
                                                            id="doctor"></select></div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="layui-col-md5">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">医助</label>
                                                <div class="layui-input-block">
                                                    <div class="layui-form-mid asst_name"> - </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">有无三高</label>
                                        <div class="layui-input-block">
                                            <input type="checkbox" name="triad_input" value="高血压" title="高血压">
                                            <input type="checkbox" name="triad_input" value="高血糖" title="高血糖">
                                            <input type="checkbox" name="triad_input" value="高血脂" title="高血脂">
                                            <input type="checkbox" name="triad_input" value="无" title="无">
                                        </div>
                                    </div>

                                    <div class="layui-form-item">
                                        <label class="layui-form-label">意向剂型</label>
                                        <div style="display: flex;">
                                            <div style="display: flex;">
                                                <input type="text" name="tx_day" class="layui-input"
                                                    oninput="if(value>100)value=100">
                                                <div style="width:28px;padding: 9px 10px">天</div>
                                            </div>
                                            <div style="display: flex;">
                                                <div style="width: 28px;padding: 9px 10px">剂型</div>
                                                <select name="tx_type">
                                                    <option value=""></option>
                                                    <option value="膏滋">膏滋</option>
                                                    <option value="丸剂">丸剂</option>
                                                    <option value="汤剂">汤剂</option>
                                                    <option value="散剂">散剂</option>
                                                    <option value="无糖膏">无糖膏</option>
                                                    <option value="水丸">水丸</option>
                                                    <option value="内服粉剂">内服粉剂</option>
                                                    <option value="外用粉剂">外用粉剂</option>
                                                    <option value="中药材">中药材</option>
                                                    <option value="清膏">清膏</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>


                                    <div class="layui-form-item">
                                        <label class="layui-form-label">患者主诉</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="chief_complaint" placeholder="请输入患者主诉症状"
                                                autocomplete="off" class="layui-input">
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">复诊主诉</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="re_chief_complaint" placeholder="请输入复 诊主诉信息"
                                                autocomplete="off" class="layui-input">
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">大小便情况</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="urination" placeholder="请输入大小便情况，如正常、便秘、腹泻等"
                                                autocomplete="off" class="layui-input">
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">既往病史</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="past_medical_history"
                                                placeholder="请输入既往病史，如高血压、糖尿病等" autocomplete="off" class="layui-input">
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">用药史</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="past_medication_history"
                                                placeholder="请输入用药史，如长期服用降压药等" autocomplete="off" class="layui-input">
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">舌象描述</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="tonguedesc" placeholder="请输入舌象描述"
                                                autocomplete="off" class="layui-input">
                                        </div>
                                    </div>

                                </div>
                                <div class="layui-col-md6">

                                    <div class="layui-form-item">
                                        <label class="layui-form-label smalltext">上次用药时间</label>
                                        <div class="layui-input-block">
                                            <div class="layui-input-wrap">
                                                <div class="layui-input-prefix">
                                                    <i class="layui-icon layui-icon-date"></i>
                                                </div>
                                                <input type="text" name="last_medication_time" id="last_medication_time"
                                                    placeholder="请选择上次用药时间" autocomplete="off" class="layui-input"
                                                    readonly>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="layui-form-item">
                                        <label class="layui-form-label smalltext">上次用药情况</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="last_medical" placeholder="请输入目前需要的治疗方法"
                                                autocomplete="off" class="layui-input">
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">现需治疗</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="now_needs" placeholder="请输入目前需要的治疗方法"
                                                autocomplete="off" class="layui-input">
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">现病史</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="history_of_present_illness"
                                                placeholder="暂不删，以防后续需要，已设置非必填" autocomplete="off" class="layui-input">
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">个人史</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="personal_history" placeholder="暂不删，以防后续需要，已设置非必填"
                                                autocomplete="off" class="layui-input">
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">家族史</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="family_history" placeholder="暂不删，以防后续需要，已设置非必填"
                                                autocomplete="off" class="layui-input">
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">过敏史</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="allergy_history" placeholder="暂不删，以防后续需要，已设置非必填"
                                                autocomplete="off" class="layui-input">
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">诊断信息</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="diagnosis_information"
                                                placeholder="暂不删，以防后续需要，已设置非必填" autocomplete="off" class="layui-input">
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">治疗方案</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="treatment_plan" placeholder="暂不删，以防后续需要，已设置非必填"
                                                autocomplete="off" class="layui-input">
                                        </div>
                                    </div>
                                </div>
                            </div>












                            <div class="layui-row">
                                <div class="layui-col-md6">
                                    <!-- 舌苔照 -->
                                    <div class="layui-upload layui-padding-4">
                                        <button type="button" class="layui-btn upload_big_btn"
                                            id="ID-upload-demo-btn-1">
                                            <div class="btn_big_font"><i
                                                    class="layui-icon layui-icon-upload btn_big_font"></i> 舌苔照上传
                                            </div>
                                            <div>jpg、png、jpeg、bmp、gif格式，2M以内</div>
                                            <div>可点选上传和拖拽上传</div>
                                        </button>
                                        <div class="image-preview-container" id="tongue-photo-container"></div>
                                    </div>
                                </div>


                                <div class="layui-col-md6">
                                    <!-- 检查单 -->
                                    <div class="layui-upload layui-padding-4">
                                        <button type="button" class="layui-btn upload_big_btn"
                                            id="ID-upload-demo-btn-2">
                                            <div class="btn_big_font"><i
                                                    class="layui-icon layui-icon-upload btn_big_font"></i> 检查单上传
                                            </div>
                                            <div>jpg、png、jpeg、bmp、gif格式，2M以内</div>
                                            <div>可点选上传和拖拽上传</div>
                                        </button>
                                        <div class="image-preview-container" id="sheet-photo-container"></div>
                                    </div>
                                </div>
                            </div>


                            <div style="display: flex; justify-content: center;margin-top: 50px;">
                                <button class="layui-btn" lay-submit lay-filter="formSubmitBtn" id="formSubmitBtn"
                                    style="margin-right: 50px;">确认修改</button>
                                <button type="reset" class="layui-btn layui-btn-primary"
                                    onclick="history.go(-1)">取消</button>
                            </div>

                        </form>


                    </div>
                </div>
                <br>
                <br>
            </div>
        </div>
    </div>


    <script>
        layui.config({
            base: './mods/'
            , version: '1.0'
        });
        layui.use(['element', 'layer', 'util'], function () {
            var element = layui.element;
            var layer = layui.layer;
            var form = layui.form;
            var util = layui.util;
            var treeTable = layui.treeTable;
            var laydate = layui.laydate;
            var dropdown = layui.dropdown;
            var $ = layui.$;
            var upload = layui.upload;
            var id = request.get('id');
            if (!id) {
                layer.msg('参数错误', { icon: 2, time: 1000 });
                return false;
            }
            layarea_lc = layui.layarea_lc;
            laydate.render({
                elem: '#ID-laydate-demo,#last_medication_time'
            });
            var has_account = false;
            // 页面顶部插入缓存的用户数据
            let local_userinfo = localStorage.getItem('local_userinfo');
            let user_name = "";
            if (local_userinfo) {
                local_userinfo = JSON.parse(local_userinfo);
                user_name = local_userinfo.Name + " ( " + local_userinfo.Roles + " )";
            } else {
                user_name = "未登录";
            }
            $('.user_name').text(user_name);
            render_menu($);
            $('.refresh_cache').click(function () {
                refresh_cache($);
            });
            // 加载医助姓名
            var load_asst_name = function (user_id) {
                $.ajax({
                    url: '/admin/user/list_low',
                    data: {
                        id: user_id,
                    },
                    type: 'post',
                    success: function (res) {
                        if (res.code == 200) {
                            let asst_name = res.data[0].Name;
                            $('.asst_name').text(asst_name);
                        }
                    }
                })
            }
            // 获取病历详情（接口/admin/patient_records/detail），并回填表单
            layer.load(2);
            $.ajax({
                url: serverUrl + "/admin/patient_records/detail",
                type: "post",
                data: {
                    id: id,
                },
                success: function (res) {
                    layer.closeAll('loading');
                    if (res.code == 200) {
                        let data = res.data;
                        form.val('form_edit', {
                            'chief_complaint': data.Chief_complaint, // 患者主诉
                            'history_of_present_illness': data.History_of_present_illness, // 现病史
                            'tonguedesc': data.Tonguedesc, // 舌象描述
                            'past_medical_history': data.Past_medical_history, // 既往病史
                            'personal_history': data.Personal_history, // 个人史
                            'family_history': data.Family_history, // 家族史
                            'allergy_history': data.Allergy_history, // 过敏史
                            'diagnosis_information': data.Diagnosis_information, // 诊断信息
                            'treatment_plan': data.Treatment_plan, // 治疗方案
                            'discharge_time': data.Discharge_time, // 出院时间
                            'status': data.Status, // 患者状态
                            'create_time': data.Create_time, // 创建时间
                            'pre_id': data.Pre_id, // 预先ID
                            'department_id': data.Department_id, // 科室ID
                            // 'triad': data.Triad, // 三高情况
                            'tx_day': data.Tx_day, // 意向剂型天数
                            'tx_type': data.Tx_type, // 意向剂型
                            're_chief_complaint': data.Re_chief_complaint, // 复诊主诉
                            'urination': data.Urination, // 大小便情况
                            'now_needs': data.Now_needs, // 现需治疗
                            'last_medical': data.Last_medical, // 上次用药情况
                            'last_medication_time': data.Last_medication_time.split('T')[0], // 上次用药时间
                            'past_medication_history': data.Past_medication_history,//用药史
                        });
                        load_full_data(data.Pat_pro_id);
                        // 回填"三高"复选框
                        // 加载医助姓名
                        load_asst_name(data.Asst_id);
                        const triadValues = data.Triad.split(','); // 获取复选框的值
                        triadValues.forEach(value => {
                            const checkbox = document.querySelector(`input[name="triad_input"][title="${value}"]`);
                            if (checkbox) {
                                checkbox.checked = true; // 选中复选框
                            }
                        });
                        form.render('checkbox');
                        // 回填舌苔照图片
                        let pic = '';
                        if (data.Photo_tongue) {
                            let tongue_photos = data.Photo_tongue.split('\n');
                            for (let i = 0; i < tongue_photos.length; i++) {
                                tongue_photos[i] = tongue_photos[i].replace(/[\r\n]/g, '');
                                pic = '/static/uploads/normal_pics/photo_tongue/' + tongue_photos[i];
                                appendImagePreview('tongue-photo-container', pic, tongue_photos[i]);
                            }
                        }
                        // 回填检查单图片
                        if (data.Photo_sheet) {
                            let sheet_photos = data.Photo_sheet.split('\n');
                            for (let i = 0; i < sheet_photos.length; i++) {
                                sheet_photos[i] = sheet_photos[i].replace(/[\r\n]/g, '');
                                pic = '/static/uploads/normal_pics/photo_sheet/' + sheet_photos[i];
                                appendImagePreview('sheet-photo-container', pic, sheet_photos[i]);
                            }
                        }
                        load_doctor_infos(data.Doc_id, data.Status);
                    } else {
                        layer.msg(res.msg, { icon: 2, time: 1000 });
                    }
                },
                error: function (res) {
                    layer.closeAll('loading');
                    layer.msg('获取病历详情失败', { icon: 2, time: 1000 });
                }
            });
            var load_doctor_infos = function (Doc_id, Status) {
                // console.log(Doc_id,Status);
                if (Status > 0) {
                    // 添加disabled类，应用CSS中定义的样式
                    $('.choose_doctor').addClass('disabled');

                    // 禁用选择框
                    $('.choose_doctor').find('select').attr('disabled', true);
                    form.render('select');

                    // 在choose_doctor内部添加提示元素
                    $('.choose_doctor').append('<div class="doctor-tips">取消或删除诊室，才可以更换医生。</div>');
                    $('.doctor-tips').click(function () {
                        window.location.href = "rtc_room_list.html";
                    });

                    return;
                }
                layer.load(2);
                $.ajax({
                    url: '/admin/department/list',
                    data: {
                        name: '医疗部门',
                        store_id: global_default_store_id,
                    },
                    type: 'post',
                    success: function (res) {
                        layer.closeAll('loading');
                        if (res.code === 200) {
                            let data = res.data;
                            // 将科室固定为"中医内科"(ID为3)
                            let html = '';
                            for (let i = 0; i < data.length; i++) {
                                if (data[i].id === 3 || data[i].name === "中医内科") {
                                    html = '<option value="' + data[i].id + '" selected>' + data[i].name + '</option>';
                                    break;
                                }
                            }
                            // 如果没有找到中医内科，使用固定值
                            if (html === '') {
                                html = '<option value="3" selected>中医内科</option>';
                            }
                            $('#keshi').html(html);
                            // 设置下拉菜单为禁用状态
                            $('#keshi').prop('disabled', true);
                            form.render('select');

                            // 自动触发获取中医内科医生的请求
                            layer.load(2);
                            $.ajax({
                                url: '/admin/user/list_low',
                                data: {
                                    role_id: 4,
                                    department_id: 3,  // 中医内科的ID
                                },
                                type: 'post',
                                success: function (res) {
                                    layer.closeAll('loading');
                                    if (res.code === 200) {
                                        let data = res.data;
                                        let html = '<option value="">选择医生</option>';
                                        if (data) {
                                            for (let i = 0; i < data.length; i++) {
                                                // console.log(data[i].ID === Doc_id, data[i].ID, Doc_id)
                                                if (data[i].ID === Doc_id) {
                                                    html += '<option value="' + data[i].ID + '" selected>' + data[i].Name + '</option>';
                                                } else {
                                                    html += '<option value="' + data[i].ID + '">' + data[i].Name + '</option>';
                                                }
                                            }
                                        } else {
                                            layer.msg('该科室暂无医生', { icon: 2, time: 1000 });
                                            html = '<option value="">该科室暂无医生</option>';
                                        }
                                        $('#doctor').html(html);
                                        form.render('select');
                                    } else {
                                        layer.msg(res.msg, { icon: 2, time: 1000 });
                                    }
                                }, error: function (res) {
                                    layer.closeAll('loading');
                                    layer.msg(res.responseJSON.msg, { icon: 2, time: 1000 });
                                }
                            });

                            // 保留原有的 form.on 事件，但实际上由于下拉框被禁用了，这个事件不会被触发
                            form.on('select(keshi)', function (data) {
                                let keshi_id = data.value;
                                if (keshi_id) {
                                    layer.load(2);
                                    $.ajax({
                                        url: '/admin/user/list_low',
                                        data: {
                                            role_id: 4,
                                            department_id: keshi_id,
                                        },
                                        type: 'post',
                                        success: function (res) {
                                            layer.closeAll('loading');
                                            if (res.code === 200) {
                                                let data = res.data;
                                                let html = '<option value="">选择医生</option>';
                                                if (data) {
                                                    for (let i = 0; i < data.length; i++) {
                                                        html += '<option value="' + data[i].ID + '">' + data[i].Name + '</option>';
                                                    }
                                                } else {
                                                    layer.msg('该科室暂无医生', { icon: 2, time: 1000 });
                                                    html = '<option value="">该科室暂无医生</option>';
                                                }
                                                $('#doctor').html(html);
                                                form.render('select');
                                            } else {
                                                layer.msg(res.msg, { icon: 2, time: 1000 });
                                            }
                                        }, error: function (res) {
                                            layer.closeAll('loading');
                                            layer.msg(res.responseJSON.msg, { icon: 2, time: 1000 });
                                        }
                                    });
                                }
                            });
                        } else {
                            layer.msg(res.msg, { icon: 2, time: 1000 });
                        }
                    }, error: function (res) {
                        layer.closeAll('loading');
                        layer.msg(res.responseJSON.msg, { icon: 2, time: 1000 });
                    }
                });
            };
            // 获取用户基础信息
            var load_full_data = function (user_id) {
                $.ajax({
                    url: serverUrl + "/admin/patient_profile/detail",
                    type: "post",
                    data: { "id": user_id },
                    success: function (res) {
                        layer.closeAll('loading');
                        let data = res.data;

                        // 基本信息
                        $('#phone').text(data.Phone || '-');
                        $('#name').text(data.Name || '-');
                        $('#sex').text(data.Sex == '1' ? '男' : '女');
                        $('#born_date').text(data.Born_date.replace("T00:00:00Z", "") || '-');
                        $('#relation').text(FamilyRelation[data.Relation] || '-');
                        $('#weixin').text(data.Weixin || '-');
                        $('#patient_from').text(Patient_From[data.Patient_from] || '-');
                        if (data.Doc_id) {
                            $.ajax({
                                url: serverUrl + "/admin/user/list_low",
                                type: "post",
                                data: {
                                    id: data.Doc_id
                                },
                                success: function (res) {
                                    if (res.code == 200 && res.data.length > 0) {
                                        $('#doctor_with_account').text(res.data[0].Name || data.Doc_id);
                                    } else {
                                        $('#doctor_with_account').text(data.Doc_id || '-');
                                    }
                                },
                                error: function () {
                                    $('#doctor_with_account').text(data.Doc_id || '-');
                                }
                            });
                        } else {
                            $('#doctor_with_account').text('-');
                        }
                        $('#patient_level').text(data.Level ? data.Level + '级' : '-');

                        // 其他信息
                        $('#idcard').text(data.Idcard || '-');
                        $('#ins_card_num').text(data.Ins_card_num || '-');
                        $('#ins_type').text(Ins_Type[data.Ins_type] || '-');
                        $('#height').text(data.Height ? data.Height + 'cm' : '-');
                        $('#weight').text(data.Weight ? data.Weight + 'kg' : '-');
                        $('#isMarried').text(data.Ismarried == '1' ? '已婚' : '未婚');

                        // 地址处理
                        let address_arr = data.Address.split('|');
                        $('#full_address').text(address_arr.join(' '));

                        // 医疗信息
                        $('#medical_history').text(data.Medical_history || '-');
                        $('#allergies').text(data.Allergies || '-');
                        $('#chief_complaint').text(data.Chief_complaint || '-');
                        $('#customer_notes').text(data.Customer_notes || '-');
                    },
                    error: function (err) {
                        layer.closeAll('loading');
                        console.error(err);
                        layer.msg(err.responseJSON ? err.responseJSON.msg : '请求失败', { icon: 2, time: 1000 });
                    }
                });
            }
            //监听提交
            form.on('submit(formSubmitBtn)', function (data) {
                let formData = new FormData();
                formData.append('id', id);
                for (let key in data.field) {
                    formData.append(key, data.field[key]);
                }
                // 处理"有无三高"复选框
                // if ($("input:checkbox[name='triad_input']:checked").length == 0) {
                //     layer.msg('请选择是否有三高', { icon: 2, time: 1000 });
                //     return;
                // }
                let triad_arr = [];
                $("input:checkbox[name='triad_input']:checked").each(function (i) {
                    triad_arr[i] = $(this).val();
                });
                let triad = triad_arr.join(",");
                formData.append('triad', triad);
                // 舌苔照
                let tongue_photos = [];
                $('#tongue-photo-container .image-preview-item').each(function () {
                    let tongue_photo = $(this).data('filename');
                    tongue_photo = tongue_photo.replace(/[\r\n]/g, '');
                    tongue_photos.push(tongue_photo);
                });
                let tongue_photos_str = tongue_photos.join('\n');
                formData.append('tongue_photos', tongue_photos_str);
                // 检查单
                let sheet_photos = [];
                $('#sheet-photo-container .image-preview-item').each(function () {
                    let sheet_photo = $(this).data('filename');
                    sheet_photo = sheet_photo.replace(/[\r\n]/g, '');
                    sheet_photos.push(sheet_photo);
                });
                let sheet_photos_str = sheet_photos.join('\n');
                formData.append('sheet_photos', sheet_photos_str);
                layer.load(2);
                $.ajax({
                    url: serverUrl + "/admin/patient_records/edit",
                    type: "post",
                    data: formData,
                    processData: false, // 不处理数据
                    contentType: false, // 不设置内容类型
                    success: function (res) {
                        layer.closeAll('loading');
                        if (res.code == 200) {
                            layer.msg(res.msg, {
                                time: 2000,
                                icon: 1
                            }, function () {
                                layer.confirm('是否继续修改？', {
                                    btn: ['继续修改', '返回列表', '取消']
                                }, function () {
                                    location.reload();
                                }, function () {
                                    window.location.href = "patient_records_list.html";
                                });
                            }
                            );
                        } else {
                            layer.msg(res.msg, {
                                time: 2000,
                                icon: 2
                            });
                        }
                    },
                    error: function (res) {
                        layer.closeAll('loading');
                        layer.msg(res.responseJSON.msg, {
                            time: 5000,
                            icon: 2
                        });
                    }
                });
                return false;
            });





            // 图片上传部分 - 开始
            // 图片预览函数
            function appendImagePreview(containerId, filepath, filename) {
                const container = $(`#${containerId}`);
                const imageItem = $(`
                    <div class="image-preview-item" data-filename="${filename}" data-filepath="${filepath}">
                        <img src="${filepath}">
                        <div class="delete-btn">删除</div>
                    </div>
                `);

                // 点击图片查看大图
                imageItem.find('img').on('click', function () {
                    layer.photos({
                        photos: {
                            title: '查看图片',
                            data: [{ src: filepath }]
                        },
                        footer: false
                    });
                });

                // 点击删除按钮删除图片
                imageItem.find('.delete-btn').on('click', function () {
                    const category = filepath.split('/')[4]; // 从路径中提取类别
                    layer.confirm('确定删除该图片吗？', { icon: 3, title: '提示' }, function (index) {
                        $.ajax({
                            url: '/admin/normal_pic_del',
                            type: 'POST',
                            data: {
                                filename: filename,
                                category: category
                            },
                            success: function (res) {
                                if (res.code === 200) {
                                    imageItem.remove();
                                    layer.msg('删除成功', { icon: 1, time: 1000 });
                                }
                            },
                            error: function (data) {
                                layer.confirm('删除失败: ' + data.responseJSON.msg + '，是否移除该图片？',
                                    { icon: 3, title: '提示' },
                                    function (index) {
                                        imageItem.remove();
                                        layer.msg('移除成功', { icon: 1, time: 1000 });
                                    }
                                );
                            }
                        });
                        layer.close(index);
                    });
                });

                container.append(imageItem);
            }

            // 初始化舌苔照上传
            upload.render({
                elem: '#ID-upload-demo-btn-1',
                url: '/admin/upload_normal_pic',
                multiple: true,
                data: {
                    category: 'photo_tongue',
                },
                drag: true,
                before: function(obj) {
                    // 预览回调
                },
                done: function(res) {
                    if (res.code === 200) {
                        const fileInfo = res.data[0];
                        appendImagePreview('tongue-photo-container', fileInfo.filepath, fileInfo.filename);
                        layer.msg('上传成功', {icon: 1, time: 1000});
                    } else {
                        layer.msg('上传失败: ' + res.msg, {icon: 2, time: 1000});
                    }
                },
                error: function() {
                    layer.msg('上传失败', {icon: 2, time: 1000});
                }
            });

            // 初始化检查单上传
            upload.render({
                elem: '#ID-upload-demo-btn-2',
                url: '/admin/upload_normal_pic',
                multiple: true,
                data: {
                    category: 'photo_sheet',
                },
                drag: true,
                before: function(obj) {
                    // 预览回调
                },
                done: function(res) {
                    if (res.code === 200) {
                        const fileInfo = res.data[0];
                        appendImagePreview('sheet-photo-container', fileInfo.filepath, fileInfo.filename);
                        layer.msg('上传成功', {icon: 1, time: 1000});
                    } else {
                        layer.msg('上传失败: ' + res.msg, {icon: 2, time: 1000});
                    }
                },
                error: function() {
                    layer.msg('上传失败', {icon: 2, time: 1000});
                }
            });
            // 图片上传部分 - 结束



        });
    </script>
</body>

</html>