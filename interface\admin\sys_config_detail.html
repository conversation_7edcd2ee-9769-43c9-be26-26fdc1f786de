<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 系统设置</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
    <style>
        /* 添加自定义样式，使面板高度一致 */
        .equal-height-panel {
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        .equal-height-panel .layui-card-header {
            flex: 0 0 auto;
        }
        .equal-height-panel .layui-padding-3 {
            flex: 1 1 auto;
            display: flex;
            flex-direction: column;
        }
        .equal-height-row {
            margin-bottom: 20px;
        }
    </style>
</head>

<body>
    <div class="layui-layout layui-layout-admin">
        <!-- 头部 -->
        <div class="layui-header">
            <div class="layui-logo">幸年堂</div>
            <ul class="layui-nav layui-layout-right">
                <li class="layui-nav-item" style="background-color:transparent!important;padding-right:10px">
                    <a href="javascript:;" style="display:flex;align-items:center;color:#fff"><img
                            src="/dist/images/user.png" class="layui-nav-img"><span class="user_name"></span></a>
                    <dl class="layui-nav-child pop_menu">
                        <dd><i class="iconfont">&#xe62f;</i><a href="change_password.html#7">用户资料</a></dd>
                        <dd><i class="iconfont">&#xe601;</i><a href="javascript:void(0)" class="refresh_cache">刷新缓存</a>
                        </dd>
                        <dd><i class="iconfont">&#xe634;</i><a href="login_out.html">退出登录</a></dd>
                    </dl>
                </li>
            </ul>
        </div>
        <!-- 导航 -->
        <ul class="layui-nav layui-nav-tree layui-nav-side" id="menuContainer" lay-bar="disabled" lay-accordion>
            <div style="margin: 50px 0;color: #bbb;text-align: center;">菜单加载中...</div>
        </ul>
        <!-- 主体内容 -->
        <div class="layui-body">
            <div class="body_child">

                <!-- 使用Layui栅格系统创建两列布局 -->
                <!-- 第一行 -->
                <div class="layui-row layui-col-space20 equal-height-row">
                    <!-- 左侧列 -->
                    <div class="layui-col-md6">
                        <div class="layui-panel equal-height-panel">
                            <div class="layui-card-header">
                                <div class="layui-row" style="padding-top:10px;">
                                    <div class="layui-col-md11 layui-col-sm10">音视频参数</div>
                                </div>
                            </div>
                            <div class="layui-padding-3" style="padding-top: 0 !important;">

                                <form class="layui-form" lay-filter="form_save_rtc_auto_record" onsubmit="return false" style="margin-top: 30px;">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">自动录制</label>
                                        <div class="layui-input-block">
                                            <input type="radio" name="isOpen" value="1" title="是">
                                            <input type="radio" name="isOpen" value="0" title="否">
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">上传云端</label>
                                        <div class="layui-input-block">
                                            <input type="radio" name="isUploadCloud" value="1" title="是">
                                            <input type="radio" name="isUploadCloud" value="0" title="否">
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <div class="layui-input-block">
                                            <button class="layui-btn" lay-submit lay-filter="saveConfig">保存配置</button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧列 -->
                    <div class="layui-col-md6">
                        <!-- NAS配置面板 -->
                        <div class="layui-panel equal-height-panel">
                            <div class="layui-card-header">
                                <div class="layui-row" style="padding-top:10px;">
                                    <div class="layui-col-md11 layui-col-sm10">NAS配置</div>
                                </div>
                            </div>
                            <div class="layui-padding-3" style="padding-top: 0 !important;">
                                <!-- 标注 -->
                                <!-- <div class="layui-badge layui-bg-gray" style="margin: 30px 0;padding: 10px 20px;">
                                    <i class="layui-icon layui-icon-tips" style="font-size: 12px; margin-right: 5px;"></i>
                                    系统提示：录制的视频将根据您的配置自动上传至 NAS 平台保存。若未配置 NAS 参数，则视频将默认为直接下载
                                </div> -->

                                <form class="layui-form" lay-filter="form_save_nas_config" onsubmit="return false">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">上传地址</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="uploadUrl" lay-verify="required" placeholder="请输入上传地址"
                                                autocomplete="off" class="layui-input">
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">用户名</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="username" lay-verify="required" placeholder="请输入用户名"
                                                autocomplete="off" class="layui-input">
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">密码</label>
                                        <div class="layui-input-block">
                                            <input type="password" name="password" lay-verify="required" placeholder="请输入密码"
                                                autocomplete="off" class="layui-input">
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <div class="layui-input-block">
                                            <button class="layui-btn" lay-submit lay-filter="saveConfig">保存配置</button>
                                            <button class="layui-btn layui-btn-primary" id="testConfig">连通测试</button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 第二行 - 空置区块 -->
                <div class="layui-row layui-col-space20 equal-height-row">
                    <!-- 左侧列 -->
                    <div class="layui-col-md6">
                        <div class="layui-panel equal-height-panel">
                            <div class="layui-card-header">
                                <div class="layui-row" style="padding-top:10px;">
                                    <div class="layui-col-md11 layui-col-sm10">红章设置</div>
                                </div>
                            </div>
                            <div class="layui-padding-3" style="padding-top: 0 !important;">
                                <div class="layui-badge layui-bg-gray" style="margin: 30px 0;padding: 10px 20px;">
                                    <i class="layui-icon layui-icon-tips" style="font-size: 12px; margin-right: 5px;"></i>
                                    系统提示：方形，500px以内，透明PNG
                                </div>
                                <!-- 预留表单区域 -->
                                <div style="min-height: 200px; display: flex; flex-direction: column; align-items: center; justify-content: center;">
                                    <!-- 印章图片展示区域 -->
                                    <div style="margin-bottom: 20px; text-align: center;">
                                        <img id="stampImage" alt="系统印章"
                                             style="width: 200px; height: 200px; object-fit: contain; cursor: pointer;"
                                             class="layui-upload-img layui-anim" lay-on="img-preview">
                                    </div>
                                    <!-- 上传按钮 -->
                                    <div style="text-align: center;">
                                        <button type="button" class="layui-btn" id="uploadStampBtn">重新上传印章</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧列 -->
                    <div class="layui-col-md6">
                        <div class="layui-panel equal-height-panel">
                            <div class="layui-card-header">
                                <div class="layui-row" style="padding-top:10px;">
                                    <div class="layui-col-md11 layui-col-sm10">配置区块</div>
                                </div>
                            </div>
                            <div class="layui-padding-3" style="padding-top: 0 !important;">
                                <div class="layui-badge layui-bg-gray" style="margin: 30px 0;padding: 10px 20px;">
                                    <i class="layui-icon layui-icon-tips" style="font-size: 12px; margin-right: 5px;"></i>
                                    系统提示：此区域预留用于后期功能扩展
                                </div>
                                <!-- 预留表单区域 -->
                                <div style="min-height: 200px;"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <br>
                <br>
            </div>
        </div>
    </div>


    <script>
        layui.use(['element', 'layer', 'util'], function () {
            var element = layui.element;
            var layer = layui.layer;
            var util = layui.util;
            var treeTable = layui.treeTable;
            var dropdown = layui.dropdown;
            var form = layui.form;
            var $ = layui.$;
            // 页面顶部插入缓存的用户数据
            let local_userinfo = localStorage.getItem('local_userinfo');
            let user_name = "";
            if (local_userinfo) {
                local_userinfo = JSON.parse(local_userinfo);
                user_name = local_userinfo.Name + " ( " + local_userinfo.Roles + " )";
            } else {
                user_name = "未登录";
            }
            $('.user_name').text(user_name);
            render_menu($)
            $('.refresh_cache').click(function () {
                refresh_cache($);
            });

            // 设置印章图片src，添加时间戳防止缓存
            $('#stampImage').attr('src', '/static/uploads/sys/stamp.png?v=' + new Date().getTime());

            //读取配置
            $.ajax({
                url: '/admin/sys_config/detail',
                type: 'POST',
                success: function (res) {
                    if (res.code == 200) {
                        // nas 配置
                        let nas_data = res.data[1];
                        if (nas_data) {
                            let data_arr = nas_data.value_data.split(',');
                            form.val('form_save_nas_config', {
                                "uploadUrl": data_arr[0],
                                "username": data_arr[1],
                                "password": data_arr[2]
                            });
                        }
                        // 自动音视频录制
                        let rtc_auto_record = res.data[2].value_data;
                        // 上传云端配置
                        let rtc_upload_cloud = res.data[3].value_data;
                        form.val('form_save_rtc_auto_record', {
                            "isOpen": rtc_auto_record,
                            "isUploadCloud": rtc_upload_cloud
                        });
                    } else {
                        layer.msg('获取配置失败：' + res.msg);
                    }
                },
                error: function () {
                    layer.msg('网络错误');
                }
            });
            // 监听提交
            form.on('submit(saveConfig)', function (data) {
                // 获取表单所在的对象
                var formObj = $(data.form);

                // 判断是哪个表单提交的
                if (formObj.attr('lay-filter') === 'form_save_nas_config') {
                    layer.load();
                    $.ajax({
                        url: '/admin/Nas_config/edit',
                        type: 'POST',
                        data: data.field,
                        success: function (res) {
                            layer.closeAll('loading');
                            layer.msg(res.msg);
                        },
                        error: function () {
                            layer.closeAll('loading');
                            layer.msg('网络错误');
                        }
                    });
                } else if (formObj.attr('lay-filter') === 'form_save_rtc_auto_record') {
                    layer.load();
                    $.ajax({
                        url: '/admin/sys_config/sys_config_rtc_setting_edit',
                        type: 'POST',
                        data: data.field,
                        success: function(res) {
                            layer.closeAll('loading');
                            if (res.code == 200) {
                                layer.msg('保存成功');
                            } else {
                                layer.msg('保存失败：' + res.msg);
                            }
                        },
                        error: function() {
                            layer.closeAll('loading');
                            layer.msg('网络错误');
                        }
                    });
                }
                return false; // 阻止表单跳转
            });

            $('#testConfig').click(function () {
                layer.msg('connecting...')
            });

            // 自适应面板高度
            function equalizeHeight() {
                $('.equal-height-row').each(function() {
                    var maxHeight = 0;
                    $(this).find('.layui-panel').css('height', 'auto');

                    $(this).find('.layui-panel').each(function() {
                        var height = $(this).outerHeight();
                        maxHeight = Math.max(maxHeight, height);
                    });

                    $(this).find('.layui-panel').css('height', maxHeight + 'px');
                });
            }

            // 页面加载和窗口大小改变时调整高度
            $(window).on('load resize', function() {
                equalizeHeight();
            });

            // 内容变化时也调整高度
            setTimeout(equalizeHeight, 300);

            // 印章图片点击预览
            $('#stampImage').on('click', function() {
                layer.photos({
                    photos: {
                        "title": "系统印章",
                        "id": 1,
                        "data": [
                            {
                                "alt": "系统印章",
                                "src": "/static/uploads/sys/stamp.png?v=" + new Date().getTime(),
                            }
                        ]
                    },
                    anim: 5
                });
            });

            // 印章上传处理
            layui.use('upload', function(){
                var upload = layui.upload;

                // 执行上传
                var uploadInst = upload.render({
                    elem: '#uploadStampBtn',
                    url: '/admin/update_stamp',
                    accept: 'images',
                    acceptMime: 'image/png',
                    exts: 'png',
                    size: 1024, // 限制文件大小，单位 KB
                    before: function(obj) {
                        layer.load(); // 上传loading
                    },
                    done: function(res){
                        layer.closeAll('loading');
                        if(res.code == 200){
                            layer.msg('上传成功');
                            // 更新图片显示（添加时间戳避免缓存）
                            $('#stampImage').attr('src', '/static/uploads/sys/stamp.png?v=' + new Date().getTime());
                        } else {
                            layer.msg('上传失败：' + res.msg);
                        }
                    },
                    error: function(){
                        layer.closeAll('loading');
                        layer.msg('上传出错');
                    }
                });
            });
        });
    </script>
</body>

</html>