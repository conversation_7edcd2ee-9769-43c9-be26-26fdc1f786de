<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 编辑药材</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
</head>

<body>
    <div class="layui-padding-3">
        <form class="layui-form" lay-filter="form">
            <input type="hidden" name="id">
            
            <div class="layui-row layui-col-space15">
                <!-- 第一列 -->
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">药材名称</label>
                        <div class="layui-input-block">
                            <input type="text" name="name" lay-verify="required" placeholder="请输入药材名称" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">药材分类</label>
                        <div class="layui-input-block">
                            <select name="pid" lay-verify="required" lay-filter="pid">
                                <option value="">请选择分类</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">规格</label>
                        <div class="layui-input-block">
                            <input type="text" name="spec" placeholder="请输入规格" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">批次</label>
                        <div class="layui-input-block">
                            <input type="text" name="batch" placeholder="请输入批次" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">批号</label>
                        <div class="layui-input-block">
                            <input type="text" name="code" placeholder="请输入批号" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">单位</label>
                        <div class="layui-input-block">
                            <input type="text" name="unit" placeholder="请输入单位" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">性味</label>
                        <div class="layui-input-block">
                            <input type="text" name="property" placeholder="请输入性味" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">产地</label>
                        <div class="layui-input-block">
                            <input type="text" name="origin" placeholder="请输入产地" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">采收时间</label>
                        <div class="layui-input-block">
                            <input type="text" name="harvestTime" id="harvestTime" placeholder="请选择采收时间" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">效期(天)</label>
                        <div class="layui-input-block">
                            <input type="number" name="validity_days" placeholder="请输入效期天数" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                </div>

                <!-- 第二列 -->
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">储存条件</label>
                        <div class="layui-input-block">
                            <input type="text" name="storage" placeholder="请输入储存条件" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">常用剂量</label>
                        <div class="layui-input-block">
                            <input type="text" name="dosage" placeholder="请输入常用剂量" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">用法</label>
                        <div class="layui-input-block">
                            <input type="text" name="directions" placeholder="请输入用法" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">药效</label>
                        <div class="layui-input-block">
                            <input type="text" name="effect" placeholder="请输入药效" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">适应症</label>
                        <div class="layui-input-block">
                            <input type="text" name="indication" placeholder="请输入适应症" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">禁忌症</label>
                        <div class="layui-input-block">
                            <input type="text" name="contraindication" placeholder="请输入禁忌症" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">副作用</label>
                        <div class="layui-input-block">
                            <input type="text" name="sideeffect" placeholder="请输入副作用" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">相互作用</label>
                        <div class="layui-input-block">
                            <input type="text" name="interaction" placeholder="请输入药物相互作用" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">参考价格</label>
                        <div class="layui-input-block">
                            <input type="number" name="price" placeholder="请输入参考价格" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                </div>
            </div>

            <!-- 底部按钮 -->
            <div class="layui-form-item" style="text-align: center; margin-top: 30px;">
                <button class="layui-btn" lay-submit lay-filter="save">保存</button>
                <button type="button" class="layui-btn layui-btn-primary" onclick="closeModalWindow()">关闭</button>
            </div>
        </form>
    </div>

    <script>
        layui.use(['form', 'layer', 'laydate'], function () {
            var form = layui.form;
            var layer = layui.layer;
            var laydate = layui.laydate;
            var $ = layui.$;

            // 初始化日期选择器
            laydate.render({
                elem: '#harvestTime',
                type: 'date'
            });

            // 获取URL参数
            var id = request.get('id');
            if (!id) {
                layer.msg('缺少必要参数', { icon: 2 });
                return;
            }

            // 先加载药材详情数据
            var drugData = null;
            $.ajax({
                url: serverUrl + '/admin/drug/detail',
                type: 'POST',
                data: { id: id },
                async: false,
                success: function (res) {
                    if (res.code === 200) {
                        drugData = res.data;
                    } else {
                        layer.msg(res.msg || '加载失败', { icon: 2 });
                    }
                },
                error: function (res) {
                    layer.msg(res.responseJSON.msg, { icon: 2, time: 1000 });
                }
            });

            // 再加载分类数据
            $.ajax({
                url: serverUrl + '/admin/drug_category/list',
                type: 'POST',
                success: function(res) {
                    if (res.code === 200) {
                        var select = $('select[name="pid"]');
                        res.data.forEach(function(item) {
                            select.append(new Option(item.Name, item.ID));
                        });
                        form.render('select');

                        // 如果有药材数据，设置表单值
                        if (drugData) {
                            var formData = {
                                id: drugData.ID,
                                name: drugData.Name,
                                batch: drugData.Batch,
                                spec: drugData.Spec,
                                code: drugData.Code,
                                unit: drugData.Unit,
                                property: drugData.Property,
                                origin: drugData.Origin,
                                harvestTime: drugData.HarvestTime.split('T')[0],
                                validity_days: drugData.Validity_Days,
                                storage: drugData.Storage,
                                dosage: drugData.Dosage,
                                directions: drugData.Directions,
                                effect: drugData.Effect,
                                indication: drugData.Indication,
                                contraindication: drugData.Contraindication,
                                sideeffect: drugData.Sideeffect,
                                interaction: drugData.Interaction,
                                price: drugData.Price,
                                pid: drugData.Pid
                            };
                            // 为表单赋值
                            form.val('form', formData);
                        }
                    }
                }
            });

            //监听提交
            form.on('submit(save)', function (data) {
                layer.load(2);
                $.ajax({
                    url: serverUrl + '/admin/drug/edit',
                    type: 'POST',
                    data: data.field,
                    success: function (res) {
                        layer.closeAll('loading');
                        if (res.code === 200) {
                            layer.msg('保存成功', { icon: 1, time: 1000 }, function () {
                                // 刷新父页面表格
                                parent.layui.table.reload('mytable');
                                // 关闭当前弹窗
                                var index = parent.layer.getFrameIndex(window.name);
                                parent.layer.close(index);
                            });
                        } else {
                            layer.msg(res.msg || '保存失败', { icon: 2 });
                        }
                    },
                    error: function (res) {
                        layer.closeAll('loading');
                        layer.msg(res.responseJSON.msg, { icon: 2, time: 1000 });
                    }
                });
                return false;
            });
        });

        function closeModalWindow() {
            var index = parent.layer.getFrameIndex(window.name);
            parent.layer.close(index);
        }
    </script>
</body>

</html> 