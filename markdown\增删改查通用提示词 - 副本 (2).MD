# 增删改查代码生成任务

## 1. 任务背景
我需要为一个基于Golang后端和LayUI前端的系统生成标准化的增删改查功能模块。这个模块包括前端页面和后端接口的完整实现。

## 2. 参考文件
### 2.1 后端参考
- 业务逻辑参考文件：`internal/app/admin/normal_template.go`中的Article_category相关函数
- 路由文件：`internal/routes/admin.go`
- 目标文件：`internal/app/admin/admin_extra_2.go`

### 2.2 前端参考
目录：`interface/admin/`
参考文件：
- 列表页：article_category_list.html
- 新增页：article_category_add.html
- 编辑页：article_category_edit.html
- 详情页：article_category_detail.html

## 3. 数据表结构信息
执行代码生成任务前，请等待接收以下信息：

### 3.1 表名规则
- 默认使用模块名作为表名（除非特别说明）
- 例如：模块名为book，则表名默认为book

### 3.2 字段信息
用户将提供类似如下格式的字段信息：
```
字段名      数据类型    字段说明
id          int         ID
title       varchar     标题
create_time timestamp   创建时间
```

### 3.3 字段用途
提供的字段信息将用于：
1. 后端函数实现：
   - 数据库查询字段
   - 参数验证
   - 返回数据结构
2. 前端页面实现：
   - 列表页显示字段
   - 表单字段验证
   - 详情页展示
   - 编辑页表单

### 3.4 搜索功能配置
用户将指定：
1. 搜索关键词参数名：默认使用'key'
2. 需要进行模糊匹配的字段：
   - 可能是单个字段（如：name）
   - 可能是多个字段（如：name, description）
   - 示例SQL片段：
     ```sql
     -- 单字段搜索
     WHERE name LIKE ?
     
     -- 多字段搜索
     WHERE name LIKE ? OR description LIKE ?
     ```

使用说明：
1. 后端实现：
   - 接收前端传递的key参数
   - 根据指定字段构建WHERE条件
   - 使用LIKE进行模糊匹配

2. 前端实现：
   - 在列表页面顶部添加搜索框
   - 搜索框的placeholder提示文本应体现可搜索的内容
   - 实现即时搜索或搜索按钮功能

## 4. 开发规范
### 4.1 命名规范
假设模块名为 {module}，则：
- 后端函数命名：
  - 列表：{Module}_list()
  - 新增：{Module}_add()
  - 修改：{Module}_edit()
  - 删除：{Module}_del()
  - 详情：{Module}_detail()

- API路由命名：
  - 列表：/admin/{module}/list
  - 新增：/admin/{module}/add
  - 修改：/admin/{module}/edit
  - 删除：/admin/{module}/del
  - 详情：/admin/{module}/detail

- 前端页面命名：
  - 列表：{module}_list.html
  - 新增：{module}_add.html
  - 修改：{module}_edit.html
  - 详情：{module}_detail.html

### 4.2 代码实现步骤
1. 分析数据表结构
2. 参考Article_category模块实现对应的后端函数
3. 在路由文件中添加新模块的路由配置
4. 基于LayUI实现前端页面

### 4.3 注意事项
1. 确保API返回的字段首字母大写
2. 实现基本的参数验证和错误处理
3. 保持与参考代码一致的错误处理方式
4. 遵循LayUI的UI设计规范
5. 实现必要的数据验证和提示信息

### 4.4 前端开发规范补充
1. URL参数获取：
   - 必须使用系统封装的request对象获取URL参数
   - 正确示例：`var id = request.get('id');`
   - 错误示例：~~`var id = getQueryString('id');`~~

2. 表单字段命名规则：
   - 所有表单字段的name属性必须与后端API返回的字段名完全匹配（包括大小写）
   - 示例：如果API返回字段为"Title"，则表单字段应写为：
     ```html
     <input type="text" name="Title" class="layui-input">
     ```
   - 注意：这是因为后端API返回的JSON字段均采用首字母大写的命名方式

### 4.5 父级数据关联处理
在涉及父级关联的场景（如分类的父级分类）：
1. 系统会自动识别名为"Pid"的字段为父级ID字段
2. 在add/edit页面中，将自动从对应的list接口获取父级数据
3. 示例：如果模块为article_category，系统将自动：
   - 从"/admin/article_category/list"获取父级数据
   - 渲染为下拉选择框
   - 在编辑时自动选中当前记录的父级

### 4.6 图片上传处理规范
当模块包含图片上传功能时：

1. 配置信息：
   - 统一上传接口："/admin/upload_normal_pic"
   - 图片存储基础路径："/static/uploads/"
   - 图片访问基础URL："/static/uploads/"

2. 实现要求：
   - 在add/edit页面实现图片上传功能
   - 上传时指定category参数，用于确定存储目录
   - 图片字段在detail/edit页面中需要正确显示图片预览

3. 代码实现示例：
   ```javascript
   // 上传配置
   upload.render({
       elem: '#uploadBtn',
       url: '/admin/upload_normal_pic',
       data: {
           category: 'your_category_name', // 指定存储目录
       },
       done: function(res) {
           if (res.code == 0) {
               $('#previewImg').attr('src', res.data.src);
               $('input[name="CoverPic"]').val(res.data.src);
           }
       }
   });
   ```

4. 图片路径处理：
   - 存储路径格式："/static/uploads/{category}/{filename}"
   - 显示路径格式："/static/uploads/{category}/{filename}"
   - 在detail和edit页面显示时，直接使用完整路径

5. 注意事项：
   - 确保category参数与业务模块相对应
   - 实现图片预览功能
   - 处理图片上传成功/失败的回调
   - 在表单提交时包含图片路径

## 5. 输出要求
在接收到具体的表结构和搜索配置信息后，请按照以下步骤处理：
1. 分析表结构和搜索需求，确认：
   - 必填字段
   - 需要验证的字段
   - 特殊处理的字段（如时间格式）
   - 搜索匹配字段
2. 根据字段特性和搜索需求生成相应的：
   - 后端验证和搜索逻辑
   - 前端表单验证
   - 数据展示格式
   - 搜索框实现
3. 按顺序提供完整代码实现：
   - 后端函数实现（admin_extra_2.go）
   - 路由配置（admin.go）
   - 前端页面实现（4个html文件）

每个代码块都需要包含必要的注释说明。
在修改前后端时，若发现前端页面已经存在，或者后端函数已经存在等冲突等问题时，请暂停操作，向我发出询问，等待确认。