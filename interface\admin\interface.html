<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 权限管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
</head>

<body>
    <div class="layui-layout layui-layout-admin">
        <!-- 头部 -->
        <div class="layui-header">
            <div class="layui-logo">幸年堂</div>
            <ul class="layui-nav layui-layout-right">
                <li class="layui-nav-item" style="background-color:transparent!important;padding-right:10px">
                    <a href="javascript:;" style="display:flex;align-items:center;color:#fff"><img
                            src="/dist/images/user.png" class="layui-nav-img"><span class="user_name"></span></a>
                    <dl class="layui-nav-child pop_menu">
                        <dd><i class="iconfont">&#xe62f;</i><a href="change_password.html#7">用户资料</a></dd>
                        <dd><i class="iconfont">&#xe601;</i><a href="javascript:void(0)" class="refresh_cache">刷新缓存</a>
                        </dd>
                        <dd><i class="iconfont">&#xe634;</i><a href="login_out.html">退出登录</a></dd>
                    </dl>
                </li>
            </ul>
        </div>
        <!-- 导航 -->
        <ul class="layui-nav layui-nav-tree layui-nav-side" id="menuContainer" lay-bar="disabled" lay-accordion>
            <div style="margin: 50px 0;color: #bbb;text-align: center;">菜单加载中...</div>
        </ul>
        <!-- 主体内容 -->
        <div class="layui-body">
            <div class="body_child">
                <div class="layui-panel">
                    <div class="layui-card-header">
                        <div class="layui-row" style="padding-top:10px;">
                            <div class="layui-col-md11 layui-col-sm10">权限 & 资源管理</div>
                            <div class="layui-col-md1 layui-col-sm2">
                                <button type="submit" class="layui-btn layui-btn-primary layui-border-red create_btn"
                                    lay-submit="">
                                    <i class="layui-icon">&#xe654;</i> 新增
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="layui-padding-3" style="min-height: 800px;">
                        <table id="treeTable" lay-filter="treeTable" class="layui-table" lay-skin="row"></table>
                    </div>
                </div>
                <br>
                <br>
            </div>
        </div>
    </div>

    <script type="text/html" id="TPL-treeTable-demo-tools">
          <a class="layui-btn layui-btn-xs" lay-event="detail">编辑</a>
          <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="delete">删除</a>
    </script>

    <script type="text/html" id="sort-toolbar">
        <div class="mysort">
            <div>{{ d.sort }}</div>
            <div>
                <i class="layui-icon layui-icon-down layui-btn" lay-event="sort-down"></i>
                <i class="layui-icon layui-icon-up layui-btn" lay-event="sort-up"></i>
            </div>
        </div>
    </script>


    <script>
        var dist_arr = ['菜单', '按钮', '接口'];
        var remake_sort;
        layui.use(['element', 'layer', 'util'], function () {
            var element = layui.element;
            var layer = layui.layer;
            var util = layui.util;
            var treeTable = layui.treeTable;
            var dropdown = layui.dropdown;
            var $ = layui.$;
            // 页面顶部插入缓存的用户数据
            let local_userinfo = localStorage.getItem('local_userinfo');
            let user_name = "";
            if (local_userinfo) {
                local_userinfo = JSON.parse(local_userinfo);
                user_name = local_userinfo.Name + " ( " + local_userinfo.Roles + " )";
            } else {
                user_name = "未登录";
            }
            $('.user_name').text(user_name);
            render_menu($);
            $('.refresh_cache').click(function () {
                refresh_cache($);
            });
            //获取菜单信息
            layer.load(2);
            $.ajax({
                url: '/admin/res/current',
                type: 'post',
                dataType: 'json',
                data: { disable_dep_data: true },//没必要请求部门数据了，本地缓存中已存在
                success: function (res) {
                    layer.closeAll();
                    let data = res.data;
                    let formattedData = format_to_treedata_role_res(data);
                    let inst = treeTable.render({
                        elem: '#treeTable',
                        tree: {
                            view: {
                                showIcon: false,
                                expandAllDefault: true,
                            }
                        },
                        data: formattedData,
                        cols: [[ // 表头
                            { field: 'id', title: 'ID', width: 90, align: 'center' },
                            {
                                field: 'name', title: '名称', minWidth: 150, templet: function (d) {
                                    return d.type == 0 ? "<font color='#1677ff'>" + d.name + "</font>" : d.name;
                                }
                            },
                            {
                                field: 'menu_icon', title: '图标', width: 90, align: 'center', templet: function (d) {
                                    return d.menu_icon == "0" ? "/" : "<i class='iconfont'>" + d.menu_icon + "</i>";
                                }
                            },
                            {
                                field: 'url', title: '链接', minWidth: 150, templet: function (d) {
                                    return d.url == "0" ? "/" : "<a href='" + d.url + "'>" + d.url + "</a>";
                                }
                            },
                            {
                                field: 'api_path', title: 'API权限标识', minWidth: 150, templet: function (d) {
                                    return d.api_path == "0" ? "/" : d.api_path;
                                }
                            },
                            { field: 'built_in', title: '内置', width: 90, align: 'center', templet: function (d) { return d.built_in ? '是' : '否'; } },
                            {
                                field: 'sort',
                                title: '排序优先级',
                                align: 'center',
                                width: 150,
                                toolbar: '#sort-toolbar' // 使用 toolbar 模板
                            }
                            ,
                            {
                                field: 'type', title: '类型', align: 'center', width: 80, templet: function (d) {
                                    return d.type == 0 ? "<font color='#1677ff'>" + dist_arr[d.type] + "</font>" : dist_arr[d.type];
                                }
                            },
                            {
                                field: 'status', title: '状态', align: 'center', width: 80, templet: function (d) {
                                    return d.status ? '可用' : '不可用';
                                }
                            },
                            { fixed: "right", title: "操作", width: 200, align: "center", toolbar: "#TPL-treeTable-demo-tools" }
                        ]],
                        id: 'treeTable',
                    });
                    // treeTable.expandAll('treeTable', false);
                    // 单元格工具事件
                    treeTable.on('tool(' + inst.config.id + ')', function (obj) {
                        var layEvent = obj.event; // 获得 lay-event 对应的值
                        var trElem = obj.tr;
                        var trData = obj.data;
                        var tableId = obj.config.id;
                        let click_id = obj.data.id;
                        let click_pid = obj.data.pid;
                        if (layEvent === "detail") {
                            // 弹出IFRAME窗口模态框
                            layer.open({
                                type: 2,
                                title: '编辑权限/资源',
                                area: ['750px', '720px'],
                                shadeClose: true,
                                content: 'res_edit.html?id=' + trData.id
                            });
                        } else if (layEvent === "delete") {
                            layer.confirm("确定要删除该资源吗？", function (index) {
                                layer.close(index);
                                layer.load(2);
                                $.ajax({
                                    url: '/admin/perm/del',
                                    type: 'post',
                                    dataType: 'json',
                                    data: { id: click_id },
                                    success: function (res) {
                                        layer.closeAll();
                                        obj.del();
                                        treeTable.removeNode(tableId, trElem.attr('data-index'))
                                        layer.msg(res.msg);
                                    },
                                    error: function (data) {
                                        layer.closeAll();
                                        layer.msg(data.responseJSON.msg);
                                    }
                                });
                            });
                        } else if (layEvent === "sort-up") {
                            remake_sort(click_id, click_pid, 1).then(function (change_success) {
                                console.log(change_success);
                                if (change_success) {
                                    window.location.reload();
                                }
                            });
                        } else if (layEvent === "sort-down") {
                            remake_sort(click_id, click_pid, -1).then(function (change_success) {
                                console.log(change_success);
                                if (change_success) {
                                    window.location.reload();
                                }
                            });
                        }
                    });
                    window.treeTableInstance = inst;
                },

                error: function (data) {
                    layer.closeAll();
                    layer.msg(data.responseJSON.msg);
                }
            });

            remake_sort = function (id, pid, action) {
                return new Promise(function (resolve, reject) {
                    layer.load(2);
                    $.ajax({
                        url: '/admin/perm/change_perm_sort',
                        type: 'post',
                        dataType: 'json',
                        data: { id: id, pid, pid, action: action },
                        success: function (res) {
                            layer.closeAll();
                            if (res.code == 200) {
                                layer.msg(res.msg);
                                resolve(true);
                            } else {
                                layer.msg(res.msg);
                                resolve(false);
                            }
                        },
                        error: function (data) {
                            layer.closeAll();
                            layer.msg(data.responseJSON.msg);
                            resolve(false);
                        }
                    });
                });
            }
            //新增资源
            $('.create_btn').on('click', function () {
                layer.open({
                    type: 2,
                    title: '新增权限/资源',
                    area: ['750px', '660px'],
                    shadeClose: true,
                    content: 'res_add.html'
                });
            });
        });
    </script>
</body>

</html>