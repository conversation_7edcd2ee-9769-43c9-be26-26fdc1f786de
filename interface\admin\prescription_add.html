<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 开具处方</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
    <style>
        h3 {
            margin: 30px 0 20px 0;
            border-left: 3px solid #666;
            padding-left: 15px;
        }

        .image-grid {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .image-grid img {
            width: 200px;
            height: 200px;
            object-fit: cover;
            cursor: pointer;
            border-radius: 4px;
            border: 1px solid #ddd;
        }

        .info-item {
            margin-bottom: 10px;
        }

        .info-label {
            font-weight: bold;
            display: inline-block;
            min-width: 100px;
        }

        #finished_drug_mode_area {
            border: 1px dashed black;
            padding: 20px;
            width: 95%;
            margin: 30px auto 0 auto;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1), 0 6px 20px rgba(0, 0, 0, 0.19);
        }

        #original_prescription_info {
            display: none;
        }
    </style>
</head>

<body>
    <div class="layui-layout layui-layout-admin">
        <!-- 头部 -->
        <div class="layui-header">
            <div class="layui-logo">幸年堂</div>
            <ul class="layui-nav layui-layout-right">
                <li class="layui-nav-item" style="background-color:transparent!important;padding-right:10px">
                    <a href="javascript:;" style="display:flex;align-items:center;color:#fff"><img
                            src="/dist/images/user.png" class="layui-nav-img"><span class="user_name"></span></a>
                    <dl class="layui-nav-child pop_menu">
                        <dd><i class="iconfont">&#xe62f;</i><a href="change_password.html#7">用户资料</a></dd>
                        <dd><i class="iconfont">&#xe601;</i><a href="javascript:void(0)" class="refresh_cache">刷新缓存</a>
                        </dd>
                        <dd><i class="iconfont">&#xe634;</i><a href="login_out.html">退出登录</a></dd>
                    </dl>
                </li>
            </ul>
        </div>
        <!-- 导航 -->
        <ul class="layui-nav layui-nav-tree layui-nav-side" id="menuContainer" lay-bar="disabled" lay-accordion>
            <div style="margin: 50px 0;color: #bbb;text-align: center;">菜单加载中...</div>
        </ul>
        <!-- 主体内容 -->
        <div class="layui-body">
            <div class="body_child">
                <div class="layui-panel">
                    <div class="layui-card-header">
                        <div class="layui-row" style="padding-top:10px;">
                            <div class="layui-col-md11 layui-col-sm10">开具处方</div>
                        </div>
                    </div>
                    <div class="layui-padding-3" style="min-height: 600px;">
                        <!-- 症状描述、诊断信息表单 -->
                        <form class="layui-form" lay-filter="form_add" onsubmit="return false">


                            <h3>患者基础信息</h3>
                            <!-- 填充患者基础信息，接口/admin/patient_profile/detail -->
                            <div class="layui-form">
                                <div id="patient_data" class="layui-row">
                                    <i
                                        class='layui-icon layui-icon-loading-1 layui-anim layui-anim-rotate layui-anim-loop'></i>
                                </div>
                            </div>

                            <h3>病历信息</h3>
                            <div class="layui-form">
                                <div id="record_data" class="layui-row">
                                    <i
                                        class='layui-icon layui-icon-loading-1 layui-anim layui-anim-rotate layui-anim-loop'></i>
                                </div>
                            </div>

                            <!-- 舌苔照显示部分 -->
                            <div class="layui-form-item">
                                <label class="layui-form-label">舌苔照</label>
                                <div class="layui-input-block">
                                    <div class="image-grid" id="photo_tongue_container"></div>
                                </div>
                            </div>

                            <!-- 检查单显示部分 -->
                            <div class="layui-form-item">
                                <label class="layui-form-label">检查单</label>
                                <div class="layui-input-block">
                                    <div class="image-grid" id="photo_sheet_container"></div>
                                </div>
                            </div>

                            <h3>诊断信息及医嘱</h3>
                            <div class="layui-form-item">
                                <div class="layui-row">
                                    <!-- 诊断信息 -->
                                    <div class="layui-col-md6 layui-col-sm12">
                                        <label class="layui-form-label">诊断信息</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="diagnosis" placeholder="请输入诊断信息"
                                                class="layui-input" autocomplete="off">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="layui-form-item">
                                <!-- 治疗计划 -->
                                <div class="layui-col-md6 layui-col-sm12">
                                    <label class="layui-form-label">治疗计划</label>
                                    <div class="layui-input-block">
                                        <input type="text" name="tx_plan" placeholder="请输入治疗计划" class="layui-input"
                                            autocomplete="off">
                                    </div>
                                </div>
                            </div>

                            <div class="layui-form-item">
                                <!-- 医嘱 -->
                                <div class="layui-col-md6 layui-col-sm12">
                                    <label class="layui-form-label">医嘱</label>
                                    <div class="layui-input-block">
                                        <input type="text" name="askfor" placeholder="请输入医嘱" class="layui-input"
                                            autocomplete="off">
                                    </div>
                                </div>
                            </div>



                            <h3>来自原型图字段</h3>
                            <!-- 第一行：用药信息 -->
                            <div class="layui-form-item">
                                <div class="layui-inline">
                                    <label class="layui-form-label">共：</label>
                                    <div class="layui-input-inline">
                                        <input type="text" name="tx_day" lay-verify="required" class="layui-input" oninput="if(value>100)value=100">
                                    </div>
                                    <div class="layui-form-mid" style="margin-left: 10px;">天</div>
                                </div>
                                <div class="layui-inline">
                                    <label class="layui-form-label">药品类型：</label>
                                    <div class="layui-input-inline" style="width: 120px;">
                                        <select name="tx_type" lay-verify="required">
                                            <option value=""></option>
                                            <option value="膏滋">膏滋</option>
                                            <option value="丸剂">丸剂</option>
                                            <option value="汤剂">汤剂</option>
                                            <option value="散剂">散剂</option>
                                            <option value="无糖膏">无糖膏</option>
                                            <option value="水丸">水丸</option>
                                            <option value="内服粉剂">内服粉剂</option>
                                            <option value="外用粉剂">外用粉剂</option>
                                            <option value="中药材">中药材</option>
                                            <option value="清膏">清膏</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="layui-inline" style="margin-left: 30px;">
                                    <label class="layui-form-label">共：</label>
                                    <div class="layui-input-inline" style="width: 120px;">
                                        <input type="text" name="totalDoses" class="layui-input" autocomplete="off" lay-verify="required">
                                    </div>
                                    <div class="layui-form-mid">剂</div>
                                </div>
                            </div>

                            <!-- 第二行：使用方式和禁忌 -->
                            <div class="layui-form-item">

                                <div class="layui-inline">
                                    <label class="layui-form-label">每</label>
                                    <div class="layui-input-inline" style="width: 50px;">
                                        <input type="text" name="dosage_1" class="layui-input" autocomplete="off" lay-verify="required" value="1">
                                    </div>
                                    <div class="layui-form-mid">天</div>
                                    <div class="layui-input-inline" style="width: 50px;">
                                        <input type="text" name="dosage_2" class="layui-input" autocomplete="off" lay-verify="required" value="2">
                                    </div>
                                    <div class="layui-form-mid">次</div>
                                </div>

                                <div class="layui-inline">
                                    <label class="layui-form-label">每次</label>
                                    <div class="layui-input-inline" style="width: 50px;">
                                        <input type="text" name="dosage_3" class="layui-input" autocomplete="off" lay-verify="required">
                                    </div>
                                    <div class="layui-input-inline" style="width: 130px;">
                                        <input type="text" name="dosage_4" class="layui-input" placeholder="请输入单位"
                                            autocomplete="off" lay-verify="required">
                                    </div>
                                </div>


                                <div class="layui-inline">
                                    <label class="layui-form-label">禁忌：</label>
                                    <div class="layui-input-inline" style="width: 300px;">
                                        <input type="text" name="dosage_5" class="layui-input" autocomplete="off" lay-verify="required" value="绿豆、白萝卜">
                                    </div>
                                </div>
                            </div>

                            <!-- 第三行：添加按钮 -->
                            <div style="display: flex;justify-content: space-between;align-items: center;">
                                <h3>处方药材信息</h3>
                                <div class="layui-col-md6" style="text-align: right;">
                                    <!-- 添加模式切换 -->
                                    <div class="layui-btn-group" style="margin-right: 15px;">
                                        <button type="button" class="layui-btn layui-btn-sm layui-btn-normal"
                                            id="drug_mode_btn">
                                            <i class="iconfont" style="font-size: 12px;">&#xe644;</i> 药材模式
                                        </button>
                                        <button type="button" class="layui-btn layui-btn-sm layui-btn-primary"
                                            id="finished_mode_btn">
                                            <i class="iconfont" style="font-size: 12px;">&#xe739;</i> 成品药模式
                                        </button>
                                    </div>
                                    <!-- 分别对应两种模式的按钮 -->
                                    <button class="layui-btn layui-btn-primary layui-border-green" id="add_meds_btn">
                                        <i class="iconfont">&#xe644;</i> 选择中药材
                                    </button>
                                    <button class="layui-btn layui-btn-primary layui-border-orange"
                                        id="select_finished_btn" style="display: none;">
                                        <i class="iconfont">&#xe739;</i> 选择成品药
                                    </button>
                                </div>
                            </div>

                            <!-- 药材模式区域 -->
                            <div id="drug_mode_area">
                                <!-- 第四行：处方药材列表 -->
                                <table class="layui-table">
                                    <thead>
                                        <tr>
                                            <th>库存ID</th>
                                            <th>药材ID</th>
                                            <th>药材名称</th>
                                            <th>单价</th>
                                            <th>库存量</th>
                                            <th>数量</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="prescription_drug_table_body">
                                        <!-- 表格内容将在这里动态插入 -->
                                    </tbody>
                                </table>
                            </div>

                            <!-- 成品药模式区域 -->
                            <div id="finished_drug_mode_area" style="display: none;">
                                <div class="layui-card">
                                    <div class="layui-card-body">
                                        <div id="finished_drug_info">
                                            <div style="display: flex; justify-content: flex-end; align-items: center;">
                                                <i class="layui-icon layui-icon-tips" style="margin-right: 5px;"></i>
                                                <span>请点击上方"选择成品药"按钮选择成品药</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 选择成品药后显示原处方信息 -->
                                <div id="original_prescription_info">
                                    <h3>库存信息</h3>
                                    <div class="layui-form">
                                        <div id="warehouse_finisheddrug" class="layui-row">
                                            <!-- 成品药库存信息将在这里动态插入 -->
                                        </div>
                                    </div>
                                    <h3>原处方病历详情</h3>
                                    <div class="layui-form">
                                        <div id="old_record_data" class="layui-row">
                                            <!-- 原病历信息将在这里动态插入 -->
                                        </div>
                                    </div>

                                    <h3>原处方药材信息</h3>
                                    <div class="layui-form">
                                        <table class="layui-table">
                                            <thead>
                                                <tr>
                                                    <th>库存ID</th>
                                                    <th>药品ID</th>
                                                    <th>药品名称</th>
                                                    <th>数量</th>
                                                </tr>
                                            </thead>
                                            <tbody id="original_prescription_drug_table_body">
                                                <!-- 原药材信息将在这里动态插入 -->
                                            </tbody>
                                        </table>
                                    </div>
                                </div>


                            </div>

                            <!-- 提交按钮 -->
                            <div class="layui-row" style="display: flex;justify-content: center;margin-top: 100px;">
                                <button class="layui-btn" lay-submit lay-filter="formSubmitBtn">确认新建</button>
                                <button type="reset" class="layui-btn layui-btn-primary" onclick="history.go(-1)">取消</button>
                            </div>

                        </form>
                    </div>
                </div>
                <br>
                <br>
            </div>
        </div>
    </div>


    <script>
        layui.use(['element', 'layer', 'util'], function () {
            var element = layui.element;
            var layer = layui.layer;
            var util = layui.util;
            var treeTable = layui.treeTable;
            var dropdown = layui.dropdown;
            var form = layui.form;
            var $ = layui.$;

            // 初始化处方模式变量和成品药ID
            var prescriptionMode = 'drug'; // 默认为药材模式，可选值：'drug' 或 'finished'
            window.finishedDrugId = 0; // 初始化成品药ID为0
            window.old_pre_id = 0; // 初始化原处方ID为0

            // 模式切换按钮事件
            $('#drug_mode_btn').on('click', function () {
                switchMode('drug');
            });

            $('#finished_mode_btn').on('click', function () {
                switchMode('finished');
            });

            // 模式切换函数
            function switchMode(mode) {
                prescriptionMode = mode;
                if (mode === 'drug') {
                    // 切换到药材模式
                    $('#drug_mode_btn').addClass('layui-btn-normal').removeClass('layui-btn-primary');
                    $('#finished_mode_btn').removeClass('layui-btn-normal').addClass('layui-btn-primary');
                    $('#add_meds_btn').show();
                    $('#select_finished_btn').hide();
                    $('#drug_mode_area').show();
                    $('#finished_drug_mode_area').hide();
                } else {
                    // 切换到成品药模式
                    $('#finished_mode_btn').addClass('layui-btn-normal').removeClass('layui-btn-primary');
                    $('#drug_mode_btn').removeClass('layui-btn-normal').addClass('layui-btn-primary');
                    $('#add_meds_btn').hide();
                    $('#select_finished_btn').show();
                    $('#drug_mode_area').hide();
                    $('#finished_drug_mode_area').show();
                }
            }

            // 选择成品药按钮点击事件
            $('#select_finished_btn').on('click', function () {
                layer.open({
                    type: 2,
                    title: '选择成品药',
                    area: ['900px', '720px'],
                    shadeClose: true,
                    content: '/admin/warehouse_finisheddrug_choose.html',
                    end: function () {
                        // 弹窗关闭后检查是否选择了成品药
                        if (window.finishedDrugId > 0 && window.old_pre_id > 0) {
                            $('#original_prescription_info').show(300);
                            $('#finished_drug_info').show(300);
                            // 1.将成品药对应的原病历信息填充到原病历信息区域
                            render_record_info(window.finishedDrugId, '#old_record_data');
                            // 2.将成品药对应的原处方药材信息填充到原处方药材信息区域
                            render_drugs_info(window.old_pre_id, '#original_prescription_drug_table_body');
                            // 3.将成品药对应的原处方库存日志填充到原处方库存日志区域
                            render_finished_drug_info(window.finishedDrugId);
                        }
                    }
                });
            });

            // 加载该成品药的库存信息
            function render_finished_drug_info(finished_drug_id) {
                $.ajax({
                    url: '/admin/warehouse_finisheddrug/detail',
                    type: 'POST',
                    data: { id: finished_drug_id },
                    success: function (res) {
                        if (res.code == 200) {
                            let data = res.data;
                            // 检查Kind字段值
                            if (data.Kind !== 1 && data.Kind !== 2) {
                                layer.alert("对不起，该库存信息类型有问题，无法选择该成品。", {
                                    icon: 2,
                                    title: "错误提示",
                                    end: function () {
                                        // 用户点击后刷新页面
                                        window.location.reload();
                                    }
                                });
                                return;
                            }

                            // 重新排版显示内容
                            let html = '';
                            html += `
                                <div class="layui-col-md4">
                                    <div class="info-item">
                                        <span class="info-label">订单ID：</span>
                                        <span class="info-content" style="max-width: 150px; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">${data.Ord_id || '-'}</span>
                                        <span class="layui-form-mid"><a href="/admin/order_show.html?id=${data.Ord_id}#/admin/prescription_list.html" target=_blank class="layui-btn layui-btn-xs" style="margin:0 20px;">查看</a></span>
                                    </div>
                                </div>
                                <div class="layui-col-md4">
                                    <div class="info-item">
                                        <span class="info-label">处方ID：</span>
                                        <span class="info-content" style="max-width: 150px; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">${data.Pre_id || '-'}</span>
                                        <span class="layui-form-mid"><a href="/admin/prescription_show.html?id=${data.Pre_id}#/admin/prescription_list.html" target=_blank class="layui-btn layui-btn-xs" style="margin:0 20px;">查看</a></span>
                                    </div>
                                </div>
                                <div class="layui-col-md4">
                                    <div class="info-item">
                                        <span class="info-label">药品数量：</span>
                                        <span class="info-content">${data.Quantity + " " + Drug_unit[data.Unit]}</span>
                                    </div>
                                </div>
                                <div class="layui-col-md6">
                                    <div class="info-item">
                                        <span class="info-label">备注：</span>
                                        <span class="info-content">${data.Notes || '-'}</span>
                                    </div>
                                </div>
                                <div class="layui-col-md6">
                                    <div class="info-item">
                                        <span class="info-label">入库时间：</span>
                                        <span class="info-content">${data.Create_time ? data.Create_time.replace('T', ' ').replace('Z', '') : '-'}</span>
                                    </div>
                                </div>
                            `;
                            $('#warehouse_finisheddrug').html(html);
                        }
                    }
                });
            }
            // 加载处方药品信息
            function render_drugs_info(prescription_id, push_obj) {
                $.ajax({
                    url: '/admin/prescription/drug',
                    type: 'POST',
                    data: { pre_id: prescription_id },
                    success: function (res) {
                        if (res.code === 200) {
                            var tableBody = $(push_obj);
                            tableBody.empty();

                            res.data.forEach(function (item) {
                                var row = `
                                    <tr>
                                        <td>${item.Wh_drug_id || '-'}</td>
                                        <td>${item.Drug_id || '-'}</td>
                                        <td>${item.Name || '-'}</td>
                                        <td>${item.Quantity || '-'}</td>
                                    </tr>
                                `;
                                tableBody.append(row);
                            });
                        } else {
                            layer.msg(res.msg || '加载药品信息失败', { icon: 2 });
                        }
                    },
                    error: function () {
                        layer.msg('服务器错误', { icon: 2 });
                    }
                });
            }


            // 页面顶部插入缓存的用户数据
            let local_userinfo = localStorage.getItem('local_userinfo');
            let user_name = "";
            if (local_userinfo) {
                local_userinfo = JSON.parse(local_userinfo);
                user_name = local_userinfo.Name + " ( " + local_userinfo.Roles + " )";
            } else {
                user_name = "未登录";
            }
            $('.user_name').text(user_name);
            render_menu($);
            $('.refresh_cache').click(function () {
                refresh_cache($);
            });
            var record_id = request.get('id');
            if (!record_id) {
                layer.msg('病历ID不能为空', { icon: 2 });
                return;
            }
            //根据处方ID求出来的病历ID来填充患者基础信息
            var render_patient_info = function (record_ids) {
                layer.load(2);
                $.ajax({
                    url: serverUrl + "/admin/patient_profile/get_patient_ids_by_record_ids",
                    type: "post",
                    data: { record_ids: record_ids },
                    success: function (res) {
                        layer.closeAll('loading');
                        let data = res.data;
                        let html = '';
                        for (let i = 0; i < data.length; i++) {
                            html += `
                                        <!-- 第1大行 -->
                                        <div class="layui-col-xs3">
                                            <div class="info-item">
                                                <span class="info-label">手机号码：</span>
                                                <span class="info-content">${data[i].Phone || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">患者姓名：</span>
                                                <span class="info-content">${data[i].Name || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">患者性别：</span>
                                                <span class="info-content">${data[i].Sex == 0 ? '女' : '男' || '-'}</span>
                                            </div>
                                        </div>

                                        <div class="layui-col-xs3">
                                            <div class="info-item">
                                                <span class="info-label">关系：</span>
                                                <span class="info-content">${FamilyRelation[data[i].Relation] || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">身高：</span>
                                                <span class="info-content">${data[i].Height || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">体重：</span>
                                                <span class="info-content">${data[i].Weight || '-'}</span>
                                            </div>
                                        </div>

                                        <div class="layui-col-xs3">
                                            <div class="info-item">
                                                <span class="info-label">身份证号：</span>
                                                <span class="info-content">${data[i].Idcard || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">年龄：</span>
                                                <span class="info-content">${date2age(data[i].Born_date)}岁</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">医保卡：</span>
                                                <span class="info-content">${data[i].Ins_card_num || '-'}</span>
                                            </div>
                                        </div>

                                        <div class="layui-col-xs3">
                                            <div class="info-item">
                                                <span class="info-label">患者来源：</span>
                                                <span class="info-content">${Patient_From[data[i].Patient_from] || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">患者等级：</span>
                                                <span class="info-content">${data[i].Level + '级' || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">微信号：</span>
                                                <span class="info-content">${data[i].Weixin || '-'}</span>
                                            </div>
                                        </div>

                                        
                                        <div class="layui-col-xs6">
                                            <div class="info-item">
                                                <span class="info-label">患者备注：</span>
                                                <span class="info-content">${data[i].Customer_notes || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">既往病史：</span>
                                                <span class="info-content">${data[i].Medical_history || '-'}</span>
                                            </div>
                                        </div>
                                        <div class="layui-col-xs6">
                                            <div class="info-item">
                                                <span class="info-label">主诉：</span>
                                                <span class="info-content">${data[i].Chief_complaint || '-'}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">过敏史：</span>
                                                <span class="info-content">${data[i].Allergies || '-'}</span>
                                            </div>
                                        </div>
                                        <div class="layui-col-xs12">
                                            <div class="info-item">
                                                <span class="info-label">详细地址：</span>
                                                <span class="info-content">${removeAllPipe(data[i].Address) || '-'}</span>
                                            </div>
                                        </div>
                            `;
                        }
                        $('#patient_data').html(html);
                    },
                    error: function () {
                        layer.msg('获取患者信息失败', { icon: 2 });
                    }
                });
            }
            render_patient_info(record_id);

            // 根据病历ID获取病历信息
            var render_record_info = function (record_id, push_obj) {
                layer.load(2);
                $.ajax({
                    url: serverUrl + "/admin/patient_records/detail",
                    type: "post",
                    data: { id: record_id },
                    success: function (res) {
                        layer.closeAll('loading');
                        if (res.code == 200) {
                            let data = res.data;
                            let html = '';

                            // 构建病历信息HTML
                            html += `
                                <!-- 第1行：患者现状 -->
                                <div class="layui-col-md4">
                                    <div class="info-item">
                                        <span class="info-label">患者主诉：</span>
                                        <span class="info-content">${data.Chief_complaint || '-'}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">复诊主诉：</span>
                                        <span class="info-content">${data.Re_chief_complaint || '-'}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">现病史：</span>
                                        <span class="info-content">${data.History_of_present_illness || '-'}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">现需治疗：</span>
                                        <span class="info-content">${data.Now_needs || '-'}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">大小便情况：</span>
                                        <span class="info-content">${data.Urination || '-'}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">有无三高：</span>
                                        <span class="info-content">${data.Triad || '-'}</span>
                                    </div>
                                </div>

                                <!-- 第2行：病史记录 -->
                                <div class="layui-col-md4">
                                    <div class="info-item">
                                        <span class="info-label">既往病史：</span>
                                        <span class="info-content">${data.Past_medical_history || '-'}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">个人史：</span>
                                        <span class="info-content">${data.Personal_history || '-'}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">家族史：</span>
                                        <span class="info-content">${data.Family_history || '-'}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">过敏史：</span>
                                        <span class="info-content">${data.Allergy_history || '-'}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">用药史：</span>
                                        <span class="info-content">${data.Past_medication_history || '-'}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">上次用药时间：</span>
                                        <span class="info-content">${data.Last_medication_time ? data.Last_medication_time.replace('T', ' ').replace('Z', '') : '-'}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">上次用药情况：</span>
                                        <span class="info-content">${data.Last_medical || '-'}</span>
                                    </div>
                                </div>

                                <!-- 第3行：诊疗信息 -->
                                <div class="layui-col-md4">
                                    <div class="info-item">
                                        <span class="info-label">诊断信息：</span>
                                        <span class="info-content">${data.Diagnosis_information || '-'}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">治疗方案：</span>
                                        <span class="info-content">${data.Treatment_plan || '-'}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">患者状态：</span>
                                        <span class="info-content">${Record_Status[data.Status] || '-'}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">核销时间：</span>
                                        <span class="info-content">${data.Discharge_time == '0001-01-01T00:00:00Z' ? "未核销" : "已核销"}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">创建时间：</span>
                                        <span class="info-content">${data.Create_time ? data.Create_time.replace('T', ' ').replace('Z', '') : '-'}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">对应处方：</span>
                                        <span class="info-content">${data.Pre_id == 0 ? "未开处方" : data.Pre_id}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">意向剂型：</span>
                                        <span class="info-content">${data.Tx_day || '-'}天，${data.Tx_type || '-'}</span>
                                    </div>
                                </div>
                            `;
                            $(push_obj).html(html);

                            // 显示舌苔照图片
                            if (data.Photo_tongue) {
                                let tongue_photos = data.Photo_tongue.split('\n');
                                let container = $('#photo_tongue_container');
                                container.empty();
                                tongue_photos.forEach(photo => {
                                    if (photo.trim()) {
                                        let pic = '/static/uploads/normal_pics/photo_tongue/' + photo.trim();
                                        container.append(`<img src="${pic}" alt="舌苔照" onclick="viewImage('${pic}')">`);
                                    }
                                });
                            }

                            // 显示检查单图片
                            if (data.Photo_sheet) {
                                let sheet_photos = data.Photo_sheet.split('\n');
                                let container = $('#photo_sheet_container');
                                container.empty();
                                sheet_photos.forEach(photo => {
                                    if (photo.trim()) {
                                        let pic = '/static/uploads/normal_pics/photo_sheet/' + photo.trim();
                                        container.append(`<img src="${pic}" alt="检查单" onclick="viewImage('${pic}')">`);
                                    }
                                });
                            }

                            // 自动填充诊断信息和治疗计划
                            if (data.Diagnosis_information) {
                                $('input[name="diagnosis"]').val(data.Diagnosis_information);
                            }
                            if (data.Treatment_plan) {
                                $('input[name="tx_plan"]').val(data.Treatment_plan);
                            }

                            // 自动填充意向剂型信息
                            if (data.Tx_day) {
                                $('input[name="tx_day"]').val(data.Tx_day);
                                form.render('input');
                            }
                            if (data.Tx_type) {
                                $('select[name="tx_type"]').val(data.Tx_type);
                                form.render('select');
                            }
                        } else {
                            layer.msg(res.msg || '获取病历信息失败', { icon: 2 });
                        }
                    },
                    error: function (res) {
                        layer.closeAll('loading');
                        
                        // 添加模糊效果
                        $('body').append('<div id="blur-overlay" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(255, 255, 255, 0.7); backdrop-filter: blur(5px); z-index: 19891014;"></div>');
                        
                        // 显示确认弹窗
                        layer.confirm(res.responseJSON.msg || '获取病历信息失败', {
                            icon: 2,
                            title: '错误提示',
                            btn: ['返回上一页'], 
                            closeBtn: 0, // 不显示关闭按钮
                            shadeClose: false // 点击遮罩不关闭
                        }, function(index){
                            // 移除模糊效果
                            $('#blur-overlay').remove();
                            // 关闭弹窗
                            layer.close(index);
                            // 返回上一页
                            history.go(-1);
                        });
                    }
                });
            };
            render_record_info(record_id, '#record_data');
            // 添加药材按钮点击事件
            $('#add_meds_btn').on('click', function () {
                layer.open({
                    type: 2,
                    title: '添加处方药材数据',
                    area: ['1200px', '800px'],
                    shadeClose: true,
                    content: '/admin/warehouse_drug_add_toolspage.html'
                });
            });
            // 表单提交时收集药材数据
            var api_url = '/admin/prescription/add';
            form.on('submit(formSubmitBtn)', function (data) {
                return layer.confirm('您确认要新建该处方吗？', {
                    btn: ['确定', '取消'],
                    title: '确认'
                }, function(index) {
                    layer.close(index);
                    let field = data.field;
                    field.record_id = record_id;

                    // 根据不同模式处理提交参数
                    if (prescriptionMode === 'drug') {
                        // 药材模式 - 收集药材数据
                        let drugs = [];
                        $('#prescription_drug_table_body tr').each(function () {
                            let drug = {
                                wh_drug_id: $(this).find('td:eq(0)').text(),  // 库存ID
                                drug_id: $(this).find('td:eq(1)').text(),  // 药材ID
                                single_dose: parseFloat($(this).find('td:eq(5)').text())  // 数量
                            };

                            // 检查库存是否充足
                            let stock = parseFloat($(this).find('td:eq(4)').text());
                            if (stock < 1) {
                                layer.msg('药材库存ID：' + drug.wh_drug_id + ' 库存不足，请检查', { icon: 2 });
                                return false;
                            }

                            drugs.push(drug);
                        });

                        // 检查是否选择了药材
                        if (drugs.length === 0) {
                            layer.msg('请至少选择一个药材', { icon: 2 });
                            return false;
                        }

                        // 设置药材数据
                        field.drugs = drugs;
                    } else {
                        // 成品药模式 - 检查是否选择了成品药
                        if (!window.finishedDrugId || window.finishedDrugId <= 0) {
                            layer.msg('请选择一个成品药', { icon: 2 });
                            return false;
                        }

                        // 设置成品药ID
                        field.finishedDrugId = window.finishedDrugId;

                        //将APIURL改为成品API
                        api_url = '/admin/prescription/add_with_finished_drug';
                    }

                    // 检查必填字段
                    if (!field.diagnosis) {
                        layer.msg('请输入诊断信息', { icon: 2 });
                        return false;
                    }
                    if (!field.tx_type) {
                        layer.msg('请选择药材类型', { icon: 2 });
                        return false;
                    }
                    if (!field.tx_day) {
                        layer.msg('请选择疗程天数', { icon: 2 });
                        return false;
                    }

                    // 收集用法用量数据
                    let dosage = [];
                    for (let key in field) {
                        if (key.startsWith('dosage_')) {
                            dosage.push(field[key] || '');
                        }
                    }
                    field.dosage = dosage.join('|');

                    // 确保不上传 old_pre_id
                    delete field.old_pre_id;

                    layer.load(2);
                    // 发送请求
                    $.ajax({
                        url: api_url,
                        type: 'POST',
                        data: field,
                        success: function (res) {
                            layer.closeAll('loading');
                            if (res.code === 200) {
                                layer.confirm('处方创建成功，是否返回列表页？', {
                                    icon: 1,
                                    title: '提示'
                                }, function (index) {
                                    layer.close(index);
                                    // 这里替换成你的处方列表页面URL
                                    window.location.href = 'prescription_list.html#/admin/prescription_list.html';
                                });
                            } else {
                                layer.msg(res.msg, { icon: 2 });
                            }
                        },
                        error: function (xhr) {
                            layer.closeAll('loading');
                            let errorMsg = '提交失败';
                            if (xhr.responseJSON && xhr.responseJSON.msg) {
                                errorMsg = xhr.responseJSON.msg;
                            }
                            layer.msg(errorMsg, { icon: 2 });
                        }
                    });

                    return false; // 阻止表单默认提交
                });
            });

            // 添加删除行的事件委托
            $(document).on('click', '.delete-row', function () {
                $(this).closest('tr').remove();
            });
        });
    </script>

    <script>
        // 图片查看函数
        function viewImage(src) {
            layui.use(['layer'], function () {
                var layer = layui.layer;
                layer.photos({
                    photos: {
                        title: "查看图片",
                        start: 0,
                        data: [{ src: src }]
                    },
                    footer: false
                });
            });
        }
    </script>
</body>

</html>