<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 患者详情</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/layui/layarea_lc.js"></script>
    <script src="/dist/js/main.js"></script>
    <style>
        /* 全局样式 */
        body {
            background-color: #f5f7fa;
        }

        /* 标题样式 */
        .line_font {
            font-size: 17px;
            font-weight: bold;
            margin: 10px 0 20px 0;
            border-bottom: 1px solid rgba(0,0,0,0.05);
            padding: 10px;
            color: #333;
            position: relative;
        }

        .line_font:after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #1E9FFF, #5FB878);
            border-radius: 3px;
        }

        /* 信息标签和内容 */
        .info-label {
            color: #666;
            padding-right: 15px;
            text-align: right;
            width: 100px;
            display: inline-block;
            font-size: 14px;
        }

        .info-content {
            color: #333;
            font-weight: 500;
            position: relative;
            transition: all 0.3s;
        }

        .info-item {
            margin: 15px 0;
            line-height: 24px;
            transition: all 0.3s;
        }

        .info-item:hover {
            transform: translateX(5px);
        }

        /* 信息区块 */
        .info-section {
            background: rgba(255, 255, 255, 0.9);
            padding: 25px;
            border-radius: 8px;
            margin-bottom: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .info-section:hover {
            box-shadow: 0 8px 25px rgba(0,0,0,0.08);
            transform: translateY(-3px);
        }

        /* 文本区域内容 */
        .text-area-content {
            background: rgba(249, 249, 249, 0.8);
            padding: 15px;
            border-radius: 8px;
            min-height: 60px;
            white-space: pre-wrap;
            border: 1px solid rgba(0,0,0,0.05);
            transition: all 0.3s;
        }

        .text-area-content:hover {
            background: rgba(249, 249, 249, 1);
            box-shadow: 0 3px 10px rgba(0,0,0,0.03);
        }

        /* 二维码样式 */
        #img_avatar {
            width: 230px;
            height: 230px;
            border-radius: 12px;
            border: none;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s;
        }

        #img_avatar:hover {
            transform: scale(1.02);
            box-shadow: 0 8px 20px rgba(0,0,0,0.15);
        }

        /* 保存按钮样式 */
        .save_local {
            border-radius: 6px;
            transition: all 0.3s;
            background: linear-gradient(to right, #f5f5f5, #ffffff);
            border-color: #e6e6e6;
        }

        .save_local:hover {
            background: linear-gradient(to right, #ffffff, #f5f5f5);
            box-shadow: 0 3px 8px rgba(0,0,0,0.08);
            border-color: #d9d9d9;
        }

        /* 相关患者链接样式 */
        .perm_item_rows {
            display: inline-block;
            padding: 5px 12px;
            background: rgba(30, 159, 255, 0.1);
            border-radius: 4px;
            margin: 3px;
            color: #1E9FFF;
            transition: all 0.3s;
        }

        .perm_item_rows:hover {
            background: rgba(30, 159, 255, 0.2);
            transform: translateY(-2px);
        }

        /* 错误提示样式 */
        .error-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(5px);
            z-index: 9999;
            display: flex;
            justify-content: center;
            align-items: center;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .error-overlay.show {
            opacity: 1;
        }

        .error-card {
            width: 400px;
            text-align: center;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.95);
            transform: translateY(20px);
            transition: all 0.3s ease;
        }

        .error-overlay.show .error-card {
            transform: translateY(0);
        }
    </style>
</head>

<body>
    <div class="layui-layout layui-layout-admin">
        <!-- 头部 -->
        <div class="layui-header">
            <div class="layui-logo">幸年堂</div>
            <ul class="layui-nav layui-layout-right">
                <li class="layui-nav-item" style="background-color:transparent!important;padding-right:10px">
                    <a href="javascript:;" style="display:flex;align-items:center;color:#fff"><img
                            src="/dist/images/user.png" class="layui-nav-img"><span class="user_name"></span></a>
                    <dl class="layui-nav-child pop_menu">
                        <dd><i class="iconfont">&#xe62f;</i><a href="change_password.html#7">用户资料</a></dd>
                        <dd><i class="iconfont">&#xe601;</i><a href="javascript:void(0)" class="refresh_cache">刷新缓存</a>
                        </dd>
                        <dd><i class="iconfont">&#xe634;</i><a href="login_out.html">退出登录</a></dd>
                    </dl>
                </li>
            </ul>
        </div>
        <!-- 导航 -->
        <ul class="layui-nav layui-nav-tree layui-nav-side" id="menuContainer" lay-bar="disabled" lay-accordion>
            <div style="margin: 50px 0;color: #bbb;text-align: center;">菜单加载中...</div>
        </ul>
        <!-- 主体内容 -->
        <div class="layui-body">
            <div class="body_child">
                <div class="layui-panel">
                    <div class="layui-card-header" style="border-bottom: 1px solid rgba(0,0,0,0.05); padding: 15px 20px;">
                        <div class="layui-row" style="padding-top:5px;">
                            <div class="layui-col-md8">
                                <span style="font-size: 18px; font-weight: 500; color: #333; position: relative; padding-left: 12px;">
                                    <span style="position: absolute; left: 0; top: 0; bottom: 0; width: 4px; background: linear-gradient(to bottom, #1E9FFF, #5FB878); border-radius: 2px;"></span>
                                    患者详情
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="layui-padding-3" style="min-height: 800px;">
                        <div class="info-section">
                            <div class="line_font">基本信息</div>
                            <div class="layui-row">
                                <div class="layui-col-xs4">
                                    <div class="info-item">
                                        <span class="info-label">手机号码：</span>
                                        <span class="info-content" id="phone">-</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">关系：</span>
                                        <span class="info-content" id="relation">-</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">患者来源：</span>
                                        <span class="info-content" id="patient_from">-</span>
                                    </div>
                                </div>
                                <div class="layui-col-xs4">
                                    <div class="info-item">
                                        <span class="info-label">患者姓名：</span>
                                        <span class="info-content" id="name">-</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">性别：</span>
                                        <span class="info-content" id="sex">-</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">微信号：</span>
                                        <span class="info-content" id="weixin">-</span>
                                    </div>
                                </div>
                                <div class="layui-col-xs4">
                                    <div class="info-item">
                                        <span class="info-label">出生日期：</span>
                                        <span class="info-content" id="born_date">-</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">主治医生：</span>
                                        <span class="info-content" id="doctor">-</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">患者等级：</span>
                                        <span class="info-content" id="patient_level">-</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="info-section">
                            <div class="line_font">其他信息</div>
                            <div class="layui-row">
                                <div class="layui-col-xs4">
                                    <div class="info-item">
                                        <span class="info-label">医保卡：</span>
                                        <span class="info-content" id="ins_card_num">-</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">身高：</span>
                                        <span class="info-content" id="height">-</span>
                                    </div>
                                </div>
                                <div class="layui-col-xs4">
                                    <div class="info-item">
                                        <span class="info-label">身份证号：</span>
                                        <span class="info-content" id="idcard">-</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">体重：</span>
                                        <span class="info-content" id="weight">-</span>
                                    </div>
                                </div>
                                <div class="layui-col-xs4">
                                    <div class="info-item">
                                        <span class="info-label">参保类型：</span>
                                        <span class="info-content" id="ins_type">-</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">婚姻状况：</span>
                                        <span class="info-content" id="isMarried">-</span>
                                    </div>
                                </div>
                            </div>
                            <div class="info-item">
                                <span class="info-label">详细地址：</span>
                                <span class="info-content" id="full_address">-</span>
                            </div>
                        </div>

                        <div class="info-section">
                            <div class="line_font">医疗信息</div>
                            <div class="layui-row">
                                <div class="layui-col-xs3">
                                    <div style="text-align: center;">
                                        <div style="padding: 15px; background: rgba(255,255,255,0.5); border-radius: 15px; display: inline-block; box-shadow: 0 5px 15px rgba(0,0,0,0.05);">
                                            <img id="img_avatar">
                                            <div style="margin: 15px 0 5px 0;">
                                                <button class="layui-btn layui-btn-primary layui-border save_local">
                                                    <i class="layui-icon layui-icon-download-circle"></i> 保存二维码
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-col-xs9">
                                    <div class="info-item">
                                        <span class="info-label">相关患者：</span>
                                        <div class="info-content moreUsers">-</div>
                                    </div>
                                    <div class="layui-row">
                                        <div class="layui-col-xs6">
                                            <div class="info-item">
                                                <div class="info-label">既往病史：</div>
                                                <div class="text-area-content" id="medical_history">-</div>
                                            </div>
                                        </div>
                                        <div class="layui-col-xs6">
                                            <div class="info-item">
                                                <div class="info-label">过敏史：</div>
                                                <div class="text-area-content" id="allergies">-</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-row">
                                        <div class="layui-col-xs6">
                                            <div class="info-item">
                                                <div class="info-label">主诉：</div>
                                                <div class="text-area-content" id="chief_complaint">-</div>
                                            </div>
                                        </div>
                                        <div class="layui-col-xs6">
                                            <div class="info-item">
                                                <div class="info-label">患者备注：</div>
                                                <div class="text-area-content" id="customer_notes">-</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <br>
                <br>
            </div>
        </div>
    </div>


    <script>
        layui.config({
            base: './mods/'
            , version: '1.0'
        });
        layui.use(['element', 'layer', 'util'], function () {
            var element = layui.element;
            var layer = layui.layer;
            var form = layui.form;
            var util = layui.util;
            var treeTable = layui.treeTable;
            var laydate = layui.laydate;
            var dropdown = layui.dropdown;
            var $ = layui.$;
            var id = Number(request.get("id"));
            if (!id) {
                layer.msg("参数错误，请从列表页面进入编辑页面", { icon: 2, time: 1000 });
                return false;
            }
            var layarea_lc = layui.layarea_lc;
            laydate.render({
                elem: '#ID-laydate-demo'
            });
            // 页面顶部插入缓存的用户数据
            let local_userinfo = localStorage.getItem('local_userinfo');
            let user_name = "";
            if (local_userinfo) {
                local_userinfo = JSON.parse(local_userinfo);
                user_name = local_userinfo.Name + " ( " + local_userinfo.Roles + " )";
            } else {
                user_name = "未登录";
            }
            $('.user_name').text(user_name);
            render_menu($);
            $('.refresh_cache').click(function () {
                refresh_cache($);
            });
            //渲染关系下拉框
            $.each(FamilyRelation, function (index, value) {
                $('#relation').append($('<option>').val(index).text(value));
            });
            //渲染参保类型下拉框
            $.each(Ins_Type, function (index, value) {
                $('#ins_type').append($('<option>').val(index).text(value));
            });
            // 渲染客户来源下拉框
            $.each(Patient_From, function (index, value) {
                $('#patient_from').append($('<option>').val(index).text(value));
            });

            // 重新渲染select元素
            form.render('select');
            //渲染医生下拉框
            layer.load(2);
            $.ajax({
                url: serverUrl + "/admin/user/list_low",
                type: "post",
                data: {
                    role_id: 4,
                },
                success: function (res) {
                    let data = res.data;
                    let html = '<option value="">选择医生</option>';
                    for (let i = 0; i < data.length; i++) {
                        html += '<option value="' + data[i].ID + '">' + data[i].Name + '</option>';
                    }
                    $('#doctor').html(html);
                    form.render('select');
                    load_full_data();
                },
                error: function (res) {
                    layer.msg(res.responseJSON.msg, {
                        time: 5000,
                    });
                }
            });
            //获取一码通的BASE64编码
            $.ajax({
                url: serverUrl + "/admin/codebar/patient_profile_qrcode",
                type: "post",
                data: {
                    "id": id,
                },
                success: function (res) {
                    let data = res.url;
                    $('#img_avatar').attr('src', 'data:image/png;base64,' + data);
                    $('.save_local').click(function () {
                        let img_base64 = data;
                        let img_name = "patient_profile_" + id + ".png";
                        let a = document.createElement('a');
                        a.href = img_base64;
                        a.download = img_name;
                        a.click();
                        layer.msg("保存成功", {
                            time: 2000,
                            icon: 1
                        });
                    });
                    $('.set_status').click(function () {
                        layer.msg('是封禁这个患者，还是封禁这个患者对应的帐号，还是只封禁一码通，让其它流程无法使用？');
                    });
                }, error: function (err) {
                    console.error(err);

                    // 创建模糊覆盖层和错误信息显示
                    let errorMsg = err.responseJSON ? err.responseJSON.msg : '请求失败，请稍后重试';
                    let blurOverlay = `
                        <div class="error-overlay">
                            <div class="error-card layui-card">
                                <div class="layui-card-body">
                                    <p style="font-size: 16px; padding: 20px 10px; color: #333;">${errorMsg}</p>
                                    <div style="padding: 10px 0 20px 0;">
                                        <button class="layui-btn layui-btn-normal return-btn" style="padding: 0 30px;">返回</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                    $('body').append(blurOverlay);

                    // 添加动画效果
                    setTimeout(function() {
                        $('.error-overlay').addClass('show');
                    }, 10);

                    // 添加返回按钮点击事件
                    $('.return-btn').click(function () {
                        $('.error-overlay').removeClass('show');
                        setTimeout(function() {
                            window.history.go(-1);
                            $('.error-overlay').remove();
                        }, 300);
                    });
                },
            });
            //查找相关用户
            $.ajax({
                url: serverUrl + "/admin/patient_profile/links",
                type: "post",
                data: {
                    "id": id,
                },
                success: function (res) {
                    let data = res.data;
                    let html = '';
                    for (let i = 0; i < data.length; i++) {
                        html += '<a href="/admin/patient_profile_show.html?id='+ data[i].ID +'#/admin/patient_profile_list.html" target=_blank><span class="perm_item_rows">' + data[i].Name + ' / ' + data[i].Phone + '</span></a>';
                    }
                    $('.moreUsers').html(html);
                }, error: function (err) {
                    console.error(err);

                    // 创建模糊覆盖层和错误信息显示
                    let errorMsg = err.responseJSON ? err.responseJSON.msg : '请求失败，请稍后重试';
                    let blurOverlay = `
                        <div class="error-overlay">
                            <div class="error-card layui-card">
                                <div class="layui-card-body">
                                    <p style="font-size: 16px; padding: 20px 10px; color: #333;">${errorMsg}</p>
                                    <div style="padding: 10px 0 20px 0;">
                                        <button class="layui-btn layui-btn-normal return-btn" style="padding: 0 30px;">返回</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                    $('body').append(blurOverlay);

                    // 添加动画效果
                    setTimeout(function() {
                        $('.error-overlay').addClass('show');
                    }, 10);

                    // 添加返回按钮点击事件
                    $('.return-btn').click(function () {
                        $('.error-overlay').removeClass('show');
                        setTimeout(function() {
                            window.history.go(-1);
                            $('.error-overlay').remove();
                        }, 300);
                    });
                },
            });
            //监听提交
            form.on('submit(formSubmitBtn)', function (data) {
                let field = data.field;
                field.id = id;
                field.address = field.province + '|' + field.city + '|' + field.county + '|' + field.address_detail;
                $.ajax({
                    url: serverUrl + "/admin/patient_profile/edit",
                    type: "post",
                    data: field,
                    success: function (res) {
                        if (res.code == 200) {
                            layer.msg(res.msg, {
                                time: 2000,
                                icon: 1
                            }
                            );
                        } else {
                            layer.msg(res.msg, {
                                time: 2000,
                                icon: 2
                            });
                        }
                    },
                    error: function (res) {
                        layer.msg(res.responseJSON.msg, {
                            time: 5000,
                            icon: 2
                        });
                    }
                });
                return false;
            });
            var load_full_data = function () {
                $.ajax({
                    url: serverUrl + "/admin/patient_profile/detail",
                    type: "post",
                    data: { "id": id },
                    success: function (res) {
                        layer.closeAll('loading');
                        let data = res.data;

                        // 基本信息
                        $('#phone').text(data.Phone || '-');
                        $('#name').text(data.Name || '-');
                        $('#sex').text(data.Sex == '1' ? '男' : '女');
                        $('#born_date').text(data.Born_date.replace("T00:00:00Z", "") || '-');
                        $('#relation').text(FamilyRelation[data.Relation] || '-');
                        $('#weixin').text(data.Weixin || '-');
                        $('#patient_from').text(Patient_From[data.Patient_from] || '-');
                        if (data.Doc_id) {
                            $.ajax({
                                url: serverUrl + "/admin/user/list_low",
                                type: "post",
                                data: {
                                    id: data.Doc_id
                                },
                                success: function (res) {
                                    if (res.code == 200 && res.data.length > 0) {
                                        $('#doctor').text(res.data[0].Name || data.Doc_id);
                                    } else {
                                        $('#doctor').text(data.Doc_id || '-');
                                    }
                                },
                                error: function() {
                                    $('#doctor').text(data.Doc_id || '-');
                                }
                            });
                        } else {
                            $('#doctor').text('-');
                        }
                        $('#patient_level').text(data.Level ? data.Level + '级' : '-');

                        // 其他信息
                        $('#idcard').text(data.Idcard || '-');
                        $('#ins_card_num').text(data.Ins_card_num || '-');
                        $('#ins_type').text(Ins_Type[data.Ins_type] || '-');
                        $('#height').text(data.Height ? data.Height + 'cm' : '-');
                        $('#weight').text(data.Weight ? data.Weight + 'kg' : '-');
                        $('#isMarried').text(data.Ismarried == '1' ? '已婚' : '未婚');

                        // 地址处理
                        let address_arr = data.Address.split('|');
                        $('#full_address').text(address_arr.join(' '));

                        // 医疗信息
                        $('#medical_history').text(data.Medical_history || '-');
                        $('#allergies').text(data.Allergies || '-');
                        $('#chief_complaint').text(data.Chief_complaint || '-');
                        $('#customer_notes').text(data.Customer_notes || '-');
                    },
                    error: function (err) {
                        layer.closeAll('loading');
                        console.error(err);

                        // 创建模糊覆盖层和错误信息显示
                        let errorMsg = err.responseJSON ? err.responseJSON.msg : '请求失败，请稍后重试';
                        let blurOverlay = `
                            <div class="error-overlay">
                                <div class="error-card layui-card">
                                    <div class="layui-card-body">
                                        <p style="font-size: 16px; padding: 20px 10px; color: #333;">${errorMsg}</p>
                                        <div style="padding: 10px 0 20px 0;">
                                            <button class="layui-btn layui-btn-normal return-btn" style="padding: 0 30px;">返回</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;
                        $('body').append(blurOverlay);

                        // 添加动画效果
                        setTimeout(function() {
                            $('.error-overlay').addClass('show');
                        }, 10);

                        // 添加返回按钮点击事件
                        $('.return-btn').click(function () {
                            $('.error-overlay').removeClass('show');
                            setTimeout(function() {
                                window.history.go(-1);
                                $('.error-overlay').remove();
                            }, 300);
                        });
                    }
                });
            }
        });
    </script>
</body>

</html>