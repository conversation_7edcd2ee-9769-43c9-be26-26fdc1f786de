<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 订单异常提示</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
</head>

<body>
    <div class="layui-layout layui-layout-admin">
        <!-- 头部 -->
        <div class="layui-header">
            <div class="layui-logo">幸年堂</div>
            <ul class="layui-nav layui-layout-right">
                <li class="layui-nav-item" style="background-color:transparent!important;padding-right:10px">
                    <a href="javascript:;" style="display:flex;align-items:center;color:#fff"><img
                            src="/dist/images/user.png" class="layui-nav-img"><span class="user_name"></span></a>
                    <dl class="layui-nav-child">
                        <dd><a href="change_password.html#7">用户资料</a></dd>
                        <dd><a href="login_out.html"><i class="iconfont">&#xe634;</i> 退出登录</a></dd>
                    </dl>
                </li>
            </ul>
        </div>
        <!-- 导航 -->
        <ul class="layui-nav layui-nav-tree layui-nav-side" id="menuContainer" lay-bar="disabled" lay-accordion>
            <div style="margin: 50px 0;color: #bbb;text-align: center;">菜单加载中...</div>
        </ul>
        <!-- 主体内容 -->
        <div class="layui-body">
            <div class="body_child">
                <div class="error-container-wrapper">
                    <div class="error-container">
                        <div class="glass-card">
                            <div class="icon-container">
                                <i class="layui-icon layui-icon-tips"></i>
                            </div>
                            <div class="message">
                                <h2>提示信息</h2>
                                <p>这个页面似乎想错了，在订单列表页是有异常、退货订单的筛选的</p>
                            </div>
                            <div class="countdown">
                                <p>将在 <span id="timer">10</span> 秒后跳转到订单列表页</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <style>
                .body_child {
                    position: relative;
                    height: 100%;
                    overflow: hidden;
                }

                .error-container-wrapper {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    height: 100%;
                    width: 100%;
                    position: absolute;
                    top: 0;
                    left: 0;
                    z-index: 10;
                }

                .error-container {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    padding: 20px;
                }

                .glass-card {
                    background: white;
                    backdrop-filter: blur(10px);
                    border-radius: 15px;
                    border: 1px solid rgba(255, 255, 255, 0.18);
                    box-shadow: 0 2px 12px 0 rgba(31, 38, 135, 0.05);
                    padding: 40px;
                    text-align: center;
                    max-width: 500px;
                    width: 100%;
                    transition: all 0.3s ease;
                }

                .glass-card:hover {
                    transform: translateY(-5px);
                    box-shadow: 0 12px 40px 0 rgba(31, 38, 135, 0.25);
                }

                .icon-container {
                    margin-bottom: 20px;
                }

                .icon-container .layui-icon {
                    font-size: 60px;
                    color: #FFB800;
                    background: linear-gradient(135deg, #FFB800, #FF5722);
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                    animation: pulse 2s infinite;
                }

                @keyframes pulse {
                    0% {
                        transform: scale(1);
                    }
                    50% {
                        transform: scale(1.1);
                    }
                    100% {
                        transform: scale(1);
                    }
                }

                .message h2 {
                    font-size: 24px;
                    margin-bottom: 15px;
                    color: #333;
                    font-weight: 500;
                }

                .message p {
                    font-size: 16px;
                    color: #666;
                    margin-bottom: 25px;
                    line-height: 1.6;
                }

                .countdown {
                    margin-top: 20px;
                    padding-top: 20px;
                    border-top: 1px solid rgba(255, 255, 255, 0.3);
                }

                .countdown p {
                    font-size: 16px;
                    color: #666;
                }

                #timer {
                    font-weight: bold;
                    font-size: 20px;
                    color: #009688;
                    background: linear-gradient(135deg, #009688, #5FB878);
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                }

                /* 移除动画效果，保持内容居中 */
            </style>
        </div>
    </div>


    <script>
        layui.use(['element', 'layer', 'util'], function () {
            var element = layui.element;
            var layer = layui.layer;
            var util = layui.util;
            var treeTable = layui.treeTable;
            var dropdown = layui.dropdown;
            var $ = layui.$;
            // 页面顶部插入缓存的用户数据
            let local_userinfo = localStorage.getItem('local_userinfo');
            let user_name = "";
            if (local_userinfo) {
                local_userinfo = JSON.parse(local_userinfo);
                user_name = local_userinfo.Name + " ( " + local_userinfo.Roles + " )";
            } else {
                user_name = "未登录";
            }
            $('.user_name').text(user_name);
            render_menu($);
            $('.refresh_cache').click(function () {
                refresh_cache($);
            });

            // 倒计时功能
            var countdown = 10;
            var timer = document.getElementById('timer');
            var countdownInterval = setInterval(function() {
                countdown--;
                timer.textContent = countdown;

                if (countdown <= 0) {
                    clearInterval(countdownInterval);
                    // 跳转到订单列表页
                    window.location.href = 'order_list.html';
                }
            }, 1000);
        });
    </script>
</body>

</html>