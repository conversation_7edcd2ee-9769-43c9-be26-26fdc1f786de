<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 患者编辑</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/layui/layarea_lc.js"></script>
    <script src="/dist/js/main.js"></script>
    <style>
        .line_font {
            font-size: 17px;
            font-weight: bold;
            margin: 10px 0 20px 0;
            border-bottom: 1px solid #efefef;
            padding: 10px;
        }
    </style>
</head>

<body>
    <div class="layui-layout layui-layout-admin">
        <!-- 头部 -->
        <div class="layui-header">
            <div class="layui-logo">幸年堂</div>
            <ul class="layui-nav layui-layout-right">
                <li class="layui-nav-item" style="background-color:transparent!important;padding-right:10px">
                    <a href="javascript:;" style="display:flex;align-items:center;color:#fff"><img
                            src="/dist/images/user.png" class="layui-nav-img"><span class="user_name"></span></a>
                    <dl class="layui-nav-child pop_menu">
                        <dd><i class="iconfont">&#xe62f;</i><a href="change_password.html#7">用户资料</a></dd>
                        <dd><i class="iconfont">&#xe601;</i><a href="javascript:void(0)" class="refresh_cache">刷新缓存</a>
                        </dd>
                        <dd><i class="iconfont">&#xe634;</i><a href="login_out.html">退出登录</a></dd>
                    </dl>
                </li>
            </ul>
        </div>
        <!-- 导航 -->
        <ul class="layui-nav layui-nav-tree layui-nav-side" id="menuContainer" lay-bar="disabled" lay-accordion>
            <div style="margin: 50px 0;color: #bbb;text-align: center;">菜单加载中...</div>
        </ul>
        <!-- 主体内容 -->
        <div class="layui-body">
            <div class="body_child">
                <div class="layui-panel">
                    <div class="layui-card-header">
                        <div class="layui-row" style="padding-top:10px;">
                            <div class="layui-col-md8">患者编辑</div>
                        </div>
                    </div>
                    <div class="layui-padding-3" style="min-height: 800px;">



                        <form class="layui-form" lay-filter="form_edit" action="" onsubmit="return false">
                            <div class="line_font">
                                <span class="red_star">*</span>必填项
                            </div>
                            <div class="layui-row">
                                <div class="layui-col-xs4">

                                    <div class="layui-form-item">
                                        <label class="layui-form-label">手机号码</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="phone" placeholder="手机号码" autocomplete="off"
                                                class="layui-input" onkeyup="isPhone(this)">
                                        </div>
                                    </div>


                                    <div class="layui-form-item">

                                        <label class="layui-form-label">关系</label>
                                        <div class="layui-input-block">
                                            <select name="relation" id="relation"></select>
                                        </div>

                                    </div>

                                    <div class="layui-form-item">
                                        <label class="layui-form-label">部门</label>
                                        <div class="layui-input-block">
                                            <input type="text" placeholder="医助是有隶属部门的，不用单独填" autocomplete="off"
                                                class="layui-input" disabled>
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">患者来源</label>
                                        <div class="layui-input-block">
                                            <select name="patient_from" id="patient_from"> </select>
                                        </div>
                                    </div>


                                </div>
                                <div class="layui-col-xs4">

                                    <div class="layui-form-item">
                                        <label class="layui-form-label">患者姓名</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="name" required placeholder="请输入内容"
                                                autocomplete="off" class="layui-input">
                                        </div>
                                    </div>

                                    <div class="layui-form-item">
                                        <label class="layui-form-label">性别</label>
                                        <div class="layui-input-block">
                                            <input type="radio" name="sex" value="1" title="男">
                                            <input type="radio" name="sex" value="0" title="女">
                                        </div>
                                    </div>

                                    <div class="layui-form-item">
                                        <label class="layui-form-label">微信号</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="weixin" required placeholder="请输入微信号"
                                                autocomplete="off" class="layui-input">
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">医助</label>
                                        <div class="layui-input-block">
                                            <input type="text" required placeholder="医助添加的，会记录他的ID" autocomplete="off"
                                                class="layui-input" disabled>
                                        </div>
                                    </div>

                                </div>
                                <div class="layui-col-xs4">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">出生日期</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="born_date" required placeholder="请输入内容"
                                                autocomplete="off" class="layui-input" id="ID-laydate-demo">
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">医生</label>
                                        <div class="layui-input-block">
                                            <select name="doctor" id="doctor">
                                            </select>
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">患者等级</label>
                                        <div class="layui-input-block">
                                            <select name="patient_level" id="patient_level">
                                                <option value="">患者等级</option>
                                                <option value="A">A级</option>
                                                <option value="B">B级</option>
                                                <option value="C">C级</option>
                                                <option value="D">D级</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">就诊类型</label>
                                        <div class="layui-input-block">
                                            <input type="text" required placeholder="初诊？诊断次数不用提交，系统可查。且新建用户，首次肯定是初诊"
                                                autocomplete="off" class="layui-input" disabled>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="line_font">
                                选填项</div>
                            <div class="layui-row">
                                <div class="layui-col-xs4">

                                    <div class="layui-form-item">
                                        <label class="layui-form-label">医保卡</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="ins_card_num" placeholder="请输入医保卡号"
                                                autocomplete="off" class="layui-input">
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">身高</label>
                                        <div class="layui-input-block">
                                            <input type="number" name="height" placeholder="请输入身高" autocomplete="off"
                                                class="layui-input">
                                        </div>
                                    </div>


                                </div>
                                <div class="layui-col-xs4">

                                    <div class="layui-form-item">
                                        <label class="layui-form-label">身份证号</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="idcard" placeholder="请输入身份证号" autocomplete="off"
                                                class="layui-input">
                                        </div>
                                    </div>

                                    <div class="layui-form-item">
                                        <label class="layui-form-label">体重</label>
                                        <div class="layui-input-block">
                                            <input type="number" name="weight" placeholder="请输入体重" autocomplete="off"
                                                class="layui-input">
                                        </div>
                                    </div>



                                </div>
                                <div class="layui-col-xs4">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">参保类型</label>
                                        <div class="layui-input-block">
                                            <select name="ins_type" id="ins_type">
                                            </select>
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">婚否</label>
                                        <div class="layui-input-block">
                                            <input type="radio" name="isMarried" value="1" title="已婚">
                                            <input type="radio" name="isMarried" value="0" title="未婚">
                                        </div>
                                    </div>

                                </div>


                                <div class="layui-col-xs12">
                                    <div class="layui-form-item" id="area-picker">
                                        <div class="layui-form-label">地址</div>
                                        <div class="layui-input-inline" style="width: 200px;">
                                            <select name="province" class="province-selector" lay-filter="province-1">
                                                <option value="">请选择省</option>
                                            </select>
                                        </div>
                                        <div class="layui-input-inline" style="width: 200px;">
                                            <select name="city" class="city-selector" lay-filter="city-1">
                                                <option value="">请选择市</option>
                                            </select>
                                        </div>
                                        <div class="layui-input-inline" style="width: 200px;">
                                            <select name="county" class="county-selector" lay-filter="county-1">
                                                <option value="">请选择区</option>
                                            </select>
                                        </div>
                                        <div class="layui-input-inline" style="width:360px;">
                                            <input type="text" name="address_detail" placeholder="请输入具体地址"
                                                autocomplete="off" class="layui-input">
                                        </div>
                                        <div class="layui-form-mid" style="color: #999;">* 订单地址信息已单独写进订单表，用户表地址已无实际意义，待删
                                        </div>
                                    </div>
                                </div>

                            </div>


                            <div class="line_font">
                                补充项<span
                                    style="font-size: 12px;font-weight: normal;margin-left: 10px;">含除了新建患者时字段之外的数据</span>
                            </div>




















                            <div class="layui-row">
                                <div class="layui-col-xs3">

                                    <!-- 显示图片（非上传），图片下方2个并排的按钮 -->
                                    <div class="layui-form-item">
                                        <div style="text-align: center;">
                                            <img id="img_avatar"
                                                style="width: 230px;height: 230px;border-radius:9px;border: 1px solid #999;">
                                            <div
                                                style="display: flex;width: 230px; justify-content: space-around;margin: 10px auto;">
                                                <button
                                                    class="layui-btn layui-btn-primary layui-border set_status">封禁</button>
                                                <button
                                                    class="layui-btn layui-btn-primary layui-border save_local">保存本地</button>
                                            </div>
                                        </div>
                                    </div>

                                </div>



                                <div class="layui-col-xs9">

                                    <!-- 单行文字 -->
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">相关患者</label>
                                        <div class="layui-form-mid moreUsers">-</div>
                                    </div>

                                    <div class="layui-form-item">
                                        <div class="layui-col-xs6">

                                            <div class="layui-form-item">
                                                <label class="layui-form-label">既往病史</label>
                                                <div class="layui-input-block">
                                                    <textarea name="medical_history" placeholder="请输入既往病史"
                                                        class="layui-textarea"></textarea>
                                                </div>
                                            </div>

                                        </div>
                                        <div class="layui-col-xs6">


                                            <div class="layui-form-item">
                                                <label class="layui-form-label">过敏史</label>
                                                <div class="layui-input-block">
                                                    <textarea name="allergies" placeholder="请输入过敏史"
                                                        class="layui-textarea"></textarea>
                                                </div>
                                            </div>


                                        </div>
                                    </div>

                                    <div class="layui-form-item">
                                        <div class="layui-col-xs6">



                                            <div class="layui-form-item">
                                                <label class="layui-form-label">主诉</label>
                                                <div class="layui-input-block">
                                                    <textarea name="chief_complaint" placeholder="请输入主诉"
                                                        class="layui-textarea"></textarea>
                                                </div>
                                            </div>



                                        </div>
                                        <div class="layui-col-xs6">


                                            <div class="layui-form-item">
                                                <label class="layui-form-label">患者备注</label>
                                                <div class="layui-input-block">
                                                    <textarea name="customer_notes" placeholder="请输入患者备注"
                                                        class="layui-textarea"></textarea>
                                                </div>
                                            </div>


                                        </div>
                                    </div>

                                </div>


                            </div>






























                            <div style="display: flex; justify-content: center;margin: 50px;">
                                <button class="layui-btn" lay-submit lay-filter="formSubmitBtn"
                                    style="margin-right: 50px;">确认修改</button>
                                <button type="reset" class="layui-btn layui-btn-primary"
                                    onclick="window.history.go(-1);">取消</button>
                            </div>

                        </form>

                    </div>
                </div>
                <br>
                <br>
            </div>
        </div>
    </div>


    <script>
        layui.config({
            base: './mods/'
            , version: '1.0'
        });
        layui.use(['element', 'layer', 'util'], function () {
            var element = layui.element;
            var layer = layui.layer;
            var form = layui.form;
            var util = layui.util;
            var treeTable = layui.treeTable;
            var laydate = layui.laydate;
            var dropdown = layui.dropdown;
            var $ = layui.$;
            var id = Number(request.get("id"));
            if (!id) {
                layer.msg("参数错误，请从列表页面进入编辑页面", { icon: 2, time: 1000 });
                return false;
            }
            var layarea_lc = layui.layarea_lc;
            laydate.render({
                elem: '#ID-laydate-demo'
            });
            // 页面顶部插入缓存的用户数据
            let local_userinfo = localStorage.getItem('local_userinfo');
            let user_name = "";
            if (local_userinfo) {
                local_userinfo = JSON.parse(local_userinfo);
                user_name = local_userinfo.Name + " ( " + local_userinfo.Roles + " )";
            } else {
                user_name = "未登录";
            }
            $('.user_name').text(user_name);
            render_menu($);
            $('.refresh_cache').click(function () {
                refresh_cache($);
            });
            //渲染关系下拉框
            $.each(FamilyRelation, function (index, value) {
                $('#relation').append($('<option>').val(index).text(value));
            });
            //渲染参保类型下拉框
            $.each(Ins_Type, function (index, value) {
                $('#ins_type').append($('<option>').val(index).text(value));
            });
            // 渲染客户来源下拉框
            $.each(Patient_From, function (index, value) {
                $('#patient_from').append($('<option>').val(index).text(value));
            });

            // 重新渲染select元素
            form.render('select');
            //渲染医生下拉框
            layer.load(2);
            $.ajax({
                url: serverUrl + "/admin/user/list_low",
                type: "post",
                data: {
                    role_id: 4,
                },
                success: function (res) {
                    let data = res.data;
                    let html = '<option value="">选择医生</option>';
                    for (let i = 0; i < data.length; i++) {
                        html += '<option value="' + data[i].ID + '">' + data[i].Name + '</option>';
                    }
                    $('#doctor').html(html);
                    form.render('select');
                    load_full_data();
                },
                error: function (res) {
                    layer.msg(res.responseJSON.msg, {
                        time: 5000,
                    });
                }
            });
            //获取一码通的BASE64编码
            $.ajax({
                url: serverUrl + "/admin/codebar/patient_profile_qrcode",
                type: "post",
                data: {
                    "id": id,
                },
                success: function (res) {
                    let data = res.url;
                    $('#img_avatar').attr('src', 'data:image/png;base64,' + data);
                    $('.save_local').click(function () {
                        let img_base64 = data;
                        let img_name = "patient_profile_" + id + ".png";
                        let a = document.createElement('a');
                        a.href = img_base64;
                        a.download = img_name;
                        a.click();
                        layer.msg("保存成功", {
                            time: 2000,
                            icon: 1
                        });
                    });
                    $('.set_status').click(function () {
                        layer.msg('是封禁这个患者，还是封禁这个患者对应的帐号，还是只封禁一码通，让其它流程无法使用？');
                    });
                }, error: function (err) {
                    console.error(err);
                    layer.msg(err.responseJSON ? err.responseJSON.msg : '请求失败', { icon: 2, time: 1000 });
                },
            });
            //查找相关用户
            $.ajax({
                url: serverUrl + "/admin/patient_profile/links",
                type: "post",
                data: {
                    "id": id,
                },
                success: function (res) {
                    let data = res.data;
                    let html = '';
                    for (let i = 0; i < data.length; i++) {
                        html += '<a href=?id=' + data[i].ID + ' target=_blank><span class="perm_item_rows">' + data[i].Name + ' / ' + data[i].Phone + '</span></a>';
                    }
                    $('.moreUsers').html(html);
                }, error: function (err) {
                    console.error(err);
                    layer.msg(err.responseJSON ? err.responseJSON.msg : '请求失败', { icon: 2, time: 1000 });
                },
            });
            //监听提交
            form.on('submit(formSubmitBtn)', function (data) {
                let field = data.field;
                field.id = id;
                field.address = field.province + '|' + field.city + '|' + field.county + '|' + field.address_detail;
                layer.load(2);
                $.ajax({
                    url: serverUrl + "/admin/patient_profile/edit",
                    type: "post",
                    data: field,
                    success: function (res) {
                        layer.closeAll('loading');
                        if (res.msg == "数据更新成功") {
                            layer.msg(res.msg, {
                                time: 2000,
                                icon: 1
                            });
                        } else {
                            layer.alert(res.msg, {
                                icon: 1,
                                title: '操作结果',
                                btn: '好的'
                            });
                        }
                    },
                    error: function (res) {
                        layer.closeAll('loading');
                        layer.alert(res.responseJSON.msg, {
                            icon: 2,
                            title: '操作结果',
                            btn: '好的'
                        });
                    }
                });
                return false;
            });
            var load_full_data = function () {
                //等医生数据加载并渲染完毕后，再加载并渲染所有数据（包括当前选择的是哪个医生），这样是为了防止异步加载时数据并行BUG
                $.ajax({
                    url: serverUrl + "/admin/patient_profile/detail",
                    type: "post",
                    data: {
                        "id": id,
                    },
                    success: function (res) {
                        layer.closeAll('loading');
                        let data = res.data;
                        form.val("form_edit", {
                            "phone": data.Phone,
                            "name": data.Name,
                            "sex": data.Sex,
                            "born_date": data.Born_date.replace("T00:00:00Z", ""),
                            "idcard": data.Idcard,
                            "weixin": data.Weixin,
                            "ins_card_num": data.Ins_card_num,
                            "ins_type": data.Ins_type,
                            "height": data.Height,
                            "weight": data.Weight,
                            "isMarried": data.Ismarried,
                            "patient_from": data.Patient_from,
                            "address_detail": data.Address.split('|')[3],
                            "doctor": data.Doc_id,
                            "patient_level": data.Level,
                            "medical_history": data.Medical_history,
                            "allergies": data.Allergies,
                            "chief_complaint": data.Chief_complaint,
                            "customer_notes": data.Customer_notes,
                        });
                        //渲染关系下拉框
                        $('#relation').val(data.Relation);
                        //渲染参保类型下拉框
                        $('#ins_type').val(data.Ins_type);
                        //渲染客户来源下拉框
                        $('#patient_from').val(data.Patient_from);
                        //渲染医生下拉框
                        $('#doctor').val(data.Doc_id);
                        //渲染地区选择器
                        // LAYUI第三方地址插件，开发文档地址：https://dev.layuion.com/extend/layarea/
                        // MD这个插件渲染数据是data不是value，MD因为这个没仔细看文档关注率浪费1个小时，标记一下，痛定思痛
                        let address_arr = data.Address.split('|');
                        layarea_lc.render({
                            elem: '#area-picker',
                            name: 'name',
                            data: {
                                province: address_arr[0],
                                city: address_arr[1],
                                county: address_arr[2]
                            },
                            change: function (res) {
                                // console.log(res);
                                // form.val("form_edit", {
                                //     "address_detail": res.province + '|' + res.city + '|' + res.county + '|' + address_arr[3]
                                // });
                            }
                        });
                        form.render('select');
                    }, error: function (err) {
                        layer.closeAll('loading');
                        // 创建模糊覆盖层和错误信息显示
                        let errorMsg = err.responseJSON ? err.responseJSON.msg : '请求失败，请稍后重试';
                        let blurOverlay = `
                            <div class="error-overlay" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; 
                                background-color: rgba(255, 255, 255, 0.8); backdrop-filter: blur(5px); z-index: 9999; 
                                display: flex; justify-content: center; align-items: center;">
                                <div class="layui-card" style="width: 400px; text-align: center; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                                    <div class="layui-card-body">
                                        <p style="font-size: 16px; padding: 20px 10px; color: #333;">${errorMsg}</p>
                                        <div style="padding: 10px 0 20px 0;">
                                            <button class="layui-btn layui-btn-normal return-btn" style="padding: 0 30px;">返回</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;
                        $('body').append(blurOverlay);

                        // 添加返回按钮点击事件
                        $('.return-btn').click(function () {
                            window.history.back();
                        });
                    },
                });
            }
        });
    </script>
</body>

</html>