        #bottom_btn {
            display: flex;
            justify-content: center;
            margin-top: 50px;
        }

        .user_info {
            display: flex;
            align-items: center;
        }

        .user_info div {
            font-weight: bold;
            margin-right: 20px;
            font-size: 17px;
        }
        /* 视频容器包装器 */
        .video-container-wrapper {
            position: fixed;
            z-index: 6;
            top: 85px;
            left: 240px;
            flex-direction: column;
            align-items: center;
            margin: 0 auto;
            max-width: 100%;
            padding: 20px;
            background-color: white;
            border-radius: 20px;
            box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
        }
        #video_container {
            width: 760px;
            height: 660px;
            background-color: black;
            position: relative;
            padding: 10px;
        }

        /* 大视频窗口样式 */
        .main-video-player {
            width: 100%;
            height: 100%;
            background-color: #333;
            position: absolute;
            top: 0;
            left: 0;
            z-index: 1; /* 设置较低的z-index使其位于底层 */
            border-radius: 8px;
            overflow: hidden;
        }

        /* 小视频窗口容器 */
        .small-videos-container {
            display: flex;
            position: absolute;
            bottom: 20px;
            right: 20px;
            z-index: 2; /* 设置较高的z-index使其位于上层 */
            gap: 10px;
        }

        /* 小视频窗口样式 */
        .small-video-player {
            width: 150px;
            height: 100px;
            background-color: #333;
            position: relative;
            border-radius: 8px;
            overflow: hidden;
            cursor: pointer;
            transition: transform 0.2s;
            border: 2px solid rgba(255, 255, 255, 0.5);
        }

        .small-video-player:hover {
            transform: scale(1.05);
            box-shadow: 0 0 8px rgba(255, 255, 255, 0.5);
        }

        .video-player video,
        .main-video-player video,
        .small-video-player video {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .video-label {
            position: absolute;
            bottom: 10px;
            left: 10px;
            color: white;
            background: rgba(0, 0, 0, 0.5);
            padding: 4px 8px;
            border-radius: 3px;
            font-size: 12px;
        }

        .main-video-player .video-label {
            font-size: 14px;
            padding: 5px 10px;
        }

        #control_buttons {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 20px;
        }

        .recording {
            background-color: #FF4D4F !important;
        }

        /* 表单容器 */
        .layui-form-item-container {
            padding: 20px;
        }