<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 角色列表</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
</head>

<body>
    <div class="layui-layout layui-layout-admin">
        <!-- 头部 -->
        <div class="layui-header">
            <div class="layui-logo">幸年堂</div>
            <ul class="layui-nav layui-layout-right">
                <li class="layui-nav-item" style="background-color:transparent!important;padding-right:10px">
                    <a href="javascript:;" style="display:flex;align-items:center;color:#fff"><img
                            src="/dist/images/user.png" class="layui-nav-img"><span class="user_name"></span></a>
                                        <dl class="layui-nav-child pop_menu">
                        <dd><i class="iconfont">&#xe62f;</i><a href="change_password.html#7">用户资料</a></dd>
                        <dd><i class="iconfont">&#xe601;</i><a href="javascript:void(0)" class="refresh_cache">刷新缓存</a></dd>
                        <dd><i class="iconfont">&#xe634;</i><a href="login_out.html">退出登录</a></dd>
                    </dl>
                </li>
            </ul>
        </div>
        <!-- 导航 -->
        <ul class="layui-nav layui-nav-tree layui-nav-side" id="menuContainer" lay-bar="disabled" lay-accordion>
            <div style="margin: 50px 0;color: #bbb;text-align: center;">菜单加载中...</div>
        </ul>
        <!-- 主体内容 -->
        <div class="layui-body">
            <div class="body_child">
                <div class="layui-panel">
                    <div class="layui-card-header">
                        <div class="layui-row" style="padding-top:10px;">
                            <div class="layui-col-md11 layui-col-sm10">角色列表</div>
                            <div class="layui-col-md1 layui-col-sm2">
                                <button type="submit"
                                    class=" layui-btn layui-btn-primary layui-border-red create_btn"
                                    lay-submit=""><i class="layui-icon">&#xe654;</i> 新增
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="layui-padding-3" style="min-height: 800px;">
                        <table id="role_list" lay-filter="role_list"></table>
                    </div>
                </div>
                <br>
                <br>
            </div>
        </div>
    </div>

    <script type="text/html" id="role_list_bar">
    {{# if(d.ID !== 1){ }}
        <a class="layui-btn layui-btn-xs role_permission" lay-event="permission">权限</a>
        <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
        <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
    {{# } else { }}
        <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    {{# } }}
</script>

    <script>
        layui.use(['element', 'layer', 'util', 'table'], function () {
            var element = layui.element;
            var layer = layui.layer;
            var util = layui.util;
            var table = layui.table;
            var $ = layui.$;

            // 页面顶部插入缓存的用户数据
            let local_userinfo = localStorage.getItem('local_userinfo');
            let user_name = "";
            if (local_userinfo) {
                local_userinfo = JSON.parse(local_userinfo);
                user_name = local_userinfo.Name + " ( " + local_userinfo.Roles + " )";
            } else {
                user_name = "未登录";
            }
            $('.user_name').text(user_name);
            render_menu($);
            $('.refresh_cache').click(function () {
                refresh_cache($);
            });
            // 角色列表渲染
            var tableIns = table.render({
                elem: '#role_list',
                method: 'post',
                url: '/admin/roles/list',
                parseData: function (res) {
                    return {
                        "code": 0,
                        "msg": "success",
                        "data": res.data
                    };
                },
                page: false,
                cols: [[
                    { field: 'ID', width: 80, align: 'center', title: 'ID' },
                    { field: 'Role', width: 100, title: '角色名' },
                    { field: 'Sys', width: 100, title: '内置', width: 80, align: 'center', templet: function (d) { return d.Sys === 1 ? '是' : '否'; } },
                    {
                        field: 'Perm_ids',
                        title: '权限名称',
                        minWidth: 300,
                        templet: function (d) {
                            const local_perm_res_data = JSON.parse(localStorage.getItem('local_perm_res_data'));
                            let perm_data = perm_id2name(local_perm_res_data, d.Perm_ids);
                            return perm_data.length > 0 ? "<span class='perm_item_rows'>" + perm_data.join('</span><span class="perm_item_rows">') + "</span>" : ' - ';
                        }
                    },
                    { title: '操作', align: 'center', toolbar: '#role_list_bar', width: 280 }
                ]],
                done: function (res, curr, count) {
                    // 可选择添加更多操作
                }
            });

            // 监听工具条事件（table 的事件监听方式）
            table.on('tool(role_list)', function (obj) {
                var data = obj.data; // 当前行的数据
                var event = obj.event; // 事件类型

                if (event === 'permission') {
                    // 弹出模态框
                    layer.open({
                        type: 2, // iframe 类型
                        title: '绑定权限 - ' + data.Role,
                        area: ['350px', '100%'], // 模态框宽高
                        shadeClose: true, // 点击遮罩关闭弹窗
                        offset: 'r',
                        anim: 'slideLeft',
                        content: '/admin/role_bind_perm.html?role_id=' + data.ID // 加载的页面
                    });
                } else if (event === 'edit') {
                    layer.open({
                        type: 2,
                        title: '编辑角色 - ' + data.Role,
                        area: ['500px', '300px'],
                        shadeClose: true,
                        content: '/admin/role_edit.html?id=' + data.ID
                    });
                    console.log('编辑', data);
                } else if (event === 'del') {
                    layer.confirm('确定删除该角色吗？', function (index) {
                        layer.load(2);
                        $.ajax({
                            url: '/admin/roles/del',
                            type: 'post',
                            data: {
                                id: data.ID
                            },
                            success: function (res) {
                                layer.closeAll('loading');
                                layer.msg(res.msg);
                                tableIns.reload();
                            },
                            error: function (res) {
                                layer.closeAll('loading');
                                layer.msg(res.responseJSON.msg);
                            },
                        });
                        layer.close(index);
                    });
                }
            });
            $('.create_btn').click(function () {
                layer.open({
                    type: 2,
                    title: '新增角色',
                    area: ['500px', '300px'],
                    shadeClose: true,
                    content: '/admin/role_add.html'
                });
            })
        });
    </script>

</body>

</html>